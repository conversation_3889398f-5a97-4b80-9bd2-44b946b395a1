plugins {
    id("com.android.library")
//    id("com.vanniktech.maven.publish")
}

android {
    namespace = "com.hbb20"
    compileSdk = 34

    defaultConfig {
        minSdk = 24
        targetSdk = 34
    }

    buildTypes {
        debug {

        }
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro")
        }
    }
    defaultConfig {
        vectorDrawables.useSupportLibrary = true
    }

    tasks.withType<Javadoc>().configureEach {
        enabled = false
    }
}

dependencies {
    implementation(fileTree(mapOf("include" to listOf("*.jar"), "dir" to "libs")))
    implementation("com.google.android.material:material:1.10.0")
    testImplementation("junit:junit:4.13.2")
    implementation("androidx.recyclerview:recyclerview:1.2.1")
    implementation("androidx.appcompat:appcompat:1.3.1")
    implementation("androidx.core:core:1.6.0")
    implementation("io.michaelrocks:libphonenumber-android:8.12.52")
    implementation("androidx.cardview:cardview:1.0.0")
}