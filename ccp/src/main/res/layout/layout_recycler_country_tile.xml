<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true">

    <LinearLayout
        android:id="@+id/linear_flag_holder"
        android:layout_width="35dp"
        android:layout_height="45dp"
        android:layout_marginLeft="@dimen/google_1x"
        android:layout_marginStart="@dimen/google_1x"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center">

        <ImageView
            android:id="@+id/image_flag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            app:srcCompat="@drawable/flag_india" />
    </LinearLayout>

    <TextView
        android:id="@+id/textView_countryName"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@+id/linear_flag_holder"
        android:layout_toLeftOf="@+id/textView_code"
        android:layout_toRightOf="@+id/linear_flag_holder"
        android:layout_toStartOf="@+id/textView_code"
        android:layout_marginStart="@dimen/google_1x"
        android:layout_marginLeft="@dimen/google_1x"
        android:gravity="center_vertical"
        android:text="India (IN)"
        android:textSize="16sp"
        android:textColor="@android:color/secondary_text_light" />

    <TextView
        android:id="@+id/textView_code"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="8dp"
        android:layout_marginRight="8dp"
        android:gravity="center_vertical"
        android:paddingEnd="10dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingStart="10dp"
        android:text="+91"
        android:textSize="18sp"
        android:textColor="@android:color/secondary_text_light"
        android:textDirection="ltr" />
    <View
        android:id="@+id/preferenceDivider"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:alpha="0.2"
        android:background="#898989" />
</RelativeLayout>