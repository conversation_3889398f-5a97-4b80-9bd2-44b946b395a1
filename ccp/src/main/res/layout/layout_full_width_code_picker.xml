<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/countryCodeHolder"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <RelativeLayout
        android:id="@+id/rlClickConsumer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/ccp_padding"
        >

        <LinearLayout
            android:id="@+id/linear_flag_holder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/google_1x"
            android:layout_marginRight="@dimen/google_1x"
            >

            <LinearLayout
                android:id="@+id/linear_flag_border"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_flag"
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:layout_margin="0.5dp"
                    android:adjustViewBounds="true"
                    tools:src="@drawable/flag_india"
                    />
            </LinearLayout>
        </LinearLayout>

        <!--</LinearLayout>-->
        <TextView
            android:id="@+id/textView_selectedCountry"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/imageView_arrow"
            android:layout_toLeftOf="@+id/imageView_arrow"
            android:layout_toEndOf="@+id/linear_flag_holder"
            android:layout_toRightOf="@+id/linear_flag_holder"
            android:gravity="center"
            android:text="(IN) +91"
            android:fontFamily="@font/roboto"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            />

        <ImageView
            android:id="@+id/imageView_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:adjustViewBounds="true"
            android:alpha="0.6"
            app:srcCompat="@drawable/ccp_ic_arrow_drop_down"
            app:tint="@android:color/black"
            />
    </RelativeLayout>
</RelativeLayout>