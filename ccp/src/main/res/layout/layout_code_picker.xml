<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/countryCodeHolder"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    >

    <RelativeLayout
        android:id="@+id/rlClickConsumer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/ccp_padding"
        >

        <LinearLayout
            android:id="@+id/linear_flag_holder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/google_1x"
            android:layout_marginRight="@dimen/google_1x"
            >

            <LinearLayout
                android:id="@+id/linear_flag_border"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                >

                <ImageView
                    android:id="@+id/image_flag"
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:layout_margin="0.5dp"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/flag_india"
                    />
            </LinearLayout>
        </LinearLayout>

        <!--</LinearLayout>-->
        <TextView
            android:id="@+id/textView_selectedCountry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/linear_flag_holder"
            android:layout_toRightOf="@+id/linear_flag_holder"
            android:singleLine="true"
            android:text="(IN) +91"
            android:textStyle="bold"
            android:autoSizeTextType="uniform"
            android:autoSizeMaxTextSize="18sp"
            android:autoSizeMinTextSize="12sp"
            android:fontFamily="@font/roboto"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:layout_marginEnd="5dp"
            />

        <ImageView
            android:id="@+id/imageView_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/textView_selectedCountry"
            android:layout_toRightOf="@+id/textView_selectedCountry"
            android:adjustViewBounds="true"
            android:alpha="1"
            app:srcCompat="@drawable/ccp_ic_arrow_drop_down"
            app:tint="@android:color/black"
            />
    </RelativeLayout>
</RelativeLayout>