<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CountryCodePicker">
        <!--{@deprecated Use defaultNameCode instead.}-->
        <attr name="ccp_defaultPhoneCode" format="integer" />
        <attr name="ccp_contentColor" format="color" />
        <attr name="ccp_arrowColor" format="color" />
        <attr name="ccp_textSize" format="dimension" />
        <attr name="ccp_arrowSize" format="dimension" />
        <attr name="ccp_customMasterCountries" format="string" />
        <attr name="ccp_excludedCountries" format="string" />
        <attr name="ccp_flagBorderColor" format="color" />
        <attr name="ccp_countryPreference" format="string" />
        <attr name="ccp_defaultNameCode" format="string" />
        <attr name="ccp_selectionMemoryTag" format="string" />
        <attr name="ccp_useFlagEmoji" format="boolean" /> <!-- beta -->
        <attr name="ccp_useDummyEmojiForPreview" format="boolean" /> <!-- beta -->

        <attr name="ccp_textGravity" format="enum">
            <enum name="LEFT" value="-1" />
            <enum name="RIGHT" value="1" />
            <enum name="CENTER" value="0" />
        </attr>
        <attr name="ccp_hintExampleNumberType" format="enum">
            <enum name="MOBILE" value="0" />
            <enum name="FIXED_LINE" value="1" />
            <enum name="FIXED_LINE_OR_MOBILE" value="2" />
            <enum name="TOLL_FREE" value="3" />
            <enum name="PREMIUM_RATE" value="4" />
            <enum name="SHARED_COST" value="5" />
            <enum name="VOIP" value="6" />
            <enum name="PERSONAL_NUMBER" value="7" />
            <enum name="PAGER" value="8" />
            <enum name="UAN" value="9" />
            <enum name="VOICEMAIL" value="10" />
            <enum name="UNKNOWN" value="11" />

        </attr>


        <!--CCP dimension properties-->
        <attr name="ccp_padding" format="dimension" />

        <!--CCP bool properties-->
        <attr name="ccp_showNameCode" format="boolean" />
        <attr name="ccp_showFlag" format="boolean" />
        <attr name="ccp_showFullName" format="boolean" />
        <attr name="ccp_internationalFormattingOnly" format="boolean" />
        <attr name="ccp_clickable" format="boolean" />
        <attr name="ccp_showPhoneCode" format="boolean" />
        <attr name="ccp_autoDetectLanguage" format="boolean" />
        <attr name="ccp_autoDetectCountry" format="boolean" />
        <attr name="ccp_areaCodeDetectedCountry" format="boolean" />
        <attr name="ccp_autoFormatNumber" format="boolean" />
        <attr name="ccp_hintExampleNumber" format="boolean" />
        <attr name="ccp_rememberLastSelection" format="boolean" />
        <attr name="ccp_showArrow" format="boolean" />
        <attr name="ccp_rippleEnable" format="boolean" />

        <!--CCP Dialog properties-->
        <attr name="ccpDialog_keyboardAutoPopup" format="boolean" />
        <attr name="ccpDialog_allowSearch" format="boolean" />
        <attr name="ccpDialog_showPhoneCode" format="boolean" />
        <attr name="ccpDialog_showNameCode" format="boolean" />
        <attr name="ccpDialog_showFlag" format="boolean" />
        <attr name="ccpDialog_showFastScroller" format="boolean" />
        <attr name="ccpDialog_fastScroller_bubbleColor" format="color" />
        <attr name="ccpDialog_fastScroller_handleColor" format="color" />
        <attr name="ccpDialog_fastScroller_bubbleTextAppearance" format="reference" />
        <attr name="ccpDialog_backgroundColor" format="reference" />
        <attr name="ccpDialog_background" format="reference" />
        <attr name="ccpDialog_cornerRadius" format="dimension" />
        <attr name="ccpDialog_textColor" format="color" />
        <attr name="ccpDialog_searchEditTextTint" format="color" />
        <attr name="ccpDialog_showCloseIcon" format="boolean" />
        <attr name="ccpDialog_showTitle" format="boolean" />
        <attr name="ccpDialog_initialScrollToSelection" format="boolean" />
        <attr name="ccpDialog_rippleEnable" format="boolean" />
        <!--list of languages-->
        <!--Make sure: order in this list must match order of Language Enum in CountryCodePicker.java. Values must be ascending starting from 0.-->
        <attr name="ccp_defaultLanguage" format="enum">
            <enum name="AFRIKAANS" value="0" />
            <enum name="ARABIC" value="1" />
            <enum name="BASQUE" value="2" />
            <enum name="BELARUSIAN" value="3" />
            <enum name="BENGALI" value="4" />
            <enum name="CHINESE_SIMPLIFIED" value="5" />
            <enum name="CHINESE_TRADITIONAL" value="6" />
            <enum name="CZECH" value="7" />
            <enum name="DANISH" value="8" />
            <enum name="DUTCH" value="9" />
            <enum name="ENGLISH" value="10" />
            <enum name="FARSI" value="11" />
            <enum name="FRENCH" value="12" />
            <enum name="GERMAN" value="13" />
            <enum name="GREEK" value="14" />
            <enum name="GUJARATI" value="15" />
            <enum name="HAUSA" value="16" />
            <enum name="HEBREW" value="17" />
            <enum name="HINDI" value="18" />
            <enum name="HUNGARIAN" value="19" />
            <enum name="INDONESIA" value="20" />
            <enum name="ITALIAN" value="21" />
            <enum name="JAPANESE" value="22" />
            <enum name="KAZAKH" value="23" />
            <enum name="KOREAN" value="24" />
            <enum name="LITHUANIAN" value="25" />
            <enum name="MARATHI" value="26" />
            <enum name="POLISH" value="27" />
            <enum name="PORTUGUESE" value="28" />
            <enum name="PUNJABI" value="29" />
            <enum name="RUSSIAN" value="30" />
            <enum name="SERBIAN" value="31" />
            <enum name="SLOVAK" value="32" />
            <enum name="SLOVENIAN" value="33" />
            <enum name="SPANISH" value="34" />
            <enum name="SWEDISH" value="35" />
            <enum name="TAGALOG" value="36" />
            <enum name="TAMIL" value="37" />
            <enum name="THAI" value="38" />
            <enum name="TURKISH" value="39" />
            <enum name="UKRAINIAN" value="40" />
            <enum name="URDU" value="41" />
            <enum name="UZBEK" value="42" />
            <enum name="VIETNAMESE" value="43" />
            <enum name="KHMER" value="44" />
        </attr>

        <attr name="ccp_countryAutoDetectionPref" format="enum">
            <enum name="SIM_ONLY" value="1" />
            <enum name="NETWORK_ONLY" value="2" />
            <enum name="LOCALE_ONLY" value="3" />
            <enum name="SIM_NETWORK" value="12" />
            <enum name="NETWORK_SIM" value="21" />
            <enum name="SIM_LOCALE" value="13" />
            <enum name="LOCALE_SIM" value="31" />
            <enum name="NETWORK_LOCALE" value="23" />
            <enum name="LOCALE_NETWORK" value="32" />
            <enum name="SIM_NETWORK_LOCALE" value="123" />
            <enum name="SIM_LOCALE_NETWORK" value="132" />
            <enum name="NETWORK_SIM_LOCALE" value="213" />
            <enum name="NETWORK_LOCALE_SIM" value="231" />
            <enum name="LOCALE_SIM_NETWORK" value="312" />
            <enum name="LOCALE_NETWORK_SIM" value="321" />
        </attr>
    </declare-styleable>
</resources>