<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_main"
    app:startDestination="@id/loginFragment">

    <fragment
        android:id="@+id/loginFragment"
        android:name="com.metathought.food_order.casheir.ui.login.LoginFragment"
        android:label="fragment_login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_mainDashboardFragment"
            app:destination="@id/mainDashboardFragment" />
    </fragment>
    <fragment
        android:id="@+id/mainDashboardFragment"
        android:name="com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment"
        android:label="fragment_main_dashboard"
        tools:layout="@layout/fragment_main_dashboard">
        <action
            android:id="@+id/action_mainDashboardFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
    </fragment>
</navigation>