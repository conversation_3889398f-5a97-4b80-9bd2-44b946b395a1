<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="FontLocalization" parent="android:Widget.TextView">
        <item name="android:fontFamily">@font/khmer_os</item>
    </style>
    <style name="CustomOutlinedBox" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/button_background_color</item>
        <item name="hintTextColor">@color/button_background_color</item>
        <item name="boxCornerRadiusBottomEnd">15dp</item>
        <item name="boxCornerRadiusBottomStart">15dp</item>
        <item name="boxCornerRadiusTopEnd">15dp</item>
        <item name="boxCornerRadiusTopStart">15dp</item>
        <item name="android:fontFamily">@font/khmer_os</item>
    </style>
    <style name="CustomOutlinedButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:fontFamily">@font/khmer_os</item>
        <item name="android:textSize">@dimen/_18ssp</item>
    </style>

    <style name="commonActionBtnStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:fontFamily">@font/khmer_os</item>
        <item name="android:gravity">center</item>
    </style>
</resources>