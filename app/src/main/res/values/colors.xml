<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="primaryColor">#0F9D58</color>
    <color name="primaryColor10">#1A0F9D58</color>
    <color name="primaryColor20">#330F9D58</color>
    <color name="primaryColorDark">#03733D</color>
    <color name="accentColor">#0F9D58</color>
    <color name="login_background_color">#E3E3E4</color>
    <color name="transparent">#00000000</color>
    <color name="black">#FF000000</color>
    <color name="black80">#CC000000</color>
    <color name="black70">#B2000000</color>
    <color name="black60">#99000000</color>
    <color name="black50">#80000000</color>
    <color name="black40">#66000000</color>
    <color name="black20">#33000000</color>
    <color name="black12">#1F000000</color>
    <color name="black10">#1A000000</color>
    <color name="black08">#14000000</color>
    <color name="mainWhite">#FFFFFFFF</color>
    <color name="disable_radio_color">#F8F8F8</color>
    <color name="white">#FFFFFFFF</color>
    <color name="white16">#29FFFFFF</color>
    <color name="white20">#33FFFFFF</color>
    <color name="white40">#66FFFFFF</color>
    <color name="white60">#99FFFFFF</color>
    <color name="white80">#CCFFFFFF</color>
    <color name="mainBackground">#FFE7E7E7</color>
    <color name="button_background_color">#0F9D58</color>
    <color name="button_background_disable">#87CEAC</color>
    <color name="button_background_color_pressed">#03733D</color>
    <color name="button_logout_background_color">#FF3141</color>
    <color name="button_logout_background_disable">#F1ADB2</color>
    <color name="button_logout_background_color_pressed">#8A101A</color>
    <color name="bg_progress">#4D000000</color>
    <color name="backgroundSpinner">#1F000000</color>
    <color name="khqr_red_color">#E1232F</color>
    <color name="printer_remark_color">#FF3141</color>
    <color name="main_red">#DC3545</color>
    <color name="red_clear_data">#FF3141</color>
    <color name="main_yellow">#FFA800</color>
    <color name="confirm_backgroud_color">#FFF4E9</color>
    <color name="color_ddd">#dddddd</color>
    <color name="color_f4f4f4">#f4f4f4</color>
    <color name="confirm_text_color">#FF7F00</color>
    <color name="unpaid_backgroud_color">#FFF4E9</color>
    <color name="unpaid_text_color">#FF7F00</color>
    <color name="paid_backgroud_color">#E7F5EE</color>
    <color name="paid_text_color">#0F9D58</color>
    <color name="reserved_backgroud_color">#FFFBDD</color>
    <color name="reserved_text_color">#FFA800</color>
    <color name="canceled_backgroud_color">#99F5F5F5</color>
    <color name="canceled_text_color">#99000000</color>
    <color name="pre_order_backgroud_color">#FFFBDD</color>
    <color name="pre_order_text_color">#FFA800</color>
    <color name="partial_refund_backgroud_color">#1AFF3141</color>
    <color name="partial_refund_text_color">#FF3141</color>
    <color name="refund_backgroud_color">#1AFF3141</color>
    <color name="RED">#FFFF0000</color>
    <color name="refund_text_color">#FF3141</color>
    <color name="confrim_button_color">#F70B0E</color>
    <color name="error_background_color">#F9DEDC</color>
    <color name="cash_chart_color">#2C99FF</color>
    <color name="credit_chart_color">#A968FD</color>
    <color name="balance_chart_color">#FF7F00</color>
    <color name="ordered_cancel_color">#99F5F5F5</color>
    <color name="ordered_refunded_color">#1AFF3141</color>
    <color name="ordered_pre_order_color">#FFFBDD</color>
    <color name="ordered_confirmed_color">#FFF4E9</color>
    <color name="filter_table_background">#E7F5EE</color>
    <color name="background_session">#F5F5F5</color>
    <color name="second_payment_bg">#f3f3f8</color>
    <color name="second_payment_result_bg">#d8d8d8</color>

    <color name="indicator_unselected_color">#7A7A7A</color>
    <color name="cancel_color">#6E6E6E</color>
    <color name="reach_limit_background_color">#FFDAD6</color>
    <color name="order_info_detail_color">#707070</color>
    <color name="weight_price_color">#0F9D58</color>

    <color name="member_price_color">#FF7F00</color>
    <color name="progress_end_color">#00AF46</color>
    <color name="progress_start_color">#1ACB74</color>
    <color name="progress_bg_color">#f5f5f5</color>
    <color name="color_f5f5f5">#f5f5f5</color>
    <color name="color_1a1a1a">#1A1A1A</color>
    <color name="color_272b41">#272b41</color>
    <color name="color_e5e5e5">#e5e5e5</color>
    <color name="color_616263">#616263</color>
    <color name="color_ff3141">#FF3141</color>
    <color name="color_ff3141_10">#1AFF3141</color>
    <color name="color_f70b0e_20">#33F70B0E</color>
    <color name="color_333333">#ff333333</color>
    <color name="color_30FFFFFF">#30FFFFFF</color>
    <color name="color_ffADB4d2">#ADB4D2</color>
    <color name="color_ff7f00">#FF7F00</color>
    <color name="color_efefef">#ffefefef</color>
    <color name="color_e7f5ee">#ffE7F5EE</color>
    <color name="color_00210f">#ff00210f</color>
    <color name="color_efefef_40">#66EFEFEF</color>
    <color name="color_FADBDC">#FFFADBDC</color>
    <color name="color_FF7700">#FFFF7700</color>
    <color name="color_F3F5F8">#FFF3F5F8</color>
    <color name="color_999999">#FF999999</color>

    <!-- Text colors -->
    <color name="text_primary">#1A1A1A</color>
    <color name="text_secondary">#616263</color>
    <color name="text_hint">#999999</color>

    <!-- UI colors -->
    <color name="primary">#0F9D58</color>
    <color name="divider">#E5E5E5</color>
</resources>