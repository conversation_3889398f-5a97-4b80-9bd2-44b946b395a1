<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="Keyboard_Row">
        <!-- Row edge flags-->
        <attr name="rowEdgeFlags">
            <!-- <PERSON> is anchored to the top of the keyboard -->
            <flag name="top" value="4" />
            <!-- <PERSON> is anchored to the bottom of the keyboard -->
            <flag name="bottom" value="8" />
        </attr>
        <!-- Mode of the keyboard. If the mode doesn't match the
             requested keyboard mode, the row will be skipped -->
        <attr name="keyboardMode" format="reference" />
    </declare-styleable>
    <declare-styleable name="Keyboard_Key">
        <!-- The unicode value or comma-separated values that this key outputs -->
        <attr name="codes" format="integer|string" />
        <!-- The XML keyboard layout of any popup keyboard -->
        <attr name="popupKeyboard" format="reference" />
        <!-- The characters to display in the popup keyboard -->
        <attr name="popupCharacters" format="string" />
        <!-- Key edge flags -->
        <attr name="keyEdgeFlags">
            <!-- Key is anchored to the left of the keyboard -->
            <flag name="left" value="1" />
            <!-- <PERSON> is anchored to the right of the keyboard -->
            <flag name="right" value="2" />
        </attr>
        <!-- Whether this is a modifier key such as Alt or Shift -->
        <attr name="isModifier" format="boolean" />
        <!-- Whether this is a toggle key -->
        <attr name="isSticky" format="boolean" />
        <!-- Whether long-pressing on this key will make it repeat -->
        <attr name="isRepeatable" format="boolean" />
        <!-- The icon to show in the popup preview -->
        <attr name="iconPreview" format="reference" />
        <!-- The string of characters to output when this key is pressed -->
        <attr name="keyOutputText" format="string" />
        <!-- The label to display on the key -->
        <attr name="keyLabel" format="string" />
        <!-- The icon to display on the key instead of the label -->
        <attr name="keyIcon" format="reference" />
        <!-- Mode of the keyboard. If the mode doesn't match the
             requested keyboard mode, the key will be skipped -->
        <attr name="keyboardMode" />
    </declare-styleable>
    <declare-styleable name="KeyboardView">
        <!-- Default KeyboardView style. -->
        <attr name="keyboardViewStyle" format="reference" />
        <!-- Image for the key. This image needs to be a StateListDrawable, with the following
             possible states: normal, pressed, checkable, checkable+pressed, checkable+checked,
             checkable+checked+pressed. -->
        <attr name="keyBackground" format="reference" />
        <!-- Size of the text for character keys. -->
        <attr name="keyTextSize" format="dimension" />
        <!-- Size of the text for custom keys with some text and no icon. -->
        <attr name="labelTextSize" format="dimension" />
        <!-- Color to use for the label in a key -->
        <attr name="keyTextColor" format="color" />
        <!-- Layout resource for key press feedback.-->
        <attr name="keyPreviewLayout" format="reference" />
        <!-- Vertical offset of the key press feedback from the key. -->
        <attr name="keyPreviewOffset" format="dimension" />
        <!-- Height of the key press feedback popup. -->
        <attr name="keyPreviewHeight" format="dimension" />
        <!-- Amount to offset the touch Y coordinate by, for bias correction. -->
        <attr name="verticalCorrection" format="dimension" />
        <!-- Layout resource for popup keyboards -->
        <attr name="popupLayout" format="reference" />
        <attr name="shadowColor" format="color" />
        <attr name="shadowRadius" format="float" />
    </declare-styleable>
    <declare-styleable name="Keyboard">
        <!-- Default width of a key, in pixels or percentage of display width -->
        <attr name="keyWidth" format="dimension|fraction" />
        <!-- Default height of a key, in pixels or percentage of display width -->
        <attr name="keyHeight" format="dimension|fraction" />
        <!-- Default horizontal gap between keys -->
        <attr name="horizontalGap" format="dimension|fraction" />
        <!-- Default vertical gap between rows of keys -->
        <attr name="verticalGap" format="dimension|fraction" />
    </declare-styleable>
    <declare-styleable name="KeyboardViewPreviewState">
        <!-- State for {@link android.inputmethodservice.KeyboardView KeyboardView}
                key preview background -->
        <attr name="state_long_pressable" format="boolean" />
    </declare-styleable>

    <!--    blur_radius:高斯模糊半径，值越大越模糊，0<r<=25-->
    <!--    blur_down_sample:采样参数-->
    <!--    blur_overlay_color:毛玻璃覆盖颜色-->
    <!--    blur_corner_radius:矩形时圆角半径（4个角一样），其他几个corner属性大家应该能看懂-->
    <!--    blur_border_width:边框线条宽度-->
    <!--    blur_border_color:边框线条颜色-->
    <!--    blur_mode:样式，rectangle：矩形；circle：圆形；oval：椭圆-->
    <declare-styleable name="ShapeBlurView">
        <attr name="blur_radius" format="dimension" />
        <attr name="blur_down_sample" format="float" />
        <attr name="blur_overlay_color" format="color" />
        <attr name="blur_corner_radius" format="dimension" />
        <attr name="blur_corner_radius_top_left" format="dimension" />
        <attr name="blur_corner_radius_top_right" format="dimension" />
        <attr name="blur_corner_radius_bottom_left" format="dimension" />
        <attr name="blur_corner_radius_bottom_right" format="dimension" />
        <attr name="blur_border_width" format="dimension" />
        <attr name="blur_border_color" format="color" />
        <attr name="blur_mode">
            <enum name="rectangle" value="0" />
            <enum name="circle" value="1" />
            <enum name="oval" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="TitleContentView">
        <attr name="title" format="string" />
    </declare-styleable>
    <declare-styleable name="TitleSwitchView">
        <attr name="switch_title" format="string" />
        <attr name="switch_desc" format="string" />
    </declare-styleable>

    <declare-styleable name="DialogTopBar">
        <attr name="dialog_title" format="string" />
    </declare-styleable>

    <declare-styleable name="GridRadioGroup">
        <attr name="columnNum" format="integer" />
    </declare-styleable>

    <declare-styleable name="CustomSearchView">
        <attr name="search_hint" format="string" />
    </declare-styleable>

    <declare-styleable name="CustomNumberKeyBoardView"></declare-styleable>

    <declare-styleable name="CustomNumberKeyBoardView2"></declare-styleable>
    <declare-styleable name="CustomerQuickInputView"></declare-styleable>
    <declare-styleable name="MultiLineMiddleEllipsizeTextView"></declare-styleable>
    <declare-styleable name="OrderGiftView">
        <attr name="content_padding_horizontal" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CustomStrikeTextView">
        <attr name="strikeWidth" format="dimension" />
        <attr name="strikeColor" format="color" />
        <attr name="strikePosition" format="float" />
    </declare-styleable>
</resources>
