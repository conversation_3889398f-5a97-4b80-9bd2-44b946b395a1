<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="MyDialogTheme" parent="Theme.AppCompat.Light.Dialog.Alert" />

    <style name="CustomOutlinedBox" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/button_background_color</item>
        <item name="hintTextColor">@color/button_background_color</item>
        <item name="boxCornerRadiusBottomEnd">15dp</item>
        <item name="boxCornerRadiusBottomStart">15dp</item>
        <item name="boxCornerRadiusTopEnd">15dp</item>
        <item name="boxCornerRadiusTopStart">15dp</item>
        <item name="android:fontFamily">@font/roboto</item>
        <!-- Add other customization attributes as needed -->
    </style>

    <style name="PopupAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <style name="FontLocalization" parent="android:Widget.TextView">
        <item name="android:fontFamily">@font/roboto</item>
    </style>

    <style name="CustomOutlinedButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textSize">@dimen/_18ssp</item>
    </style>

    <style name="CustomOutlinedBoxPhoneNumber" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@android:color/transparent</item>
        <item name="hintTextColor">@android:color/transparent</item>
        <item name="boxCornerRadiusBottomEnd">15dp</item>
        <item name="boxCornerRadiusBottomStart">15dp</item>
        <item name="boxCornerRadiusTopEnd">15dp</item>
        <item name="boxCornerRadiusTopStart">15dp</item>
        <item name="android:fontFamily">@font/roboto</item>
        <!-- Add other customization attributes as needed -->
    </style>

    <style name="CustomCardViewStyle" parent="@style/Widget.MaterialComponents.CardView">
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay_card_custom_corners</item>
    </style>


    <style name="ShapeAppearanceOverlay_card_custom_corners" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">25dp</item>
        <item name="cornerSizeTopLeft">0dp</item>
        <item name="cornerSizeBottomRight">25dp</item>
        <item name="cornerSizeBottomLeft">25dp</item>
    </style>


    <style name="commonDividerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/black08</item>
    </style>

    <style name="ticketDividerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/black</item>
    </style>

    <style name="commonSearchStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">@drawable/radiobutton_filter_backgroud</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:drawableStart">@drawable/ic_magnifer</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:imeOptions">actionSearch</item>
        <item name="android:inputType">text</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColorHint">@color/black40</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="commonCalendarTextViewStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">@drawable/radiobutton_filter_backgroud</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:drawableStart">@drawable/ic_calendar</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColorHint">@color/black50</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">14sp</item>
        <item name="android:hint">@string/start_date_end_date</item>
    </style>

    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">
            @drawable/background_splash_theme
        </item>
    </style>

    <declare-styleable name="NumberBadgeView">
        <attr name="textSize" format="dimension" />
        <attr name="padding" format="dimension" />
        <attr name="textColor" format="color" />
        <attr name="backgroundColor" format="color" />
    </declare-styleable>

    <style name="commonActionBtnStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:gravity">center</item>
    </style>

</resources>