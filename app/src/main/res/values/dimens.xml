<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="fragment_horizontal_margin">16dp</dimen>
    <dimen name="fragment_vertical_margin">16dp</dimen>
    <dimen name="_32ssp">32sp</dimen>
    <dimen name="_24ssp">24sp</dimen>
    <dimen name="_20ssp">20sp</dimen>
    <dimen name="_18ssp">18sp</dimen>
    <dimen name="_16ssp">16sp</dimen>
    <dimen name="_15ssp">15sp</dimen>
    <dimen name="_14ssp">14sp</dimen>
    <dimen name="_12ssp">12sp</dimen>
    <dimen name="_11ssp">11sp</dimen>
    <dimen name="_10ssp">10sp</dimen>
    <dimen name="_9ssp">9sp</dimen>
    <dimen name="height_bar">1dp</dimen>

    <dimen name="indicator_size">5dp</dimen>
    <dimen name="padding_border">2dp</dimen>
    <dimen name="small_text_size">10sp</dimen>
    <dimen name="top_image_px">800px</dimen>
    <dimen name="bottom_banner_height_px">123px</dimen>
    <dimen name="app_item_box_height_px">200px</dimen>
    <dimen name="icon_button_home_height">123px</dimen>
    <dimen name="text_home_size_px">38px</dimen>
    <dimen name="_printer_default_sp">24px</dimen>
    <dimen name="_printer_eight_default_sp">24px</dimen>
    <dimen name="_default_sp">22sp</dimen>
    <dimen name="_printer_default_margin_top">-4px</dimen>
</resources>