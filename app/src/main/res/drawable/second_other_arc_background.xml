<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape>
            <solid android:color="@color/paid_text_color" />
        </shape>
    </item>


<!--    <item-->
<!--        android:bottom="-85dp"-->
<!--        android:end="-20dp"-->
<!--        android:gravity="bottom"-->
<!--        android:start="-20dp">-->
<!--        <shape-->
<!--            android:shape="oval"-->
<!--            android:useLevel="true">-->
<!--            <solid android:color="#FFFFFF" />-->
<!--            &lt;!&ndash; 修改height会改变圆弧的弧度 &ndash;&gt;-->
<!--            <size android:height="250dp" />-->
<!--        </shape>-->
<!--    </item>-->

</layer-list>
