<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 进度条的背景色 -->
    <item
        android:id="@android:id/background"
        android:gravity="center_vertical|fill_horizontal">
        <shape android:shape="rectangle">
            <!-- 设置水平进度条的高度、圆角、颜色 -->
            <size android:height="12dp" />
            <corners android:radius="20dp" />
            <solid android:color="@color/progress_bg_color" />
        </shape>
    </item>
    <!--    &lt;!&ndash; 缓冲进度条的背景色 &ndash;&gt;-->
    <!--    <item android:id="@android:id/secondaryProgress"-->
    <!--        android:gravity="center_vertical|fill_horizontal">-->
    <!--        &lt;!&ndash; 属性android:scaleWidth="100%"一定要加上，否则设置进度无效 &ndash;&gt;-->
    <!--        <scale android:scaleWidth="100%">-->
    <!--            <shape android:shape="rectangle">-->
    <!--                <size android:height="12dp" />-->
    <!--                <corners android:radius="5dp" />-->
    <!--                <solid android:color="@color/purple_200" />-->
    <!--            </shape>-->
    <!--        </scale>-->
    <!--    </item>-->
    <!-- 进度条的背景色 -->
    <item
        android:id="@android:id/progress"
        android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <shape android:shape="rectangle">
                <size android:height="12dp" />
                <corners android:radius="20dp" />
                <gradient
                    android:type="linear"
                    android:startColor="@color/progress_start_color"
                    android:endColor="@color/progress_end_color"
                    android:angle="0" />
<!--                <solid android:color="@color/progress_color" />-->
            </shape>
        </scale>
    </item>
</layer-list>