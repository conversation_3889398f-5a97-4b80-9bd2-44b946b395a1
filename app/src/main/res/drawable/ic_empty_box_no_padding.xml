<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="104dp"
    android:height="58dp"
    android:viewportWidth="104"
    android:viewportHeight="58">
  <path
      android:pathData="M70.46,18.46V57.85H16.28C14.14,57.85 12.54,56.08 12.54,53.98V18.46H70.46Z"
      android:fillColor="#000000"
      android:fillAlpha="0.06"/>
  <path
      android:pathData="M91.95,18.46V53.98C91.95,56.19 90.17,57.85 88.06,57.85H70.46V18.46H91.95Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="91.69"
          android:startY="58.17"
          android:endX="69.59"
          android:endY="18.98"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#11000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M33.44,18.46L44.83,0H103.3L91.58,18.46H33.44Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="68.37"
          android:startY="0"
          android:endX="68.4"
          android:endY="19.54"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#00000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M91.95,18.57V39.38H76.28C74.82,39.38 73.82,38.41 73.59,37.01L70.46,18.46L91.95,18.57Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1145.68"
          android:startY="2154.46"
          android:endX="1145.68"
          android:endY="247.96"
          android:type="linear">
        <item android:offset="0" android:color="#00606673"/>
        <item android:offset="1" android:color="#FFD2D2D2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M91.5,18.46H70.46L81.14,35.15C81.91,36.26 83.11,36.92 84.31,36.92H100.66C101.75,36.92 102.52,35.59 101.86,34.71L91.5,18.46Z"
      android:fillColor="#000000"
      android:fillAlpha="0.06"/>
  <path
      android:pathData="M70.46,18.46L58.99,0H0L11.91,18.46H70.46Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="40.75"
          android:startY="0.79"
          android:endX="42.68"
          android:endY="18.41"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#11000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M46.87,37.17H19.35C18.71,37.17 18.19,36.67 18.19,36.04C18.19,35.42 18.71,34.92 19.35,34.92H46.87C47.51,34.92 48.03,35.42 48.03,36.04C47.9,36.67 47.51,37.17 46.87,37.17Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M46.87,43.17H19.35C18.71,43.17 18.19,42.66 18.19,42.04C18.19,41.41 18.71,40.92 19.35,40.92H46.87C47.51,40.92 48.03,41.41 48.03,42.04C47.9,42.66 47.51,43.17 46.87,43.17Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M32.3,49.92H19.37C18.71,49.92 18.19,49.42 18.19,48.79C18.19,48.18 18.71,47.67 19.37,47.67H32.3C32.95,47.67 33.47,48.18 33.47,48.79C33.34,49.42 32.82,49.92 32.3,49.92Z"
      android:fillColor="#ffffff"/>
</vector>
