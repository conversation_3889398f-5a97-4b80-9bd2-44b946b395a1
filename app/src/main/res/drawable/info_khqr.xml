<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item>
        <shape android:shape="rectangle" >
            <solid android:color="@color/mainWhite" />
        </shape>
    </item>
    <item android:drawable="@drawable/ic_khqr_corner" android:gravity="end" android:width="50dp" android:height="50dp"></item>
    <item android:top="-2dp" android:right="-2dp" android:left="-2dp">
        <shape>
            <solid android:color="@android:color/transparent" />
            <stroke
                android:dashGap="1dp"
                android:dashWidth="4dp"
                android:width="1dp"
                android:color="#AAAAAA" />
        </shape>
    </item>
</layer-list>