<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M14.674,4.256C16.271,4.256 16.405,4.257 17.265,4.373C18.108,4.486 18.593,4.698 18.947,5.053C19.355,5.461 19.533,5.777 19.631,6.34C19.742,6.976 19.744,6.882 19.744,8.488C19.744,8.835 20.025,9.116 20.372,9.116C20.719,9.116 21,8.835 21,8.488L21,8.408C21,6.901 21,6.881 20.868,6.124C20.721,5.283 20.408,4.738 19.835,4.165C19.209,3.538 18.414,3.26 17.433,3.128C16.479,3 16.26,3 14.722,3H14.674C14.328,3 14.047,3.281 14.047,3.628C14.047,3.975 14.328,4.256 14.674,4.256Z"
      android:fillColor="#000000"/>
  <path
      android:pathData="M3.628,14.884C3.975,14.884 4.256,15.165 4.256,15.512C4.256,17.118 4.258,17.024 4.369,17.66C4.467,18.223 4.645,18.539 5.053,18.947C5.407,19.302 5.892,19.514 6.735,19.627C7.595,19.743 7.729,19.744 9.326,19.744C9.672,19.744 9.953,20.025 9.953,20.372C9.953,20.719 9.672,21 9.326,21H9.278C7.74,21 7.521,21 6.567,20.872C5.586,20.74 4.791,20.462 4.165,19.835C3.592,19.262 3.279,18.717 3.132,17.875C3,17.119 3,17.099 3,15.592L3,15.512C3,15.165 3.281,14.884 3.628,14.884Z"
      android:fillColor="#000000"/>
  <path
      android:pathData="M20.372,14.884C20.719,14.884 21,15.165 21,15.512L21,15.592C21,17.099 21,17.119 20.868,17.875C20.721,18.717 20.408,19.262 19.835,19.835C19.209,20.462 18.414,20.74 17.433,20.872C16.479,21 16.26,21 14.722,21H14.674C14.328,21 14.047,20.719 14.047,20.372C14.047,20.025 14.328,19.744 14.674,19.744C16.271,19.744 16.405,19.743 17.265,19.627C18.108,19.514 18.593,19.302 18.947,18.947C19.355,18.539 19.533,18.223 19.631,17.66C19.742,17.024 19.744,17.118 19.744,15.512C19.744,15.165 20.025,14.884 20.372,14.884Z"
      android:fillColor="#000000"/>
  <path
      android:pathData="M9.278,3H9.326C9.672,3 9.953,3.281 9.953,3.628C9.953,3.975 9.672,4.256 9.326,4.256C7.729,4.256 7.595,4.257 6.735,4.373C5.892,4.486 5.407,4.698 5.053,5.053C4.645,5.461 4.467,5.777 4.369,6.34C4.258,6.976 4.256,6.882 4.256,8.488C4.256,8.835 3.975,9.116 3.628,9.116C3.281,9.116 3,8.835 3,8.488L3,8.408C3,6.901 3,6.881 3.132,6.124C3.279,5.283 3.592,4.738 4.165,4.165C4.791,3.538 5.586,3.26 6.567,3.128C7.521,3 7.74,3 9.278,3Z"
      android:fillColor="#000000"/>
  <path
      android:pathData="M3.628,11.372C3.281,11.372 3,11.653 3,12C3,12.347 3.281,12.628 3.628,12.628H20.372C20.719,12.628 21,12.347 21,12C21,11.653 20.719,11.372 20.372,11.372H3.628Z"
      android:fillColor="#000000"/>
  <path
      android:pathData="M14.674,4.256C16.271,4.256 16.405,4.257 17.265,4.373C18.108,4.486 18.593,4.698 18.947,5.053C19.355,5.461 19.533,5.777 19.631,6.34C19.742,6.976 19.744,6.882 19.744,8.488C19.744,8.835 20.025,9.116 20.372,9.116C20.719,9.116 21,8.835 21,8.488L21,8.408C21,6.901 21,6.881 20.868,6.124C20.721,5.283 20.408,4.738 19.835,4.165C19.209,3.538 18.414,3.26 17.433,3.128C16.479,3 16.26,3 14.722,3H14.674C14.328,3 14.047,3.281 14.047,3.628C14.047,3.975 14.328,4.256 14.674,4.256Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.3"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M3.628,14.884C3.975,14.884 4.256,15.165 4.256,15.512C4.256,17.118 4.258,17.024 4.369,17.66C4.467,18.223 4.645,18.539 5.053,18.947C5.407,19.302 5.892,19.514 6.735,19.627C7.595,19.743 7.729,19.744 9.326,19.744C9.672,19.744 9.953,20.025 9.953,20.372C9.953,20.719 9.672,21 9.326,21H9.278C7.74,21 7.521,21 6.567,20.872C5.586,20.74 4.791,20.462 4.165,19.835C3.592,19.262 3.279,18.717 3.132,17.875C3,17.119 3,17.099 3,15.592L3,15.512C3,15.165 3.281,14.884 3.628,14.884Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.3"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20.372,14.884C20.719,14.884 21,15.165 21,15.512L21,15.592C21,17.099 21,17.119 20.868,17.875C20.721,18.717 20.408,19.262 19.835,19.835C19.209,20.462 18.414,20.74 17.433,20.872C16.479,21 16.26,21 14.722,21H14.674C14.328,21 14.047,20.719 14.047,20.372C14.047,20.025 14.328,19.744 14.674,19.744C16.271,19.744 16.405,19.743 17.265,19.627C18.108,19.514 18.593,19.302 18.947,18.947C19.355,18.539 19.533,18.223 19.631,17.66C19.742,17.024 19.744,17.118 19.744,15.512C19.744,15.165 20.025,14.884 20.372,14.884Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.3"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M9.278,3H9.326C9.672,3 9.953,3.281 9.953,3.628C9.953,3.975 9.672,4.256 9.326,4.256C7.729,4.256 7.595,4.257 6.735,4.373C5.892,4.486 5.407,4.698 5.053,5.053C4.645,5.461 4.467,5.777 4.369,6.34C4.258,6.976 4.256,6.882 4.256,8.488C4.256,8.835 3.975,9.116 3.628,9.116C3.281,9.116 3,8.835 3,8.488L3,8.408C3,6.901 3,6.881 3.132,6.124C3.279,5.283 3.592,4.738 4.165,4.165C4.791,3.538 5.586,3.26 6.567,3.128C7.521,3 7.74,3 9.278,3Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.3"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M3.628,11.372C3.281,11.372 3,11.653 3,12C3,12.347 3.281,12.628 3.628,12.628H20.372C20.719,12.628 21,12.347 21,12C21,11.653 20.719,11.372 20.372,11.372H3.628Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.3"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
</vector>
