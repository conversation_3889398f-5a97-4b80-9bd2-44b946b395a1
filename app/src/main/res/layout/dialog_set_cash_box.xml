<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end|center_vertical"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvTitleName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/set_money_box"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvCashBoxPwd"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="25dp"
                android:gravity="center"
                android:text="@string/cash_box_money"
                android:textColor="@color/black80"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <ImageView
                android:id="@+id/ivSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:src="@drawable/icon_switch_close"
                app:layout_constraintBottom_toBottomOf="@id/tvCashBoxPwd"
                app:layout_constraintStart_toEndOf="@id/tvCashBoxPwd"
                app:layout_constraintTop_toTopOf="@id/tvCashBoxPwd" />

            <TextView
                android:id="@+id/tvRest"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/reset_cash_box_pwd"
                android:textColor="@color/primaryColor"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                android:layout_marginTop="30dp"
                app:layout_constraintTop_toBottomOf="@id/tvCashBoxPwd"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/llPwd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/tvCashBoxPwd">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/please_input_cash_box_pwd"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold" />


                <com.maning.pswedittextlibrary.MNPasswordEditText
                    android:id="@+id/edtPassword"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="10dp"
                    android:focusableInTouchMode="true"
                    android:inputType="number"
                    android:maxLength="6"
                    android:textSize="20sp"
                    app:psw_border_color="@color/black20"
                    app:psw_border_radius="12dp"
                    app:psw_border_selected_color="@color/black20"
                    app:psw_border_width="2dp"
                    app:psw_cover_circle_color="@color/black"
                    app:psw_cover_circle_radius="6dp"
                    app:psw_cover_text="*"
                    app:psw_cursor_width="2dp"
                    app:psw_item_margin="20dp"
                    app:psw_mode="Circle"
                    app:psw_show_cursor="false"
                    app:psw_style="StyleOneself"
                    app:psw_text_color="@color/black" />

                <LinearLayout
                    android:id="@+id/llError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/background_cash_pwd_error"
                    android:orientation="horizontal"
                    android:paddingHorizontal="14dp"
                    android:paddingVertical="10dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:src="@drawable/icon_red_error" />

                    <TextView
                        android:id="@+id/tvError"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:gravity="start"
                        android:text="@string/pwd_error_input_again"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnYes"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="40dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm2"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                android:visibility="gone"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/primaryColor"
                app:strokeWidth="0dp" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
