<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="15dp">

    <TextView
        android:id="@+id/tvOrderID"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:paddingHorizontal="16dp"
        android:text="@string/order_id"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="98908989089890" />

    <TextView
        android:id="@+id/tvCreditAmount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:paddingHorizontal="16dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="$99999.9" />

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:paddingHorizontal="16dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="2024/11/19 13:22:10" />

    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingHorizontal="16dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="挂账-已支付" />

    <TextView
        android:id="@+id/tvDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingHorizontal="16dp"
        android:text="@string/detail"
        android:textColor="@color/primaryColor"
        android:textSize="16sp" />
</LinearLayout>