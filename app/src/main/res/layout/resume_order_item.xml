<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardViewMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="15dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/black08"
        app:strokeWidth="1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="10dp">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:autoSizeMaxTextSize="@dimen/_18ssp"
                android:autoSizeMinTextSize="@dimen/_12ssp"
                android:autoSizeTextType="uniform"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:lines="1"
                android:text="@string/login_time"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                android:visibility="visible"
                app:layout_constraintEnd_toStartOf="@id/tvPrice"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvItems"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="start"
                android:maxLines="1"
                android:textColor="@color/black50"
                android:textSize="12sp"
                app:layout_constraintEnd_toStartOf="@id/tvPrice"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTableID"
                app:layout_constraintVertical_bias="1" />

            <TextView
                android:id="@+id/tvTime"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="start"
                android:maxLines="1"
                android:textColor="@color/black50"
                android:textSize="12sp"
                app:layout_constraintEnd_toStartOf="@id/tvPrice"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvItems"
                tools:text="Ordering Time" />

            <TextView
                android:id="@+id/tvRemark"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="start"
                android:maxLines="1"
                android:textColor="@color/black50"
                android:textSize="12sp"
                android:ellipsize="end"
                app:layout_constraintEnd_toStartOf="@id/tvPrice"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTime"
                tools:text="Ordering Time" />


            <TextView
                android:id="@+id/tvPrice"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:drawablePadding="0dp"
                android:gravity="bottom|end"
                android:maxLines="1"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="$99.99" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>
</FrameLayout>
