<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:background="@android:color/transparent"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/tvChannelName"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1.5"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="支付渠道" />

        <TextView
            android:id="@+id/tvOrderNum"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="订单总数" />

        <TextView
            android:id="@+id/tvOrderAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="订单总额" />

        <TextView
            android:id="@+id/itemTotalAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/proportion_of_amount" />

    </LinearLayout>

    <View
        android:id="@+id/vLine"
        style="@style/commonDividerStyle"
        android:layout_gravity="bottom" />
</FrameLayout>