<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDishedName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/service_fee_detail"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layoutMainOrdered"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo">

            <LinearLayout
                android:id="@+id/layoutFood"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/background_dialog_info"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/layoutHeader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="2"
                        android:drawablePadding="3dp"
                        android:text="@string/items"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/quantity"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1.2"
                        android:gravity="center_horizontal"
                        android:text="@string/service_fee"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/total_price2"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />
                </LinearLayout>

                <View style="@style/commonDividerStyle" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerOrderedFood"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginHorizontal="12dp"
                    android:layout_weight="1"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:paddingBottom="10dp"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/service_fee_detail_item" />

                <View style="@style/commonDividerStyle" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginEnd="10dp"
                        android:gravity="center_horizontal"
                        android:text="@string/subtotal"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_18ssp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvServiceFee"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:gravity="end"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_18ssp"
                            tools:text="$8.00" />

                        <TextView
                            android:id="@+id/tvVipServiceFee"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="4dp"

                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            tools:text="$0.00"
                            tools:visibility="visible" />
                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>