<?xml version="1.0" encoding="utf-8"?>
<cn.bingoogolapple.badgeview.BGABadgeFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="6dp"
    android:paddingEnd="6dp"
    tools:layout_width="200dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/black08"
        app:strokeWidth="1dp">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp">

                    <!--用来固定视图大小-->
                    <TextView
                        android:id="@+id/tvViewHeight"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginTop="10dp"
                        android:ellipsize="end"
                        android:lines="4"
                        android:paddingBottom="6dp"
                        android:text="背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景背景"
                        android:textSize="@dimen/_16ssp"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/llHasImage"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <!--图-->
                        <ImageView
                            android:id="@+id/imgImage"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:background="@color/color_f5f5f5"
                            android:scaleType="center"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible" />
                        <!--优惠活动-->
                        <TextView
                            android:id="@+id/tvCouponActivity"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:paddingHorizontal="6dp"
                            android:paddingVertical="4dp"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12ssp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="spread"
                            app:layout_constraintWidth_default="wrap"
                            tools:text="第二份半价第二份半价" />

                        <!--有图时候的名字-->
                        <TextView
                            android:id="@+id/tvNameWithImage"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="@drawable/menu_gradient"
                            android:ellipsize="end"
                            android:gravity="bottom"
                            android:lines="1"
                            android:paddingHorizontal="10dp"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            android:visibility="visible"
                            app:layout_constraintBottom_toBottomOf="@id/imgImage"
                            tools:text="Caramel Cake"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvSoldOutWithImage"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:background="@color/black40"
                            android:gravity="center"
                            android:text="@string/sold_out"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/llNoImage"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <!--优惠活动-->
                        <TextView
                            android:id="@+id/tvCouponActivityNoImage"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:paddingHorizontal="6dp"
                            android:paddingVertical="4dp"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12ssp"
                            android:visibility="gone"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="0.0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="spread"
                            app:layout_constraintWidth_default="wrap"
                            tools:text="第二份半价第二份半价"
                            tools:visibility="visible" />

                        <!--无图时候的名字-->
                        <TextView
                            android:id="@+id/tvNameNoImage"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="10dp"
                            android:layout_marginTop="10dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:lines="2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            android:visibility="visible"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvCouponActivityNoImage"
                            tools:text="Caramel CakeCaramel CakeCaramel CakeCaramel CakeCaramel CakeCaramel Cake"
                            tools:visibility="visible" />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>


                <TextView
                    android:id="@+id/tvPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_marginBottom="10dp"
                    android:textColor="@color/primaryColorDark"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="$0.00" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvSoldOut"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black40"
                android:gravity="center"
                android:text="@string/sold_out"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>
    </androidx.cardview.widget.CardView>

</cn.bingoogolapple.badgeview.BGABadgeFrameLayout>
