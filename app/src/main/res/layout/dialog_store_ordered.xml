<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog_info"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            tools:ignore="UseCompoundDrawables">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/orders_list"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                tools:context=".ui.table.TableFragment">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!--                    <EditText-->
                    <!--                        android:id="@+id/edtSearch"-->
                    <!--                        style="@style/commonSearchStyle"-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_marginEnd="10dp"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:hint="@string/order_id_table_phone_number"-->
                    <!--                        app:layout_constraintStart_toStartOf="parent"-->
                    <!--                        app:layout_constraintTop_toTopOf="parent"-->
                    <!--                        tools:ignore="Autofill" />-->

                    <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                        android:id="@+id/edtSearch"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        app:search_hint="@string/order_id_table_phone_number" />

                    <LinearLayout
                        android:id="@+id/dropdownOrderStatus"
                        android:layout_width="0dp"
                        android:layout_height="32dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_language_spiner"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingHorizontal="10dp"
                        android:visibility="visible"
                        app:layout_constraintStart_toEndOf="@id/edtSearch"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tvOrderStatus"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:maxLines="1"
                            android:text="@string/all_order"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/arrowOrderStatus"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:src="@drawable/ic_dropdown"
                            android:visibility="visible"
                            tools:ignore="ContentDescription" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/dropdownPaymentMethod"
                        android:layout_width="0dp"
                        android:layout_height="32dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_language_spiner"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingHorizontal="10dp"
                        android:visibility="visible"
                        app:layout_constraintStart_toEndOf="@id/dropdownOrderStatus"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tvDropPaymentMethod"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:maxLines="1"
                            android:text="@string/all_payment_method"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/arrowPaymentMethod"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:src="@drawable/ic_dropdown"
                            android:visibility="visible"
                            tools:ignore="ContentDescription" />
                    </LinearLayout>

                    <com.metathought.food_order.casheir.ui.widget.CalendarTextView
                        android:id="@+id/tvCalendar"
                        style="@style/commonCalendarTextViewStyle"
                        android:layout_width="0dp"
                        android:layout_marginStart="15dp"
                        android:layout_weight="1"
                        android:maxWidth="210dp"
                        app:layout_constraintStart_toEndOf="@id/dropdownPaymentMethod"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvClearFilter"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="10dp"
                        android:drawablePadding="10dp"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="15dp"
                        android:singleLine="true"
                        android:text="@string/clear_filter"
                        android:textColor="@color/primaryColor"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toEndOf="@id/tvCalendar"
                        app:layout_constraintTop_toTopOf="parent" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/background_dialog"
                    android:orientation="vertical"
                    android:paddingHorizontal="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvCalendar">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/order_id"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/table"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.9"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/customer_name"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.7"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/total"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.7"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/order_status"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1.5"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/payment_method"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/ordered_time"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/payment_time"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/action"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <View style="@style/commonDividerStyle" />

                    <com.scwang.smart.refresh.layout.SmartRefreshLayout
                        android:id="@+id/refreshLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/background_dialog"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">


                        <com.scwang.smart.refresh.header.MaterialHeader
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerviewStoreOrder"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clipToPadding="false"
                            android:overScrollMode="never"
                            android:paddingBottom="16dp"
                            android:scrollbars="none"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="5"
                            tools:listitem="@layout/store_order_item" />

                        <com.scwang.smart.refresh.footer.ClassicsFooter
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
                </LinearLayout>


            </LinearLayout>

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />

            <FrameLayout
                android:id="@+id/layoutProgressBar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible">

                <ProgressBar
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="10dp"
                    android:indeterminate="true"
                    android:indeterminateTintMode="src_atop" />
            </FrameLayout>
        </FrameLayout>
    </LinearLayout>


    <com.metathought.food_order.casheir.ui.widget.OrderDetailsView
        android:id="@+id/layoutDetail"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>
