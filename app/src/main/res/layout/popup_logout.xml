<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_setting_popup_dropdown"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/imgUserProfile"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginEnd="10dp"
            android:gravity="center"
            android:src="@drawable/ic_profile"
            android:visibility="visible"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tvUserName"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="2"
            android:text="@string/staff_name"
            android:textColor="@color/black"
            android:textSize="16sp"
            tools:text="1111" />

        <cn.bingoogolapple.badgeview.BGABadgeTextView
            android:id="@+id/tvVersion"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="start"
            android:gravity="start|center_vertical"
            android:paddingEnd="15dp"
            android:textColor="@color/black40"
            android:textSize="@dimen/_12ssp"
            tools:text="v1.0.0 Version" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvStoreManager"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/store_manager"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvStoreConfiguration"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/store_configuration"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvSetBox"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/set_money_box"
        android:textColor="@color/black"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvPrinterReport"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/store_reports"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvPrinterManager"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/printer_manager"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/llChangeLanguage"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:visibility="visible"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvChangeLanguage"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:fontFamily="@font/roboto"
            android:gravity="start|center_vertical"
            android:text="@string/change_language"
            android:textColor="@color/black" />

        <LinearLayout
            android:id="@+id/dropdownLanguage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvLanguages"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="English" />

            <ImageView
                android:id="@+id/arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:src="@drawable/ic_arrow_right"
                android:visibility="visible"
                app:tint="@color/black" />

        </LinearLayout>
    </LinearLayout>

    <!--    <RadioGroup-->
    <!--        android:id="@+id/radioGroupPaymentMethod"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="40dp"-->
    <!--        android:layout_weight="1"-->
    <!--        android:checkedButton="@id/radioAll"-->
    <!--        android:orientation="horizontal"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        tools:visibility="visible">-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioEn"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_marginEnd="10dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_language_background"-->
    <!--            android:button="@null"-->
    <!--            android:checked="true"-->
    <!--            android:gravity="center"-->
    <!--            android:paddingHorizontal="10dp"-->
    <!--            android:text="@string/english_language"-->
    <!--            android:textColor="@drawable/selector_language_color"-->
    <!--            android:textSize="14sp"-->
    <!--            android:textStyle="bold"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioKm"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_marginEnd="10dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_language_background"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="10dp"-->
    <!--            android:text="@string/khmer_language"-->
    <!--            android:textColor="@drawable/selector_language_color"-->
    <!--            android:textSize="14sp"-->
    <!--            android:textStyle="bold"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioZh"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_language_background"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="10dp"-->
    <!--            android:text="@string/chinese_language"-->
    <!--            android:textColor="@drawable/selector_language_color"-->
    <!--            android:textSize="14sp"-->
    <!--            android:textStyle="bold"-->
    <!--            app:buttonCompat="@null" />-->

    <!--    </RadioGroup>-->


    <TextView
        android:id="@+id/tvContactUs"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/contact_us"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvShiftHandoverRecords"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/shift_handover_records"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvClassesStart"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/classes_start"
        android:textColor="@color/black"
        android:visibility="visible"
        tools:visibility="visible" />


<!--    <TextView-->
<!--        android:id="@+id/tvClassesClose"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="40dp"-->
<!--        android:fontFamily="@font/roboto"-->
<!--        android:gravity="start|center_vertical"-->
<!--        android:text="@string/classes_close"-->
<!--        android:textColor="@color/black"-->
<!--        android:visibility="visible"-->
<!--        tools:visibility="visible" />-->

    <TextView
        android:id="@+id/tvLogout"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/log_out"
        android:textColor="@color/color_ff3141" />

</LinearLayout>