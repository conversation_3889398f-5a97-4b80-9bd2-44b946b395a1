<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llPrintAgain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvPrintAgain0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:gravity="center"
                    android:text="@string/print_again"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPrintAgain1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:layout_marginStart="2dp"
                    android:gravity="center"
                    android:text="@string/print_again"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvFirstStoreName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="26px"
                        android:textStyle="bold"
                        tools:text="JM美味海鲜餐厅(体验店)" />
                </LinearLayout>


                <TextView
                    android:id="@+id/tvSecondStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="JM美味海鲜餐厅(体验店)"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvSeriesNo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:textSize="26px"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="-NO.0003"
                    tools:visibility="visible" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llOrderType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/llDiningStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDiningStyle0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="Dining-In" />

                    <TextView
                        android:id="@+id/tvDiningStyle1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:text="ការប្រើប្រាស់ផ្ទៃក្នុង"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvPickUpNo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="15px"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    tools:text="001" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llTableName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top">

                <LinearLayout
                    android:id="@+id/llTableNameTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:text="@string/print_title_table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTableName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="A01" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top">

                <LinearLayout
                    android:id="@+id/llDateTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDateTime0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_datetime"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvDateTime1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:text="@string/print_title_datetime"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


                <TextView
                    android:id="@+id/tvOrderTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="2023-10-10 10:10:10" />
            </LinearLayout>

            <include
                layout="@layout/dot_divider"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="0px"
                android:layout_marginBottom="0px" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="1"
                tools:listitem="@layout/item_kitchen_ticket_order_menu"
                tools:visibility="visible" />

            <View
                android:id="@+id/vLine"
                style="@style/ticketDividerStyle"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvZplist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="1"
                tools:listitem="@layout/item_kitchen_ticket_order_menu"
                tools:visibility="visible" />

            <include
                layout="@layout/dot_divider"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="0px"
                android:layout_marginBottom="0dp" />

            <LinearLayout
                android:id="@+id/llRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llRemarkTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_remark"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:text="@string/print_title_remark"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


                <TextView
                    android:id="@+id/tvRemark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="No Spicy! No chili, ginger, onion, garlic" />

<!--                <include-->
<!--                    layout="@layout/dot_divider"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="2dp"-->
<!--                    android:layout_marginVertical="0px" />-->
            </LinearLayout>

            <include
                android:id="@+id/vFooter"
                layout="@layout/view_printer_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>