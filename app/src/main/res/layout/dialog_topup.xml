<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:layout_height="560dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMain"
        android:layout_width="800dp"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainWhite"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/top_up"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvTitle"
                tools:ignore="ContentDescription" />

            <View
                android:id="@+id/vCustomerInfo"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="25dp"
                android:layout_marginBottom="-16dp"
                android:background="@drawable/background_dialog_info"
                app:layout_constraintBottom_toBottomOf="@+id/tvBalanceLabel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />


            <TextView
                android:id="@+id/tvCustomerInfoLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/customer_info"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toTopOf="@+id/vCustomerInfo" />


            <TextView
                android:id="@+id/tvNameLabel"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/customer_nickname"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfoLabel" />

            <TextView
                android:id="@+id/tvNickname"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="end"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvNameLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintStart_toEndOf="@+id/tvNameLabel"
                tools:text="1231231212312123123121123121" />


            <TextView
                android:id="@+id/tvPhoneLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/customer_account"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvPhoneNumber"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvNameLabel" />

            <TextView
                android:id="@+id/tvPhoneNumber"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvPhoneLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                tools:text="088 89 90 098" />


            <TextView
                android:id="@+id/tvBalanceLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/customer_balance"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvBalance"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvPhoneLabel" />

            <TextView
                android:id="@+id/tvBalance"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvBalanceLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                tools:text="$99.99" />


            <TextView
                android:id="@+id/tvTopupAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/top_up_amount"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvTopupAmount"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/vCustomerInfo" />

            <TextView
                android:id="@+id/tvTopupAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$--"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTopupAmountLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel" />

            <TextView
                android:id="@+id/tvAddGiftAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/additional_gift_amount"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvAddGiftAmount"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvTopupAmountLabel" />

            <TextView
                android:id="@+id/tvAddGiftAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvAddGiftAmountLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                tools:text="$300.00" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupAddGiftAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvAddGiftAmountLabel,tvAddGiftAmount"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvExtraCouponGiveawayLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/extra_coupon_giveaway"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvExtraCouponGiveaway"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvAddGiftAmountLabel" />

            <TextView
                android:id="@+id/tvExtraCouponGiveaway"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvExtraCouponGiveawayLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                tools:text="2张" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupExtraCouponGiveaway"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvExtraCouponGiveawayLabel,tvExtraCouponGiveaway"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/tvTotalRechargeAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/total_recharge_amount"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvTotalRechargeAmount"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvExtraCouponGiveawayLabel" />

            <TextView
                android:id="@+id/tvTotalRechargeAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTotalRechargeAmountLabel"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                tools:text="$300.00" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupTotalRechargeAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvTotalRechargeAmountLabel,tvTotalRechargeAmount"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvPayableAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/payable_amount"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/tvPayAmountUnit"
                app:layout_constraintStart_toStartOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toBottomOf="@+id/tvTotalRechargeAmountLabel" />


            <TextView
                android:id="@+id/tvPayAmountUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="3dp"
                android:src="@drawable/ic_dollar"
                app:drawableStartCompat="@drawable/ic_dollar"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvPayableAmount"
                app:layout_constraintEnd_toStartOf="@+id/tvPayableAmount" />

            <TextView
                android:id="@+id/tvPayableAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"
                android:textColor="@color/primaryColor"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/tvCustomerInfoLabel"
                app:layout_constraintTop_toTopOf="@+id/tvPayableAmountLabel" />


            <LinearLayout
                android:id="@+id/btnTopUp"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="60dp"
                android:alpha="0.5"
                android:background="@drawable/button_login_background2"
                android:clickable="false"
                android:enabled="false"
                android:focusable="false"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvPayableAmountLabel"
                app:layout_constraintVertical_bias="1">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/top_up"
                    android:textColor="@color/mainWhite"
                    android:textSize="18sp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutRecharge"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBackground"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvRechargeTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/top_up_amount_require"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvRechargeLevelList"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="3dp"
                android:layout_marginBottom="16dp"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/tvCouponLabel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvRechargeTitle"
                tools:itemCount="4" />

            <TextView
                android:id="@+id/tvCouponLabel"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="@string/coupon"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                app:layout_constraintBottom_toTopOf="@+id/tilRemark"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tvCoupon"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:drawablePadding="4dp"
                android:gravity="end"
                android:maxLines="1"
                android:paddingVertical="5dp"
                android:text="@string/hint_no_can_use_coupon"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                android:visibility="visible"
                app:drawableEndCompat="@drawable/icon_coupon_arrow_right"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvCouponLabel"
                app:layout_constraintEnd_toEndOf="parent"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilRemark"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/remark"
                android:textColorHint="@color/black60"
                android:visibility="visible"
                app:boxBackgroundColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtRemark"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:inputType="text|textMultiLine"
                    android:maxLength="150"
                    android:maxLines="4"
                    android:minLines="4"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />

            </com.google.android.material.textfield.TextInputLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/layoutRechargeEmpty"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBackground"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />

        </FrameLayout>


        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_centerInParent="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
