<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/llFoodName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="5"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFoodNameEn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:ellipsize="end"
                android:textColor="@color/black"
                android:textSize="32px"
                android:textStyle="bold"
                tools:text="Pasta" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.2"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFoodCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:gravity="center_horizontal"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                android:textStyle="bold"
                tools:text="x1" />
        </LinearLayout>
    </LinearLayout>


    <TextView
        android:id="@+id/tvFoodNameKh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:textColor="@color/black"
        android:textSize="32px"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="ប៉ាស្តា"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/setMealRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="1"
        tools:listitem="@layout/item_printer_kitchen_feed" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/specRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="1"
        tools:listitem="@layout/item_printer_kitchen_feed" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/feedRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="1"
        tools:listitem="@layout/item_printer_kitchen_feed" />

    <TextView
        android:id="@+id/tvRemarkTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="备注:"
        android:textColor="@color/black"
        android:textSize="22px"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvRemark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20px"
        android:textColor="@color/black"
        android:textSize="22px"
        tools:text="备注:" />

    <View
        android:id="@+id/vLine"
        style="@style/ticketDividerStyle" />
</LinearLayout>
