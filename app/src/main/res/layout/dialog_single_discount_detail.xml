<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:minHeight="210dp"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/detail" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/original_price"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:baselineAligned="true"
                android:gravity="end"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvVipOldPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:drawablePadding="2dp"
                    android:textColor="@color/member_price_color"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:drawableStartCompat="@drawable/icon_vip"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tvOldPrice"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="$0.00"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvOldPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="$99.99" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/new_price"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:baselineAligned="true"
                android:gravity="end"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvVipNewPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:drawablePadding="2dp"
                    android:textColor="@color/member_price_color"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:drawableStartCompat="@drawable/icon_vip"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tvNewPrice"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="$0.00"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvNewPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="$99.99" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/hint_reason"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:baselineAligned="true"
                android:gravity="end"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvReason"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="$99.99" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>