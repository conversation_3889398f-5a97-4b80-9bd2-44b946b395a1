<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="20dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@mipmap/icon_download_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivRocket"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="13dp"
                android:background="@mipmap/icon_download_rocket"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_cross_closed"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="24dp"
                android:text="@string/find_new_version"
                android:textColor="@color/paid_text_color"
                android:textSize="28sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvVersionName"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:textColor="@color/paid_text_color"
                android:textSize="16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                tools:text="V1.12.0" />

            <TextView
                android:id="@+id/tvUpdateContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="45dp"
                android:layout_marginBottom="20dp"
                android:paddingHorizontal="30dp"
                android:text="@string/update_content"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                app:layout_constraintBottom_toBottomOf="@id/ivRocket"
                app:layout_constraintStart_toStartOf="parent" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvUpdateContent">

                <ScrollView
                    android:id="@+id/scrollView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="24dp"
                    android:layout_weight="1"
                    android:paddingHorizontal="30dp"
                    android:scrollbars="none">

                    <TextView
                        android:id="@+id/tvContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp"
                        tools:text="更新内容" />
                </ScrollView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/llProgress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="30dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginBottom="31dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/scrollView">

                        <ProgressBar
                            android:id="@+id/progress_bar"
                            style="@android:style/Widget.Material.ProgressBar.Horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:max="100"
                            android:min="0"
                            android:progress="0"
                            android:progressDrawable="@drawable/horizontal_progress_style"
                            android:secondaryProgress="0"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/scrollView" />

                        <TextView
                            android:id="@+id/tvProgress"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="V1.12.0"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/progress_bar" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="30dp"
                        android:layout_marginBottom="30dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnUpdate"
                            style="@style/CustomOutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:text="@string/update_now"
                            android:textAllCaps="false"
                            android:textColor="@color/white"
                            android:textSize="20sp"
                            app:backgroundTint="@color/primaryColor"
                            app:cornerRadius="12dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/scrollView" />

                        <TextView
                            android:id="@+id/tvNoCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:textColor="@color/black40"
                            android:text="@string/do_not_remind_me_again" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>
