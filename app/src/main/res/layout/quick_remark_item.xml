<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingEnd="10dp"
    android:paddingBottom="10dp"
    >

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layoutMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:cardBackgroundColor="@color/white"
        app:strokeColor="@color/black20"
        app:strokeWidth="1dp">

        <TextView
            android:id="@+id/tvRemark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:padding="10dp"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            tools:text="Hello" />

    </com.google.android.material.card.MaterialCardView>
</LinearLayout>
