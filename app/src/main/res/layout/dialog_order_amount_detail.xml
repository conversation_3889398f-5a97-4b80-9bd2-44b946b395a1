<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="520dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/consumption_details" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layoutTotal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/subtotal"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvVipSubTotalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvSubtotal"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscountActivity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="top"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvDiscountActivityTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/discount_activity"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/btnDiscountActivityPriceCue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvVipDiscountActivityAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvDiscountActivityAmount"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvDiscountActivityAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPackPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/packing_price"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/btnPackPriceCue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvVipPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvPackingAmount"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llServiceFee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="top"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/service_fee"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/btnServiceFeeCue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvVipServiceFee"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvServiceFee"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvServiceFee"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llCoupon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/coupon"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <LinearLayout
                        android:id="@+id/llCouponContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end">

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/tvVipCoupon"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:drawableStart="@drawable/icon_vip"-->
                        <!--                                android:drawablePadding="2dp"-->
                        <!--                                android:textColor="@color/member_price_color"-->
                        <!--                                android:textSize="14sp"-->
                        <!--                                android:textStyle="bold"-->
                        <!--                                android:visibility="gone"-->
                        <!--                                tools:text="$0.00"-->
                        <!--                                tools:visibility="visible" />-->

                        <TextView
                            android:id="@+id/tvViewCouponGiftGood"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:text="@string/view_give_away_goods"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvCoupon"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            android:visibility="visible"
                            tools:text="-$99.99"
                            tools:visibility="visible" />

                        <!--                <ImageView-->
                        <!--                    android:id="@+id/iconCouponArrow"-->
                        <!--                    android:layout_width="wrap_content"-->
                        <!--                    android:layout_height="match_parent"-->
                        <!--                    android:layout_marginStart="5dp"-->
                        <!--                    android:paddingHorizontal="5dp"-->
                        <!--                    android:src="@drawable/icon_coupon_arrow_right" />-->
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvDiscountTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/discounts"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/btnDiscountCue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvVipDiscount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvDiscount"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvDiscount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscountAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvDiscountAmountTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/amount_of_reduction_usd"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/btnDiscountAmountCue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvVipDiscountAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvDiscountAmount"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvDiscountAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llVat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/titleVat"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/vat"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvVipVat"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tvVat"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvVat"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCommission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/commission"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/btnCommissionCue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end">


                        <TextView
                            android:id="@+id/tvCommissionPrice"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$99.99" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llTotalPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:gravity="center_vertical"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center"
                        android:text="@string/total_price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:baselineAligned="true"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            app:layout_constraintBaseline_toBaselineOf="@id/tvTotalPrice"
                            app:layout_constraintEnd_toStartOf="@id/tvTotalPrice"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvTotalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0" />

                        <TextView
                            android:id="@+id/tvTotalKhrPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvTotalPrice"
                            tools:text="៛0" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>


                <!--                <LinearLayout-->
                <!--                    android:id="@+id/llPartialRefundAmount"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:visibility="visible">-->

                <!--                    <TextView-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="match_parent"-->
                <!--                        android:layout_marginEnd="15dp"-->
                <!--                        android:gravity="center_vertical"-->
                <!--                        android:text="@string/partial_refund_amount"-->
                <!--                        android:textColor="@color/black60"-->
                <!--                        android:textSize="@dimen/_14ssp" />-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tvPartialRefundAmount"-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="0dp"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_weight="1"-->
                <!--                        android:gravity="end"-->
                <!--                        android:maxLines="1"-->
                <!--                        android:textColor="@color/main_red"-->
                <!--                        android:textSize="@dimen/_14ssp"-->
                <!--                        android:textStyle="bold"-->
                <!--                        tools:text="-$99.99" />-->
                <!--                </LinearLayout>-->

                <!--                <LinearLayout-->
                <!--                    android:id="@+id/llPartialRefundPackFee"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:visibility="visible">-->

                <!--                    <TextView-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="match_parent"-->
                <!--                        android:layout_marginEnd="15dp"-->
                <!--                        android:gravity="center_vertical"-->
                <!--                        android:text="@string/refund_pack_fee"-->
                <!--                        android:textColor="@color/black60"-->
                <!--                        android:textSize="@dimen/_14ssp" />-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tvPartialRefundPackFee"-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="0dp"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_weight="1"-->
                <!--                        android:gravity="end"-->
                <!--                        android:maxLines="1"-->
                <!--                        android:textColor="@color/main_red"-->
                <!--                        android:textSize="@dimen/_14ssp"-->
                <!--                        android:textStyle="bold"-->
                <!--                        tools:text="-$99.99" />-->
                <!--                </LinearLayout>-->

                <!--                <LinearLayout-->
                <!--                    android:id="@+id/llPartialRefundServiceFee"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:visibility="visible">-->

                <!--                    <TextView-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="match_parent"-->
                <!--                        android:layout_marginEnd="15dp"-->
                <!--                        android:gravity="center_vertical"-->
                <!--                        android:text="@string/refund_service_fee"-->
                <!--                        android:textColor="@color/black60"-->
                <!--                        android:textSize="@dimen/_14ssp" />-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tvPartialRefundServiceFee"-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="0dp"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_weight="1"-->
                <!--                        android:gravity="end"-->
                <!--                        android:maxLines="1"-->
                <!--                        android:textColor="@color/main_red"-->
                <!--                        android:textSize="@dimen/_14ssp"-->
                <!--                        android:textStyle="bold"-->
                <!--                        tools:text="-$99.99" />-->
                <!--                </LinearLayout>-->

                <!--                <LinearLayout-->
                <!--                    android:id="@+id/llVatRefundAmount"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:visibility="visible">-->

                <!--                    <TextView-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="match_parent"-->
                <!--                        android:layout_marginEnd="15dp"-->
                <!--                        android:gravity="center_vertical"-->
                <!--                        android:text="@string/vat_refund"-->
                <!--                        android:textColor="@color/black60"-->
                <!--                        android:textSize="@dimen/_14ssp" />-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tvVatRefundAmount"-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="0dp"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_weight="1"-->
                <!--                        android:gravity="end"-->
                <!--                        android:maxLines="1"-->
                <!--                        android:textColor="@color/main_red"-->
                <!--                        android:textSize="@dimen/_14ssp"-->
                <!--                        android:textStyle="bold"-->
                <!--                        tools:text="-$99.99" />-->
                <!--                </LinearLayout>-->


            </LinearLayout>


        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>