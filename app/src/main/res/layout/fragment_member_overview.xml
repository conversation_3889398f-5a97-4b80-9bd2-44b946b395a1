<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    tools:context=".ui.table.TableFragment">

    <RadioGroup
        android:id="@+id/radioGroupFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/round_background_25dp"
        android:checkedButton="@id/radioAll"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/radioOverview"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/overview"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioMember"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/customer"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioBalance"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/details"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioCredit"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/credit"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />
    </RadioGroup>

    <LinearLayout
        android:id="@+id/dropdownFilter"
        android:layout_width="150dp"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/background_language_spiner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:visibility="visible"
        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvType"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/today"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/all" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_dropdown"
            android:visibility="visible"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <!--    <RadioGroup-->
    <!--        android:id="@+id/radioFilter"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="40dp"-->
    <!--        android:layout_marginStart="15dp"-->
    <!--        android:background="@drawable/round_background_25dp"-->
    <!--        android:checkedButton="@id/radioAll"-->
    <!--        android:orientation="horizontal"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"-->
    <!--        app:layout_constraintTop_toTopOf="parent">-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioToday"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:checked="true"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="15dp"-->
    <!--            android:text="@string/today"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="@dimen/_14ssp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioThisWeek"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="15dp"-->
    <!--            android:text="@string/this_week"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="@dimen/_14ssp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioMonth"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="15dp"-->
    <!--            android:text="@string/this_month"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="@dimen/_14ssp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioQuarter"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="15dp"-->
    <!--            android:text="@string/this_quarter"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="@dimen/_14ssp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioYear"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="15dp"-->
    <!--            android:text="@string/this_year"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="@dimen/_14ssp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--    </RadioGroup>-->

    <com.metathought.food_order.casheir.ui.widget.CalendarTextView
        android:id="@+id/tvCalendar"
        style="@style/commonCalendarTextViewStyle"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@id/tvClearFilter"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="01 03, 2024 - 01 03, 2024" />

    <TextView
        android:id="@+id/tvClearFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:drawablePadding="6dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="15dp"
        android:singleLine="true"
        android:text="@string/clear_filter"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCalendar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:paddingBottom="6dp"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingBottom="6dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/white">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView

                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="center"
                            android:text="@string/account_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvAccountBalance"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/default_data_showing"
                            android:textColor="@color/black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvYesterdayAccountBalance"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="10dp"
                            android:autoSizeMaxTextSize="16sp"
                            android:autoSizeMinTextSize="11sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/yesterday"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            tools:text="Yesterday $888.88" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/white">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView

                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="center"
                            android:text="@string/top_up_amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvTopupAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/default_data_showing"
                            android:textColor="@color/black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvYesterTopupAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="10dp"
                            android:autoSizeMaxTextSize="16sp"
                            android:autoSizeMinTextSize="11sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/yesterday"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            tools:text="Yesterday $888.88" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/white">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView

                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="center"
                            android:text="@string/paid_amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvPaidAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/default_data_showing"
                            android:textColor="@color/black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvYesterdayPaidAmuont"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="10dp"
                            android:autoSizeMaxTextSize="16sp"
                            android:autoSizeMinTextSize="11sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/yesterday"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            tools:text="Yesterday $888.88" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/white">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView

                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="center"
                            android:text="@string/number_of_members"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvNumberofMember"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="--"
                            android:textColor="@color/black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvYesterdayNumberofMember"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="10dp"
                            android:autoSizeMaxTextSize="16sp"
                            android:autoSizeMinTextSize="11sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/yesterday"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            tools:text="Yesterday $888.88" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/white">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView

                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="center"
                            android:text="@string/number_of_top_up_member"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvNumberofTopupMember"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/default_data_showing"
                            android:textColor="@color/black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvYesterdayNumberofTopupMember"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="10dp"
                            android:autoSizeMaxTextSize="16sp"
                            android:autoSizeMinTextSize="11sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/yesterday"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            tools:text="Yesterday $888.88" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    app:strokeColor="@color/white">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView

                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="center"
                            android:text="@string/number_of_paid_member"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvNumberofPaidMember"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/default_data_showing"
                            android:textColor="@color/black"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHeight_percent="0.5"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvYesterdayNumberofPaidMember"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="10dp"
                            android:autoSizeMaxTextSize="16sp"
                            android:autoSizeMinTextSize="11sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="top"
                            android:paddingTop="5dp"
                            android:text="@string/yesterday"
                            android:textColor="@color/black60"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHeight_percent="0.5" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </LinearLayout>

        <FrameLayout
            android:id="@+id/layoutChartUSD"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingEnd="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.55"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.5">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/white">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/tvTtitleChart"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:text="@string/line_chart_of_daily_top_up_amount"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:gravity="center"
                        android:text="@string/unit_usd"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/lineChart"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/tvTtitleChart"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="10dp" />
                </RelativeLayout>
            </com.google.android.material.card.MaterialCardView>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/layoutChartPeople"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.55"
            app:layout_constraintWidth_percent="0.5">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="15dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/white">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/tvTtitleChartMember"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:text="@string/line_chart_of_number_of_top_up_member"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:gravity="center"
                        android:text="@string/unit_people"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/lineChartMember"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/tvTtitleChartMember"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="10dp" />
                </RelativeLayout>
            </com.google.android.material.card.MaterialCardView>
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
