<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="50dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvName"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1.5"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/print_title_item_name" />

        <TextView
            android:id="@+id/tvNum"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/quantity" />


        <TextView
            android:id="@+id/tvSinglePrice"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/single_price" />

        <TextView
            android:id="@+id/tvServiceFee"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/service_fee" />

        <TextView
            android:id="@+id/tvPackingFee"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/packing_price" />

    </LinearLayout>

    <View
        android:id="@+id/vLine"
        style="@style/commonDividerStyle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>

