<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/change_language" />


        <TextView
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginTop="25dp"
            android:gravity="start"
            android:text="@string/please_select_require"
            android:textColor="@color/black80"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/tvEnglish"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/selector_rounded_rectangle_border"
                android:button="@null"
                android:checked="false"
                android:drawableStart="@drawable/radiobutton_payment_icon"
                android:drawablePadding="10dp"
                android:gravity="start|center_vertical"
                android:paddingHorizontal="20dp"
                android:text="@string/english_language"
                android:textColor="@drawable/selector_primary_color"
                android:textSize="14sp"
                android:textStyle="bold"
                app:buttonCompat="@null" />

            <TextView
                android:id="@+id/tvKm"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/selector_rounded_rectangle_border"
                android:button="@null"
                android:checked="false"
                android:drawableStart="@drawable/radiobutton_payment_icon"
                android:drawablePadding="10dp"
                android:gravity="start|center_vertical"
                android:paddingHorizontal="20dp"
                android:text="@string/khmer_language"
                android:textColor="@drawable/selector_primary_color"
                android:textSize="14sp"
                android:textStyle="bold"
                app:buttonCompat="@null" />

            <TextView
                android:id="@+id/tvChinese"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/selector_rounded_rectangle_border"
                android:button="@null"
                android:checked="false"
                android:drawableStart="@drawable/radiobutton_payment_icon"
                android:drawablePadding="10dp"
                android:gravity="start|center_vertical"
                android:paddingHorizontal="20dp"
                android:text="@string/chinese_language"
                android:textColor="@drawable/selector_primary_color"
                android:textSize="14sp"
                android:textStyle="bold"
                app:buttonCompat="@null" />
        </LinearLayout>

        <!--        <RadioGroup-->
        <!--            android:id="@+id/radioGroupPaymentMethod"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="40dp"-->
        <!--            android:layout_marginTop="8dp"-->
        <!--            android:layout_weight="1"-->
        <!--            android:checkedButton="@id/radioAll"-->
        <!--            android:orientation="horizontal"-->
        <!--            android:visibility="visible"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="parent"-->
        <!--            tools:visibility="visible">-->

        <!--            <RadioButton-->
        <!--                android:id="@+id/radioEn"-->
        <!--                style="@style/FontLocalization"-->
        <!--                android:layout_width="0dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:layout_marginEnd="10dp"-->
        <!--                android:layout_weight="1"-->
        <!--                android:background="@drawable/radiobutton_language_background"-->
        <!--                android:button="@null"-->
        <!--                android:checked="true"-->
        <!--                android:gravity="center"-->
        <!--                android:paddingHorizontal="10dp"-->
        <!--                android:text="@string/english_language"-->
        <!--                android:textColor="@drawable/selector_language_color"-->
        <!--                android:textSize="14sp"-->
        <!--                android:textStyle="bold"-->
        <!--                app:buttonCompat="@null" />-->

        <!--            <RadioButton-->
        <!--                android:id="@+id/radioKm"-->
        <!--                style="@style/FontLocalization"-->
        <!--                android:layout_width="0dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:layout_marginEnd="10dp"-->
        <!--                android:layout_weight="1"-->
        <!--                android:background="@drawable/radiobutton_language_background"-->
        <!--                android:button="@null"-->
        <!--                android:gravity="center"-->
        <!--                android:minHeight="0dp"-->
        <!--                android:paddingHorizontal="10dp"-->
        <!--                android:text="@string/khmer_language"-->
        <!--                android:textColor="@drawable/selector_language_color"-->
        <!--                android:textSize="14sp"-->
        <!--                android:textStyle="bold"-->
        <!--                app:buttonCompat="@null" />-->

        <!--            <RadioButton-->
        <!--                android:id="@+id/radioZh"-->
        <!--                style="@style/FontLocalization"-->
        <!--                android:layout_width="0dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:layout_weight="1"-->
        <!--                android:background="@drawable/radiobutton_language_background"-->
        <!--                android:button="@null"-->
        <!--                android:gravity="center"-->
        <!--                android:minHeight="0dp"-->
        <!--                android:paddingHorizontal="10dp"-->
        <!--                android:text="@string/chinese_language"-->
        <!--                android:textColor="@drawable/selector_language_color"-->
        <!--                android:textSize="14sp"-->
        <!--                android:textStyle="bold"-->
        <!--                app:buttonCompat="@null" />-->

        <!--        </RadioGroup>-->


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
