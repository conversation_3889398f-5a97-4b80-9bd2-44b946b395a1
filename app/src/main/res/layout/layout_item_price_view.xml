<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="top"
    android:gravity="end"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvFoodPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="end"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="$9.9" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/tvWeight"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            tools:text="1/kl" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvVipPrice"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="2dp"
        android:textColor="@color/member_price_color"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/icon_vip"
        tools:text="$0.00"
        tools:visibility="visible" />

    <!--    <TextView-->
    <!--        android:id="@+id/tvOriginalPrice"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="4dp"-->
    <!--        android:ellipsize="end"-->
    <!--        android:foreground="@drawable/strike_price"-->
    <!--        android:maxLines="3"-->
    <!--        android:textColor="@color/black60"-->
    <!--        android:textSize="@dimen/_14ssp"-->
    <!--        android:visibility="gone"-->
    <!--        tools:text="12.99"-->
    <!--        tools:visibility="visible" />-->

    <TextView
        android:id="@+id/tvRefundAmount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:textColor="@color/main_red"
        android:textSize="@dimen/_14ssp"
        android:visibility="gone"
        tools:text="11111"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTimePriceSign"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:text="@string/time_price_parentheses"
        android:textColor="@color/black60"
        android:textSize="@dimen/_12ssp"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivWarn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_warn_green"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>