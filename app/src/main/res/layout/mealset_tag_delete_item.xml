<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="0dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="56dp"
        android:layout_margin="5dp"
        android:orientation="horizontal"
        app:cardBackgroundColor="@color/white"
        app:strokeColor="@color/black20"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:paddingHorizontal="10dp">

            <TextView
                android:id="@+id/tvTag"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@drawable/selector_primary_color"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                tools:text="Hello $9.99" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="8dp"
                android:gravity="center">

                <ImageView
                    android:id="@+id/imgMinus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_minus"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/tvQTY"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLength="4"
                    android:paddingHorizontal="10dp"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_18ssp"
                    tools:text="1" />

                <ImageView
                    android:id="@+id/imgPlus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_add" />
            </LinearLayout>
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</FrameLayout>