<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/reach_limit_background_color"
    android:gravity="center"
    android:visibility="gone"
    tools:visibility="visible"
    android:layout_height="40dp">
    <ImageView
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:src="@drawable/ic_info"/>
    <TextView
        android:layout_marginStart="10dp"
        android:id="@+id/tvReachLimit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:text="@string/you_have_reached_the_maximum_quantity_limit_of"/>
</LinearLayout>