<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clOtherLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/second_other_arc_background"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/ivOtherIcon"
            android:layout_width="515dp"
            android:layout_height="515dp"
            android:layout_marginEnd="26dp"
            android:background="@mipmap/ic_second_other_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="65dp"
            android:layout_marginEnd="-80dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivOtherIcon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgOtherLogo"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_logo" />

            <TextView
                android:id="@+id/tvOtherStoreName"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:ellipsize="end"
                android:fontFamily="@font/khmer_os"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="35sp"
                android:textStyle="bold"
                tools:text="山河茶餐厅山河茶餐厅山河茶餐厅山河茶餐厅山河茶餐厅山河茶餐厅" />

            <TextView
                android:id="@+id/welcome"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/khmer_os"
                android:maxLines="2"
                android:text="@string/welcome"
                android:textColor="@color/white"
                android:textSize="35sp"
                android:textStyle="bold" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMenOrder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/toolbar"
            android:layout_width="0dp"
            android:layout_height="70dp"
            android:background="@color/mainWhite"
            android:backgroundTint="@color/mainWhite"
            android:gravity="center_vertical"
            android:paddingHorizontal="30dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgMenuLogo"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_logo" />

            <TextView
                android:id="@+id/tvMenuStoreName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="20sp"
                tools:text="@string/brand_name" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutOrder"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@drawable/background_e7e7e7_top_right_radius_10dp"
                android:paddingHorizontal="10dp"
                android:paddingTop="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.4">

                <FrameLayout
                    android:id="@+id/layoutTableID"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintEnd_toStartOf="@id/dingingLayout"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvSelectTable"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/select_table"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_24ssp"
                        tools:text="A001" />
                </FrameLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/dingingLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:elevation="0dp"
                    app:cardBackgroundColor="@color/paid_backgroud_color"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="0dp"
                    app:layout_constraintBottom_toBottomOf="@id/layoutTableID"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@id/layoutTableID"
                    app:layout_constraintTop_toTopOf="@id/layoutTableID">

                    <TextView
                        android:id="@+id/tvMenuDiningStyle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical|end"
                        android:maxLines="1"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="5dp"
                        android:textColor="@color/paid_text_color"
                        android:textSize="@dimen/_12ssp"
                        android:textStyle="bold"
                        tools:text="堂食" />
                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/background_white_top_radius_12dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/dingingLayout">

                    <LinearLayout
                        android:id="@+id/layoutMainOrdered"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:paddingHorizontal="5dp"
                        android:visibility="visible">

                        <LinearLayout
                            android:id="@+id/layoutHeader"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:orientation="horizontal"
                            android:paddingHorizontal="10dp">

                            <TextView
                                android:id="@+id/tvDeleteAll"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="2dp"
                                android:layout_weight="1.8"
                                android:drawablePadding="3dp"
                                android:text="@string/items"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/menuQuantity"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="2dp"
                                android:layout_weight="1"
                                android:gravity="center_horizontal"
                                android:text="@string/quantity"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/menuAmount"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:text="@string/amount"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />
                        </LinearLayout>

                        <View
                            android:id="@+id/vTopLine"
                            style="@style/commonDividerStyle" />
                        <!--            <include-->
                        <!--                android:id="@+id/menuTest"-->
                        <!--                layout="@layout/selected_menu_item"/>-->
                        <!--            <include-->
                        <!--                android:id="@+id/menuTest2"-->
                        <!--                layout="@layout/selected_menu_item"/>-->

                        <androidx.cardview.widget.CardView
                            android:id="@+id/layoutNewOrderTitle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="5dp"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/background_session"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="0dp"
                            app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo"
                            app:strokeWidth="0dp"
                            tools:visibility="visible">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/tvOrderMoreCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingHorizontal="5dp"
                                    android:paddingVertical="5dp"
                                    android:text="@string/new_order"
                                    android:textColor="@color/primaryColor"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tvNewOrderTotalPrice"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="end"
                                    android:paddingHorizontal="5dp"
                                    android:paddingVertical="5dp"
                                    android:text="$0.00"
                                    android:textColor="@color/black"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                                <ImageView
                                    android:id="@+id/arrowNewOrder"
                                    android:layout_width="12dp"
                                    android:layout_height="12dp"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="10dp"
                                    android:rotation="180"
                                    android:src="@drawable/ic_dropdown"
                                    android:visibility="visible"
                                    app:tint="@color/primaryColor"
                                    tools:ignore="ContentDescription" />
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <FrameLayout
                            android:id="@+id/flOrderedFood"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_weight="1">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerOrderedFood"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:clipToPadding="false"
                                android:overScrollMode="never"
                                android:scrollbars="none"
                                android:visibility="visible"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:itemCount="2"
                                tools:listitem="@layout/selected_menu_item" />

                            <include
                                android:id="@+id/layoutEmpty"
                                layout="@layout/layout_empty_data"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </FrameLayout>


                        <androidx.cardview.widget.CardView
                            android:id="@+id/layoutOrderMore"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="5dp"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/background_session"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="0dp"
                            app:strokeWidth="0dp"
                            tools:visibility="visible">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/tvPreviousOrderCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingHorizontal="5dp"
                                    android:paddingVertical="5dp"
                                    android:text="@string/old_order_items"
                                    android:textColor="@color/primaryColor"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tvPricePreviousOrder"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="end"
                                    android:paddingHorizontal="5dp"
                                    android:paddingVertical="5dp"
                                    android:text="$0.00"
                                    android:textColor="@color/black"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                                <ImageView
                                    android:id="@+id/arrowOldOrder"
                                    android:layout_width="12dp"
                                    android:layout_height="12dp"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="10dp"
                                    android:src="@drawable/ic_dropdown"
                                    android:visibility="visible"
                                    app:tint="@color/primaryColor"
                                    tools:ignore="ContentDescription" />
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerPreviousOrderedFood"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginBottom="10dp"
                            android:layout_weight="1"
                            android:clipToPadding="false"
                            android:overScrollMode="never"
                            android:scrollbars="none"
                            android:visibility="gone"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="2"
                            tools:listitem="@layout/selected_menu_item"
                            tools:visibility="gone" />


                    </LinearLayout>

                    <View style="@style/commonDividerStyle" />

                    <LinearLayout
                        android:id="@+id/menuLayoutTotal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingHorizontal="10dp"
                        android:paddingTop="17dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">


                        <!--                        <LinearLayout-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginBottom="5dp"-->
                        <!--                            android:gravity="center_vertical">-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/menuSubtotal"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="match_parent"-->
                        <!--                                android:layout_marginEnd="15dp"-->
                        <!--                                android:gravity="center_vertical"-->
                        <!--                                android:text="@string/subtotal"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp" />-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/tvMenuSubtotal"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="0dp"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_weight="1"-->
                        <!--                                android:gravity="end"-->
                        <!--                                android:maxLines="1"-->
                        <!--                                android:textColor="@color/black"-->
                        <!--                                android:textSize="@dimen/_12ssp"-->
                        <!--                                android:textStyle="bold"-->
                        <!--                                tools:text="$99.99" />-->
                        <!--                        </LinearLayout>-->


                        <!--                        <LinearLayout-->
                        <!--                            android:id="@+id/llMenuPackPrice"-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginBottom="5dp"-->
                        <!--                            android:gravity="center_vertical">-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/menuPackingPrice"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="match_parent"-->
                        <!--                                android:layout_marginEnd="4dp"-->
                        <!--                                android:gravity="center_vertical"-->
                        <!--                                android:text="@string/packing_price"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp" />-->


                        <!--                            <TextView-->
                        <!--                                android:id="@+id/tvMenuPackingAmount"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="0dp"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_weight="1"-->
                        <!--                                android:gravity="end"-->
                        <!--                                android:maxLines="1"-->
                        <!--                                android:textColor="@color/black"-->
                        <!--                                android:textSize="@dimen/_12ssp"-->
                        <!--                                android:textStyle="bold"-->
                        <!--                                tools:text="$99.99" />-->
                        <!--                        </LinearLayout>-->


                        <!--                        <LinearLayout-->
                        <!--                            android:id="@+id/llMenuService"-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginBottom="5dp"-->
                        <!--                            android:gravity="top">-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/menuService"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_marginEnd="4dp"-->
                        <!--                                android:gravity="center_vertical"-->
                        <!--                                android:text="@string/service_fee"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp" />-->


                        <!--                            <LinearLayout-->
                        <!--                                android:layout_width="0dp"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_weight="1"-->
                        <!--                                android:gravity="end"-->
                        <!--                                android:orientation="vertical">-->

                        <!--                                <TextView-->
                        <!--                                    android:id="@+id/tvMenuService"-->
                        <!--                                    style="@style/FontLocalization"-->
                        <!--                                    android:layout_width="match_parent"-->
                        <!--                                    android:layout_height="wrap_content"-->
                        <!--                                    android:gravity="end"-->
                        <!--                                    android:maxLines="1"-->
                        <!--                                    android:textColor="@color/black"-->
                        <!--                                    android:textSize="@dimen/_12ssp"-->
                        <!--                                    android:textStyle="bold"-->
                        <!--                                    tools:text="$99.99" />-->

                        <!--                                &lt;!&ndash;                                <TextView&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:id="@+id/tvMenuVipService"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    style="@style/FontLocalization"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:layout_width="wrap_content"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:layout_height="wrap_content"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:layout_marginStart="2dp"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:drawableStart="@drawable/icon_vip"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:drawablePadding="2dp"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:textColor="@color/member_price_color"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:textSize="@dimen/_12ssp"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:textStyle="bold"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    android:visibility="gone"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    tools:text="$0.00"&ndash;&gt;-->
                        <!--                                &lt;!&ndash;                                    tools:visibility="visible" />&ndash;&gt;-->
                        <!--                            </LinearLayout>-->
                        <!--                        </LinearLayout>-->

                        <!--                        <LinearLayout-->
                        <!--                            android:id="@+id/llMenuVat"-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginBottom="5dp"-->
                        <!--                            android:gravity="center_vertical">-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/menuVat"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="match_parent"-->
                        <!--                                android:layout_marginEnd="15dp"-->
                        <!--                                android:gravity="center_vertical"-->
                        <!--                                android:text="@string/vat"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp" />-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/tvMenuVat"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="0dp"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_weight="1"-->
                        <!--                                android:gravity="end"-->
                        <!--                                android:maxLines="1"-->
                        <!--                                android:textColor="@color/black"-->
                        <!--                                android:textSize="@dimen/_12ssp"-->
                        <!--                                android:textStyle="bold"-->
                        <!--                                tools:text="$99.99" />-->
                        <!--                        </LinearLayout>-->

                        <!--                        <LinearLayout-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginBottom="5dp"-->
                        <!--                            android:gravity="center_vertical">-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/menuCoupon"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_marginEnd="15dp"-->
                        <!--                                android:gravity="center_vertical"-->
                        <!--                                android:text="@string/coupon"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp" />-->

                        <!--                            <LinearLayout-->
                        <!--                                android:id="@+id/llMenuCouponContent"-->
                        <!--                                android:layout_width="0dp"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_weight="1"-->
                        <!--                                android:gravity="end">-->

                        <!--                                <TextView-->
                        <!--                                    android:id="@+id/tvMenuViewCouponGiftGood"-->
                        <!--                                    style="@style/FontLocalization"-->
                        <!--                                    android:layout_width="wrap_content"-->
                        <!--                                    android:layout_height="wrap_content"-->
                        <!--                                    android:layout_marginStart="10dp"-->
                        <!--                                    android:gravity="end"-->
                        <!--                                    android:maxLines="1"-->
                        <!--                                    android:text="@string/view_give_away_goods"-->
                        <!--                                    android:textColor="@color/primaryColor"-->
                        <!--                                    android:textSize="@dimen/_12ssp"-->
                        <!--                                    android:textStyle="bold"-->
                        <!--                                    android:visibility="gone"-->
                        <!--                                    tools:visibility="visible" />-->

                        <!--                                <TextView-->
                        <!--                                    android:id="@+id/tvMenuCoupon"-->
                        <!--                                    style="@style/FontLocalization"-->
                        <!--                                    android:layout_width="wrap_content"-->
                        <!--                                    android:layout_height="wrap_content"-->
                        <!--                                    android:layout_marginStart="10dp"-->
                        <!--                                    android:gravity="end"-->
                        <!--                                    android:maxLines="1"-->
                        <!--                                    android:textColor="@color/black"-->
                        <!--                                    android:textSize="@dimen/_12ssp"-->
                        <!--                                    android:textStyle="bold"-->
                        <!--                                    android:visibility="visible"-->
                        <!--                                    tools:text="-$99.99"-->
                        <!--                                    tools:visibility="visible" />-->


                        <!--                                <ImageView-->
                        <!--                                    android:id="@+id/iconMenuCouponArrow"-->
                        <!--                                    android:layout_width="wrap_content"-->
                        <!--                                    android:layout_height="match_parent"-->
                        <!--                                    android:layout_marginStart="5dp"-->
                        <!--                                    android:src="@drawable/icon_coupon_arrow_right" />-->
                        <!--                            </LinearLayout>-->


                        <!--                        </LinearLayout>-->


                        <!--                        <LinearLayout-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginBottom="5dp"-->
                        <!--                            android:gravity="center_vertical"-->
                        <!--                            android:visibility="gone"-->
                        <!--                            tools:visibility="visible">-->

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/discounted"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="match_parent"-->
                        <!--                                android:layout_marginEnd="15dp"-->
                        <!--                                android:gravity="center_vertical"-->
                        <!--                                android:text="@string/discounted"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp" />-->

                        <!--                            <TextView-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="0dp"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_weight="1"-->
                        <!--                                android:gravity="end"-->
                        <!--                                android:maxLines="1"-->
                        <!--                                android:textColor="@color/black"-->
                        <!--                                android:textSize="@dimen/_12ssp"-->
                        <!--                                android:textStyle="bold"-->
                        <!--                                tools:text="-$99.99" />-->
                        <!--                        </LinearLayout>-->


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/menuTotalPrice"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/total_price"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_16ssp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end|center_vertical"
                                android:orientation="vertical">


                                <TextView
                                    android:id="@+id/tvMenuTotalPrice"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textSize="28sp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="$999999.99" />

                                <TextView
                                    android:id="@+id/tvMenuTotalKhrPrice"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textSize="28sp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="៛999999.99" />

                                <TextView
                                    android:id="@+id/tvMenuVipPrice"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="2dp"
                                    android:drawableStart="@drawable/icon_vip"
                                    android:drawablePadding="2dp"
                                    android:textColor="@color/member_price_color"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:visibility="gone"
                                    tools:text="$0.00"
                                    tools:visibility="visible" />

                                <!--                                <TextView-->
                                <!--                                    android:id="@+id/tvMenuOriginalPrice"-->
                                <!--                                    style="@style/FontLocalization"-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:layout_marginStart="2dp"-->
                                <!--                                    android:drawablePadding="2dp"-->
                                <!--                                    android:foreground="@drawable/strike_price"-->
                                <!--                                    android:textColor="@color/black60"-->
                                <!--                                    android:textSize="14sp"-->
                                <!--                                    android:textStyle="bold"-->
                                <!--                                    android:visibility="gone"-->
                                <!--                                    tools:text="$0.00"-->
                                <!--                                    tools:visibility="visible" />-->

                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="6dp"
                android:background="@drawable/background_e7e7e7_top_left_radius_10dp"
                android:paddingHorizontal="5dp"
                android:paddingTop="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/layoutOrder"
                app:layout_constraintTop_toTopOf="parent">

                <!--                <LinearLayout-->
                <!--                    android:id="@+id/layoutMenuCategories"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="match_parent"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:background="@drawable/background_dialog"-->
                <!--                    android:orientation="vertical"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toTopOf="parent"-->
                <!--                    app:layout_constraintWidth_percent="0.15">-->

                <!--                    <androidx.recyclerview.widget.RecyclerView-->
                <!--                        android:id="@+id/recyclerViewCategories"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="match_parent"-->
                <!--                        android:overScrollMode="never"-->
                <!--                        android:scrollbars="none"-->
                <!--                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"-->
                <!--                        tools:listitem="5" />-->
                <!--                </LinearLayout>-->


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewMenu"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:clipToPadding="true"
                    android:overScrollMode="never"
                    android:paddingBottom="11dp"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:spanCount="4"
                    tools:listitem="@layout/second_menu_item" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clOrderedInfo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/orderedToolbar"
            android:layout_width="0dp"
            android:layout_height="70dp"
            android:background="@color/mainWhite"
            android:backgroundTint="@color/mainWhite"
            android:gravity="center_vertical"
            android:paddingHorizontal="30dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgOrderedLogo"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_logo" />

            <TextView
                android:id="@+id/tvOrderedStoreName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="20sp"
                tools:text="@string/brand_name" />

        </LinearLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/orderedToolbar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutMenu"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@drawable/background_e7e7e7_top_right_radius_10dp"
                android:padding="10dp"
                app:layout_constraintEnd_toStartOf="@id/layoutResumeOrder"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvTableID"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@id/cardStatus"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="A001" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:elevation="0dp"
                    app:cardBackgroundColor="@color/paid_backgroud_color"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="0dp"
                    app:layout_constraintBottom_toBottomOf="@id/tvTableID"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tvTableID"
                    app:layout_constraintTop_toTopOf="@id/tvTableID">

                    <TextView
                        android:id="@+id/tvOrderStyle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical|end"
                        android:maxLines="1"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="5dp"
                        android:textColor="@color/paid_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        tools:text="堂食" />
                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/background_white_radius_12dp"
                    android:orientation="vertical"
                    android:padding="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTableID">

                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginTop="5dp"-->
                    <!--                        android:orientation="horizontal">-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/items"-->
                    <!--                            android:layout_width="0dp"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_gravity="end"-->
                    <!--                            android:layout_marginTop="5dp"-->
                    <!--                            android:layout_marginEnd="10dp"-->
                    <!--                            android:layout_weight="2"-->
                    <!--                            android:text="@string/items"-->
                    <!--                            android:textColor="@color/black60"-->
                    <!--                            android:textSize="14sp" />-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/quantity"-->
                    <!--                            android:layout_width="0dp"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_gravity="end"-->
                    <!--                            android:layout_marginTop="5dp"-->
                    <!--                            android:layout_marginEnd="10dp"-->
                    <!--                            android:layout_weight="1.2"-->
                    <!--                            android:gravity="center_horizontal"-->
                    <!--                            android:text="@string/quantity"-->
                    <!--                            android:textColor="@color/black60"-->
                    <!--                            android:textSize="14sp" />-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/amount"-->
                    <!--                            android:layout_width="0dp"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_gravity="end"-->
                    <!--                            android:layout_marginTop="5dp"-->
                    <!--                            android:layout_marginEnd="10dp"-->
                    <!--                            android:layout_weight="1"-->
                    <!--                            android:gravity="center_horizontal"-->
                    <!--                            android:text="@string/amount"-->
                    <!--                            android:textColor="@color/black60"-->
                    <!--                            android:textSize="14sp" />-->
                    <!--                    </LinearLayout>-->

                    <!--                    <View style="@style/commonDividerStyle" />-->

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/orderedInfoRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_second_ordered_food" />

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutResumeOrder"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="6dp"
                android:background="@drawable/background_e7e7e7_top_left_radius_10dp"
                android:padding="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/layoutMenu"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.4">

                <LinearLayout
                    android:id="@+id/layoutDetailInfo"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@drawable/background_white_radius_12dp"
                    android:orientation="vertical"
                    android:padding="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">


                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:orientation="vertical">-->

                    <!--                        <LinearLayout-->
                    <!--                            android:layout_width="match_parent"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:orientation="vertical">-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llCustomerTitle"-->
                    <!--                                android:layout_width="wrap_content"-->
                    <!--                                android:layout_height="wrap_content">-->
                    <!--                                -->
                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvCustomerTitle"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_gravity="center_vertical"-->
                    <!--                                    android:text="@string/customer_info"-->
                    <!--                                    android:textColor="@color/color_00210f"-->
                    <!--                                    android:textSize="@dimen/_18ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:visibility="visible" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llCustomerName"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone"-->
                    <!--                                tools:visibility="visible">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/customerName"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/customer_name"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvCustomerName"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="林超正" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llCustomerPhone"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone"-->
                    <!--                                tools:visibility="visible">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/phoneNumber"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/phone_number"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvCustomerPhone"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="15006099788" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:layout_width="wrap_content"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="24dp">-->

                    <!--                                &lt;!&ndash;                                <ImageView&ndash;&gt;-->
                    <!--                                &lt;!&ndash;                                    android:layout_width="3dp"&ndash;&gt;-->
                    <!--                                &lt;!&ndash;                                    android:layout_height="14dp"&ndash;&gt;-->
                    <!--                                &lt;!&ndash;                                    android:layout_gravity="center_vertical"&ndash;&gt;-->
                    <!--                                &lt;!&ndash;                                    android:src="@drawable/shape_second_ordered_arc" />&ndash;&gt;-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/orderInfo"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:text="@string/order_info"-->
                    <!--                                    android:textColor="@color/color_00210f"-->
                    <!--                                    android:textSize="@dimen/_18ssp"-->
                    <!--                                    android:textStyle="bold" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llPickUpNo"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/pickUpNo"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/print_title_pick_up_no"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvPickUpNo"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="20241234567890" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/orderId"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/order_id"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvOrderNo"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="20241234567890" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@id/llInvoiceNumber"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/invoiceNumber"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/print_title_invoiceNumber"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvInvoiceNumber"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="20241234567890" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/orderedTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/ordered_time"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvOrderTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="2024/04/02 12:00:00" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/orderType"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/order_type"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvOrderType"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="Dine In" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llPeople"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/people"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="match_parent"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/people"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvCustomerNum"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="5" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/orderBy"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/order_by"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvOrderBy"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="Kiosk" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llPaymentMethod"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/paymentMethod"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/payment_method"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvPaymentMethod"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="U-Pay" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llDiningTime"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/diningTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/ordered_dining_time"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvDiningTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="2024/04/02 12:00:00" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llCancelTime"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone"-->
                    <!--                                tools:visibility="visible">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/cancelTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/cancel_time"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvCancelTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="2024/04/02 12:00:00" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llCancelReason"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone"-->
                    <!--                                tools:visibility="visible">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/cancelReasonTitle"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/cancel_reason"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <com.metathought.food_order.casheir.ui.widget.ExpandTextView-->
                    <!--                                    android:id="@+id/tvCancelReason"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    android:visibility="visible" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llPaymentTime"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone"-->
                    <!--                                tools:visibility="visible">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/paymentTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/payment_time"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvPaymentTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="2024/04/02 12:00:00" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:id="@+id/llRefundTime"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:visibility="gone">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/refundTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/refund_time"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvRefundTime"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="2024/04/02 12:00:00" />-->
                    <!--                            </LinearLayout>-->

                    <!--                            <LinearLayout-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="wrap_content"-->
                    <!--                                android:layout_marginTop="5dp"-->
                    <!--                                android:layout_weight="1">-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/remark"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="wrap_content"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_marginEnd="15dp"-->
                    <!--                                    android:text="@string/remark"-->
                    <!--                                    android:textColor="@color/black60"-->
                    <!--                                    android:textSize="@dimen/_12ssp" />-->

                    <!--                                <TextView-->
                    <!--                                    android:id="@+id/tvOrderRemark"-->
                    <!--                                    style="@style/FontLocalization"-->
                    <!--                                    android:layout_width="0dp"-->
                    <!--                                    android:layout_height="wrap_content"-->
                    <!--                                    android:layout_weight="1"-->
                    <!--                                    android:gravity="end"-->
                    <!--                                    android:maxLines="2"-->
                    <!--                                    android:textColor="@color/black"-->
                    <!--                                    android:textSize="@dimen/_12ssp"-->
                    <!--                                    android:textStyle="bold"-->
                    <!--                                    tools:text="多加点醋，不吃辣，葱多放点可以吗？备注信息比较多的时候自动换行" />-->
                    <!--                            </LinearLayout>-->
                    <!--                        </LinearLayout>-->


                    <!--                    </LinearLayout>-->

                    <!--                    <View style="@style/commonDividerStyle" />-->

                    <LinearLayout
                        android:id="@+id/layoutTotal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/subtotal"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/subtotal"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvSubtotal"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llDiscountActivity"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tvDiscountActivityTitle"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="4dp"
                                android:gravity="center_vertical"
                                android:text="@string/discount_activity"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />


                            <TextView
                                android:id="@+id/tvDiscountActivityAmount"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPackPrice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/packingPrice"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="4dp"
                                android:gravity="center_vertical"
                                android:text="@string/packing_price"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />


                            <TextView
                                android:id="@+id/tvPackingAmount"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llServiceFee"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="top"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/serviceFee"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="4dp"
                                android:gravity="center_vertical"
                                android:text="@string/service_fee"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvServiceFee"
                                    style="@style/FontLocalization"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_12ssp"
                                    android:textStyle="bold"
                                    tools:text="$99.99" />

                                <!--                                <TextView-->
                                <!--                                    android:id="@+id/tvVipServiceFee"-->
                                <!--                                    style="@style/FontLocalization"-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:layout_marginStart="2dp"-->
                                <!--                                    android:layout_marginTop="4dp"-->
                                <!--                                    android:drawableStart="@drawable/icon_vip"-->
                                <!--                                    android:drawablePadding="2dp"-->
                                <!--                                    android:textColor="@color/member_price_color"-->
                                <!--                                    android:textSize="@dimen/_12ssp"-->
                                <!--                                    android:textStyle="bold"-->
                                <!--                                    android:visibility="gone"-->
                                <!--                                    tools:text="$0.00"-->
                                <!--                                    tools:visibility="visible" />-->
                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llCoupon"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/coupon"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/coupon"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <LinearLayout
                                android:id="@+id/llCouponContent"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end">

                                <!--                            <TextView-->
                                <!--                                android:id="@+id/tvVipCoupon"-->
                                <!--                                style="@style/FontLocalization"-->
                                <!--                                android:layout_width="wrap_content"-->
                                <!--                                android:layout_height="wrap_content"-->
                                <!--                                android:drawableStart="@drawable/icon_vip"-->
                                <!--                                android:drawablePadding="2dp"-->
                                <!--                                android:textColor="@color/member_price_color"-->
                                <!--                                android:textSize="14sp"-->
                                <!--                                android:textStyle="bold"-->
                                <!--                                android:visibility="gone"-->
                                <!--                                tools:text="$0.00"-->
                                <!--                                tools:visibility="visible" />-->

                                <TextView
                                    android:id="@+id/tvViewCouponGiftGood"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="10dp"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:text="@string/view_give_away_goods"
                                    android:textColor="@color/primaryColor"
                                    android:textSize="@dimen/_12ssp"
                                    android:textStyle="bold"
                                    android:visibility="gone"
                                    tools:visibility="visible" />

                                <TextView
                                    android:id="@+id/tvCoupon"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="10dp"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_12ssp"
                                    android:textStyle="bold"
                                    tools:text="-$99.99"
                                    tools:visibility="visible" />

                                <!--                            <TextView-->
                                <!--                                android:id="@+id/tvNoCoupon"-->
                                <!--                                style="@style/FontLocalization"-->
                                <!--                                android:layout_width="wrap_content"-->
                                <!--                                android:layout_height="wrap_content"-->
                                <!--                                android:layout_marginStart="10dp"-->
                                <!--                                android:gravity="end"-->
                                <!--                                android:maxLines="1"-->
                                <!--                                android:text="@string/hint_no_can_use_coupon"-->
                                <!--                                android:textColor="@color/black40"-->
                                <!--                                android:textSize="@dimen/_14ssp"-->
                                <!--                                android:textStyle="bold"-->
                                <!--                                android:visibility="gone"-->
                                <!--                                tools:visibility="visible" />-->

                                <!--                                <ImageView-->
                                <!--                                    android:id="@+id/iconCouponArrow"-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="match_parent"-->
                                <!--                                    android:layout_marginStart="5dp"-->
                                <!--                                    android:src="@drawable/icon_coupon_arrow_right" />-->
                            </LinearLayout>


                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llDiscount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tvDiscountTitle"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:text="@string/discounts"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvDiscount"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawablePadding="4dp"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llDiscountAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tvDiscountAmountTitle"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:text="@string/amount_of_reduction_usd"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvDiscountAmount"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawablePadding="4dp"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llVat"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/vat"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/vat"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvVat"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCommission"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="9dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/commission"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="4dp"
                                android:gravity="center_vertical"
                                android:text="@string/commission"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvCommissionPrice"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llTotalPrice2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/total2"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="top"
                                android:text="@string/total"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalPrice2"
                                    style="@style/FontLocalization"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_12ssp"
                                    android:textStyle="bold"
                                    tools:text="$99.99" />

                                <TextView
                                    android:id="@+id/tvTotalKhrPrice2"
                                    style="@style/FontLocalization"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_12ssp"
                                    android:textStyle="bold"
                                    tools:text="៛0" />
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPartialRefundAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/partialRefundAmount"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/partial_refund_amount"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvPartialRefundAmount"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/main_red"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="-$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPartialRefundPackFee"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/refund_pack_fee"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvPartialRefundPackFee"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/main_red"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="-$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPartialRefundServiceFee"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="9dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/partialRefundServiceFee"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/refund_service_fee"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvPartialRefundServiceFee"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/main_red"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="-$99.99" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llVatRefundAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/vatRefund"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/vat_refund"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvVatRefundAmount"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/main_red"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="-$99.99" />
                        </LinearLayout>
                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/llTotalPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/totalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="top"
                            android:text="@string/total_price"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvTotalPrice"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="$0" />

                            <TextView
                                android:id="@+id/tvTotalKhrPrice"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                tools:text="៛0" />

                            <TextView
                                android:id="@+id/tvVipPrice"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="2dp"
                                android:layout_marginTop="4dp"
                                android:drawableStart="@drawable/icon_vip"
                                android:drawablePadding="2dp"
                                android:textColor="@color/member_price_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:text="$0.00"
                                tools:visibility="visible" />

                            <!--                            <TextView-->
                            <!--                                android:id="@+id/tvOriginalPrice"-->
                            <!--                                style="@style/FontLocalization"-->
                            <!--                                android:layout_width="wrap_content"-->
                            <!--                                android:layout_height="wrap_content"-->
                            <!--                                android:layout_marginTop="4dp"-->
                            <!--                                android:drawablePadding="2dp"-->
                            <!--                                android:foreground="@drawable/strike_price"-->
                            <!--                                android:textColor="@color/black60"-->
                            <!--                                android:textSize="14sp"-->
                            <!--                                android:textStyle="bold"-->
                            <!--                                android:visibility="gone"-->
                            <!--                                tools:text="$0.00"-->
                            <!--                                tools:visibility="visible" />-->

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llActualReceiveAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/actualReceivedAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/actual_received_amount"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp" />

                        <TextView
                            android:id="@+id/tvActualReceiveAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llRefundAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/refundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_amount"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp" />

                        <TextView
                            android:id="@+id/tvRefundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clQRPayment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/second_payment_bg"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clPaymentLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:visibility="gone">

            <LinearLayout
                android:id="@+id/layoutQR"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginVertical="15dp"
                android:layout_marginStart="15dp"
                android:background="@drawable/rectangle"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:padding="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.45">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:background="@drawable/top_khqr"
                    android:padding="6dp"
                    android:src="@drawable/ic_khqr_logo" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="110dp"
                    android:background="@drawable/info_khqr"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingLeft="30dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvScanQRName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="40dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="25sp"
                        android:visibility="visible"
                        app:autoSizeTextType="uniform"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:text="KUNTHEA SOT" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvScanQRAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:lines="1"
                        android:textColor="@color/black"
                        android:textSize="50sp"
                        android:textStyle="bold"
                        app:autoSizeTextType="uniform"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:text="$673.57" />


                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bottom_khqr"
                    android:padding="30dp">

                    <ImageView
                        android:id="@+id/imgQR"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_qr_download" />
                </RelativeLayout>

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvInfo"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="15dp"
                android:drawableStart="@drawable/icon_second_screen_scan"
                android:drawablePadding="15dp"
                android:lines="1"
                android:text="@string/scan_to_pay"
                android:textColor="@color/black"
                android:textSize="36sp"
                android:textStyle="bold"
                app:autoSizeTextType="uniform"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/llTotalCoast"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/layoutQR"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/llTotalCoast"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="15dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/layoutQR"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTotalCoast"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:lines="1"
                    android:text="@string/total_cost"
                    android:textColor="@color/color_616263"
                    android:textSize="30sp"
                    android:textStyle="bold"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTotalCoastAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center"
                    android:lines="1"
                    android:textColor="@color/primaryColor"
                    android:textSize="80sp"
                    android:textStyle="bold"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true"
                    tools:text="1000000.00" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/layoutQR"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/layoutQR">

                <TextView
                    android:id="@+id/tvAcceptedPayment"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/accepted_payment"
                    android:textColor="@color/color_616263"
                    android:textStyle="italic" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:src="@mipmap/icon_second_screen_khqr" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:src="@mipmap/icon_second_screen_alipay" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaymentDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/layoutQR"
                app:layout_constraintTop_toBottomOf="@id/llTotalCoast">

                <!--                <androidx.appcompat.widget.AppCompatTextView-->
                <!--                    android:id="@+id/tvDurationTitle"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_gravity="center_vertical"-->
                <!--                    android:lines="1"-->
                <!--                    android:paddingVertical="10dp"-->
                <!--                    android:text="@string/expired_in"-->
                <!--                    android:textColor="@color/color_616263"-->
                <!--                    android:textSize="30sp"-->
                <!--                    android:textStyle="bold"-->
                <!--                    app:autoSizeTextType="uniform"-->
                <!--                    app:layout_constrainedWidth="true"-->
                <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
                <!--                    app:layout_constraintEnd_toEndOf="parent"-->
                <!--                    app:layout_constraintStart_toEndOf="@+id/layoutQR" />-->

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvDuration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:lines="1"
                    android:paddingVertical="10dp"
                    android:textColor="@color/khqr_red_color"
                    android:textSize="30sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/layoutQR"
                    tools:text="04:12" />
            </LinearLayout>

            <!--            <androidx.appcompat.widget.AppCompatTextView-->
            <!--                android:id="@+id/tvDuration"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="center_vertical"-->
            <!--                android:layout_marginBottom="125dp"-->
            <!--                android:lines="1"-->
            <!--                android:paddingHorizontal="30dp"-->
            <!--                android:paddingVertical="10dp"-->
            <!--                android:textColor="@color/khqr_red_color"-->
            <!--                android:textSize="30sp"-->
            <!--                android:textStyle="bold"-->
            <!--                app:autoSizeTextType="uniform"-->
            <!--                app:layout_constrainedWidth="true"-->
            <!--                app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintStart_toEndOf="@+id/layoutQR"-->
            <!--                tools:text="04:11" />-->

            <!--            <ImageView-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="0dp"-->
            <!--                android:layout_centerInParent="true"-->
            <!--                android:adjustViewBounds="true"-->
            <!--                android:padding="20dp"-->
            <!--                android:src="@mipmap/ic_second_payment_icon"-->
            <!--                app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintStart_toEndOf="@+id/layoutQR"-->
            <!--                app:layout_constraintTop_toBottomOf="@id/tvDuration" />-->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clPaymentResultLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingHorizontal="60dp"
            android:paddingVertical="35dp"
            tools:visibility="visible">

            <FrameLayout
                android:id="@+id/printTicketLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.55">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:background="@drawable/shape_payment_result_rounded_rectangle" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/shape_payment_result_rounded_rectangle_white_bg"
                    android:orientation="vertical"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="25dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tableName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/table_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp" />

                        <TextView
                            android:id="@+id/tvTableName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="A001" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:id="@+id/numberProducts"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/number_products"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp" />

                        <TextView
                            android:id="@+id/tvFoodCount"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="10" />
                    </LinearLayout>

                    <include layout="@layout/dot_divider" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/qrSubtotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/subtotal"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvQrSubtotal"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$100" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentResultCouponActivity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp">

                        <TextView
                            android:id="@+id/couponActivity"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvCouponActivityPrice"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$2" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentResultPackageFee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp">

                        <TextView
                            android:id="@+id/packageFee"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/packing_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvPackageFee"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$2" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llQrService"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:visibility="visible"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/qrService"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvQrService"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$2" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentCoupon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvPaymentCouponTitle"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:text="@string/coupon"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvPaymentCoupon"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentDiscount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvPaymentDiscountTitle"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:text="@string/discounts"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvPaymentDiscount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentDiscountAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvPaymentDiscountAmountTitle"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:maxLines="2"
                            android:text="@string/amount_of_reduction_usd"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvPaymentDiscountAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llQrVat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/qrVat"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp" />

                        <TextView
                            android:id="@+id/tvQrVat"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_default_sp"
                            tools:text="$2" />
                    </LinearLayout>
                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginTop="20dp"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        tools:visibility="visible">-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/discount"-->
                    <!--                            android:layout_width="wrap_content"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:text="@string/discount_reduction"-->
                    <!--                            android:textColor="@color/black"-->
                    <!--                            android:textSize="@dimen/_default_sp" />-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/tvDiscountReduction"-->
                    <!--                            android:layout_width="0dp"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_weight="1"-->
                    <!--                            android:gravity="end"-->
                    <!--                            android:textColor="@color/black"-->
                    <!--                            android:textSize="@dimen/_default_sp"-->
                    <!--                            tools:text="$2" />-->
                    <!--                    </LinearLayout>-->

                    <include layout="@layout/dot_divider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/receivable"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="top"
                            android:text="@string/total_price"
                            android:textColor="@color/paid_text_color"
                            android:textSize="@dimen/_18ssp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvReceivable"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/paid_text_color"
                                android:textSize="@dimen/_default_sp"
                                tools:text="$92" />

                            <TextView
                                android:id="@+id/tvKhrReceivable"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/paid_text_color"
                                android:textSize="@dimen/_default_sp"
                                tools:text="៛0" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>


            </FrameLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/printTicketLayout"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_second_payment_done" />

                <TextView
                    android:id="@+id/paymentResult"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_horizontal"
                    android:text="@string/payment_successfully"
                    android:textColor="@color/paid_text_color"
                    android:textSize="40sp"
                    android:textStyle="bold" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/qrTotalPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:text="$"
                            android:textColor="@color/black"
                            android:textSize="30sp"
                            android:textStyle="bold"
                            app:layout_constraintBaseline_toBaselineOf="@id/tvQrTotalPrice"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvQrTotalPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="50sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/qrTotalPrice"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="92.00" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clPaymentTopUpResultLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingHorizontal="60dp"
            android:paddingVertical="35dp"
            tools:visibility="gone">


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_second_payment_done" />

                <TextView
                    android:id="@+id/paymentTopUpResult"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_horizontal"
                    android:text="@string/top_up_successfully"
                    android:textColor="@color/paid_text_color"
                    android:textSize="40sp"
                    android:textStyle="bold" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginTop="80dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvTotalTopUpCoast"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:lines="1"
                        android:text="@string/total_cost"
                        android:textColor="@color/color_616263"
                        android:textSize="30sp"
                        android:textStyle="bold"
                        app:autoSizeTextType="uniform"
                        app:layout_constrainedWidth="true" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/qrTotalTopUpPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:text="$"
                            android:textColor="@color/black"
                            android:textSize="30sp"
                            android:textStyle="bold"
                            app:layout_constraintBaseline_toBaselineOf="@id/tvQrTotalTopUpPrice"
                            app:layout_constraintStart_toStartOf="parent" />

                        <TextView
                            android:id="@+id/tvQrTotalTopUpPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="50sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/qrTotalTopUpPrice"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="92.00" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTopup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/second_payment_bg"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="50dp"
            android:background="@drawable/background_white_radius_20"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:id="@+id/vCustomerInfo"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="-16dp"
                android:background="@drawable/background_f3f5f8_radius_12"
                app:layout_constraintBottom_toBottomOf="@+id/tvBalanceLabel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvNameLabel"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/customer_nickname"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toTopOf="@+id/vCustomerInfo" />

            <TextView
                android:id="@+id/tvNickname"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="16dp"
                android:gravity="end"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvNameLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                app:layout_constraintStart_toEndOf="@+id/tvNameLabel"
                tools:text="1231231212312123123121123121123123121231212312312112312112312312123121231231211231211231231212312123123121123121" />


            <TextView
                android:id="@+id/tvPhoneLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/customer_account"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvPhoneNumber"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvNameLabel" />

            <TextView
                android:id="@+id/tvPhoneNumber"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvPhoneLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                tools:text="088 89 90 098" />


            <TextView
                android:id="@+id/tvBalanceLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/customer_balance"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvBalance"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvPhoneLabel" />

            <TextView
                android:id="@+id/tvBalance"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvBalanceLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                tools:text="$99.99" />


            <TextView
                android:id="@+id/tvTopupAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="32dp"
                android:text="@string/top_up_amount"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvTopupAmount"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/vCustomerInfo" />

            <TextView
                android:id="@+id/tvTopupAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:text="$--"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTopupAmountLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo" />

            <TextView
                android:id="@+id/tvAddGiftAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/additional_gift_amount"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvAddGiftAmount"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvTopupAmountLabel" />

            <TextView
                android:id="@+id/tvAddGiftAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvAddGiftAmountLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                tools:text="$300.00" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupAddGiftAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvAddGiftAmountLabel,tvAddGiftAmount"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvExtraCouponGiveawayLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/extra_coupon_giveaway"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvExtraCouponGiveaway"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvAddGiftAmountLabel" />

            <TextView
                android:id="@+id/tvExtraCouponGiveaway"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvExtraCouponGiveawayLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                tools:text="2张" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupExtraCouponGiveaway"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvExtraCouponGiveawayLabel,tvExtraCouponGiveaway"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/tvTotalRechargeAmountLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/total_recharge_amount"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvTotalRechargeAmount"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvExtraCouponGiveawayLabel" />

            <TextView
                android:id="@+id/tvTotalRechargeAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvTotalRechargeAmountLabel"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                tools:text="$300.00" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupTotalRechargeAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvTotalRechargeAmountLabel,tvTotalRechargeAmount"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvCouponLabel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/coupon"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toStartOf="@+id/tvCouponAmount"
                app:layout_constraintStart_toStartOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toBottomOf="@+id/tvTotalRechargeAmountLabel" />

            <TextView
                android:id="@+id/tvCouponAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintEnd_toEndOf="@+id/vCustomerInfo"
                app:layout_constraintTop_toTopOf="@+id/tvCouponLabel"
                tools:text="-$300.00" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupCouponAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tvCouponLabel,tvCouponAmount"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />


        <TextView
            android:id="@+id/tvTopupTips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="56dp"
            android:layout_marginEnd="100dp"
            android:gravity="center"
            android:text="@string/payable_amount"
            android:textColor="@color/color_616263"
            android:textSize="30sp"
            app:layout_constraintBottom_toTopOf="@+id/tvPayableAmount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />


        <TextView
            android:id="@+id/tvPayableAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="56dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="100dp"
            android:gravity="center"
            android:textColor="@color/primaryColor"
            android:textSize="50sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@+id/tvTopupTips"
            tools:text="$100.00" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--    <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--        android:id="@+id/clPaymentInfoLayout"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:background="@color/second_payment_bg"-->
    <!--        android:orientation="vertical"-->
    <!--        android:visibility="gone"-->
    <!--        tools:visibility="visible">-->

    <!--        <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:paddingHorizontal="60dp"-->
    <!--            android:paddingVertical="35dp"-->
    <!--            tools:visibility="visible">-->

    <!--            <FrameLayout-->
    <!--                android:id="@+id/flPaymentInfoLeftPart"-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                app:layout_constraintBottom_toBottomOf="parent"-->
    <!--                app:layout_constraintStart_toStartOf="parent"-->
    <!--                app:layout_constraintTop_toTopOf="parent"-->
    <!--                app:layout_constraintWidth_percent="0.55">-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="match_parent"-->
    <!--                    android:layout_height="match_parent"-->
    <!--                    android:layout_marginHorizontal="15dp"-->
    <!--                    android:layout_marginTop="15dp"-->
    <!--                    android:background="@drawable/background_white_radius_20"-->
    <!--                    android:orientation="vertical"-->
    <!--                    android:paddingHorizontal="16dp"-->
    <!--                    android:paddingVertical="24dp">-->

    <!--                    <LinearLayout-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:paddingHorizontal="16dp">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/paymentInfoSubtotal"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:text="@string/subtotal"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoSubtotal"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="end"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$100" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoCouponActivity"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:paddingHorizontal="16dp">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/paymentInfocouponActivity"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:text="@string/discount_activity"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoCouponActivityPrice"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="end"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$2" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoResultPackageFee"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="10dp"-->
    <!--                        android:paddingHorizontal="16dp">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/PaymentInfoPackageFee"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:text="@string/packing_price"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoPackageFee"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="end"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$2" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoService"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:paddingHorizontal="16dp"-->
    <!--                        android:visibility="visible"-->
    <!--                        tools:visibility="visible">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/paymentInfoService"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:text="@string/service_fee"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoService"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="end"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$2" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoCoupon"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:gravity="center_vertical"-->
    <!--                        android:paddingHorizontal="16dp"-->
    <!--                        android:visibility="gone"-->
    <!--                        tools:visibility="visible">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoCouponTitle"-->
    <!--                            style="@style/FontLocalization"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="match_parent"-->
    <!--                            android:layout_marginEnd="15dp"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="center_vertical"-->
    <!--                            android:text="@string/coupon"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoCoupon"-->
    <!--                            style="@style/FontLocalization"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:drawablePadding="4dp"-->
    <!--                            android:gravity="end"-->
    <!--                            android:maxLines="1"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$99.99" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoDiscount"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:gravity="center_vertical"-->
    <!--                        android:paddingHorizontal="16dp"-->
    <!--                        android:visibility="gone"-->
    <!--                        tools:visibility="visible">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoDiscountTitle"-->
    <!--                            style="@style/FontLocalization"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="match_parent"-->
    <!--                            android:layout_marginEnd="15dp"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="center_vertical"-->
    <!--                            android:text="@string/discounts"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoDiscount"-->
    <!--                            style="@style/FontLocalization"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:drawablePadding="4dp"-->
    <!--                            android:gravity="end"-->
    <!--                            android:maxLines="1"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$99.99" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoDiscountAmount"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:gravity="center_vertical"-->
    <!--                        android:paddingHorizontal="16dp"-->
    <!--                        android:visibility="gone"-->
    <!--                        tools:visibility="visible">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoDiscountAmountTitle"-->
    <!--                            style="@style/FontLocalization"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="match_parent"-->
    <!--                            android:layout_marginEnd="15dp"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="center_vertical"-->
    <!--                            android:maxLines="2"-->
    <!--                            android:text="@string/amount_of_reduction_usd"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoDiscountAmount"-->
    <!--                            style="@style/FontLocalization"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:drawablePadding="4dp"-->
    <!--                            android:gravity="end"-->
    <!--                            android:maxLines="1"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$99.99" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoVat"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:paddingHorizontal="16dp"-->
    <!--                        android:visibility="visible">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/paymentInfoVat"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:text="@string/vat"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoVat"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:gravity="end"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_default_sp"-->
    <!--                            tools:text="$2" />-->
    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="8dp"-->
    <!--                        android:paddingHorizontal="16dp">-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/paymentInfoReceivable"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:gravity="top"-->
    <!--                            android:text="@string/total_price"-->
    <!--                            android:textColor="@color/black"-->
    <!--                            android:textSize="@dimen/_18ssp" />-->

    <!--                        <LinearLayout-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:orientation="vertical">-->

    <!--                            <TextView-->
    <!--                                android:id="@+id/tvPaymentInfoReceivable"-->
    <!--                                android:layout_width="match_parent"-->
    <!--                                android:layout_height="wrap_content"-->
    <!--                                android:gravity="end"-->
    <!--                                android:textColor="@color/black"-->
    <!--                                android:textSize="@dimen/_default_sp"-->
    <!--                                tools:text="$92" />-->

    <!--                            <TextView-->
    <!--                                android:id="@+id/tvPaymentInfoKhrReceivable"-->
    <!--                                android:layout_width="match_parent"-->
    <!--                                android:layout_height="wrap_content"-->
    <!--                                android:gravity="end"-->
    <!--                                android:textColor="@color/paid_text_color"-->
    <!--                                android:textSize="@dimen/_default_sp"-->
    <!--                                tools:text="៛0" />-->
    <!--                        </LinearLayout>-->

    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:id="@+id/llPaymentInfoCash"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginTop="32dp"-->
    <!--                        android:background="@drawable/background_f3f5f8_radius_12"-->
    <!--                        android:orientation="vertical"-->
    <!--                        android:padding="16dp">-->

    <!--                        <LinearLayout-->
    <!--                            android:id="@+id/llPaymentInfoConversionRatio"-->
    <!--                            android:layout_width="match_parent"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:visibility="visible">-->

    <!--                            <TextView-->
    <!--                                android:id="@+id/paymentInfoConversionRatio"-->
    <!--                                android:layout_width="wrap_content"-->
    <!--                                android:layout_height="wrap_content"-->
    <!--                                android:text="@string/conversion_ratio"-->
    <!--                                android:textColor="@color/black"-->
    <!--                                android:textSize="@dimen/_18ssp" />-->

    <!--                            <TextView-->
    <!--                                android:id="@+id/tvPaymentInfoConversionRatio"-->
    <!--                                android:layout_width="0dp"-->
    <!--                                android:layout_height="wrap_content"-->
    <!--                                android:layout_weight="1"-->
    <!--                                android:gravity="end"-->
    <!--                                android:textColor="@color/black"-->
    <!--                                android:textSize="@dimen/_default_sp"-->
    <!--                                tools:text="$2 = 4000" />-->
    <!--                        </LinearLayout>-->

    <!--                        <LinearLayout-->
    <!--                            android:id="@+id/llPaymentInfoChange"-->
    <!--                            android:layout_width="match_parent"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:layout_marginTop="8dp"-->
    <!--                            android:visibility="visible">-->

    <!--                            <TextView-->
    <!--                                android:id="@+id/paymentInfoChange"-->
    <!--                                android:layout_width="wrap_content"-->
    <!--                                android:layout_height="wrap_content"-->
    <!--                                android:text="@string/back_your_change_amount"-->
    <!--                                android:textColor="@color/black"-->
    <!--                                android:textSize="@dimen/_18ssp" />-->

    <!--                            <TextView-->
    <!--                                android:id="@+id/tvPaymentInfoChange"-->
    <!--                                android:layout_width="0dp"-->
    <!--                                android:layout_height="wrap_content"-->
    <!--                                android:layout_weight="1"-->
    <!--                                android:gravity="end"-->
    <!--                                android:textColor="@color/black"-->
    <!--                                android:textSize="@dimen/_default_sp"-->
    <!--                                tools:text="$2" />-->
    <!--                        </LinearLayout>-->

    <!--                    </LinearLayout>-->

    <!--                </LinearLayout>-->


    <!--            </FrameLayout>-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginTop="20dp"-->
    <!--                android:gravity="center_horizontal"-->
    <!--                android:orientation="vertical"-->
    <!--                app:layout_constraintBottom_toBottomOf="parent"-->
    <!--                app:layout_constraintEnd_toEndOf="parent"-->
    <!--                app:layout_constraintStart_toEndOf="@id/flPaymentInfoLeftPart"-->
    <!--                app:layout_constraintTop_toTopOf="parent">-->

    <!--                &lt;!&ndash;                <ImageView&ndash;&gt;-->
    <!--                &lt;!&ndash;                    android:layout_width="wrap_content"&ndash;&gt;-->
    <!--                &lt;!&ndash;                    android:layout_height="wrap_content"&ndash;&gt;-->
    <!--                &lt;!&ndash;                    android:src="@drawable/ic_second_payment_done" />&ndash;&gt;-->

    <!--                <TextView-->
    <!--                    android:id="@+id/paymentInfoNeedPayTitle"-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_marginTop="20dp"-->
    <!--                    android:gravity="center_horizontal"-->
    <!--                    android:text="@string/payment_successfully"-->
    <!--                    android:textColor="@color/color_616263"-->
    <!--                    android:textSize="40sp"-->
    <!--                    android:textStyle="bold" />-->


    <!--                <LinearLayout-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="0dp"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:gravity="center">-->

    <!--                    <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--                        android:layout_width="wrap_content"-->
    <!--                        android:layout_height="wrap_content">-->

    <!--                        &lt;!&ndash;                        <TextView&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:id="@+id/qrTotalPrice"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:layout_width="wrap_content"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:layout_height="wrap_content"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:layout_gravity="bottom"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:text="$"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:textColor="@color/black"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:textSize="30sp"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            android:textStyle="bold"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            app:layout_constraintBaseline_toBaselineOf="@id/tvQrTotalPrice"&ndash;&gt;-->
    <!--                        &lt;!&ndash;                            app:layout_constraintStart_toStartOf="parent" />&ndash;&gt;-->

    <!--                        <TextView-->
    <!--                            android:id="@+id/tvPaymentInfoNeedPay"-->
    <!--                            android:layout_width="wrap_content"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            android:textColor="@color/primaryColor"-->
    <!--                            android:textSize="50sp"-->
    <!--                            android:textStyle="bold"-->
    <!--                            app:layout_constraintBottom_toBottomOf="parent"-->
    <!--                            app:layout_constraintEnd_toEndOf="parent"-->
    <!--                            app:layout_constraintStart_toStartOf="parent"-->
    <!--                            app:layout_constraintTop_toTopOf="parent"-->
    <!--                            tools:text="92.00" />-->
    <!--                    </androidx.constraintlayout.widget.ConstraintLayout>-->
    <!--                </LinearLayout>-->
    <!--            </LinearLayout>-->

    <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->
    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

    <ImageView
        android:id="@+id/ivPrinter"
        android:layout_width="370dp"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>