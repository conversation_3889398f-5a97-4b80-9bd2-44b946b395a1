<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_e7e7e7_radius_20dp"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvTitle"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/shift_handover_records"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/btnClose"
            app:layout_constraintEnd_toStartOf="@+id/btnClose"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/btnClose" />

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="5dp"
            android:src="@drawable/ic_cross_closed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <com.metathought.food_order.casheir.ui.widget.CalendarTextView
            android:id="@+id/tvCalendar"
            style="@style/commonCalendarTextViewStyle"
            android:layout_width="0dp"
            android:layout_marginTop="25dp"
            android:layout_weight="1"
            android:maxWidth="210dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <!--        <EditText-->
        <!--            android:id="@+id/edtSearch"-->
        <!--            style="@style/commonSearchStyle"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_marginStart="10dp"-->
        <!--            android:layout_marginEnd="16dp"-->
        <!--            android:hint="@string/staff_name"-->
        <!--            app:layout_constraintEnd_toStartOf="@+id/tvClearFilter"-->
        <!--            app:layout_constraintStart_toEndOf="@+id/tvCalendar"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/tvCalendar"-->
        <!--            tools:ignore="Autofill" />-->
        <com.metathought.food_order.casheir.ui.widget.CustomSearchView
            android:id="@+id/edtSearch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintEnd_toStartOf="@+id/tvClearFilter"
            app:layout_constraintStart_toEndOf="@+id/tvCalendar"
            app:layout_constraintTop_toTopOf="@+id/tvCalendar"
            app:search_hint="@string/staff_name" />

        <TextView
            android:id="@+id/tvClearFilter"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="10dp"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="@string/clear_filter"
            android:textColor="@color/primaryColor"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvCalendar" />


        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:minHeight="150dp"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCalendar">


            <com.scwang.smart.refresh.header.MaterialHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvShiftHandoverRecords"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constrainedHeight="true"
                tools:itemCount="3"
                tools:listitem="@layout/shift_handover_records_item" />


            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <include
            android:id="@+id/layoutEmpty"
            layout="@layout/layout_empty_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCalendar"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/layoutProgressBar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:minHeight="150dp"
            android:visibility="gone"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCalendar"
            tools:visibility="visible">

            <ProgressBar
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:layout_marginStart="10dp"
                android:indeterminate="true"
                android:indeterminateTintMode="src_atop" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
