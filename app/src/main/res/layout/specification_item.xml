<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="5dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layoutMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:cardBackgroundColor="@color/white"
        app:strokeColor="@color/black20"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="10dp">

            <TextView
                android:id="@+id/tvDishedNameAndPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:maxLines="3"
                android:paddingHorizontal="8dp"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                tools:text="Hello $9.99" />

            <TextView
                android:id="@+id/tvOriginalPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="15dp"
                android:ellipsize="end"
                android:foreground="@drawable/strike_price"
                android:maxLines="3"
                android:textColor="@color/black40"
                android:textSize="@dimen/_12ssp"
                android:visibility="gone"
                tools:text="12.99"
                tools:visibility="visible" />
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</FrameLayout>