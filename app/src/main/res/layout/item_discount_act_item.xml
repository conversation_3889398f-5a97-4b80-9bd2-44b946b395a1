<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tvGoodName"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/black"
        android:textSize="@dimen/_14ssp"
        tools:text="商品名称" />

    <TextView
        android:id="@+id/tvVipPrice"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/member_price_color"
        android:textSize="@dimen/_14ssp"
        android:layout_marginEnd="10dp"
        android:textStyle="bold"
        app:drawableStartCompat="@drawable/icon_vip"
        tools:text="￥99" />

    <TextView
        android:id="@+id/tvPrice"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        tools:text="￥99" />

</LinearLayout>