<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBackground"
    tools:context=".ui.order.MenuOrderFragment">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutOrder"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="6dp"
        android:paddingTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTop"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginEnd="6dp"
            app:layout_constraintEnd_toStartOf="@id/layoutAction"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvDingStyle"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@drawable/background_white_border_black12_radius_20"
                android:drawablePadding="5dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:paddingHorizontal="10dp"
                android:singleLine="true"
                android:text="@string/dine_in"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                app:drawableEndCompat="@drawable/ic_dropdown_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.25"
                tools:text="@string/dine_in" />

            <!--            <RadioGroup-->
            <!--                android:id="@+id/radioGroupOrderType"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="match_parent"-->
            <!--                android:background="@drawable/background_white_border_black12_radius_100"-->
            <!--                android:checkedButton="@id/radioAll"-->
            <!--                android:orientation="horizontal"-->
            <!--                app:layout_constraintStart_toStartOf="parent"-->
            <!--                app:layout_constraintTop_toTopOf="parent">-->

            <!--                <RadioButton-->
            <!--                    android:id="@+id/radioDineIn"-->
            <!--                    style="@style/FontLocalization"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="match_parent"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:background="@drawable/radiobutton_ding_background"-->
            <!--                    android:button="@null"-->
            <!--                    android:checked="true"-->
            <!--                    android:gravity="center"-->
            <!--                    android:minHeight="0dp"-->
            <!--                    android:paddingHorizontal="25dp"-->
            <!--                    android:text="@string/dine_in"-->
            <!--                    android:textColor="@drawable/radio_filter_text_selector"-->
            <!--                    android:textSize="@dimen/_14ssp"-->
            <!--                    app:buttonCompat="@null" />-->

            <!--                <RadioButton-->
            <!--                    android:id="@+id/radioTakeAway"-->
            <!--                    style="@style/FontLocalization"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="match_parent"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:background="@drawable/radiobutton_ding_background"-->
            <!--                    android:button="@null"-->
            <!--                    android:gravity="center"-->
            <!--                    android:minHeight="0dp"-->
            <!--                    android:paddingHorizontal="25dp"-->
            <!--                    android:text="@string/take_away"-->
            <!--                    android:textColor="@drawable/radio_filter_text_selector"-->
            <!--                    android:textSize="@dimen/_14ssp"-->
            <!--                    app:buttonCompat="@null" />-->

            <!--                <RadioButton-->
            <!--                    android:id="@+id/radioReserve"-->
            <!--                    style="@style/FontLocalization"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="match_parent"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:background="@drawable/radiobutton_ding_background"-->
            <!--                    android:button="@null"-->
            <!--                    android:gravity="center"-->
            <!--                    android:minHeight="0dp"-->
            <!--                    android:paddingHorizontal="25dp"-->
            <!--                    android:text="@string/pre_order"-->
            <!--                    android:textColor="@drawable/radio_filter_text_selector"-->
            <!--                    android:textSize="@dimen/_14ssp"-->
            <!--                    app:buttonCompat="@null" />-->

            <!--            </RadioGroup>-->

            <TextView
                android:id="@+id/tvCustomerInfo"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="6dp"
                android:background="@drawable/background_white_border_black12_radius_20"
                android:drawablePadding="5dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:paddingHorizontal="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:drawableEndCompat="@drawable/icon_edit"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/edtSearch"
                app:layout_constraintStart_toEndOf="@id/tvDingStyle"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.3"
                tools:text="@string/print_title_number_of_pax" />


            <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                android:id="@+id/edtSearch"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:drawablePadding="10dp"
                android:ellipsize="end"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvCustomerInfo"
                app:layout_constraintTop_toTopOf="parent"
                app:search_hint="@string/search_product" />

            <!--            <EditText-->
            <!--                android:id="@+id/edtSearch"-->
            <!--                style="@style/commonSearchStyle"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_marginStart="12dp"-->
            <!--                android:background="@drawable/background_white_border_black12_radius_100"-->
            <!--                android:drawablePadding="10dp"-->
            <!--                android:ellipsize="end"-->
            <!--                android:hint="@string/search_product"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintStart_toEndOf="@id/tvCustomerInfo"-->
            <!--                app:layout_constraintTop_toTopOf="parent"-->
            <!--                tools:ignore="Autofill,LabelFor" />-->


        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/layoutMainOrdered"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/background_white_radius_12dp"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/layoutAction"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutTop">

            <!--            <LinearLayout-->
            <!--                android:id="@+id/layoutHeader"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="47dp"-->
            <!--                android:gravity="center_vertical"-->
            <!--                android:orientation="horizontal"-->
            <!--                android:paddingHorizontal="10dp">-->

            <!--                <TextView-->
            <!--                    android:id="@+id/tvDeleteAll"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="match_parent"-->
            <!--                    android:layout_gravity="end"-->
            <!--                    android:layout_marginEnd="2dp"-->
            <!--                    android:layout_weight="1.8"-->
            <!--                    android:drawablePadding="3dp"-->
            <!--                    android:gravity="center_vertical"-->
            <!--                    android:text="@string/items"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_14ssp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    app:drawableStartCompat="@drawable/ic_trash" />-->

            <!--                <TextView-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="match_parent"-->
            <!--                    android:layout_gravity="end"-->
            <!--                    android:layout_marginEnd="2dp"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:gravity="center"-->
            <!--                    android:text="@string/quantity"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_14ssp"-->
            <!--                    android:textStyle="bold" />-->

            <!--                <TextView-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="match_parent"-->
            <!--                    android:layout_gravity="end"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:gravity="center"-->
            <!--                    android:text="@string/amount"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_14ssp"-->
            <!--                    android:textStyle="bold" />-->
            <!--            </LinearLayout>-->

            <!--            <View-->
            <!--                android:id="@+id/vTopLine"-->
            <!--                style="@style/commonDividerStyle" />-->
            <!--            <include-->
            <!--                android:id="@+id/menuTest"-->
            <!--                layout="@layout/selected_menu_item"/>-->
            <!--            <include-->
            <!--                android:id="@+id/menuTest2"-->
            <!--                layout="@layout/selected_menu_item"/>-->

            <LinearLayout
                android:id="@+id/layoutNewOrderTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/background_f5f5f5_top_radius_8"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvOrderMoreCount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="@string/new_order"
                    android:textColor="@color/primaryColor"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvNewOrderTotalPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="$0.00"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/arrowNewOrder"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="10dp"
                    android:rotation="180"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    app:tint="@color/primaryColor"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/flOrderedFood"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <!--                <androidx.core.widget.NestedScrollView-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content">-->

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerOrderedFood"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginTop="10dp"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="10dp"
                        android:scrollbars="none"
                        android:visibility="visible"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/selected_menu_item" />


                    <!--                        <com.google.android.material.textfield.TextInputLayout-->
                    <!--                            android:id="@+id/textInputLayoutRemark"-->
                    <!--                            style="@style/CustomOutlinedBox"-->
                    <!--                            android:layout_width="match_parent"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_marginVertical="13dp"-->
                    <!--                            android:hint="@string/remark"-->
                    <!--                            android:textColorHint="@color/black60"-->
                    <!--                            android:visibility="gone"-->
                    <!--                            tools:visibility="visible">-->

                    <!--                            <com.google.android.material.textfield.TextInputEditText-->
                    <!--                                android:id="@+id/edtRemark"-->
                    <!--                                style="@style/FontLocalization"-->
                    <!--                                android:layout_width="match_parent"-->
                    <!--                                android:layout_height="80dp"-->
                    <!--                                android:drawablePadding="10dp"-->
                    <!--                                android:gravity="top"-->
                    <!--                                android:lineSpacingExtra="5dp"-->
                    <!--                                android:maxLength="140"-->
                    <!--                                android:textColor="@color/black"-->
                    <!--                                android:textColorHint="@color/black" />-->

                    <!--                        </com.google.android.material.textfield.TextInputLayout>-->

                </LinearLayout>


                <!--                </androidx.core.widget.NestedScrollView>-->

                <include
                    android:id="@+id/layoutEmpty"
                    layout="@layout/layout_empty_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/layoutOrderMore"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@color/color_f5f5f5"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvPreviousOrderCount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="@string/old_order_items"
                    android:textColor="@color/primaryColor"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPricePreviousOrder"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="$0.00"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/arrowOldOrder"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="10dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    app:tint="@color/primaryColor"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerPreviousOrderedFood"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="10dp"
                android:layout_weight="1"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:scrollbars="none"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="1"
                tools:listitem="@layout/selected_menu_item"
                tools:visibility="visible" />

            <com.metathought.food_order.casheir.ui.widget.OrderGiftListView
                android:id="@+id/viewOrderGiftList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:visibility="gone"
                app:content_padding_horizontal="10dp"
                tools:visibility="visible" />

            <View
                android:id="@+id/vBottomLine"
                style="@style/commonDividerStyle"
                android:layout_marginHorizontal="10dp" />

            <LinearLayout
                android:id="@+id/layoutTotal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="20dp"
                android:orientation="horizontal"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/layoutPrice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="2"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/btnTotalPriceDetail"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:drawableEnd="@drawable/icon_circle_warn"
                        android:drawablePadding="5dp"
                        android:gravity="center_vertical"
                        android:text="@string/total_price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                    <!--                    <ImageView-->
                    <!--                        android:id="@+id/btnTotalPriceDetail"-->
                    <!--                        android:layout_width="wrap_content"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="center_vertical"-->
                    <!--                        android:layout_marginEnd="5dp"-->
                    <!--                        android:src="@drawable/icon_circle_warn"-->
                    <!--                        android:visibility="visible" />-->

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="start"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvTotalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:autoSizeMinTextSize="18sp"
                            android:autoSizeStepGranularity="1sp"
                            android:autoSizeTextType="uniform"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="$0" />

                        <TextView
                            android:id="@+id/tvTotalKhrPrice"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:autoSizeMinTextSize="18sp"
                            android:autoSizeStepGranularity="1sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="start"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="៛ 0" />

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:autoSizeMinTextSize="10sp"
                            android:autoSizeStepGranularity="1sp"
                            android:autoSizeTextType="uniform"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <!--                        <TextView-->
                        <!--                            android:id="@+id/tvOriginalPrice"-->
                        <!--                            style="@style/FontLocalization"-->
                        <!--                            android:layout_width="wrap_content"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginTop="4dp"-->
                        <!--                            android:autoSizeMinTextSize="10sp"-->
                        <!--                            android:autoSizeStepGranularity="1sp"-->
                        <!--                            android:autoSizeTextType="uniform"-->
                        <!--                            android:foreground="@drawable/strike_price"-->
                        <!--                            android:textColor="@color/black60"-->
                        <!--                            android:textSize="16sp"-->
                        <!--                            android:textStyle="bold"-->
                        <!--                            android:visibility="gone"-->
                        <!--                            tools:text="$0.00"-->
                        <!--                            tools:visibility="visible" />-->


                    </LinearLayout>

                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnPayNow"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="15dp"
                    android:layout_weight="1"
                    android:clickable="true"
                    android:ellipsize="end"
                    android:focusable="true"
                    android:gravity="center"
                    android:maxLines="1"
                    android:orientation="horizontal"
                    android:text="@string/pay_now"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_18ssp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primaryColor"
                    app:cornerRadius="8dp"
                    app:layout_constraintEnd_toEndOf="parent" />
            </LinearLayout>


            <!--            <LinearLayout-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="10dp"-->
            <!--                android:layout_marginBottom="10dp"-->
            <!--                android:orientation="horizontal">-->

            <!--                <com.google.android.material.button.MaterialButton-->
            <!--                    android:id="@+id/btnPending"-->
            <!--                    style="@style/CustomOutlinedBox"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="50dp"-->
            <!--                    android:layout_marginEnd="10dp"-->
            <!--                    android:clickable="true"-->
            <!--                    android:focusable="true"-->
            <!--                    android:gravity="center"-->
            <!--                    android:orientation="horizontal"-->
            <!--                    android:text="@string/pending"-->
            <!--                    android:textAllCaps="false"-->
            <!--                    android:textColor="@color/primaryColor"-->
            <!--                    android:textSize="@dimen/_18ssp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    app:backgroundTint="@color/white"-->
            <!--                    app:cornerRadius="8dp"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:strokeColor="@color/primaryColor"-->
            <!--                    app:strokeWidth="1dp" />-->


            <!--            </LinearLayout>-->
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layoutAction"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="10dp"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/layoutMainOrdered"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvSelectTable"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/select_table"
                android:textColor="@color/black"
                android:textSize="@dimen/_24ssp"
                tools:text="@tools:sample/lorem/random" />


            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="6dp"
                android:layout_weight="1"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_white_border_black12_radius_12"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/btnReduce"
                        style="@style/commonActionBtnStyle"
                        android:scaleType="centerInside"
                        android:src="@drawable/selector_good_reduce" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black12" />

                    <TextView
                        android:id="@+id/tvGoodNum"
                        style="@style/commonActionBtnStyle"
                        android:background="@null"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_24ssp"
                        android:textStyle="bold"
                        tools:text="999" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black12" />

                    <ImageButton
                        android:id="@+id/btnAdd"
                        style="@style/commonActionBtnStyle"
                        android:background="@color/transparent"
                        android:scaleType="centerInside"
                        android:src="@drawable/selector_good_add" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black12" />


                    <TextView
                        android:id="@+id/btnClearGood"
                        style="@style/commonActionBtnStyle"
                        android:background="@null"
                        android:enabled="false"
                        android:text="@string/clear_cart"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black12" />


                    <TextView
                        android:id="@+id/btnRemark"
                        style="@style/commonActionBtnStyle"
                        android:background="@null"
                        android:text="@string/remark"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/black12" />

                    <LinearLayout
                        android:id="@+id/llChangeTakeOutPlatform"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/btnChangeTakeOutPlatform"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:paddingHorizontal="4dp"
                            android:text="@string/change_take_out_platform"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llChangeTable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/btnChangeTable"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:paddingHorizontal="4dp"
                            android:text="@string/change_table"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llTmpGood"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/btnTmpGood"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:paddingHorizontal="4dp"
                            android:text="@string/add_temporary_goods"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="visible"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llWeight"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/btnWeight"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:enabled="false"
                            android:paddingHorizontal="4dp"
                            android:text="@string/weigh"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="visible"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llSetTimePrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/btnSetTimePrice"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:enabled="false"
                            android:paddingHorizontal="4dp"
                            android:text="@string/set_time_price"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="visible"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPending"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/btnPending"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:text="@string/pending"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />
                    </LinearLayout>
                    <!--11 -->
                    <LinearLayout
                        android:id="@+id/llPrint"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/btnPrint"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:text="@string/printer"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llCredit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/btnCredit"
                            style="@style/commonActionBtnStyle"
                            android:background="@null"
                            android:text="@string/credit"
                            android:textColor="@drawable/selector_action_btn_text_color"
                            android:textSize="@dimen/_14ssp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/btnMore"
                        style="@style/commonActionBtnStyle"
                        android:background="@null"
                        android:text="@string/more"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="visible"
                        tools:visibility="visible" />

                    <!--                    <TextView-->
                    <!--                        android:id="@+id/btnWholeDiscount"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:background="@drawable/selector_action_btn"-->
                    <!--                        android:gravity="center"-->
                    <!--                        android:minHeight="55dp"-->
                    <!--                        android:text="@string/discount_whole_order"-->
                    <!--                        android:textColor="@drawable/selector_action_btn_text_color"-->
                    <!--                        android:textSize="@dimen/_14ssp"-->
                    <!--                        android:textStyle="bold"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        tools:visibility="visible" />-->

                    <!--                    <TextView-->
                    <!--                        android:id="@+id/btnSingleDiscount"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:background="@drawable/selector_action_btn"-->
                    <!--                        android:gravity="center"-->
                    <!--                        android:minHeight="55dp"-->
                    <!--                        android:text="@string/discount_single_good"-->
                    <!--                        android:textColor="@drawable/selector_action_btn_text_color"-->
                    <!--                        android:textSize="@dimen/_14ssp"-->
                    <!--                        android:textStyle="bold"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        tools:visibility="visible" />-->


                    <!--                    <TextView-->
                    <!--                        android:id="@+id/btnCoupon"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:background="@drawable/selector_action_btn"-->
                    <!--                        android:gravity="center"-->
                    <!--                        android:minHeight="55dp"-->
                    <!--                        android:text="@string/coupon"-->
                    <!--                        android:textColor="@drawable/selector_action_btn_text_color"-->
                    <!--                        android:textSize="@dimen/_14ssp"-->
                    <!--                        android:textStyle="bold"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        tools:visibility="visible" />-->


                </LinearLayout>

            </ScrollView>


        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMenu"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layoutOrder"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/layoutMenuCategories"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginStart="6dp"
            android:layout_marginBottom="6dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewCategories"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:listitem="5" />
        </LinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutMenuCategories"
            app:layout_constraintVertical_bias="0.0">

            <com.scwang.smart.refresh.header.MaterialHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewMenu"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutMenuCategories"
                app:layout_constraintVertical_bias="0.0"
                app:spanCount="5"
                tools:listitem="@layout/new_menu_item" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <include
            android:id="@+id/layoutEmptyFood"
            layout="@layout/layout_empty_data"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="50dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ProgressBar
            android:id="@+id/pdMenu"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/pdLoading"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>