<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:orientation="vertical">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:dialog_title="@string/detail" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <!--            <TextView-->
            <!--                android:id="@+id/tvTitle"-->
            <!--                style="@style/FontLocalization"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="center_vertical"-->
            <!--                android:text="@string/detail"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="20sp"-->
            <!--                android:textStyle="bold"-->
            <!--                app:layout_constraintBottom_toBottomOf="@+id/btnClose"-->
            <!--                app:layout_constraintEnd_toStartOf="@+id/btnClose"-->
            <!--                app:layout_constraintStart_toStartOf="parent"-->
            <!--                app:layout_constraintTop_toTopOf="@+id/btnClose" />-->

            <!--            <ImageView-->
            <!--                android:id="@+id/btnClose"-->
            <!--                android:layout_width="40dp"-->
            <!--                android:layout_height="40dp"-->
            <!--                android:padding="5dp"-->
            <!--                android:src="@drawable/ic_cross_closed"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintTop_toTopOf="parent"-->
            <!--                tools:ignore="ContentDescription" />-->

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/labelStaffName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/staff_name"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvStaffName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="@+id/labelStaffName"
                app:layout_constraintTop_toBottomOf="@+id/labelStaffName"
                tools:text="Staff Name" />

            <TextView
                android:id="@+id/labelClassStartTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/class_start_time"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelStaffName"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/guideline" />

            <TextView
                android:id="@+id/tvClassStartTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/labelClassStartTime"
                app:layout_constraintTop_toBottomOf="@+id/labelClassStartTime"
                tools:text="2023/03/12  13:09" />

            <TextView
                android:id="@+id/labelHandoverCash"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/handover_cash"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStaffName" />

            <TextView
                android:id="@+id/tvHandoverCash"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="@+id/labelHandoverCash"
                app:layout_constraintTop_toBottomOf="@+id/labelHandoverCash"
                tools:text="$122.60 + ៛0" />

            <TextView
                android:id="@+id/labelClassCloseTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/class_close_time"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelHandoverCash"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/guideline" />

            <TextView
                android:id="@+id/tvClassCloseTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/labelClassCloseTime"
                app:layout_constraintTop_toBottomOf="@+id/labelClassCloseTime"
                tools:text="2023/03/12  19:09" />

            <TextView
                android:id="@+id/labelOrderAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/order_amount"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvHandoverCash" />

            <TextView
                android:id="@+id/tvOrderAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="@+id/labelOrderAmount"
                app:layout_constraintTop_toBottomOf="@+id/labelOrderAmount"
                tools:text="$122.60" />

            <TextView
                android:id="@+id/labelOrderNum"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/order_num"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelOrderAmount"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/guideline" />

            <TextView
                android:id="@+id/tvOrderNum"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/labelOrderNum"
                app:layout_constraintTop_toBottomOf="@+id/labelOrderNum"
                tools:text="20" />

            <TextView
                android:id="@+id/labelOnlinePaymentAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/online_payment_amount"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOrderAmount" />

            <TextView
                android:id="@+id/tvOnlinePaymentAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="@+id/labelOnlinePaymentAmount"
                app:layout_constraintTop_toBottomOf="@+id/labelOnlinePaymentAmount"
                tools:text="$122.60" />

            <TextView
                android:id="@+id/labelOfflinePaymentAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/offline_payment_amount"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelOnlinePaymentAmount"
                app:layout_constraintStart_toStartOf="@+id/guideline" />

            <TextView
                android:id="@+id/labelOfflinePaymentAmountView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="start"
                android:text="@string/review"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelOnlinePaymentAmount"
                app:layout_constraintStart_toEndOf="@id/labelOfflinePaymentAmount" />

            <TextView
                android:id="@+id/tvOfflinePaymentAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/labelOfflinePaymentAmount"
                app:layout_constraintTop_toBottomOf="@+id/labelOfflinePaymentAmount"
                tools:text="$122.60" />

            <!--            <TextView-->
            <!--                android:id="@+id/labelCashReceiptsPoint"-->
            <!--                android:layout_width="20dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="16dp"-->
            <!--                android:gravity="center"-->
            <!--                android:text="·"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="@dimen/_14ssp"-->
            <!--                android:textStyle="bold"-->
            <!--                app:layout_constraintStart_toStartOf="@+id/guideline"-->
            <!--                app:layout_constraintTop_toBottomOf="@+id/tvOfflinePaymentAmount" />-->

            <!--            <TextView-->
            <!--                android:id="@+id/labelCashReceipts"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="16dp"-->
            <!--                android:text="@string/cash_receipts"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="@dimen/_14ssp"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintStart_toEndOf="@+id/labelCashReceiptsPoint"-->
            <!--                app:layout_constraintTop_toBottomOf="@+id/tvOfflinePaymentAmount" />-->

            <!--            <TextView-->
            <!--                android:id="@+id/tvCashReceipts"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="10dp"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="@dimen/_16ssp"-->
            <!--                android:textStyle="bold"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintStart_toStartOf="@+id/labelCashReceipts"-->
            <!--                app:layout_constraintTop_toBottomOf="@+id/labelCashReceipts"-->
            <!--                tools:text="$122.60" />-->


            <TextView
                android:id="@+id/labelBalanceReceipts"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/balance_receipts"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOnlinePaymentAmount" />

            <TextView
                android:id="@+id/tvBalanceReceipts"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="@+id/labelBalanceReceipts"
                app:layout_constraintTop_toBottomOf="@+id/labelBalanceReceipts"
                tools:text="$122.60" />


            <TextView
                android:id="@+id/labelCreditReceipts"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/received_credit"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelBalanceReceipts"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/guideline"
                app:layout_constraintTop_toBottomOf="@+id/tvOnlinePaymentAmount" />

            <TextView
                android:id="@+id/tvCreditReceipts"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/labelCreditReceipts"
                app:layout_constraintStart_toStartOf="@+id/labelCreditReceipts"
                app:layout_constraintTop_toBottomOf="@+id/labelCreditReceipts"
                tools:text="$122.60" />


            <TextView
                android:id="@+id/labelImprest"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/imprest"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toEndOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvBalanceReceipts" />

            <TextView
                android:id="@+id/tvImprest"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/labelImprest"
                app:layout_constraintStart_toStartOf="@+id/labelImprest"
                app:layout_constraintTop_toBottomOf="@+id/labelImprest"
                tools:text="$122.60 + ៛0" />

            <TextView
                android:id="@+id/labelShiftExpenses"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/shift_expenses"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelImprest"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline"
                app:layout_constraintTop_toBottomOf="@+id/tvBalanceReceipts" />

            <TextView
                android:id="@+id/tvShiftExpenses"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/labelShiftExpenses"
                app:layout_constraintStart_toStartOf="@+id/labelShiftExpenses"
                app:layout_constraintTop_toBottomOf="@+id/labelShiftExpenses"
                tools:text="$122.60 + ៛0" />


            <TextView
                android:id="@+id/labelDifferenceAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/difference_amount"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toEndOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvImprest" />

            <TextView
                android:id="@+id/tvDifferenceAmount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/labelDifferenceAmount"
                app:layout_constraintStart_toStartOf="@id/labelDifferenceAmount"
                app:layout_constraintTop_toBottomOf="@+id/labelDifferenceAmount"
                tools:text="$122.60 + ៛0" />


            <TextView
                android:id="@+id/labelShiftBalance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/shift_balance"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBaseline_toBaselineOf="@+id/labelDifferenceAmount"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline"
                app:layout_constraintTop_toBottomOf="@id/tvShiftExpenses" />

            <TextView
                android:id="@+id/tvShiftBalance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@id/labelShiftBalance"
                app:layout_constraintStart_toStartOf="@+id/labelShiftBalance"
                app:layout_constraintTop_toBottomOf="@+id/labelShiftBalance"
                tools:text="$122.60 + ៛0" />


            <TextView
                android:id="@+id/labelRemark"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/remark"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintEnd_toEndOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvDifferenceAmount" />

            <TextView
                android:id="@+id/tvRemark"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="@id/labelRemark"
                app:layout_constraintTop_toBottomOf="@+id/labelRemark"
                tools:text="拿了50美元去买了塑料杯/下午店长 紧急拿了5万瑞尔拿了50美元去买了塑料杯/下午店长 紧急拿了5万瑞尔拿了50美元去买了塑料杯/下午店长 紧急拿了5万瑞尔" />


            <ProgressBar
                android:id="@+id/pbLogout"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/btnPrint"
        style="@style/FontLocalization"
        android:layout_width="400dp"
        android:layout_height="50dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="36dp"
        android:layout_marginTop="25dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/button_login_background"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/printer"
        android:textColor="@color/mainWhite"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvRemark" />
</LinearLayout>
