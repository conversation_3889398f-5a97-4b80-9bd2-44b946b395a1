<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    android:padding="6dp"
    tools:context=".ui.table.TableFragment">

    <RadioGroup
        android:id="@+id/radioGroupFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/round_background_25dp"
        android:checkedButton="@id/radioAll"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/radioAll"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="25dp"
            android:text="@string/filter_all"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioAvailable"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="25dp"
            android:text="@string/filter_available"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioOccupied"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="25dp"
            android:text="@string/filter_dining"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioReserve"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="25dp"
            android:text="@string/filter_reserved"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />


    </RadioGroup>

    <com.metathought.food_order.casheir.ui.widget.CustomSearchView
        android:id="@+id/edtSearch"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        app:search_hint="@string/search_table_customer_phone_number" />

    <!--    <EditText-->
    <!--        android:id="@+id/edtSearch"-->
    <!--        style="@style/commonSearchStyle"-->
    <!--        android:hint="@string/search_table_customer_phone_number"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintHorizontal_bias="1"-->
    <!--        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"-->
    <!--        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"-->
    <!--        tools:ignore="Autofill" />-->

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewTable"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingBottom="6dp"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/layoutPage"
        app:layout_constraintTop_toBottomOf="@id/radioGroupFilter"
        app:spanCount="5"
        tools:itemCount="90"
        tools:listitem="@layout/table_item" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layoutPage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="horizontal"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:strokeColor="@color/black10"
        app:strokeWidth="0dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewPage"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:layout_margin="6dp"
            android:background="@drawable/background_language_spiner"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="8"
            tools:listitem="@layout/floor_item" />

    </com.google.android.material.card.MaterialCardView>


    <ProgressBar
        android:id="@+id/pbTableLoading"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
