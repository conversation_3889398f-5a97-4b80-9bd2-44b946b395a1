<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_weight="2"
        android:layout_marginEnd="10dp"
        android:orientation="vertical"
        android:text="@string/items"
        android:textColor="@color/black50"
        android:textSize="14sp">

        <TextView
            android:id="@+id/tvFoodName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1.2"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="Lemonade"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTmpSign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingHorizontal="4dp"
                android:paddingVertical="2dp"
                android:text="@string/temporary"
                android:textColor="@color/black60"
                android:textSize="@dimen/_11ssp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvDiscountActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingHorizontal="4dp"
                android:paddingVertical="2dp"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_11ssp"
                android:visibility="gone"
                tools:text="第二份半价"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvFoodSubName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1.2"
            android:ellipsize="end"
            android:textColor="@color/black60"
            android:textSize="@dimen/_12ssp"
            android:visibility="gone"
            tools:text="Large Normal ice, Normal sugar"
            tools:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_marginEnd="10dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvFoodCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="center_horizontal"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            tools:text="1" />

    </LinearLayout>


    <TextView
        android:id="@+id/tvServiceFeePer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:gravity="center"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="0" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_gravity="top">
        <TextView
            android:id="@+id/tvServiceFee"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            tools:text="$9.9" />
        <TextView
            android:id="@+id/tvVipServiceFee"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="4dp"
            android:drawablePadding="2dp"
            android:textColor="@color/member_price_color"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/icon_vip"
            tools:text="$0.00"
            tools:visibility="visible" />

    </LinearLayout>


</LinearLayout>
