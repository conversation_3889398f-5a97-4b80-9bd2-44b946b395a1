<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="45dp"
    android:layout_height="45dp"
    android:layout_marginEnd="10dp"
    android:background="@color/mainWhite"
    android:backgroundTint="@color/mainWhite"
    app:cardCornerRadius="10dp"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@color/white"
    app:strokeColor="@color/backgroundSpinner">

    <TextView
        android:backgroundTint="@color/mainWhite"
        android:id="@+id/tvValue"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.6"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:textColor="@color/black"
        android:textStyle="bold" />
</com.google.android.material.card.MaterialCardView>