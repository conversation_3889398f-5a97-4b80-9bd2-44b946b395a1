<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="5dp"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llOrderInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="10dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvOrderIndex"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black60"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            tools:text="订单1" />

        <TextView
            android:id="@+id/tvOrderId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black60"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            tools:text="订单号" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:showDividers="end">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.7" />

        <LinearLayout
            android:id="@+id/llGoodsInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="2"
            android:orientation="vertical"
            android:text="@string/items"
            android:textColor="@color/black50"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/guideline5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvFoodName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_weight="1.2"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/black"
                android:textSize="@dimen/_12ssp"
                tools:text="Lemonade" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvTmpSign"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="2dp"
                    android:text="@string/temporary"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_11ssp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvDiscountActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="2dp"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_11ssp"
                    android:visibility="gone"
                    tools:text="第二份半价"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvFoodSubName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_weight="1.2"
                android:ellipsize="end"
                android:maxLines="3"
                android:textColor="@color/black60"
                android:textSize="@dimen/_12ssp"
                tools:text="Large Normal ice, Normal sugar" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_weight="1"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@+id/guideline7"
            app:layout_constraintStart_toEndOf="@+id/guideline5"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvFoodCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:gravity="center_horizontal"
                android:textColor="@color/black"
                android:textSize="12sp"
                tools:text="1" />

            <TextView
                android:id="@+id/tvRefundMinusCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:gravity="center_horizontal"
                android:textColor="@color/main_red"
                android:textSize="12sp"
                android:visibility="gone"
                tools:text="1"
                tools:visibility="visible" />
        </LinearLayout>

        <include
            android:id="@+id/layoutPrice"
            layout="@layout/layout_item_price_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline7"
            app:layout_constraintTop_toTopOf="parent" />
        <!--        <LinearLayout-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_gravity="top"-->
        <!--            android:layout_weight="1"-->
        <!--            android:gravity="center"-->
        <!--            android:orientation="vertical"-->
        <!--            android:text="@string/items"-->
        <!--            android:textColor="@color/black50"-->
        <!--            android:textSize="12sp">-->

        <!--            <TextView-->
        <!--                android:id="@+id/tvFoodPrice"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_gravity="center_horizontal"-->
        <!--                android:ellipsize="end"-->
        <!--                android:gravity="center"-->
        <!--                android:maxLines="1"-->
        <!--                android:textColor="@color/black"-->
        <!--                android:textSize="@dimen/_14ssp"-->
        <!--                tools:text="$9.9" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/tvWeight"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_gravity="center_horizontal"-->
        <!--                android:ellipsize="end"-->
        <!--                android:gravity="center"-->
        <!--                android:maxLines="1"-->
        <!--                android:textColor="@color/black"-->
        <!--                android:textSize="@dimen/_14ssp"-->
        <!--                tools:text="$9.9" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/tvVipPrice"-->
        <!--                style="@style/FontLocalization"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginTop="4dp"-->
        <!--                android:drawableStart="@drawable/icon_vip"-->
        <!--                android:drawablePadding="2dp"-->
        <!--                android:textColor="@color/member_price_color"-->
        <!--                android:textSize="12sp"-->
        <!--                android:textStyle="bold"-->
        <!--                android:visibility="gone"-->
        <!--                tools:text="$0.00"-->
        <!--                tools:visibility="visible" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/tvOriginalPrice"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_gravity="center_horizontal"-->
        <!--                android:layout_marginTop="4dp"-->
        <!--                android:ellipsize="end"-->
        <!--                android:foreground="@drawable/strike_price"-->
        <!--                android:maxLines="3"-->
        <!--                android:textColor="@color/black60"-->
        <!--                android:textSize="@dimen/_12ssp"-->
        <!--                android:visibility="gone"-->
        <!--                tools:text="12.99"-->
        <!--                tools:visibility="visible" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/tvRefundAmount"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_gravity="end"-->
        <!--                android:layout_marginTop="4dp"-->
        <!--                android:gravity="center_horizontal"-->
        <!--                android:textColor="@color/main_red"-->
        <!--                android:textSize="@dimen/_12ssp"-->
        <!--                android:visibility="gone"-->
        <!--                tools:text="11111"-->
        <!--                tools:visibility="visible" />-->
        <!--        </LinearLayout>-->

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="llGoodsInfo,llCount" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvMealGoodsList"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toStartOf="@+id/guideline7"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/barrier"
            tools:listitem="@layout/food_sub_table_item"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvRemark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="start"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/_12ssp"
        android:visibility="gone"
        tools:text="备注 : $9.9"
        tools:visibility="visible" />
</LinearLayout>
