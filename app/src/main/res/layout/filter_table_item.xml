<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutWrap"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="10dp"
    android:layout_marginHorizontal="15dp"
    app:cardBackgroundColor="@color/filter_table_background"
    app:cardCornerRadius="15dp"
    app:cardElevation="0dp"
    app:strokeColor="@android:color/transparent"
    app:strokeWidth="0dp">

    <LinearLayout
        android:paddingVertical="15dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvTableID"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:paddingHorizontal="10dp"
            android:backgroundTint="@color/mainWhite"
            android:fontFamily="@font/roboto"
            android:gravity="start"
            android:maxLines="2"
            tools:text="@tools:sample/cities"
            android:ellipsize="end"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />




    </LinearLayout>
</com.google.android.material.card.MaterialCardView>