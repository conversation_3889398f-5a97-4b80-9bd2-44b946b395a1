<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="380dp"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btnCancel"
        android:id="@+id/ivBitmap"
        android:layout_width="380dp"
        android:layout_height="0dp"/>

    <Button
        app:layout_constraintTop_toBottomOf="@id/ivBitmap"
        android:id="@+id/btnCancel"
        app:layout_constraintStart_toStartOf="parent"
        android:text="关闭"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="50dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>