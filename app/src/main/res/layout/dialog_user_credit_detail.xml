<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/mainLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="end"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/detail"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <TextView
                    android:id="@+id/labelNickname"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/customer_nickname"
                    android:textColor="@color/black80"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvNickname"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="@+id/labelNickname"
                    app:layout_constraintTop_toBottomOf="@+id/labelNickname"
                    tools:text="Lynn" />


                <TextView
                    android:id="@+id/labelAccount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/customer_account"
                    android:textColor="@color/black80"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/guideline"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvAccount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/labelAccount"
                    app:layout_constraintTop_toBottomOf="@+id/labelAccount"
                    tools:text="+855 *********" />

                <TextView
                    android:id="@+id/labelRechargeNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/recharge_times"
                    android:textColor="@color/black80"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvNickname" />

                <TextView
                    android:id="@+id/tvRechargeNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="@+id/labelRechargeNum"
                    app:layout_constraintTop_toBottomOf="@+id/labelRechargeNum"
                    tools:text="0" />


                <TextView
                    android:id="@+id/labelConsumerNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/consumption_closed"
                    android:textColor="@color/black80"
                    android:textSize="14sp"
                    app:layout_constraintStart_toEndOf="@+id/guideline"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBaseline_toBaselineOf="@+id/labelRechargeNum"/>

                <TextView
                    android:id="@+id/tvConsumerNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/labelConsumerNum"
                    app:layout_constraintTop_toBottomOf="@+id/labelConsumerNum"
                    tools:text="0" />

                <TextView
                    android:id="@+id/labelBalance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/customer_balance"
                    android:textColor="@color/black80"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvRechargeNum" />

                <TextView
                    android:id="@+id/tvBalance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/guideline"
                    app:layout_constraintStart_toStartOf="@+id/labelBalance"
                    app:layout_constraintTop_toBottomOf="@+id/labelBalance"
                    tools:text="$ 0.00" />


                <TextView
                    android:id="@+id/labelCreditAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/credit_amount"
                    android:textColor="@color/black80"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/guideline"
                    app:layout_constraintBaseline_toBaselineOf="@+id/labelBalance"/>

                <TextView
                    android:id="@+id/tvCreditAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/black80"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/labelCreditAmount"
                    app:layout_constraintTop_toBottomOf="@+id/labelCreditAmount"
                    tools:text="$ 0.00" />

                <TextView
                    android:id="@+id/labelCreditRecords"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="29dp"
                    android:text="@string/credit_records"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvCreditAmount" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnPrint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:paddingStart="24dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="24dp"
                    android:paddingBottom="4dp"
                    android:text="@string/printer"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primaryColor"
                    app:cornerRadius="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/labelCreditRecords"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/labelCreditRecords" />

                <LinearLayout
                    android:id="@+id/creditRecordsLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/background_efefef_radius_12"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/btnPrint">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="15dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.2"
                            android:paddingHorizontal="16dp"
                            android:text="@string/order_id"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.2"
                            android:paddingHorizontal="16dp"
                            android:text="@string/credit_amount"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.2"
                            android:paddingHorizontal="16dp"
                            android:text="@string/time"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingHorizontal="16dp"
                            android:text="@string/status"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingHorizontal="16dp"
                            android:text="@string/operate"
                            android:textColor="@color/black"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <View style="@style/commonDividerStyle" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvCreditRecords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/labelCreditRecords"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_credit_records" />

                    <TextView
                        android:id="@+id/tvMoreCreditRecords"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="8dp"
                        android:text="@string/view_more"
                        android:textColor="@color/primaryColor"
                        android:textSize="14sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvNoCreditRecords"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/no_credit_records"
                    android:textColor="@color/black60"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/creditRecordsLayout"
                    />


                <TextView
                    android:id="@+id/labelPaymentRecords"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/payment_records"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvNoCreditRecords" />

                <LinearLayout
                    android:id="@+id/paymentRecordsLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/background_efefef_radius_12"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/labelPaymentRecords"
                    tools:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="15dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingHorizontal="16dp"
                            android:text="@string/amount"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingHorizontal="16dp"
                            android:text="@string/payment_method"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingHorizontal="16dp"
                            android:text="@string/time"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <View style="@style/commonDividerStyle" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvPaymentRecords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/labelCreditRecords"
                        tools:listitem="@layout/item_payment_records"
                        tools:itemCount="1" />

                    <TextView
                        android:id="@+id/tvMorePaymentRecords"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="8dp"
                        android:text="@string/view_more"
                        android:textColor="@color/primaryColor"
                        android:textSize="14sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvNoPaymentRecords"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/no_payment_records"
                    android:textColor="@color/black60"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/paymentRecordsLayout"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mainLayout"
        app:layout_constraintEnd_toEndOf="@+id/mainLayout"
        app:layout_constraintStart_toStartOf="@+id/mainLayout"
        app:layout_constraintTop_toTopOf="@+id/mainLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>
