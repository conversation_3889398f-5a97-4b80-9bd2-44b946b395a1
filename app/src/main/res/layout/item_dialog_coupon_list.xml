<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@drawable/background_white_radius_12dp"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:paddingVertical="21dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/llLeftPart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingEnd="10dp"
            app:layout_constraintBottom_toBottomOf="@id/llRightPart"
            app:layout_constraintEnd_toStartOf="@id/llRightPart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.25">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvUnit"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="2dp"
                    android:gravity="center"
                    android:text="$"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvAmount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    tools:text="9.99" />

                <TextView
                    android:id="@+id/tvPercent"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:gravity="center"
                    android:text="% 0FF"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="gone" />

            </LinearLayout>


            <TextView
                android:id="@+id/tvLimitAmount"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black60"
                android:textSize="@dimen/_12ssp"
                tools:text="满50可用" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llRightPart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/llLeftPart"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.75">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCouponName"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="优惠券名称名很长的样式" />

                    <TextView
                        android:id="@+id/tvEffectiveTime"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black40"
                        android:textSize="@dimen/_11ssp"
                        tools:text="2024/03/05 12:00 - 2024/03/05 12:00" />

                    <LinearLayout
                        android:id="@+id/llPreferential"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="10dp"
                            android:text="@string/this_order_offers_discounts"
                            android:textColor="@color/confirm_text_color"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="10dp"
                            android:drawableStart="@drawable/icon_vip"
                            android:paddingStart="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$6.99" />

                        <TextView
                            android:id="@+id/tvPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$6.99" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvGiftGoodNum"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        android:drawablePadding="4dp"
                        android:text="@string/gift_products_num"
                        android:textColor="@color/primaryColor"
                        app:drawableEndCompat="@drawable/icon_arrow_down" />


                </LinearLayout>

                <ImageView
                    android:id="@+id/checkbox"
                    style="@style/FontLocalization"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    android:scaleType="centerCrop"
                    android:src="@drawable/coupon_checkbox_circle_drawable"
                    android:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvGiftGoods"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/background_f5f5f5_radius_6"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="@id/llRightPart"
            app:layout_constraintStart_toStartOf="@id/llRightPart"
            app:layout_constraintTop_toBottomOf="@id/llRightPart"
            tools:itemCount="3"
            tools:listitem="@layout/item_coupon_good" />


    </androidx.constraintlayout.widget.ConstraintLayout>


<!--    <com.metathought.food_order.casheir.ui.widget.DashedLineView-->
<!--        android:id="@+id/vDLine"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="1dp"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/llTop" />-->
    <View
        android:id="@+id/vDLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/imaginary_line"
        android:layerType="software"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llTop"/>

    <LinearLayout
        android:id="@id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="10dp"
        android:paddingBottom="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vDLine">

        <LinearLayout
            android:id="@+id/llGoods"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvGoodsTitle"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold"
                    tools:text="适用商品(10):" />

                <ImageView
                    android:id="@+id/ivGoodsExpand"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingStart="10dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_arrow_down" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvGoods"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="3"
                tools:listitem="@layout/item_coupon_good" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/llRule"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvRuleTitle"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/use_rule"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/ivRuleExpand"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingStart="10dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_arrow_down" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvRuleContent"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:textColor="@color/black40"
                android:textSize="@dimen/_12ssp"
                android:visibility="gone"
                tools:text="规则"
                tools:visibility="visible" />

            <!--            <androidx.recyclerview.widget.RecyclerView-->
            <!--                android:id="@+id/rvRule"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:overScrollMode="never" />-->

        </LinearLayout>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>