<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvItem"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:gravity="start"
        android:textColor="@color/black"
        android:textSize="22px"
        android:textStyle="bold"
        tools:text="Item" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="end"
        android:textColor="@color/black"
        android:textSize="22px"
        android:textStyle="bold"
        tools:text="$Total" />

</LinearLayout>
