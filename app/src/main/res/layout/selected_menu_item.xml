<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutBg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_menu_order_bg_color"
    android:orientation="vertical"
    android:paddingHorizontal="10dp"
    android:paddingVertical="6dp">

    <LinearLayout
        android:id="@+id/layoutContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginEnd="2dp"
                android:layout_weight="1.8"
                android:orientation="vertical"
                android:text="@string/items"
                android:textColor="@color/black50"
                android:textSize="@dimen/_14ssp">

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:ellipsize="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    tools:text="Lemonade" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvTmpSign"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingHorizontal="4dp"
                        android:text="@string/temporary"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvDiscountActivity"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingHorizontal="4dp"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_12ssp"
                        android:visibility="gone"
                        tools:text="第二份半价"
                        tools:visibility="visible" />

                </LinearLayout>


                <TextView
                    android:id="@+id/tvSpecification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:ellipsize="end"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_12ssp"
                    android:visibility="gone"
                    tools:text="Large Normal ice, Normal sugar"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:gravity="center">

                <!--            <ImageView-->
                <!--                android:id="@+id/imgMinus"-->
                <!--                android:layout_width="24dp"-->
                <!--                android:layout_height="24dp"-->
                <!--                android:src="@drawable/ic_minus" />-->

                <TextView
                    android:id="@+id/tvQTY"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginTop="1dp"
                    android:maxLength="4"
                    android:paddingHorizontal="5dp"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp"
                    tools:text="10" />

                <!--            <ImageView-->
                <!--                android:id="@+id/imgPlus"-->
                <!--                android:layout_width="24dp"-->
                <!--                android:layout_height="24dp"-->
                <!--                android:src="@drawable/ic_add" />-->

            </LinearLayout>

            <include
                android:id="@+id/layoutPrice"
                layout="@layout/layout_item_price_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />
<!--            <LinearLayout-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="top"-->
<!--                android:layout_weight="1"-->
<!--                android:gravity="center_horizontal"-->
<!--                android:orientation="vertical">-->

<!--                <TextView-->
<!--                    android:id="@+id/tvPrice"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_gravity="center_horizontal"-->
<!--                    android:ellipsize="end"-->
<!--                    android:gravity="center"-->
<!--                    android:maxLines="2"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="@dimen/_16ssp"-->
<!--                    tools:text="$9.9" />-->

<!--                <TextView-->
<!--                    android:id="@+id/tvWeight"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_gravity="center_horizontal"-->
<!--                    android:ellipsize="end"-->
<!--                    android:gravity="center"-->
<!--                    android:maxLines="2"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="@dimen/_16ssp"-->
<!--                    android:visibility="gone"-->
<!--                    tools:text="$9.9"-->
<!--                    tools:visibility="visible" />-->

<!--                <TextView-->
<!--                    android:id="@+id/tvVipPrice"-->
<!--                    style="@style/FontLocalization"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="2dp"-->
<!--                    android:layout_marginTop="4dp"-->
<!--                    android:drawablePadding="2dp"-->
<!--                    android:textColor="@color/member_price_color"-->
<!--                    android:textSize="12sp"-->
<!--                    android:textStyle="bold"-->
<!--                    app:drawableStartCompat="@drawable/icon_vip"-->
<!--                    tools:text="$0.00" />-->

<!--                <TextView-->
<!--                    android:id="@+id/tvOriginalPrice"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_gravity="center_horizontal"-->
<!--                    android:layout_marginTop="4dp"-->
<!--                    android:ellipsize="end"-->
<!--                    android:foreground="@drawable/strike_price"-->
<!--                    android:maxLines="3"-->
<!--                    android:textColor="@color/black60"-->
<!--                    android:textSize="@dimen/_12ssp"-->
<!--                    android:visibility="gone"-->
<!--                    tools:text="12.99"-->
<!--                    tools:visibility="visible" />-->

<!--                <ImageView-->
<!--                    android:id="@+id/ivWarn"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:src="@drawable/icon_warn_green"-->
<!--                    android:visibility="gone"-->
<!--                    tools:visibility="visible" />-->
<!--            </LinearLayout>-->
        </LinearLayout>

        <TextView
            android:id="@+id/tvRemark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="start"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/_12ssp"
            tools:text="备注 : $9.9" />
    </LinearLayout>


</LinearLayout>
