<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    android:padding="6dp"
    tools:context=".ui.table.TableFragment">

    <LinearLayout
        android:id="@+id/dropdownFilter"
        android:layout_width="150dp"
        android:layout_height="32dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/background_language_spiner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvType"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/today"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="@string/all" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_dropdown"
            android:visibility="visible"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layoutViewOrderDetail"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/background_language_spiner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingHorizontal="15dp"
            android:text="@string/view_order_detail"
            android:textColor="@color/primaryColor"
            android:textSize="14sp"
            tools:text="@string/view_order_detail" />

    </LinearLayout>

    <!--    <RadioGroup-->
    <!--        android:id="@+id/radioGroupFilter"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="40dp"-->
    <!--        android:background="@drawable/round_background_25dp"-->
    <!--        android:checkedButton="@id/radioAll"-->
    <!--        android:orientation="horizontal"-->
    <!--        android:visibility="visible"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent">-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioToday"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:checked="true"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="25dp"-->
    <!--            android:text="@string/today"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="14sp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioThisWeek"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="25dp"-->
    <!--            android:text="@string/this_week"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="14sp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioMonth"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="25dp"-->
    <!--            android:text="@string/this_month"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="14sp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioQuarter"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="25dp"-->
    <!--            android:text="@string/this_quarter"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="14sp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioYear"-->
    <!--            style="@style/FontLocalization"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/radiobutton_filter_backgroud"-->
    <!--            android:button="@null"-->
    <!--            android:gravity="center"-->
    <!--            android:minHeight="0dp"-->
    <!--            android:paddingHorizontal="25dp"-->
    <!--            android:text="@string/this_year"-->
    <!--            android:textColor="@drawable/radio_filter_text_selector"-->
    <!--            android:textSize="14sp"-->
    <!--            app:buttonCompat="@null" />-->

    <!--    </RadioGroup>-->

    <TextView
        android:id="@+id/tvClearFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginHorizontal="6dp"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="15dp"
        android:singleLine="true"
        android:text="@string/clear_filter"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/tvCalendar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.metathought.food_order.casheir.ui.widget.CalendarTextView
        android:id="@+id/tvCalendar"
        style="@style/commonCalendarTextViewStyle"
        android:layout_marginHorizontal="6dp"
        app:layout_constraintStart_toEndOf="@id/dropdownFilter"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="01 03, 2024 - 01 03, 2024" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/background_table_fragment"
        android:paddingTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCalendar"
        tools:context=".ui.table.TableFragment">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:paddingBottom="6dp"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.83">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingBottom="6dp">

                <include
                    android:id="@+id/layoutRevenueAmount"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutUnPaidAmount"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutActualRevenue"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutUnsettledAmount"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutSettledAmount"
                    layout="@layout/dashboard_item" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <include
                    android:id="@+id/layoutOrderNumbers"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutUnPaidOrders"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutPaidOrders"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutRefundsOrders"
                    layout="@layout/dashboard_item" />

                <include
                    android:id="@+id/layoutRefundAmount"
                    layout="@layout/dashboard_item" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingBottom="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.17">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="15dp"
                app:strokeColor="@color/white">

                <LinearLayout
                    android:id="@+id/llChart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_horizontal"
                    android:orientation="vertical"
                    android:padding="6dp">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1">

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/pieChartView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal" />
                        <ImageView
                            android:id="@+id/tvPaymentMethodTitleCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="3dp"
                            android:layout_gravity="end"
                            android:src="@drawable/icon_circle_warn"
                            android:visibility="visible"
                            android:layout_margin="3dp"
                            tools:visibility="visible" />
                    </FrameLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableLeft="@drawable/ic_circle"
                            android:drawablePadding="10dp"
                            android:text="@string/online_payment_btn_with_colon"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvOnlinePaymentValue"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_weight="1"
                            android:drawablePadding="10dp"
                            android:gravity="end|center_vertical"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableStart="@drawable/ic_circle"
                            android:drawablePadding="10dp"
                            android:text="@string/offline_payments_btn_with_colon"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            app:drawableTint="@color/cash_chart_color" />

                        <TextView
                            android:id="@+id/tvCashValue"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_weight="1"
                            android:drawablePadding="10dp"
                            android:gravity="end|center_vertical"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableLeft="@drawable/ic_circle"
                            android:drawablePadding="10dp"
                            android:drawableTint="@color/balance_chart_color"
                            android:text="@string/balance_chart_title"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvBalanceValue"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_weight="1"
                            android:drawablePadding="10dp"
                            android:gravity="end|center_vertical"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="10dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableLeft="@drawable/ic_circle"
                            android:drawablePadding="10dp"
                            android:drawableTint="@color/credit_chart_color"
                            android:text="@string/credit_chart_title"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvCreditValue"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_weight="1"
                            android:drawablePadding="10dp"
                            android:gravity="end|center_vertical"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingEnd="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.55"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.27">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp">

                    <TextView
                        android:id="@+id/tvTtitleChart"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center"
                        android:text="@string/top_sales_list"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerViewTopSale"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        android:visibility="visible"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="6"
                        tools:listitem="@layout/top_sales_list_item"
                        tools:visibility="visible" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/layoutChartPeople"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.55"
            app:layout_constraintWidth_percent="0.73">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/date"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/revenue_amount"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/actual_revenue"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".7"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/order_management1"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/paid_orders"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="10dp"
                            android:text="@string/refund_orders"
                            android:textColor="@color/black80"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <View style="@style/commonDividerStyle" />
                    <!--                    <androidx.recyclerview.widget.RecyclerView-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="match_parent"-->
                    <!--                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"-->
                    <!--                        tools:itemCount="30"-->
                    <!--                        tools:listitem="@layout/dashboard_revenue_item"/>-->
                    <com.scwang.smart.refresh.layout.SmartRefreshLayout
                        android:id="@+id/refreshLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/background_dialog"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.scwang.smart.refresh.header.MaterialHeader
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />


                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerviewDashboardRevenue"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clipToPadding="false"
                            android:overScrollMode="never"
                            android:paddingBottom="16dp"
                            android:scrollbars="none"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="1"
                            tools:listitem="@layout/dashboard_revenue_item" />

                        <com.scwang.smart.refresh.footer.ClassicsFooter
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
