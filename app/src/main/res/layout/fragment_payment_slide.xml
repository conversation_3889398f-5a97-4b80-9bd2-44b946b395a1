<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!--    &lt;!&ndash; 顶部状态栏占位 &ndash;&gt;-->
    <!--    <View-->
    <!--        android:id="@+id/statusBarPlaceholder"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="24dp"-->
    <!--        android:background="@color/primaryColor" />-->

    <!-- 顶部DialogTopBar -->
    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="24dp"
        android:paddingVertical="10dp"
        app:dialog_title="@string/pay_now" />

    <!-- 主要内容区域 - 左右平分 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/color_efefef"
        android:orientation="horizontal"
        android:padding="10dp">

        <!-- 左侧内容区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 左侧第一块 - 100dp -->
            <LinearLayout
                android:id="@+id/leftBlock1"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_4dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="左侧区域1"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

            <!-- 左侧第二块 - 100dp -->
            <LinearLayout
                android:id="@+id/leftBlock2"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_4dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="左侧区域2"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

            <!-- 左侧第三块 - 剩余空间 -->
            <LinearLayout
                android:id="@+id/leftBlock3"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_radius_4dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="左侧区域3 - 剩余空间"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 右侧内容区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="6dp">

            <!-- 右侧上半部分 - RecyclerView布局 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rightBlockTop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:itemCount="9"
                tools:layout_height="400dp"
                tools:listitem="@layout/item_grid_payment" />


            <!-- 右侧下半部分 - LinearLayout布局 -->
            <LinearLayout
                android:id="@+id/rightBlockBottom"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- 第一行 - 4个TextView -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="3dp"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/grid_row1_col1"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row1_col2"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="2"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row1_col3"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="3"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row1_col4"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="+"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                </LinearLayout>

                <!-- 第二行 - 4个TextView -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="3dp"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/grid_row2_col1"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="4"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row2_col2"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="5"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row2_col3"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="6"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row2_col4"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="-"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                </LinearLayout>

                <!-- 第三行 - 4个TextView -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="3dp"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/grid_row3_col1"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="7"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row3_col2"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="8"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row3_col3"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="9"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row3_col4"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="×"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                </LinearLayout>

                <!-- 第四行 - 2个TextView (1格 + 3格) -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/grid_row4_col1"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="1"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/grid_row4_col2"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="3dp"
                        android:layout_weight="3"
                        android:background="@drawable/background_white_radius_4dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="确认"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
