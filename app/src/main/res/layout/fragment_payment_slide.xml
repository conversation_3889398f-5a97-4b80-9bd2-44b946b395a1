<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:orientation="vertical">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="24dp"
        android:paddingTop="25dp"
        app:dialog_title="@string/pay_now" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvPaymentChannel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="20dp"
        android:overScrollMode="never"
        android:scrollbars="none"
        tools:itemCount="3"
        tools:listitem="@layout/item_payment_channel" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clQrCode"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone">

        <ImageView
            android:id="@+id/imgQR"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_logo" />

        <TextView
            android:id="@+id/tvScanQRAmount"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="@color/black"
            android:textSize="@dimen/_20ssp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgQR"
            tools:text="$10.00" />

        <TextView
            android:id="@+id/tvScanQRName"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvScanQRAmount"
            tools:text="Store Name" />

        <TextView
            android:id="@+id/tvQrCodeTips"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/scan_qr_code_to_pay"
            android:textColor="@color/gray_666"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvScanQRName" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBalance"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone">

        <TextView
            android:id="@+id/tvBalanceTitle"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/balance_payment"
            android:textColor="@color/black"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvCurrentBalance"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvBalanceTitle"
            tools:text="Current Balance: $100.00" />

        <TextView
            android:id="@+id/tvBalanceInsufficient"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/balance_insufficient"
            android:textColor="@color/red"
            android:textSize="@dimen/_14ssp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvCurrentBalance" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clCash"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/llCashInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/flUsdAmount"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_border_gray_radius_8">

                <EditText
                    android:id="@+id/edtUsdAmount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent"
                    android:hint="USD Amount"
                    android:inputType="numberDecimal"
                    android:paddingHorizontal="16dp"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/flKhrAmount"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_border_gray_radius_8">

                <EditText
                    android:id="@+id/edtKhrAmount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent"
                    android:hint="KHR Amount"
                    android:inputType="numberDecimal"
                    android:paddingHorizontal="16dp"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp" />

            </FrameLayout>

        </LinearLayout>

        <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView2
            android:id="@+id/viewKeyBoard"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/viewCustomInput"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llCashInput" />

        <com.metathought.food_order.casheir.ui.widget.CustomInputView
            android:id="@+id/viewCustomInput"
            android:layout_width="200dp"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llCashInput" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/llOther"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/other_payment_method"
            android:textColor="@color/black"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginVertical="20dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btnMixedPayment"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginEnd="20dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_border_primary_radius_12"
            android:gravity="center"
            android:minWidth="200dp"
            android:paddingHorizontal="10dp"
            android:text="@string/mixed_payment"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />

        <TextView
            android:id="@+id/btnDone"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/background_primary_radius_12"
            android:gravity="center"
            android:minWidth="200dp"
            android:paddingHorizontal="10dp"
            android:text="@string/pay_now"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
