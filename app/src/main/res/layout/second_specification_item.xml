<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutMain"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:background="@drawable/selector_rounded_rectangle_border"
    android:gravity="center_vertical"
    android:padding="8dp">

    <TextView
        android:id="@+id/tvDishedNameAndPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:ellipsize="end"
        android:maxLines="3"
        android:paddingHorizontal="8dp"
        tools:text="Hello $9.99"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvOriginalPrice"
        tools:text="12.99"
        tools:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="15dp"
        android:ellipsize="end"
        android:foreground="@drawable/strike_price"
        android:maxLines="3"
        android:textColor="@color/black40"
        android:textSize="@dimen/_12ssp"
        android:visibility="gone" />
</LinearLayout>
