<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="25dp"
            android:gravity="center_vertical"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/edit_member"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingHorizontal="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:layout_marginTop="16dp"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutCustomerName"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/user_nickname"
                android:textColorHint="@color/black60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtCustomerName"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text|textNoSuggestions"
                    android:maxLength="@integer/name_max_length"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <FrameLayout
                android:id="@+id/flPhoneNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutPhoneNumber"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:hint="@string/mobile_phone_number"
                    android:textColorHint="@color/black60"
                    app:startIconDrawable="@drawable/ic_rectangle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPhoneNumber"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:inputType="phone"
                        android:maxLength="15"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.hbb20.CountryCodePicker
                    android:id="@+id/countryCodeHolder"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:visibility="visible"
                    app:ccpDialog_keyboardAutoPopup="false"
                    app:ccp_defaultPhoneCode="855"
                    app:ccp_showFlag="false"
                    app:ccp_showNameCode="false"
                    app:ccp_textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/flValidateCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutValidateCode"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:hint="@string/validate_code"
                    android:textColorHint="@color/black60"
                    app:endIconDrawable="@drawable/ic_rectangle"
                    app:errorIconDrawable="@null">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtValidateCode"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:inputType="number"
                        android:maxLength="4"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>


                <TextView
                    android:id="@+id/tvGetValidateCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="12dp"
                    android:drawablePadding="8dp"
                    android:paddingVertical="10dp"
                    android:text="@string/get_validate_code"
                    android:textColor="@color/primaryColor"
                    android:textSize="14sp"
                    app:drawableStartCompat="@drawable/ic_line" />
            </FrameLayout>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/btnConfirm"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/button_login_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/confirm2"
                android:textColor="@color/mainWhite"
                android:textSize="18sp" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
