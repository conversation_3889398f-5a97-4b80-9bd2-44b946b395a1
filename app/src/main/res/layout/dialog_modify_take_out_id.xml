<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/modify_take_out_order_id" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="42dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutId"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:clickable="false"
                android:enabled="false"
                android:focusable="false"
                android:hint="@string/take_out_order_id_require"
                android:orientation="horizontal"

                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:clickable="false"
                    android:cursorVisible="false"
                    android:enabled="false"
                    android:focusable="false"
                    android:gravity="center_vertical"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingVertical="8dp"
                    android:singleLine="true"
                    android:text=" "
                    android:textColor="@color/black"
                    android:textColorHint="@color/black40"
                    android:textSize="@dimen/_16ssp" />

            </com.google.android.material.textfield.TextInputLayout>

            <EditText
                android:id="@+id/edtId"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:layout_marginTop="7dp"
                android:background="@color/transparent"
                android:maxLength="60"
                android:maxLines="1"
                android:paddingHorizontal="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnYes"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="40dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:text="@string/save"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            app:backgroundTint="@color/primaryColor"
            app:cornerRadius="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:strokeColor="@color/primaryColor"
            app:strokeWidth="0dp" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
