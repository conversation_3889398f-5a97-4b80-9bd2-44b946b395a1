<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_marginEnd="6dp"
    android:layout_weight="1"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="0dp"
    app:strokeColor="@color/white"
    tools:layout_width="300dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:gravity="top"
                android:textColor="@color/black"
                android:textSize="@dimen/_12ssp"
                android:textStyle="bold"
                tools:text="test" />

            <ImageView
                android:id="@+id/tvTitleCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:src="@drawable/icon_circle_warn"
                android:visibility="visible"
                tools:visibility="visible" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvValue"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginStart="16dp"
                android:layout_weight="1.5"
                android:autoSizeMaxTextSize="20sp"
                android:autoSizeMinTextSize="11sp"
                android:autoSizeTextType="uniform"
                android:maxLines="1"
                android:paddingTop="5dp"
                android:text="@string/default_data_showing"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvValueYesterday"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:autoSizeMaxTextSize="14sp"
                android:autoSizeMinTextSize="11sp"
                android:autoSizeTextType="uniform"
                android:gravity="top"
                android:maxLines="1"
                android:paddingTop="5dp"
                android:text="@string/yesterday"
                android:textColor="@color/black60"
                tools:text="Yesterday $888.88" />
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>