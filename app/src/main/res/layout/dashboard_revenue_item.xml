<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvDate"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="2024/02/02" />

    <TextView
        android:id="@+id/tvRevenueAmount"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="$9999.99" />

    <TextView
        android:id="@+id/tvActualRevenue"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="$9999.99" />

    <TextView
        android:id="@+id/tvOrder"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight=".7"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="1111" />

    <TextView
        android:id="@+id/tvPaidOrder"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="112" />

    <TextView
        android:id="@+id/tvRefundOrder"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="90" />

</LinearLayout>