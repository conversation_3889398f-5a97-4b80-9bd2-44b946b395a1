<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_14ssp"
        android:text="title" />


    <TextView
        android:id="@+id/tvContent"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        tools:text="content" />

</LinearLayout>