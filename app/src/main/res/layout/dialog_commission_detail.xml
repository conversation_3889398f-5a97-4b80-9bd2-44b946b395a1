<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="24dp"
            android:paddingVertical="23dp" />


        <LinearLayout
            android:id="@+id/layoutMainOrdered"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo">

            <LinearLayout
                android:id="@+id/layoutFood"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="30dp"
                android:layout_weight="1"
                android:background="@drawable/background_f5f5f5_radius_16"
                android:orientation="vertical"
                android:paddingHorizontal="20dp">

                <LinearLayout
                    android:id="@+id/layoutHeader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="2"
                        android:drawablePadding="3dp"
                        android:text="@string/items"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/quantity"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1.2"
                        android:gravity="center_horizontal"
                        android:text="@string/commission_percent"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/commission"
                        android:textColor="@color/black60"
                        android:textSize="12sp" />
                </LinearLayout>

                <View style="@style/commonDividerStyle" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerOrderedFood"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:paddingBottom="10dp"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/service_fee_detail_item" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="30dp"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="35dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:layout_marginEnd="26dp"
                    android:layout_weight="1"
                    android:gravity="end|center_vertical"
                    android:text="@string/total2"
                    android:textColor="@color/black80"
                    android:textSize="@dimen/_18ssp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvCommissionPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_24ssp"
                    tools:text="$8.00" />


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>