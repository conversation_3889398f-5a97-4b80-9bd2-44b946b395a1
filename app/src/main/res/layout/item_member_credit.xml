<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:orientation="horizontal">


    <TextView
        android:id="@+id/tvAccountName"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="林超正超正常的..." />

    <TextView
        android:id="@+id/tvAccountNumber"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="***********" />


    <TextView
        android:id="@+id/tvCreditAmount"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="$100" />


    <TextView
        android:id="@+id/tvReceivePayment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.3"
        android:paddingStart="16dp"
        android:paddingEnd="0dp"
        android:text="@string/receive_payment"
        android:textColor="@color/primaryColor"
        android:textSize="16sp" />


    <TextView
        android:id="@+id/tvDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.3"
        android:paddingHorizontal="16dp"
        android:text="@string/detail"
        android:textColor="@color/primaryColor"
        android:textSize="16sp" />

</LinearLayout>