<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="center_vertical"
            android:paddingBottom="24dp">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/detail"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textColor="@color/black"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            tools:text="@string/announcement" />

        <TextView
            android:id="@+id/tvTime"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="start"
            android:textColor="@color/black40"
            android:textSize="@dimen/_12ssp"
            tools:text="@string/announcement" />

        <!--        <TextView-->
        <!--            android:id="@+id/tvContent"-->
        <!--            style="@style/FontLocalization"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="0dp"-->
        <!--            android:layout_marginTop="24dp"-->
        <!--            android:layout_weight="1"-->
        <!--            android:gravity="start"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="15sp"-->
        <!--            android:textStyle="bold"-->
        <!--            tools:text="@string/announcement" />-->
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scrollbars="none" />

    </LinearLayout>


    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
