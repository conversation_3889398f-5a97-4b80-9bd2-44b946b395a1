<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@drawable/selector_payment_item_bg"
    android:gravity="center"
    android:minWidth="124dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvChannel"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@drawable/selector_payment_item_color"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="现金支付" />

    <TextView
        android:id="@+id/tvChannelType"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@drawable/selector_payment_item_color2"
        android:textSize="@dimen/_12ssp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="现金支付" />

</LinearLayout>