<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTime"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/btnClose"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/btnClose"
            tools:text="04:55" />

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="5dp"
            android:src="@drawable/ic_cross_closed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:id="@+id/layoutQR"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/rectangle"
            android:clipToPadding="false"
            android:orientation="vertical"
            android:padding="1dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTime"
            app:layout_constraintWidth_percent="0.8">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/top_khqr"
                android:src="@drawable/ic_khqr_logo" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/info_khqr"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingLeft="30dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvScanQRName"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="60dp"
                    android:ellipsize="end"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="$KUNTHEA SOT" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvScanQRAmount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/black"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="$673.57" />
            </LinearLayout>

            <ImageView
                android:id="@+id/imgQR"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:padding="30dp" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>