<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 第一行 - 4个TextView -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/grid_row1_col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row1_col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="2"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row1_col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="3"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row1_col4"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="+"
            android:textColor="@color/black"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- 第二行 - 4个TextView -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/grid_row2_col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="4"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row2_col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="5"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row2_col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="6"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row2_col4"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="-"
            android:textColor="@color/black"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- 第三行 - 4个TextView -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/grid_row3_col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row3_col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="8"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row3_col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="9"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row3_col4"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="×"
            android:textColor="@color/black"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- 第四行 - 2个TextView (1格 + 3格) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/grid_row4_col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/grid_row4_col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/black"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>
