<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvTagNameEn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:textStyle="bold"
        android:visibility="visible"
        tools:text="Large Normal ice, Normal sugar"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTagNameKm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="Large Normal ice, Normal sugar"
        tools:visibility="visible" />

</LinearLayout>
