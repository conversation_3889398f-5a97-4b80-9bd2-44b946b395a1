<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="200px"
                android:layout_height="200px"
                android:layout_gravity="center"
                android:layout_marginTop="22px"
                android:scaleType="centerCrop"
                tools:background="@color/black" />

            <LinearLayout
                android:id="@+id/llStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvFirstStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="20px"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    tools:text="JM美味海鲜餐厅(体验店)" />

            </LinearLayout>


            <View
                android:id="@+id/lineTop"
                style="@style/ticketDividerStyle"
                android:layout_marginVertical="22px" />

            <LinearLayout
                android:id="@+id/llOrderType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tvOrderStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold"
                    tools:text="挂账-未支付" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRepaymentTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvRepaymentTime0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/repayment_time"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />


                <TextView
                    android:id="@+id/tvRepaymentTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="2023/03/12 13:09" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llNickname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvNickname0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/customer_nickname"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />


                <TextView
                    android:id="@+id/tvNickname"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="LEE" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvAccount0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/customer_account"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvAccount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="*********" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRepaymentAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvRepaymentAmount0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/repayment_amount"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvRepaymentAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$50.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCreditAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvCreditAmount0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/credit_amount"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvCreditAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$50.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPayChannel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvPayChannel0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/pay_channel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPayChannel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="线下支付" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaidByBalance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvPaidByBalance0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/print_title_paid_by_balance"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPaidByBalance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$ 40.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAccountBalance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvAccountBalance0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/customer_balance"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvAccountBalance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$ 40.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMixedPayment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvMixedPayChannel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="线下-ABA" />

                <TextView
                    android:id="@+id/tvMixedPayAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$ 40.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPayByCash"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvPayByCash0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/pay_by_cash"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPayByCash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$ 40.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llChangeAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvChangeAmount0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/print_title_change"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvChangeAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="$ 40.05" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOrderNum"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvOrderNum0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/order_num"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvOrderNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="5" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOrderInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvOrderInfo0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/order_info"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/item_credit_record_menu"
                tools:visibility="visible" />


            <LinearLayout
                android:id="@+id/llPaymentQrCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible">

                <View
                    style="@style/ticketDividerStyle"
                    android:layout_marginVertical="0dp" />

                <ImageView
                    android:id="@+id/ivOrderQr"
                    android:layout_width="250px"
                    android:layout_height="250px"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginVertical="10px" />

            </LinearLayout>


            <include
                android:id="@+id/vFooter"
                layout="@layout/view_printer_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>