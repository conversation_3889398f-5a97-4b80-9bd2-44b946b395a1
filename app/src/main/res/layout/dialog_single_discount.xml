<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="560dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:paddingBottom="24dp"
            app:dialog_title="@string/discount_single_good" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDiscountSingleGood"
            android:layout_width="match_parent"
            android:layout_height="238dp"
            android:background="@drawable/background_efefef_40_radius_20dp"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="2"
            tools:listitem="@layout/single_discount_item" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutRemark"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:layout_marginTop="16dp"
            android:hint="@string/please_enter_the_reason"
            android:textColorHint="@color/black60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtRemark"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawablePadding="10dp"
                android:gravity="top|start"
                android:inputType="textMultiLine"
                android:maxLength="1000"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="25dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnNo"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/white"
                app:cornerRadius="12dp"
                app:elevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnYes"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm2"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/primaryColor"
                app:strokeWidth="0dp" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
