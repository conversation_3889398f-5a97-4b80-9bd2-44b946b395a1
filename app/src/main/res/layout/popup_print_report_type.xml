<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:background="@drawable/background_popup_bg_mainwhite"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">


    <TextView
        android:id="@+id/tvProduct"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/product_report"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvPayment"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/payment_method_report"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvSaleReport"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:fontFamily="@font/roboto"
        android:gravity="start|center_vertical"
        android:text="@string/sales_report"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        android:visibility="visible"
        tools:visibility="visible" />

</LinearLayout>