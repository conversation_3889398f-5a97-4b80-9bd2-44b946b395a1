<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_f5f5f5_radius_20"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="end"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/coupon"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCouponList"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_dialog_coupon_list" />

                <include
                    android:id="@+id/layoutEmpty"
                    layout="@layout/layout_empty_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center"
                    android:visibility="gone" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/btnIdentifyCoupon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/icon_scanner_green" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnConfirm"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:text="@string/confirm2"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primaryColor"
                    app:cornerRadius="12dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>