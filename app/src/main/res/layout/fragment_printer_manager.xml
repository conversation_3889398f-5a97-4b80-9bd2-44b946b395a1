<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    android:padding="6dp">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/background_white_top_radius_16dp"
            android:gravity="center_vertical"
            android:minHeight="70dp">

            <TextView
                android:id="@+id/tvPrinterName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/printer_name"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvPrinterType"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/printer_type"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvPrinterDevice"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/printer_device"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvBindingTime"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.4"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/binding_time"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvReceiptSize"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/ticket_scale"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvTemplateConfigured"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.8"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/template_configured"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvConnectionStatus"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.2"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/connection_status"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <!--            <TextView-->
            <!--                android:id="@+id/tvOperate"-->
            <!--                style="@style/FontLocalization"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_weight="1.2"-->
            <!--                android:gravity="center"-->
            <!--                android:text="@string/operate"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="@dimen/_16ssp" />-->

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:background="@drawable/background_white_bottom_radius_16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="30"
            tools:listitem="@layout/item_manager_printer" />


    </LinearLayout>


    <include
        android:id="@+id/layoutEmptyDetail"
        layout="@layout/layout_empty_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>
