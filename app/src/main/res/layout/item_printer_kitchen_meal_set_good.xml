<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!--        <LinearLayout-->
        <!--            android:id="@+id/llFeedName"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_gravity="end"-->
        <!--            android:layout_weight="5"-->
        <!--            android:orientation="vertical"-->
        <!--            android:textColor="@color/black50"-->
        <!--            android:textSize="14sp">-->

        <TextView
            android:id="@+id/tvFeedNameEn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="5"
            android:ellipsize="end"
            android:textColor="@color/black"
            android:textSize="@dimen/_printer_default_sp"
            android:textStyle="bold"
            tools:text="+ Large Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugar" />
        <!--        </LinearLayout>-->
        <!---->
        <!--        <LinearLayout-->
        <!--            android:id="@+id/llFeedFoodCount"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_gravity="center_horizontal"-->
        <!--            android:layout_weight="1"-->
        <!--            android:orientation="vertical">-->

        <TextView
            android:id="@+id/tvFeedFoodCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.2"
            android:gravity="center_horizontal"
            android:textColor="@color/black"
            android:textSize="@dimen/_printer_default_sp"
            android:textStyle="bold"
            tools:text="x1" />
        <!--        </LinearLayout>-->
    </LinearLayout>

    <TextView
        android:id="@+id/tvFeedNameKm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="Large Normal ice, Normal sugar"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMealGoodTag"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="30px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="2"
        tools:listitem="@layout/item_printer_kitchen_meal_set_good_tag" />

</LinearLayout>
