<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="96dp"
    android:layout_height="match_parent"
    android:background="@drawable/background_white_16">

    <LinearLayout
        android:id="@+id/layoutWrap"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="5dp"
        android:paddingVertical="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <cn.bingoogolapple.badgeview.BGABadgeFrameLayout
            android:id="@+id/badgeFrame"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingHorizontal="20dp">

            <ImageView
                android:id="@+id/imgIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_table"
                app:tint="@color/white" />

        </cn.bingoogolapple.badgeview.BGABadgeFrameLayout>

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:maxLines="1"
            android:text="@string/customer"
            android:textColor="@color/white"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgIcon" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>