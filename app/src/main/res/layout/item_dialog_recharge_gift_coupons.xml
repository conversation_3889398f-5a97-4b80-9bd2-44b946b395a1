<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@drawable/background_white_border_black12_radius_12"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:paddingBottom="21dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/llLeftPart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingEnd="10dp"
            app:layout_constraintBottom_toBottomOf="@id/llRightPart"
            app:layout_constraintEnd_toStartOf="@id/llRightPart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/llRightPart"
            app:layout_constraintWidth_percent="0.25">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvUnit"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="2dp"
                    android:gravity="center"
                    android:text="$"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvAmount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    tools:text="9.99" />

                <TextView
                    android:id="@+id/tvPercent"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:gravity="center"
                    android:text="% 0FF"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="gone" />

            </LinearLayout>


            <TextView
                android:id="@+id/tvLimitAmount"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black60"
                android:textSize="@dimen/_12ssp"
                tools:text="满50可用满50可用满50可用满50可用满50可用满50可用" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llRightPart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/llLeftPart"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.75">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCouponName"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="优惠券名称名很长的样式" />

                    <TextView
                        android:id="@+id/tvEffectiveTime"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black40"
                        android:textSize="@dimen/_11ssp"
                        tools:text="2024/03/05 12:00 - 2024/03/05 12:00" />


                    <TextView
                        android:id="@+id/tvGiftGoodNum"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        android:drawablePadding="4dp"
                        android:text="@string/gift_products_num"
                        android:textColor="@color/primaryColor"
                        app:drawableEndCompat="@drawable/icon_arrow_down" />


                </LinearLayout>

                <ImageView
                    android:id="@+id/checkbox"
                    style="@style/FontLocalization"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    android:scaleType="centerCrop"
                    android:src="@drawable/coupon_checkbox_circle_drawable"
                    android:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvGiftGoods"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/background_f5f5f5_radius_6"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="@id/llRightPart"
            app:layout_constraintStart_toStartOf="@id/llRightPart"
            app:layout_constraintTop_toBottomOf="@id/llRightPart"
            tools:itemCount="3"
            tools:listitem="@layout/item_coupon_good" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="vDLine,tvCouponNumTitle,etCouponNum" />

    <View
        android:id="@+id/vDLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/imaginary_line"
        android:layerType="software"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llTop" />

    <TextView
        android:id="@+id/tvCouponNumTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="15dp"
        android:layout_marginStart="10dp"
        android:text="@string/coupon_num"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vDLine" />

    <EditText
        android:id="@+id/etCouponNum"
        android:layout_width="100dp"
        android:layout_height="30dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/background_white_border_black08_radius_8"
        android:gravity="center"
        android:imeOptions="actionDone"
        android:inputType="number"
        android:textColor="@color/black80"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vDLine"
        tools:text="1" />
</androidx.constraintlayout.widget.ConstraintLayout>