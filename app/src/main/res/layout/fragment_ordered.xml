<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutFirst"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="6dp"
        android:background="@color/mainBackground"
        android:paddingHorizontal="6dp"
        android:paddingTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTopPart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivFilterToogle"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:paddingEnd="6dp"
                android:src="@drawable/selector_order_filter_toogle"
                app:layout_constraintBottom_toBottomOf="@+id/llFilterBar1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/llFilterBar1" />


            <LinearLayout
                android:id="@+id/llFilterBar1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toStartOf="@+id/tvClearFilter"
                app:layout_constraintStart_toEndOf="@+id/ivFilterToogle"
                app:layout_constraintTop_toTopOf="parent">

                <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                    android:id="@+id/edtSearch"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    app:search_hint="@string/hint_order_id_and_phone_number" />

                <com.metathought.food_order.casheir.ui.widget.CalendarTextView
                    android:id="@+id/tvCalendar"
                    style="@style/commonCalendarTextViewStyle"
                    android:layout_width="0dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    tools:text="01 03, 2024 - 01 03, 2024" />

                <LinearLayout
                    android:id="@+id/dropdownFilter"
                    android:layout_width="0dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_language_spiner"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="10dp">

                    <TextView
                        android:id="@+id/tvFilter"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="start"
                        android:text="@string/all_order"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/arrowFilter"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_dropdown"
                        android:visibility="visible"
                        tools:ignore="ContentDescription" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFilterBar2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/llFilterBar1"
                app:layout_constraintStart_toStartOf="@+id/llFilterBar1"
                app:layout_constraintTop_toBottomOf="@+id/llFilterBar1"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/dropdownTable"
                    android:layout_width="0dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_language_spiner"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="10dp">

                    <TextView
                        android:id="@+id/tvTable"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:gravity="start"
                        android:maxLines="1"
                        android:text="@string/all_table"
                        android:textColor="@color/black"
                        android:textSize="12sp"
                        tools:text="" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cardViewFilterTable"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        android:visibility="gone"
                        app:cardBackgroundColor="@color/main_red"
                        app:cardCornerRadius="12.5dp"
                        app:cardElevation="0dp"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvCount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="25dp"
                            android:gravity="center_vertical|end"
                            android:maxLines="1"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="3dp"
                            android:text="+1"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold" />
                    </androidx.cardview.widget.CardView>

                    <ImageView
                        android:id="@+id/arrowTable"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_dropdown"
                        android:visibility="visible"
                        tools:ignore="ContentDescription" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/dropdownNickname"
                    android:layout_width="0dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_language_spiner"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="10dp">

                    <TextView
                        android:id="@+id/tvFilterNickname"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="start"
                        android:text="@string/customer_nickname"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/arrowNickname"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_dropdown"
                        android:visibility="visible"
                        tools:ignore="ContentDescription" />
                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1" />
            </LinearLayout>


            <TextView
                android:id="@+id/tvClearFilter"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="10dp"
                android:text="@string/clear_filter"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBottom_toBottomOf="@+id/llFilterBar1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/llFilterBar1" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutPendingList"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingEnd="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutTopPart"
            app:layout_constraintWidth_percent="0.5">

            <androidx.cardview.widget.CardView
                android:id="@+id/contentCardView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toTopOf="@id/clBottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/refreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.scwang.smart.refresh.header.MaterialHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/orderListRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="16dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="10"
                        tools:listitem="@layout/ordered_item" />

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>
            </androidx.cardview.widget.CardView>


            <LinearLayout
                android:id="@+id/clBottom"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="6dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/contentCardView">

                <LinearLayout
                    android:id="@+id/btnUnRead"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_white_radius_12dp"
                    android:paddingHorizontal="12dp"
                    app:layout_constraintStart_toStartOf="parent">

                    <TextView
                        android:id="@+id/tvUnReadTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:drawablePadding="4dp"
                        android:gravity="center"
                        android:text="@string/unread"
                        android:textColor="@color/black"
                        app:drawableStartCompat="@drawable/icon_unread" />

                    <TextView
                        android:id="@+id/tvUnRead"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:drawablePadding="4dp"
                        android:gravity="end"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnUnPrint"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_white_radius_12dp"
                    android:paddingHorizontal="12dp"
                    app:layout_constraintStart_toStartOf="parent">

                    <TextView
                        android:id="@+id/tvUnPrintTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:drawablePadding="4dp"
                        android:gravity="center"
                        android:text="@string/unprint"
                        android:textColor="@color/black"
                        app:drawableStartCompat="@drawable/icon_unprint" />

                    <TextView
                        android:id="@+id/tvUnPrint"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:drawablePadding="4dp"
                        android:gravity="end"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>

            <ProgressBar
                android:id="@+id/pbOrderedList"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutMenu"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layoutPendingList"
            app:layout_constraintTop_toBottomOf="@id/layoutTopPart"
            app:layout_constraintWidth_percent="0.5">

            <!--                <FrameLayout-->
            <!--                    android:id="@+id/layoutTableID"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginEnd="10dp"-->
            <!--                    app:layout_constraintEnd_toStartOf="@id/cardStatus"-->
            <!--                    app:layout_constraintStart_toStartOf="parent"-->
            <!--                    app:layout_constraintTop_toTopOf="parent">-->

            <!--                    <TextView-->
            <!--                        android:id="@+id/tvTableID"-->
            <!--                        style="@style/FontLocalization"-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:ellipsize="end"-->
            <!--                        android:maxLines="1"-->
            <!--                        android:text=""-->
            <!--                        android:textColor="@color/black"-->
            <!--                        android:textSize="24sp"-->
            <!--                        android:textStyle="bold"-->
            <!--                        tools:text="@tools:sample/lorem/random" />-->
            <!--                </FrameLayout>-->

            <!--                <androidx.cardview.widget.CardView-->
            <!--                    android:id="@+id/cardStatus"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:elevation="0dp"-->
            <!--                    app:cardBackgroundColor="@color/confirm_backgroud_color"-->
            <!--                    app:cardCornerRadius="5dp"-->
            <!--                    app:cardElevation="0dp"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:layout_constraintHorizontal_bias="1"-->
            <!--                    app:layout_constraintStart_toEndOf="@id/layoutTableID"-->
            <!--                    app:layout_constraintTop_toTopOf="parent">-->

            <!--                    <TextView-->
            <!--                        android:id="@+id/imgDelete"-->
            <!--                        style="@style/FontLocalization"-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:gravity="center_vertical|end"-->
            <!--                        android:maxLines="1"-->
            <!--                        android:paddingHorizontal="10dp"-->
            <!--                        android:paddingVertical="5dp"-->
            <!--                        android:text=""-->
            <!--                        android:textColor="@color/confirm_text_color"-->
            <!--                        android:textSize="@dimen/_16ssp"-->
            <!--                        android:textStyle="bold" />-->
            <!--                </androidx.cardview.widget.CardView>-->


            <LinearLayout
                android:id="@+id/llWaitReceiveLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_12dp"
                android:orientation="vertical"
                android:padding="6dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/llMenu"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="6dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:text="@string/add_good_info"
                        android:textColor="@color/black60"
                        android:textSize="14sp" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cardAcceptStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:cardBackgroundColor="@color/confirm_backgroud_color"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintStart_toEndOf="@id/layoutTableID"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tvReceiveState"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical|end"
                            android:maxLines="1"
                            android:paddingHorizontal="10dp"
                            android:paddingVertical="5dp"
                            android:textColor="@color/confirm_text_color"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="@string/wait_accept_order" />
                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <View style="@style/commonDividerStyle" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/receiverInfoRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="6dp"
                        android:maxHeight="90dp"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:itemCount="1"
                        tools:listitem="@layout/pending_order_menu_item" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <TextView
                    android:id="@+id/tvViewAllReceiveInfo"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="14dp"
                    android:text="@string/view_all"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp" />

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/llReceiveButtons"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:divider="@drawable/shape_option_item_pading_12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:showDividers="middle">

                    <LinearLayout
                        android:id="@+id/btnNoAccept"
                        android:layout_width="wrap_content"
                        android:layout_height="50dp"
                        android:background="@drawable/button_outline_background_enable"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:minWidth="100dp"
                        android:orientation="horizontal"
                        android:paddingHorizontal="20dp"
                        android:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/no_accept_order"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_18ssp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/btnAcceptOrder"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_weight="1"
                        android:background="@drawable/button_login_background"
                        android:gravity="center"
                        android:text="@string/receiving_orders"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        android:visibility="visible" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <!--                <LinearLayout-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:orientation="vertical">-->

                <!--                    <View style="@style/commonDividerStyle" />-->

                <!--                    <LinearLayout-->
                <!--                        android:id="@+id/llCancelTime"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_marginTop="12dp"-->
                <!--                        android:visibility="gone"-->
                <!--                        tools:visibility="visible">-->

                <!--                        <TextView-->
                <!--                            style="@style/FontLocalization"-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_marginEnd="15dp"-->
                <!--                            android:text="@string/cancel_time"-->
                <!--                            android:textColor="@color/black60"-->
                <!--                            android:textSize="@dimen/_14ssp" />-->

                <!--                        <TextView-->
                <!--                            android:id="@+id/tvCancelTime"-->
                <!--                            style="@style/FontLocalization"-->
                <!--                            android:layout_width="0dp"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_weight="1"-->
                <!--                            android:gravity="end"-->
                <!--                            android:maxLines="2"-->
                <!--                            android:textColor="@color/black"-->
                <!--                            android:textSize="@dimen/_14ssp"-->
                <!--                            android:textStyle="bold"-->
                <!--                            tools:text="2024/04/02 12:00:00" />-->
                <!--                    </LinearLayout>-->

                <!--                    <LinearLayout-->
                <!--                        android:id="@+id/llCancelReason"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_marginTop="12dp"-->
                <!--                        android:visibility="gone"-->
                <!--                        tools:visibility="visible">-->

                <!--                        <TextView-->
                <!--                            style="@style/FontLocalization"-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_marginEnd="15dp"-->
                <!--                            android:text="@string/cancel_reason"-->
                <!--                            android:textColor="@color/black60"-->
                <!--                            android:textSize="@dimen/_14ssp" />-->


                <!--                        <com.metathought.food_order.casheir.ui.widget.ExpandTextView-->
                <!--                            android:id="@+id/tvCancelReason"-->
                <!--                            style="@style/FontLocalization"-->
                <!--                            android:layout_width="0dp"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_weight="1"-->
                <!--                            android:gravity="end"-->
                <!--                            android:textColor="@color/black"-->
                <!--                            android:textSize="@dimen/_14ssp"-->
                <!--                            android:textStyle="bold"-->
                <!--                            android:visibility="visible" />-->

                <!--                    </LinearLayout>-->
                <!--                </LinearLayout>-->
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMenu"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_12dp"
                android:orientation="vertical"
                android:padding="6dp"
                app:layout_constraintBottom_toTopOf="@id/llReceiveCancelLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llWaitReceiveLayout">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/orderedInfoRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/pending_order_menu_item" />

                <com.metathought.food_order.casheir.ui.widget.OrderGiftListView
                    android:id="@+id/viewOrderGiftList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llReceiveCancelLayout"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_12dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/llOrderButtons"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llMenu"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="10dp"
                    android:text="@string/add_good_info"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardCancelAcceptStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:elevation="0dp"
                    app:cardBackgroundColor="@color/ordered_cancel_color"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@id/layoutTableID"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvCancelReceiveState"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical|end"
                        android:maxLines="1"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="5dp"
                        android:text="@string/order_cancel"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp" />
                </androidx.cardview.widget.CardView>

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvCancelReceiveView"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="5dp"
                    android:text="@string/review"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llOrderButtons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:divider="@drawable/shape_option_item_pading_12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:showDividers="middle">

                <!--                <LinearLayout-->
                <!--                    android:id="@+id/btnOrderMore"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="50dp"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:layout_weight="1"-->
                <!--                    android:background="@drawable/button_outline_background"-->
                <!--                    android:clickable="true"-->
                <!--                    android:focusable="true"-->
                <!--                    android:gravity="center"-->
                <!--                    android:orientation="horizontal"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:visibility="visible">-->

                <!--                    <ImageView-->
                <!--                        android:layout_width="30dp"-->
                <!--                        android:layout_height="30dp"-->
                <!--                        android:indeterminate="true"-->
                <!--                        android:indeterminateTint="@color/mainWhite"-->
                <!--                        android:indeterminateTintMode="src_atop"-->
                <!--                        android:progressTint="@color/mainWhite"-->
                <!--                        android:src="@drawable/ic_plus"-->
                <!--                        tools:ignore="ContentDescription" />-->

                <!--                    <TextView-->
                <!--                        style="@style/FontLocalization"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:text="@string/order_more"-->
                <!--                        android:textColor="@color/primaryColor"-->
                <!--                        android:textSize="18sp"-->
                <!--                        android:textStyle="bold" />-->

                <!--                    <ProgressBar-->
                <!--                        android:id="@+id/pbOrderMore"-->
                <!--                        android:layout_width="25dp"-->
                <!--                        android:layout_height="25dp"-->
                <!--                        android:layout_marginStart="10dp"-->
                <!--                        android:indeterminate="true"-->
                <!--                        android:indeterminateTint="@color/mainWhite"-->
                <!--                        android:indeterminateTintMode="src_atop"-->
                <!--                        android:progressTint="@color/mainWhite"-->
                <!--                        android:visibility="gone"-->
                <!--                        tools:visibility="gone" />-->
                <!--                </LinearLayout>-->

                <!--                <TextView-->
                <!--                    android:id="@+id/btnCancelDish"-->
                <!--                    style="@style/FontLocalization"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="50dp"-->
                <!--                    android:layout_marginBottom="16dp"-->
                <!--                    android:layout_weight="1"-->
                <!--                    android:background="@drawable/button_outline_background"-->
                <!--                    android:gravity="center"-->
                <!--                    android:text="@string/cancel_dish"-->
                <!--                    android:textColor="@color/primaryColor"-->
                <!--                    android:textSize="18sp"-->
                <!--                    android:textStyle="bold"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:visibility="visible" />-->
            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/layoutEmptyList"
            layout="@layout/layout_empty_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutResumeOrder"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layoutFirst"
        app:layout_constraintTop_toTopOf="parent">

        <ScrollView
            android:id="@+id/actionScrollview"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginVertical="6dp"
            android:layout_marginEnd="6dp"
            android:background="@color/mainBackground"
            android:scrollbars="none"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/layoutDetailInfo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="12dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:id="@+id/layoutAction"
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/btnPrint"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/printer"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/btnExport"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/export_ticket"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnOrderMore"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/order_more"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnTmpGood"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:paddingHorizontal="3dp"
                        android:text="@string/add_temporary_goods"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnWeight"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/weigh"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/btnTimePrice"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/set_time_price"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="visible"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnWholeDiscount"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:paddingHorizontal="3dp"
                        android:text="@string/discount_whole_order"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnSingleDiscount"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:paddingHorizontal="3dp"
                        android:text="@string/discount_single_good"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnCoupon"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:paddingHorizontal="10dp"
                        android:text="@string/coupon"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnMerge"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:paddingHorizontal="3dp"
                        android:text="@string/merge_order"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnSplit"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/split_order"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnRemark"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/remark"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnCancelDish"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/cancel_dish"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnAntiSettlement"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/anti_settlement"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnOrderRefund"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/refund_btn"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnChangeTable"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/change_table"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <!--                <View-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="1dp"-->
                    <!--                    android:background="@color/black12" />-->

                    <TextView
                        android:id="@+id/btnCredit"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:text="@string/credit"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/btnCancelCredit"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:paddingHorizontal="3dp"
                        android:text="@string/cancel_credit"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />


                    <TextView
                        android:id="@+id/btnCancelOrder"
                        style="@style/commonActionBtnStyle"
                        android:background="@drawable/selector_action_btn"
                        android:gravity="center"
                        android:text="@string/cancel_order"
                        android:textColor="@drawable/selector_action_btn_text_color"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </ScrollView>

        <LinearLayout
            android:id="@+id/layoutDetailInfo"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginVertical="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/background_white_radius_12dp"
            android:orientation="vertical"
            android:paddingBottom="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/actionScrollview"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">


            <include
                android:id="@+id/layoutOrderInfo"
                layout="@layout/layout_order_info"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_radius_12dp"
                android:orientation="vertical" />

            <LinearLayout
                android:id="@+id/llPaymentLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/btnPay"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginHorizontal="6dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/button_login_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/pay_now"
                        android:textColor="@color/mainWhite"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold" />

                    <ProgressBar
                        android:id="@+id/pbConfirm"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="10dp"
                        android:indeterminate="true"
                        android:indeterminateTint="@color/mainWhite"
                        android:indeterminateTintMode="src_atop"
                        android:progressTint="@color/mainWhite"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/btnSubmit"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="6dp"
                android:layout_marginTop="6dp"
                android:background="@drawable/button_login_background"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/confirm_order"
                    android:textColor="@color/mainWhite"
                    android:textSize="@dimen/_18ssp"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/pbSubmit"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginStart="6dp"
                    android:indeterminate="true"
                    android:indeterminateTint="@color/mainWhite"
                    android:indeterminateTintMode="src_atop"
                    android:progressTint="@color/mainWhite"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnBackMenu"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="6dp"
                android:layout_marginTop="6dp"
                android:background="@drawable/button_login_background"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/back_to_menu"
                    android:textColor="@color/mainWhite"
                    android:textSize="@dimen/_18ssp"
                    android:textStyle="bold" />

            </LinearLayout>
        </LinearLayout>

        <include
            android:id="@+id/layoutEmptyDetail"
            layout="@layout/layout_empty_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/pbOrderedInfo"
        android:layout_width="25dp"
        android:layout_height="25dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>