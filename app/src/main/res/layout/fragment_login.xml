<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="language"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/login_background_color"
        tools:context=".ui.login.LoginFragment">

        <FrameLayout
            android:id="@+id/flContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/background_login"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.7"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5">

            <LinearLayout
                android:id="@+id/dropdownLanguage"
                android:layout_width="130dp"
                android:layout_height="40dp"
                android:layout_gravity="end"
                android:layout_marginTop="17dp"
                android:layout_marginEnd="17dp"
                android:background="@drawable/background_language_spiner"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp">

                <TextView
                    android:id="@+id/tvLanguages"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    tools:text="English" />

                <ImageView
                    android:id="@+id/arrow"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="130dp"
                android:layout_marginVertical="40dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/imgLogo"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_logo" />

                <TextView
                    android:id="@+id/tvStoreName"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:fontFamily="@font/khmer_os"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    tools:text="@string/brand_name" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/layoutError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    app:cardBackgroundColor="#1AFF3141"
                    app:cardCornerRadius="8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:strokeColor="#33F70B0E"
                    tools:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="14dp"
                        android:gravity="start"
                        android:paddingVertical="10dp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="15dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginEnd="10dp"
                            android:adjustViewBounds="true"
                            android:src="@drawable/ic_error_info" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvErrorLayout"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:maxLines="3"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            tools:text="Username or password is incorrect." />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutUserName"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginTop="10dp"
                    android:hint="@string/hint_user_name"
                    android:textColorHint="@color/bg_progress"
                    app:errorIconDrawable="@null">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtUserName"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawableStart="@drawable/ic_user_name"
                        android:drawablePadding="10dp"
                        android:maxLines="1"
                        android:padding="10dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutPassword"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginTop="10dp"
                    android:hint="@string/hint_password"
                    android:textColorHint="@color/bg_progress"
                    app:errorIconDrawable="@null"
                    app:passwordToggleDrawable="@drawable/show_password_selector"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/bg_progress">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPassword"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawableStart="@drawable/ic_password"
                        android:drawablePadding="10dp"
                        android:imeOptions="actionDone"
                        android:inputType="textPassword"
                        android:padding="10dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/bg_progress" />
                </com.google.android.material.textfield.TextInputLayout>


                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/checkboxRemember"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="5dp"
                    android:button="@null"
                    android:drawableLeft="@drawable/checkbox_circle_drawable"
                    android:drawablePadding="8dp"
                    android:text="@string/remember_password"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp"
                    android:visibility="visible" />


                <LinearLayout
                    android:id="@+id/btnLogin"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/button_login_background"
                    android:clickable="false"
                    android:enabled="false"
                    android:focusable="false"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/button_login"
                        android:textColor="@color/mainWhite"
                        android:textSize="18sp" />

                    <ProgressBar
                        android:id="@+id/pbConfirm"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="10dp"
                        android:indeterminate="true"
                        android:indeterminateTint="@color/mainWhite"
                        android:indeterminateTintMode="src_atop"
                        android:progressTint="@color/mainWhite"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>

        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/flContainer">

            <TextView
                android:id="@+id/tvVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                tools:text="v1.0.0 Version" />

            <TextView
                android:id="@+id/tvCompany"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/roboto"
                android:text="@string/power_by_mpos"
                android:textColor="@color/black"
                android:textSize="14sp"
                tools:text="Power by M-POS F&amp;B Management System" />

        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>