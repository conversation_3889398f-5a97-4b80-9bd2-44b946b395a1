<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/identify_coupons"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCouponCode"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/hint_coupon_code"
                    android:textColorHint="@color/black60"
                    app:endIconDrawable="@drawable/icon_printer_connect">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCouponCode"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingEnd="60dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />

                </com.google.android.material.textfield.TextInputLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="19dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/icon_scanner"
                    android:visibility="gone" />
            </FrameLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfirm"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="bottom"
                android:layout_marginTop="34dp"
                android:alpha="0.5"
                android:enabled="false"
                android:text="@string/identify_coupons"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="12dp" />
        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>