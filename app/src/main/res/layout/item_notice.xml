<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@android:color/transparent"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="top"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvTitle"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_marginEnd="6dp"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvNew"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="V2.6.0" />

        <TextView
            android:id="@+id/tvNew"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity=""
            android:layout_marginTop="2dp"
            android:background="@drawable/background_ff3141_radius_4dp"
            android:paddingHorizontal="6dp"
            android:paddingVertical="3dp"
            android:text="NEW"
            android:textColor="@color/white"
            android:textSize="@dimen/_10ssp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvTime"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColor="@color/black40"
        android:textSize="@dimen/_12ssp"
        tools:text="2024/09/14  17:30" />

    <View
        android:id="@+id/vBottomLine"
        style="@style/commonDividerStyle"
        android:layout_marginTop="16dp"
        android:background="@color/black12" />

</LinearLayout>