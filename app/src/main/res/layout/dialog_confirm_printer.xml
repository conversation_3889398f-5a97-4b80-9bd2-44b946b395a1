<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="564dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="25dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/manual_printing" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/layoutPrinter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="invisible"
                tools:visibility="visible">

                <!--                <TextView-->
                <!--                    style="@style/FontLocalization"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_gravity="center_vertical"-->
                <!--                    android:layout_weight="1"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:text="@string/choose_type_of_receipt"-->
                <!--                    android:textColor="@color/black50"-->
                <!--                    android:textSize="16sp"-->
                <!--                    android:textStyle="bold" />-->

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/llCheckboxCashier"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCheckboxCashier"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/cashier_receipt"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="normal" />

                        <LinearLayout
                            android:id="@+id/radioGroupCheckboxCashier"
                            android:layout_width="wrap_content"
                            android:layout_height="50dp"
                            android:layout_marginTop="10dp"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/checkboxCashier"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="10dp"
                                android:layout_weight="1"
                                android:background="@drawable/selector_rounded_rectangle_border"
                                android:button="@null"
                                android:checked="false"
                                android:drawableStart="@drawable/radiobutton_payment_icon"
                                android:drawablePadding="10dp"
                                android:gravity="center"
                                android:paddingHorizontal="20dp"
                                android:text="@string/cashier_receipt"
                                android:textColor="@drawable/selector_primary_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:buttonCompat="@null" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llToBePaid"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pre_checkout_bill"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="normal" />

                        <LinearLayout
                            android:id="@+id/radioGroupToBePaid"
                            android:layout_width="wrap_content"
                            android:layout_height="50dp"
                            android:layout_marginTop="10dp"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/checkboxPreSettlement"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="10dp"
                                android:layout_weight="1"
                                android:background="@drawable/selector_rounded_rectangle_border"
                                android:button="@null"
                                android:checked="false"
                                android:drawableStart="@drawable/radiobutton_payment_icon"
                                android:drawablePadding="10dp"
                                android:gravity="center"
                                android:paddingHorizontal="20dp"
                                android:text="@string/pre_checkout_bill"
                                android:textColor="@drawable/selector_primary_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:buttonCompat="@null" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llKitchen"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/kitchen_receipt"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="normal" />

                        <LinearLayout
                            android:id="@+id/radioGroupKitchen"
                            android:layout_width="wrap_content"
                            android:layout_height="50dp"
                            android:layout_marginTop="10dp"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/radioPart"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="10dp"
                                android:background="@drawable/selector_rounded_rectangle_border"
                                android:button="@null"
                                android:checked="false"
                                android:drawableStart="@drawable/radiobutton_payment_icon"
                                android:drawablePadding="10dp"
                                android:gravity="center"
                                android:paddingHorizontal="20dp"
                                android:text="@string/latest_purchase"
                                android:textColor="@drawable/selector_primary_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:buttonCompat="@null" />

                            <TextView
                                android:id="@+id/radioAll"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="10dp"
                                android:background="@drawable/selector_rounded_rectangle_border"
                                android:button="@null"
                                android:checked="false"
                                android:drawableStart="@drawable/radiobutton_payment_icon"
                                android:drawablePadding="10dp"
                                android:gravity="center"
                                android:paddingHorizontal="20dp"
                                android:text="@string/print_the_entire_order"
                                android:textColor="@drawable/selector_primary_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:buttonCompat="@null" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_stickers"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="normal" />

                        <LinearLayout
                            android:id="@+id/radioGroupLabelStickers"
                            android:layout_width="wrap_content"
                            android:layout_height="50dp"
                            android:layout_marginTop="10dp"
                            android:checkedButton="@id/radioAll"
                            android:orientation="horizontal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:id="@+id/radioLabelStickers"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="10dp"
                                android:background="@drawable/selector_rounded_rectangle_border"
                                android:button="@null"
                                android:checked="false"
                                android:drawableStart="@drawable/radiobutton_payment_icon"
                                android:drawablePadding="10dp"
                                android:gravity="center"
                                android:paddingHorizontal="20dp"
                                android:text="@string/label_stickers"
                                android:textColor="@drawable/selector_primary_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                app:buttonCompat="@null" />
                        </LinearLayout>

                    </LinearLayout>


                    <!--                    <com.google.android.material.checkbox.MaterialCheckBox-->
                    <!--                        android:id="@+id/checkboxPreSettlement"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:background="@drawable/checkbox_drawable_background"-->
                    <!--                        android:button="@null"-->
                    <!--                        android:drawableLeft="@drawable/checkbox_drawable"-->
                    <!--                        android:drawablePadding="15dp"-->
                    <!--                        android:paddingVertical="10dp"-->
                    <!--                        android:paddingStart="20dp"-->
                    <!--                        android:text="@string/pre_checkout_bill"-->
                    <!--                        android:textColor="@color/black"-->
                    <!--                        android:textSize="@dimen/_18ssp"-->
                    <!--                        android:textStyle="bold"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        tools:visibility="visible" />-->

                    <!--                    <com.google.android.material.checkbox.MaterialCheckBox-->
                    <!--                        android:id="@+id/checkboxCashier"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:background="@drawable/checkbox_drawable_background"-->
                    <!--                        android:button="@null"-->
                    <!--                        android:checked="false"-->
                    <!--                        android:drawableLeft="@drawable/checkbox_drawable"-->
                    <!--                        android:drawablePadding="15dp"-->
                    <!--                        android:paddingVertical="10dp"-->
                    <!--                        android:paddingStart="20dp"-->
                    <!--                        android:text="@string/cashier_receipt"-->
                    <!--                        android:textColor="@color/black"-->
                    <!--                        android:textSize="@dimen/_18ssp"-->
                    <!--                        android:textStyle="bold" />-->

                    <!--                    <com.google.android.material.checkbox.MaterialCheckBox-->
                    <!--                        android:id="@+id/checkboxKitchen"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:background="@drawable/checkbox_drawable_background"-->
                    <!--                        android:button="@null"-->
                    <!--                        android:drawableLeft="@drawable/checkbox_drawable"-->
                    <!--                        android:drawablePadding="15dp"-->
                    <!--                        android:paddingVertical="10dp"-->
                    <!--                        android:paddingStart="20dp"-->
                    <!--                        android:text="@string/kitchen_receipt"-->
                    <!--                        android:textColor="@color/black"-->
                    <!--                        android:textSize="@dimen/_18ssp"-->
                    <!--                        android:textStyle="bold" />-->

                    <!--                    <com.google.android.material.checkbox.MaterialCheckBox-->
                    <!--                        android:id="@+id/checkboxLabel"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:background="@drawable/checkbox_drawable_background"-->
                    <!--                        android:button="@null"-->
                    <!--                        android:drawableLeft="@drawable/checkbox_drawable"-->
                    <!--                        android:drawablePadding="15dp"-->
                    <!--                        android:paddingVertical="10dp"-->
                    <!--                        android:paddingStart="20dp"-->
                    <!--                        android:text="@string/label_print"-->
                    <!--                        android:textColor="@color/black"-->
                    <!--                        android:textSize="@dimen/_18ssp"-->
                    <!--                        android:textStyle="bold" />-->
                </LinearLayout>

                <TextView
                    android:id="@+id/tvRemindView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginTop="16dp"
                    android:drawableStart="@drawable/ic_warning"
                    android:drawablePadding="15dp"
                    android:text="@string/this_order_has_been_printed_many_times_please_be_careful_to_avoid_repeated_printing"
                    android:textColor="@color/black"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnConfirm"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_gravity="bottom"
                    android:layout_marginTop="24dp"
                    android:text="@string/printer"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primaryColor"
                    app:cornerRadius="12dp" />
            </LinearLayout>

            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </FrameLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>