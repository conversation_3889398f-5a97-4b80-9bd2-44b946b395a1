<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="2.6"
            android:orientation="vertical"
            android:text="@string/items"
            android:textColor="@color/black50"
            android:textSize="14sp">

            <TextView
                android:id="@+id/tvFoodName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:ellipsize="end"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                tools:text="Lemonade" />
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvTmpSign"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="2dp"
                    android:text="@string/temporary"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_11ssp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvDiscountActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="2dp"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_11ssp"
                    android:visibility="gone"
                    tools:text="第二份半价"
                    tools:visibility="visible" />

            </LinearLayout>
            <TextView
                android:id="@+id/tvFoodSubName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:ellipsize="end"
                android:textColor="@color/black60"
                android:textSize="@dimen/_12ssp"
                tools:text="Large Normal ice, Normal sugar"
                tools:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFoodCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:gravity="center_horizontal"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                tools:text="1" />


        </LinearLayout>

<!--        <LinearLayout-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="top"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center_horizontal"-->
<!--            android:orientation="vertical">-->

<!--            <TextView-->
<!--                android:id="@+id/tvFoodPrice"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="center_horizontal"-->
<!--                android:ellipsize="end"-->
<!--                android:gravity="center"-->
<!--                android:maxLines="2"-->
<!--                android:textColor="@color/black"-->
<!--                android:textSize="@dimen/_16ssp"-->
<!--                tools:text="$9.9" />-->

<!--            <TextView-->
<!--                android:id="@+id/tvWeight"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="center_horizontal"-->
<!--                android:ellipsize="end"-->
<!--                android:gravity="center"-->
<!--                android:maxLines="2"-->
<!--                android:textColor="@color/black"-->
<!--                android:textSize="@dimen/_16ssp"-->
<!--                tools:text="(10kg)" />-->

<!--            <TextView-->
<!--                android:id="@+id/tvOriginalPrice"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="center_horizontal"-->
<!--                android:ellipsize="end"-->
<!--                android:foreground="@drawable/strike_price"-->
<!--                android:maxLines="3"-->
<!--                android:textColor="@color/black60"-->
<!--                android:textSize="@dimen/_12ssp"-->
<!--                android:visibility="gone"-->
<!--                tools:text="12.99"-->
<!--                tools:visibility="visible" />-->
<!--            <TextView-->
<!--                android:id="@+id/tvTimePriceSign"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:gravity="center"-->
<!--                android:textColor="@color/black60"-->
<!--                android:textSize="@dimen/_12ssp"-->
<!--                android:visibility="gone"-->
<!--                android:text="@string/time_price_parentheses"-->
<!--                tools:visibility="visible" />-->
<!--        </LinearLayout>-->
        <include
            android:id="@+id/layoutPrice"
            layout="@layout/layout_item_price_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginHorizontal="8dp"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tvRefundMinusCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="5dp"
                android:gravity="center_horizontal"
                android:textColor="@color/refund_text_color"
                android:textSize="@dimen/_18ssp"
                tools:text="-"
                tools:visibility="visible" />
        </LinearLayout>
    </LinearLayout>
    <TextView
        android:id="@+id/tvRemark"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="start"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/_12ssp"
        android:visibility="gone"
        tools:text="备注 : $9.9"
        tools:visibility="visible" />
</LinearLayout>
