<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="26px"
                android:textStyle="bold"
                tools:text="商店名" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="@string/payment_method_report"
                android:textColor="@color/black"
                android:textSize="36px"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="22px"
                android:textStyle="bold"
                tools:text="商店名" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/print_time"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPrintTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold"
                    tools:text="时间" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <include
                    layout="@layout/dot_divider"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="15px"
                    android:layout_marginBottom="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/payment_info"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <include
                    layout="@layout/dot_divider"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="15px"
                    android:layout_marginBottom="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/order_num"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/print_title_total"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvTotalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <include
                layout="@layout/dot_divider"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginVertical="10px" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/payment_channels"
                android:textColor="@color/black"

                android:textSize="26px"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10px"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="1"
                tools:listitem="@layout/item_product_report"
                tools:visibility="visible" />

            <include
                android:id="@+id/vFooter"
                layout="@layout/view_printer_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <!--            <androidx.constraintlayout.widget.ConstraintLayout-->
            <!--                android:id="@+id/llThankYouLayout"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="center_horizontal"-->
            <!--                android:orientation="horizontal">-->

            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_gravity="center_vertical"-->
            <!--                    android:gravity="start"-->
            <!--                    android:singleLine="true"-->
            <!--                    android:text="****"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_printer_default_sp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                    app:layout_constraintEnd_toStartOf="@id/llThankYou"-->
            <!--                    app:layout_constraintTop_toTopOf="parent" />-->

            <!--                <LinearLayout-->
            <!--                    android:id="@+id/llThankYou"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:gravity="center_horizontal"-->
            <!--                    android:orientation="vertical"-->
            <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:layout_constraintStart_toStartOf="parent"-->
            <!--                    app:layout_constraintTop_toTopOf="parent">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="Powered by MPOS Cambodia"-->
            <!--                        android:textColor="@color/black60"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="Phone: +855 1122 3328"-->
            <!--                        android:textColor="@color/black60"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="https://www.m-pos.cc/"-->
            <!--                        android:textColor="@color/black60"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                </LinearLayout>-->


            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_gravity="center_vertical"-->
            <!--                    android:gravity="start"-->
            <!--                    android:singleLine="true"-->
            <!--                    android:text="****"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_printer_default_sp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                    app:layout_constraintStart_toEndOf="@id/llThankYou"-->
            <!--                    app:layout_constraintTop_toTopOf="parent" />-->


            <!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>