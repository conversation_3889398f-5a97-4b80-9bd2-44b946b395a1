<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="0dp">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:background="@drawable/selector_rounded_rectangle_border"
        android:gravity="center_vertical"
        android:minHeight="56dp"
        android:orientation="horizontal"
        android:padding="10dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:visibility="visible"
            app:cardCornerRadius="6dp"
            app:cardElevation="0dp">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@id/ivGood"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center_vertical" />

                <View
                    android:id="@+id/vDisable"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="@color/black40"
                    android:visibility="gone" />
            </FrameLayout>

        </androidx.cardview.widget.CardView>


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <com.metathought.food_order.casheir.ui.widget.MultiLineMiddleEllipsizeTextView
                android:id="@+id/tvDishedName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:drawablePadding="6dp"
                android:ellipsize="middle"
                android:maxLines="2"
                android:textColor="@drawable/selector_primary_color"
                android:textSize="@dimen/_16ssp" />

            <TextView
                android:id="@+id/tvTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="Hello $9.99" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="8dp"
            android:gravity="center">

            <ImageView
                android:id="@+id/imgMinus"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_minus"
                android:visibility="visible" />

            <TextView
                android:id="@+id/tvQTY"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLength="4"
                android:paddingHorizontal="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                tools:text="1" />

            <ImageView
                android:id="@+id/imgPlus"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_add" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>