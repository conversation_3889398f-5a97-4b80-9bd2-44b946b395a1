<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="25dp"
            android:gravity="center_vertical"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/credit"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingHorizontal="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_dialog_info"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="16dp">


                    <TextView
                        android:id="@+id/labelOrderPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="start"
                        android:text="@string/order_amount"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="0.5" />

                    <TextView
                        android:id="@+id/tvOrderPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintBaseline_toBaselineOf="@+id/labelOrderPrice"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintWidth_percent="0.5"
                        tools:text="$999.99" />


                    <TextView
                        android:id="@+id/labelCustomerNickname"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="start"
                        android:layout_marginTop="16dp"
                        android:text="@string/customer_nickname"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/labelOrderPrice"
                        app:layout_constraintWidth_percent="0.5" />

                    <TextView
                        android:id="@+id/tvCustomerNickname"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintBaseline_toBaselineOf="@+id/labelCustomerNickname"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintWidth_percent="0.5"
                        tools:text="---" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textInputLayoutPhoneNumber"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:hint="@string/customer_account_required"
                        android:textColorHint="@color/black60"
                        app:startIconDrawable="@drawable/ic_rectangle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edtPhoneNumber"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:inputType="phone"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black"
                            android:textSize="15sp" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.hbb20.CountryCodePicker
                        android:id="@+id/countryCodeHolder"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="14dp"
                        android:visibility="visible"
                        app:ccpDialog_keyboardAutoPopup="false"
                        app:ccp_defaultPhoneCode="855"
                        app:ccp_showFlag="false"
                        app:ccp_showNameCode="false"
                        app:ccp_textSize="14sp"
                        app:layout_constraintStart_toStartOf="parent" />

                </FrameLayout>


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCustomerName"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/customer_nickname_required"
                    android:textColorHint="@color/black60"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCustomerName"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text|textNoSuggestions"
                        android:maxLength="@integer/name_max_length"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutRemark"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="140dp"
                    android:layout_marginTop="16dp"
                    android:hint="@string/please_enter_the_reason"
                    android:textColorHint="@color/black60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtRemark"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:gravity="top|start"
                        android:inputType="textMultiLine"
                        android:maxLength="140"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>
        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="0.6"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/white"
                app:cornerRadius="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfirm"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm2"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="12dp"
                app:layout_constraintEnd_toEndOf="parent" />
        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
