<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/background_popup_dropdown"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvAllPayment"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/all_payment_method"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvOnlinePayment"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/online_payment"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvPayByCash"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/offline_channel"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvPayByBalance"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/pay_by_balance"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

</LinearLayout>