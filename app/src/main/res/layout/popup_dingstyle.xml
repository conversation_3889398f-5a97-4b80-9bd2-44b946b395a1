<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/background_popup_dropdown"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvDinein"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/selector_popup_item"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/dine_in"
        android:textColor="@drawable/selector_primary_color"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold" />


    <TextView
        android:id="@+id/tvTakeAway"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/selector_popup_item"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/take_away"
        android:textColor="@drawable/selector_primary_color"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvPreOrder"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/selector_popup_item"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/pre_order"
        android:textColor="@drawable/selector_primary_color"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvTakeOut"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/selector_popup_item"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/take_out"
        android:textColor="@drawable/selector_primary_color"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold" />

</LinearLayout>