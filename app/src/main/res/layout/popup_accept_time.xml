<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="3dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:id="@+id/tvTen"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="10"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvTwenty"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="20"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvThirty"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="30"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvSixty"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="60"
                android:textColor="@color/black"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>


</LinearLayout>