<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvCount"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Lemonade" />

    <TextView
        android:id="@+id/tvCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="18dp"
        android:textColor="@color/black60"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/tvName"
        app:layout_constraintEnd_toStartOf="@+id/ivIcon"
        app:layout_constraintTop_toTopOf="@+id/tvName"
        tools:text="x 1" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvNewPrice"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvName"
        tools:text="原价格 $20" />

    <TextView
        android:id="@+id/tvVipPrice"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:drawablePadding="2dp"
        android:textColor="@color/member_price_color"
        android:textSize="12sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/icon_vip"
        app:layout_constraintBottom_toBottomOf="@+id/tvPrice"
        app:layout_constraintStart_toEndOf="@+id/tvPrice"
        app:layout_constraintTop_toTopOf="@+id/tvPrice"
        tools:text="$0.00"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvNewPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/accentColor"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPrice"
        tools:text="新价格 $20"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvNewVipPrice"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:drawablePadding="2dp"
        android:textColor="@color/member_price_color"
        android:textSize="12sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/icon_vip"
        app:layout_constraintBottom_toBottomOf="@+id/tvNewPrice"
        app:layout_constraintStart_toEndOf="@+id/tvNewPrice"
        app:layout_constraintTop_toTopOf="@+id/tvNewPrice"
        tools:text="$0.00"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
