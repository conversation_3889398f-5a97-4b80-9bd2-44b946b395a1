<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutWrap"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    app:cardBackgroundColor="@color/primaryColor"
    app:cardCornerRadius="16dp"
    app:cardElevation="0dp"
    app:strokeColor="@color/transparent"
    app:strokeWidth="0dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:fontFamily="@font/roboto"
                android:gravity="center"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="A001" />

            <TextView
                android:id="@+id/tvStatus"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginVertical="4dp"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/imgChair"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="3dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/ic_table"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/tvChairAvailable"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:drawablePadding="2dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    tools:text="0/5" />
            </LinearLayout>
        </LinearLayout>
        <ImageView
            android:id="@+id/ivSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:src="@drawable/icon_table_select"/>
    </FrameLayout>


</com.google.android.material.card.MaterialCardView>