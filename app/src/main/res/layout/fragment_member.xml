<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    android:padding="6dp"
    tools:context=".ui.table.TableFragment">


    <LinearLayout
        android:id="@+id/dropdownFilter"
        android:layout_width="150dp"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/background_language_spiner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvLanguages"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="This Quartar" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_dropdown"
            android:visibility="visible"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <EditText
        android:id="@+id/edtSearch"
        style="@style/commonSearchStyle"
        android:layout_marginEnd="10dp"
        android:hint="@string/search_name"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/tvCalendar"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        tools:ignore="Autofill" />

    <com.metathought.food_order.casheir.ui.widget.CalendarTextView
        android:id="@+id/tvCalendar"
        style="@style/commonCalendarTextViewStyle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        tools:text="01 03, 2024 - 01 03, 2024" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragmentMember"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:spanCount="5"
        tools:itemCount="20"
        tools:listitem="@layout/table_item" />

    <RadioGroup
        android:id="@+id/radioGroupFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/round_background_25dp"
        android:checkedButton="@id/radioAll"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/radioOverview"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/overview"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioMember"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/customer"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioBalance"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/details"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioCredit"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/credit"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

    </RadioGroup>
</androidx.constraintlayout.widget.ConstraintLayout>
