<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="600dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/request_refund" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:orientation="horizontal">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/refund_Type"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/radioRefundType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/radioPartialRefund"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:background="@drawable/radio_refunds_backgroud"
                    android:button="@null"
                    android:checked="true"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="10dp"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/partial_refund"
                    android:textColor="@drawable/radio_refunds_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <RadioButton
                    android:id="@+id/radioFullRefund"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/radio_refunds_backgroud"
                    android:button="@null"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="10dp"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/full_refund"
                    android:textColor="@drawable/radio_refunds_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </RadioGroup>
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/layoutMainOrdered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo">

                <LinearLayout
                    android:id="@+id/layoutFood"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_dialog_info"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <LinearLayout
                        android:id="@+id/layoutHeader"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="2"
                            android:drawablePadding="3dp"
                            android:text="@string/items"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:gravity="center_horizontal"
                            android:text="@string/quantity"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1.2"
                            android:gravity="center_horizontal"
                            android:text="@string/refund_qty"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:gravity="center_horizontal"
                            android:text="@string/amount"
                            android:textColor="@color/black"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/height_bar"
                        android:background="@color/black08" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerOrderedFood"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_weight="1"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="10dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/selected_menu_item" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPaymentLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="10dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/choose_payment"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/radioGroupPaymentMethod"
                        android:layout_width="wrap_content"
                        android:layout_height="45dp"
                        android:checkedButton="@id/radioAll"
                        android:orientation="horizontal"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <RadioButton
                            android:id="@+id/radioOnline"
                            style="@style/FontLocalization"
                            android:layout_width="120dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radio_refunds_backgroud"
                            android:button="@null"
                            android:checked="true"
                            android:drawableLeft="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="0dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/online_refund"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                        <RadioButton
                            android:id="@+id/radioCash"
                            style="@style/FontLocalization"
                            android:layout_width="120dp"
                            android:layout_height="match_parent"
                            android:background="@drawable/radio_refunds_backgroud"
                            android:button="@null"
                            android:drawableLeft="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="0dp"
                            android:gravity="center"
                            android:minHeight="0dp"
                            android:paddingHorizontal="10dp"
                            android:text="@string/offline_refund"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />


                    </RadioGroup>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_quantity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundQTY"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentMethod1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/paymentMethod1"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:text="@string/payment_method"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvPaymentMethod1"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            tools:text="U-Pay" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llBalanceRefund"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/balanceRefund"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_total_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundTotalAmount1"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPaymentMethod"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/paymentMethod2"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:text="@string/payment_method"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvPaymentMethod"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            tools:text="U-Pay" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutRefundPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical"
                        android:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundPrice"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutRefundPack"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_pack_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundPack"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutRefundService"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundService"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutRefundVat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundVat"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/refundTotalAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_total_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvRefundTotalAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvRefundTotalKhrAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        tools:text="៛0" />

                    <LinearLayout
                        android:id="@+id/llAutoInStore"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginVertical="24dp"
                            android:background="@color/black12" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="start"
                                android:text="@string/automatically_store_returned_goods"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_16ssp" />

                            <ImageView
                                android:id="@+id/ivSwitch"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/icon_switch_close" />

                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCancel"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:text="@string/cancel"
                        android:textAllCaps="false"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18ssp"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:strokeColor="@color/black20"
                        app:strokeWidth="1dp" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnConfirmRefund"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:text="@string/confirm_refund"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_18ssp"
                        app:backgroundTint="@color/primaryColor"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent" />
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>