<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvOrderID"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="4"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvTable"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvCustomerName"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.9"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvTotal"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.7"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvOrderStatus"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.7"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvPaymentMethod"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1.5"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="4"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp"
        tools:text="是的方法" />

    <TextView
        android:id="@+id/tvOrderTime"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="4"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvPaymentTime"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="4"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black80"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tvAction"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:paddingHorizontal="10dp"
        android:text="@string/detail"
        android:textColor="@color/primaryColor"
        android:textSize="16sp" />
</LinearLayout>

