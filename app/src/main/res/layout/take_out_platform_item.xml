<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvName"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/selector_rounded_rectangle_border"
        android:button="@null"
        android:checked="false"
        android:drawableStart="@drawable/radiobutton_payment_icon"
        android:drawablePadding="10dp"
        android:ellipsize="end"
        android:gravity="start|center"
        android:maxLines="1"
        android:paddingHorizontal="20dp"
        android:textColor="@drawable/selector_primary_color"
        android:textSize="14sp"
        android:textStyle="bold"
        app:buttonCompat="@null"
        tools:text="@string/cashier_receipt" />
</LinearLayout>
