<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginVertical="20dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="10dp"
        app:cardMaxElevation="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/background_e7f5ee_radius_16"
            android:gravity="center_horizontal"
            android:minWidth="200dp"
            android:orientation="vertical"
            android:padding="10dp">

            <TextView
                android:id="@+id/btnWholeDiscount"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:minHeight="50dp"
                android:text="@string/discount_whole_order"
                android:textColor="@drawable/selector_primary_color2"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/btnSingleDiscount"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:minHeight="50dp"
                android:text="@string/discount_single_good"
                android:textColor="@drawable/selector_primary_color2"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/btnCoupon"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:minHeight="50dp"
                android:text="@string/coupon"
                android:textColor="@drawable/selector_primary_color2"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</FrameLayout>
