<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvOrderNo"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/print_title_order_no" />

        <TextView
            android:id="@+id/tvTable"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/print_title_table_name" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvList"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="6"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <TextView
            android:id="@+id/tvOrderAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/print_report_discount_amount" />

        <TextView
            android:id="@+id/tvReduceAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/print_report_discount_amount" />

        <TextView
            android:id="@+id/tvVat"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/print_title_vat" />

        <TextView
            android:id="@+id/tvReceiveAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/amount_receivable" />


    </LinearLayout>

    <View
        android:id="@+id/vLine"
        style="@style/commonDividerStyle"
        android:layout_gravity="bottom" />
</FrameLayout>