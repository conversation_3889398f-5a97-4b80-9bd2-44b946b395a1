<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="75dp"
    android:paddingVertical="18dp">

    <TextView
        android:id="@+id/tvPrinterName"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1.5"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="SAM 打印机" />

    <TextView
        android:id="@+id/tvPrinterType"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1.5"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="小票" />

    <TextView
        android:id="@+id/tvPrinterDevice"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.5"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="MPOS Wi-Fi打印机" />

    <TextView
        android:id="@+id/tvBindingTime"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.4"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="2024/02/02 12:00:00" />

    <TextView
        android:id="@+id/tvReceiptSize"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="80" />

    <TextView
        android:id="@+id/tvTemplateConfigured"
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1.8"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="堂食小票、外带小票、预定小票、结账小票、厨房小票" />

    <LinearLayout
        android:id="@+id/llStatus"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2">

        <TextView
            android:id="@+id/tvConnectionStatus"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingHorizontal="10dp"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_16ssp"
            tools:text="成功" />

        <ImageView
            android:id="@+id/btnConnect"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_printer_connect"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>


    <!--    <LinearLayout-->
    <!--        android:id="@+id/llOperate"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_weight="1.2"-->
    <!--        android:gravity="center">-->


    <!--    </LinearLayout>-->


</LinearLayout>
