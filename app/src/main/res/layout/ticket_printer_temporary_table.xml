<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="20px"
                    android:gravity="center"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="JM美味海鲜餐厅(体验店)" />

                <TextView
                    android:id="@+id/tvStoreNameKH"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="20px"
                    android:gravity="center"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="JM美味海鲜餐厅(体验店)"
                    tools:visibility="visible" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15px">

                <LinearLayout
                    android:id="@+id/tableNameTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Table Name"
                        android:textSize="25px"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ឈ្មោះតុ"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTableId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textSize="25px"
                    android:textStyle="bold"
                    tools:text="A001A001A001A001A001" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="15px">

                <LinearLayout
                    android:id="@+id/expiryTimeTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Expiry Time"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ពេលវេលាផុតកំណត់"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTableExpirationTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="2024/01/24 12:00:00" />
            </LinearLayout>

            <include layout="@layout/dot_divider" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="15px"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/descTopTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="Please scan to order food"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="សូមស្កេនដើម្បីបញ្ជាអាហារ"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


                <ImageView
                    android:id="@+id/ivOrderQr"
                    android:layout_width="250px"
                    android:layout_height="250px"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10px" />

                <LinearLayout
                    android:id="@+id/descBottomTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15px"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:text="Please bring this receipt with you when checking out"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:text="សូមយកបង្កាន់ដៃនេះមកជាមួយពេលចេញលុយ"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

            </LinearLayout>

            <include layout="@layout/dot_divider" />

            <TextView
                android:id="@+id/tvThankYouWords"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10px"
                android:gravity="center"
                android:textSize="@dimen/_printer_default_sp"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="哈哈" />

            <include
                android:id="@+id/vFooter"
                layout="@layout/view_printer_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


</LinearLayout>