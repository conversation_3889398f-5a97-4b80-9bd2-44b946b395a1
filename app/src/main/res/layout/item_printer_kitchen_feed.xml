<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/llFeedName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="5"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFeedNameEn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginStart="15dp"
                android:ellipsize="end"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                android:textStyle="bold"
                tools:text="+ Large Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugarLarge Normal ice, Normal sugar" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llFeedFoodCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.2"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFeedFoodCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:gravity="center_horizontal"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                android:textStyle="bold"
                tools:text="x1" />

            <!--            <TextView-->
            <!--                android:id="@+id/tvRefundMinusCount"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="end"-->
            <!--                android:layout_marginTop="5dp"-->
            <!--                android:gravity="center_horizontal"-->
            <!--                tools:text="1"-->
            <!--                android:visibility="gone"-->
            <!--                android:textColor="@color/main_red"-->
            <!--                android:textSize="@di" />-->
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tvFeedNameKm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="Large Normal ice, Normal sugar"
        tools:visibility="visible" />


</LinearLayout>
