<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvTitle"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/classes_start"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/btnClose"
            app:layout_constraintEnd_toStartOf="@+id/btnClose"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/btnClose" />

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="5dp"
            android:src="@drawable/ic_cross_closed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />


        <TextView
            android:id="@+id/tvImprest"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:maxLines="2"
            android:text="@string/imprest"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@+id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/tilUSD"
            style="@style/CustomOutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="9dp"
            android:textColorHint="@color/bg_progress"
            app:layout_constraintEnd_toStartOf="@+id/tilKhmer"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvImprest">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtUSD"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawableStart="@drawable/ic_dollar"
                android:drawablePadding="10dp"
                android:drawableTint="@color/black"
                android:inputType="numberDecimal|numberSigned"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/tilKhmer"
            style="@style/CustomOutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColorHint="@color/bg_progress"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tilUSD"
            app:layout_constraintTop_toTopOf="@+id/tilUSD">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtKhmer"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:digits="-0123456789"
                android:drawableStart="@drawable/icon_km_unit"
                android:drawablePadding="10dp"
                android:inputType="numberDecimal|numberSigned"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/tilRemark"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:hint="@string/remark"
            android:textColorHint="@color/black60"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tilUSD">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtRemark"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top"
                android:inputType="text|textMultiLine"
                android:maxLength="150"
                android:maxLines="4"
                android:minLines="4"
                android:textSize="@dimen/_14ssp"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />

        </com.google.android.material.textfield.TextInputLayout>


        <TextView
            android:id="@+id/btnSaveImprest"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="36dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/button_login_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/save_imprest"
            android:textColor="@color/mainWhite"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnStartClasses"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tilRemark" />


        <TextView
            android:id="@+id/btnStartClasses"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="36dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/button_login_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/start_classes"
            android:textColor="@color/mainWhite"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnSaveImprest"
            app:layout_constraintTop_toBottomOf="@+id/tilRemark" />


        <ProgressBar
            android:id="@+id/pbProgress"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
