<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="482dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="end|top"
            tools:ignore="UseCompoundDrawables">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvTableID"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="A001" />

            </LinearLayout>

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <TextView
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/dining"
            android:textColor="@color/black"
            android:textSize="30sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvPeople"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="16sp"
            tools:text="2/5" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="16dp">

                <LinearLayout
                    android:id="@+id/btnViewOrder"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:background="@drawable/button_outline_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/view_ordered"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_16ssp" />

                </LinearLayout>


                <androidx.cardview.widget.CardView
                    android:id="@+id/cardPrinter"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:elevation="0dp"
                    app:cardBackgroundColor="@color/primaryColor10"
                    app:cardCornerRadius="15dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@id/tvTableID"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:paddingHorizontal="10dp"
                        android:text="@string/print_temporary_table_code"
                        android:textColor="@color/paid_text_color"
                        android:textSize="@dimen/_16ssp" />
                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnOrder"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="20dp"
                android:background="@drawable/button_login_background"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/order_table"
                    android:textColor="@color/mainWhite"
                    android:textSize="@dimen/_18ssp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
