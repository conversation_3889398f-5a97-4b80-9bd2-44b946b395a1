<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/print_report" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginVertical="16dp"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutType"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/report_type_required"
                    android:textColorHint="@color/black60"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtType"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawableEnd="@drawable/ic_dropdown"
                        android:drawablePadding="10dp"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:inputType="text"
                        android:maxLength="100"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:id="@+id/llPrintType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="2dp"
                        android:text="@string/please_select_require"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/radioPrintType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/radioPrint"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radiobutton_only_stroke_background"
                            android:button="@null"
                            android:checked="true"
                            android:drawableStart="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="8dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/printer"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                        <RadioButton
                            android:id="@+id/radioExport"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radiobutton_only_stroke_background"
                            android:button="@null"
                            android:checked="false"
                            android:drawableStart="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="8dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/export"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                    </RadioGroup>
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/background_efefef_radius_12"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="15dp">

                    <TextView
                        android:id="@+id/tvStartTime"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <View
                        android:layout_width="15dp"
                        android:layout_height="3dp"
                        android:background="@drawable/round_primary_background_100dp" />

                    <TextView
                        android:id="@+id/tvEndTime"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvToday"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_weight="1"
                        android:background="@drawable/selector_rounded_rectangle_border"
                        android:gravity="center"
                        android:text="@string/today"
                        android:textColor="@drawable/selector_primary_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvYesterday"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/selector_rounded_rectangle_border"
                        android:gravity="center"
                        android:text="@string/yesterday"
                        android:textColor="@drawable/selector_primary_color"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvLastWeek"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_weight="1"
                        android:background="@drawable/selector_rounded_rectangle_border"
                        android:gravity="center"
                        android:text="@string/last_week"
                        android:textColor="@drawable/selector_primary_color"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvLastMonth"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:background="@drawable/selector_rounded_rectangle_border"
                        android:gravity="center"
                        android:text="@string/last_month"
                        android:textColor="@drawable/selector_primary_color"
                        android:textStyle="bold" />

                </LinearLayout>


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutStartTime"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:hint="@string/select_time_start_required"
                    android:textColorHint="@color/black60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtStartTime"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:drawablePadding="10dp"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:inputType="text"
                        android:maxLength="1000"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutEndTime"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:hint="@string/select_time_end_required"
                    android:textColorHint="@color/black60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtEndTime"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:drawablePadding="10dp"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:inputType="text"
                        android:maxLength="1000"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:id="@+id/llExportFormatGroupType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="2dp"
                        android:text="@string/export_formats_require"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/exportFormatsRadioType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/radioExcel"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radiobutton_only_stroke_background"
                            android:button="@null"
                            android:checked="true"
                            android:drawableStart="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="8dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/excel"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                        <RadioButton
                            android:id="@+id/radioPdf"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radiobutton_only_stroke_background"
                            android:button="@null"
                            android:checked="false"
                            android:drawableStart="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="8dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/pdf"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                    </RadioGroup>
                </LinearLayout>

            </LinearLayout>
        </ScrollView>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnYes"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:alpha="0.5"
            android:clickable="true"
            android:enabled="false"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:text="@string/printer"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            app:backgroundTint="@color/primaryColor"
            app:cornerRadius="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:strokeColor="@color/primaryColor"
            app:strokeWidth="0dp" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
