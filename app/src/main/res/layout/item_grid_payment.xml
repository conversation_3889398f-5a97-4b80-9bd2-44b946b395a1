<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:background="@drawable/background_white_radius_4dp"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="12dp">

    <ImageView
        android:id="@+id/gridIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginBottom="4dp"
        android:src="@drawable/ic_logo" />

    <TextView
        android:id="@+id/gridTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选项"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

</LinearLayout>
