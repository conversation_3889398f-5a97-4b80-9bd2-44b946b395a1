<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingEnd="6dp"
    android:paddingBottom="6dp"
    tools:layout_height="120dp"
    tools:layout_width="240dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/primaryColor"
        app:cardCornerRadius="15dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp">

            <!--            android:autoSizeMaxTextSize="@dimen/_18ssp"-->
            <!--            android:autoSizeMinTextSize="@dimen/_12ssp"-->
            <!--            android:autoSizeTextType="uniform"-->

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:lines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@id/tvPrice"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/login_time" />

            <TextView
                android:id="@+id/tvPrice"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:autoSizeMaxTextSize="@dimen/_16ssp"
                android:autoSizeMinTextSize="@dimen/_10ssp"
                android:autoSizeTextType="uniform"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/tvTableID"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toEndOf="@id/tvTableID"
                tools:text="$9,999.08" />

            <TextView
                android:id="@+id/tvTime"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginBottom="5dp"
                android:autoSizeMaxTextSize="@dimen/_14ssp"
                android:autoSizeMinTextSize="@dimen/_9ssp"
                android:autoSizeTextType="uniform"
                android:gravity="bottom|end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBottom_toTopOf="@id/tvInfo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvPrice"
                app:layout_constraintVertical_bias="1"
                tools:text="20:00" />

            <TextView
                android:id="@+id/tvCustomerName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:gravity="bottom|end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/tvStatus"
                app:layout_constraintEnd_toStartOf="@id/tvInfo"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTime"
                app:layout_constraintVertical_bias="1"
                tools:text="@tools:sample/lorem/random" />

            <TextView
                android:id="@+id/tvInfo"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:gravity="bottom|end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBottom_toTopOf="@id/tvStatus"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTime"
                app:layout_constraintVertical_bias="1"
                tools:text="(***8555)" />

            <androidx.cardview.widget.CardView
                android:id="@+id/layoutChairAvailable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white20"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toBottomOf="@id/tvStatus"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvStatus">

                <TextView
                    android:id="@+id/tvChairAvailable"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:drawableStart="@drawable/ic_chair_available"
                    android:drawablePadding="4dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:paddingHorizontal="10dp"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    tools:text="0/5" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/tvStatus"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:autoSizeMaxTextSize="@dimen/_16ssp"
                android:autoSizeMinTextSize="@dimen/_10ssp"
                android:autoSizeTextType="uniform"
                android:drawablePadding="0dp"
                android:gravity="bottom|end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toEndOf="@id/layoutChairAvailable"
                tools:text="@string/available" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>
