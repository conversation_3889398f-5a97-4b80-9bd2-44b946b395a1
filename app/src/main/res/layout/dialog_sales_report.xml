<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp"
            app:dialog_title="@string/sales_report" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32dp">

                <com.metathought.food_order.casheir.ui.widget.CalendarTextView
                    android:id="@+id/tvCalendar"
                    style="@style/commonCalendarTextViewStyle"
                    android:layout_width="0dp"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1.3"
                    android:background="@drawable/background_white_border_black12_radius_100"
                    app:layout_constraintStart_toEndOf="@id/dropdownFilter"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="01 03, 2024 - 01 03, 2024" />

                <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                    android:id="@+id/edtSearch"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    app:search_hint="@string/hint_sale_items_report_search" />

                <TextView
                    android:id="@+id/tvClearFilter"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="15dp"
                    android:singleLine="true"
                    android:text="@string/clear_filter"
                    android:textColor="@color/primaryColor"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@id/tvCalendar"
                    app:layout_constraintTop_toTopOf="parent" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="71dp"
                android:layout_marginHorizontal="32dp"
                android:layout_marginTop="10dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/background_e7f5ee_radius_16"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvOnlineAmount"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_24ssp"
                        android:textStyle="bold"
                        tools:text="0.00" />

                    <TextView
                        android:id="@+id/onlinePayment"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center"
                        android:text="@string/online_payment"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_e7f5ee_radius_16"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvOfflineAmount"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_24ssp"
                        android:textStyle="bold"
                        tools:text="0.00" />

                    <TextView
                        android:id="@+id/offlinePayment"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center"
                        android:text="@string/offline_payments"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/background_e7f5ee_radius_16"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvBalanceAmount"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_24ssp"
                        android:textStyle="bold"
                        tools:text="0.00" />

                    <TextView
                        android:id="@+id/balancePayment"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center"
                        android:text="@string/pay_by_balance"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp" />


                </LinearLayout>


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/background_e7f5ee_radius_16"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCreditAmount"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_24ssp"
                        android:textStyle="bold"
                        tools:text="0.00" />

                    <TextView
                        android:id="@+id/creditPayment"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center"
                        android:text="@string/credit_payment"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:gravity="top"
                android:orientation="horizontal">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/order_id"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvItemTableName"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        app:drawableEndCompat="@drawable/icon_sort_normal" />


                </FrameLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="6"
                    android:orientation="horizontal">

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1.5">

                        <TextView
                            android:id="@+id/tvItemProductName"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/product_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:drawableEndCompat="@drawable/icon_sort_normal" />

                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1">

                        <TextView
                            android:id="@+id/tvItemQuantity"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/quantity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:drawableEndCompat="@drawable/icon_sort_normal" />

                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1">

                        <TextView
                            android:id="@+id/itemUnitPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="4dp"
                            android:text="@string/single_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:drawableEndCompat="@drawable/icon_sort_normal" />

                    </FrameLayout>

                    <TextView
                        android:id="@+id/itemServiceFee"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:text="@string/service_fee"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/itemPackingFee"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/pac"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />
                </LinearLayout>


                <TextView
                    android:id="@+id/itemOrderAmount"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/order_amount"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/itemReduceAmount"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/print_report_discount_amount"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/itemVat"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/print_title_vat"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/itemReceiveAmount"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/print_title_total"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />


            </LinearLayout>

            <View
                style="@style/commonDividerStyle"
                android:layout_marginHorizontal="32dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvList"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="32dp"
                android:layout_weight="1"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_data"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <View style="@style/commonDividerStyle" />

        <LinearLayout
            android:id="@+id/llBottom"
            android:layout_width="match_parent"
            android:layout_height="107dp"
            android:layout_marginStart="42dp"
            android:layout_marginEnd="32dp"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTotalOrderTitle"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/order_total_num"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_10ssp" />

                <TextView
                    android:id="@+id/tvTotalOrderNum"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="@dimen/_20ssp"
                    android:textStyle="bold"
                    tools:text="$1000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTotalAmountTitle"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/order_amount"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_10ssp" />

                <TextView
                    android:id="@+id/tvTotalAmount"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="@dimen/_20ssp"
                    android:textStyle="bold"
                    tools:text="$1000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvReduceAmountTitle"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/print_report_discount_amount"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_10ssp" />

                <TextView
                    android:id="@+id/tvReduceAmount"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="@dimen/_20ssp"
                    android:textStyle="bold"
                    tools:text="$1000" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvActualReceiveAmountTitle"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/print_title_total"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_10ssp" />

                <TextView
                    android:id="@+id/tvActualReceiveAmount"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="@dimen/_20ssp"
                    android:textStyle="bold"
                    tools:text="$1000" />

            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnExport"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_gravity="center_vertical"
                android:minWidth="120dp"
                android:text="@string/export"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/pbLoading"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:indeterminateTint="@color/primaryColor"
        android:indeterminateTintMode="src_atop"
        android:progressTint="@color/primaryColor"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
