<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:orientation="vertical"
    android:padding="25dp">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        app:dialog_title="@string/store_manager" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llLogo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:gravity="center_vertical"
                    android:minHeight="50dp"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="@id/tvStoreName"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvLogoTitle"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:text="@string/manager_store_logo"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <androidx.cardview.widget.CardView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:id="@+id/ivLogo"
                            style="@style/FontLocalization"
                            android:layout_width="160dp"
                            android:layout_height="160dp"
                            android:scaleType="centerCrop" />
                    </androidx.cardview.widget.CardView>


                </LinearLayout>

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvStoreName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:layout_constraintEnd_toStartOf="@id/tvStorePhone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/llLogo"
                    app:title="@string/manager_store_name" />

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvStorePhone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tvStoreName"
                    app:layout_constraintTop_toTopOf="@id/tvStoreName"
                    app:title="@string/manager_store_phone" />

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvStoreLocation"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:layout_constraintEnd_toEndOf="@id/tvStoreName"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvStoreName"
                    app:title="@string/manager_store_location" />

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvStoreIntro"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tvStorePhone"
                    app:layout_constraintTop_toBottomOf="@id/tvStorePhone"
                    app:title="@string/manager_store_intro" />

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvDistanceLimit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:layout_constraintEnd_toEndOf="@id/tvStoreName"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvStoreLocation"
                    app:title="@string/limit_custom_ding" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="24dp"
                android:background="@color/black12"
                android:visibility="gone"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvDataList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"
                tools:listitem="@layout/item_store_manager_data"
                android:visibility="gone"/>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
                <!--                    android:id="@+id/tvStoreStatus"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    app:layout_constraintEnd_toStartOf="@id/tvStorePayment"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toTopOf="parent"-->
                <!--                    app:title="@string/manager_store_status" />-->

                <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
                <!--                    android:id="@+id/tvStorePayment"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginStart="12dp"-->
                <!--                    app:layout_constraintEnd_toEndOf="parent"-->
                <!--                    app:layout_constraintStart_toEndOf="@id/tvStoreStatus"-->
                <!--                    app:layout_constraintTop_toTopOf="parent"-->
                <!--                    app:title="@string/manager_store_payment" />-->

                <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
                <!--                    android:id="@+id/tvStoreTableService"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginTop="24dp"-->
                <!--                    app:layout_constraintEnd_toEndOf="@id/tvStoreStatus"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/tvStoreStatus"-->
                <!--                    app:title="@string/manager_store_table_service" />-->

                <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
                <!--                    android:id="@+id/tvStoreShowTable"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginTop="24dp"-->
                <!--                    app:layout_constraintEnd_toEndOf="parent"-->
                <!--                    app:layout_constraintStart_toStartOf="@id/tvStorePayment"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/tvStorePayment"-->
                <!--                    app:title="@string/manager_store_show_table" />-->

                <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
                <!--                    android:id="@+id/tvStoreAutoAccept"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginTop="24dp"-->
                <!--                    app:layout_constraintEnd_toEndOf="@id/tvStoreStatus"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/tvStoreTableService"-->
                <!--                    app:title="@string/manager_store_auto_accept" />-->

                <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
                <!--                    android:id="@+id/tvStoreMenuShowPrice"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_marginTop="24dp"-->
                <!--                    app:layout_constraintEnd_toEndOf="parent"-->
                <!--                    app:layout_constraintStart_toStartOf="@id/tvStorePayment"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/tvStoreShowTable"-->
                <!--                    app:title="@string/manager_store_casher_menu_show_image" />-->


            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="24dp"
                android:background="@color/black12"
                android:visibility="gone"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvStoreMealCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toStartOf="@id/tvStoreInvoiceNumber"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:title="@string/manager_store_ticket_need_meal_code" />

                <com.metathought.food_order.casheir.ui.widget.TitleContentView
                    android:id="@+id/tvStoreInvoiceNumber"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tvStoreMealCode"
                    app:layout_constraintTop_toTopOf="@id/tvStoreMealCode"
                    app:title="@string/manager_store_invoice_number" />

                <LinearLayout
                    android:id="@+id/llTax"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:gravity="center_vertical"
                    android:minHeight="50dp"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvStoreMealCode">

                    <!--                    <TextView-->
                    <!--                        android:id="@+id/tvTitle"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginBottom="10dp"-->
                    <!--                        android:text="@string/manager_store_ticket_need_tax"-->
                    <!--                        android:textColor="@color/black"-->
                    <!--                        android:textSize="@dimen/_14ssp" />-->

                    <com.metathought.food_order.casheir.ui.widget.TitleContentView
                        android:id="@+id/tvTaxTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tvStoreMealCode"
                        app:layout_constraintTop_toTopOf="@id/tvStoreMealCode"
                        app:title="@string/manager_store_ticket_need_tax" />

                    <TextView
                        android:id="@+id/tvCompanyName"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:text="@string/company_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvCompanyTax"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:text="@string/company_vat_tin"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvCompanyAddress"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:text="@string/company_address"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvCompanyPhone"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:text="@string/company_phone"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvCompanyEmail"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/company_email"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="24dp"
                android:background="@color/black12"
                android:visibility="gone"/>

            <!--            <androidx.constraintlayout.widget.ConstraintLayout-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:orientation="vertical">-->

            <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
            <!--                    android:id="@+id/tvStoreBindUpay"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:layout_constraintStart_toStartOf="parent"-->
            <!--                    app:layout_constraintTop_toTopOf="parent"-->
            <!--                    app:title="@string/manager_store_bind_upay" />-->


            <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
            <!--                    android:id="@+id/tvStoreNeedPush"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginTop="24dp"-->
            <!--                    app:layout_constraintEnd_toStartOf="@id/tvStorePushLanguage"-->
            <!--                    app:layout_constraintStart_toStartOf="parent"-->
            <!--                    app:layout_constraintTop_toBottomOf="@id/tvStoreBindUpay"-->
            <!--                    app:title="@string/manager_store_need_push" />-->

            <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
            <!--                    android:id="@+id/tvStorePushLanguage"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginStart="12dp"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:layout_constraintStart_toEndOf="@id/tvStoreNeedPush"-->
            <!--                    app:layout_constraintTop_toTopOf="@id/tvStoreNeedPush"-->
            <!--                    app:title="@string/manager_store_push_language" />-->

            <!--                <com.metathought.food_order.casheir.ui.widget.TitleContentView-->
            <!--                    android:id="@+id/tvStorePushType"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginTop="24dp"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:layout_constraintStart_toStartOf="parent"-->
            <!--                    app:layout_constraintTop_toBottomOf="@id/tvStorePushLanguage"-->
            <!--                    app:title="@string/manager_store_push_type" />-->


            <!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

            <!--            <View-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="1dp"-->
            <!--                android:layout_marginVertical="24dp"-->
            <!--                android:background="@color/black12" />-->
        </LinearLayout>


    </androidx.core.widget.NestedScrollView>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llButtom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:orientation="horizontal"
        app:divider="@drawable/shape_option_item_pading_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:showDividers="middle">

        <LinearLayout
            android:id="@+id/btnMultLanguage"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:background="@drawable/button_outline_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:minWidth="100dp"
            android:orientation="horizontal"
            android:paddingHorizontal="20dp"
            android:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/mult_language"
                android:textColor="@color/primaryColor"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/btnEdit"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_login_background"
            android:gravity="center"
            android:text="@string/edit"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</LinearLayout>