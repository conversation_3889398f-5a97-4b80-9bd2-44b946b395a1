<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llPrintAgain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvPrintAgain0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:gravity="center"
                    android:text="@string/print_again"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp" />

                <TextView
                    android:id="@+id/tvPrintAgain1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:gravity="center"
                    android:text="@string/print_again"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="200px"
                android:layout_height="200px"
                android:layout_gravity="center"
                android:scaleType="centerCrop"
                tools:background="@color/black" />

            <LinearLayout
                android:id="@+id/llStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvFirstStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    tools:text="JM美味海鲜餐厅(体验店)" />

                <TextView
                    android:id="@+id/tvSecondStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="JM美味海鲜餐厅(体验店)"
                    tools:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCompanyInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llCompanyName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvCompanyName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="Happy Co.ltd" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llVatTin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvVatTin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="d855-*********" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCompanyAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">


                    <TextView
                        android:id="@+id/tvCompanyAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="address" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llContactPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvContactPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="855 *********" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llContactEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvContactEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="<EMAIL>" />
                </LinearLayout>
            </LinearLayout>

            <View
                android:id="@+id/lineTop"
                style="@style/ticketDividerStyle"
                android:layout_marginVertical="0dp" />

            <LinearLayout
                android:id="@+id/llOrderType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llDiningStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDiningStyle0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="Dining-In" />

                    <TextView
                        android:id="@+id/tvDiningStyle1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:text="ការប្រើប្រាស់ផ្ទៃក្នុង"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llReceipt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical"
                    android:visibility="visible"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvReceipt0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Receipt"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvReceipt1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="បង្កាន់ដៃ"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llTableName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llTableNameTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTableName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    tools:text="A01" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPickUpNo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llPickUpNoTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pick_up_no"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pick_up_no"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvPickUpNo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    tools:text="001" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llReserveTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llReserveTimeTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pre_order_time"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pre_order_time"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvReserveTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="001" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llTicketTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="24px"
                    android:textStyle="bold"
                    tools:text="发票" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:textColor="@color/black"
                    android:textSize="24px"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="Invoice"
                    tools:visibility="visible" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llDateTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top">

                <LinearLayout
                    android:id="@+id/llDateTimeTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDateTime0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_datetime"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:id="@+id/tvDateTime1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_datetime"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvOrderTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="2023-10-10 10:10:10" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llInvoiceNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llInvoiceNumberTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvInvoiceNumber0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_invoiceNumber"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:id="@+id/tvInvoiceNumber1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_invoiceNumber"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvInvoiceNumber"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="发票号" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCustomerType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llCustomerTypeTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCustomerType0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Customer Type"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:id="@+id/tvCustomerType1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Customer Type"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvCustomerType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="@string/walk_in"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llTakeOutPlatform"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llTakeOutPlatformTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTakeOutPlatform0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="外卖平台" />

                    <TextView
                        android:id="@+id/tvTakeOutPlatform1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:text="外卖订单号"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTakeOutPlatform"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="堂食" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llTakeOutId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llTakeOutIdTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTakeOutId0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="外卖订单号" />

                    <TextView
                        android:id="@+id/tvTakeOutId1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:text="外卖订单号"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTakeOutId"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="堂食" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOrderTypeWithInvoice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llOrderTypeTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvOrderType0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="订单类型"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:id="@+id/tvOrderType1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="订单类型"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvOrderType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="堂食" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llOrderNo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llOrderNoTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_order_no"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_order_no"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


                <TextView
                    android:id="@+id/tvOrderNo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="9876543321234567899" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llCashier"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llCashierTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Cashier"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Cashier"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvCashierContent"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="收银员名字" />
            </LinearLayout>

            <!--税务小票这里显示-->
            <LinearLayout
                android:id="@+id/llTableNameWithInvoice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llTableNameWithInvoiceTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTableNameWithInvoice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="A01" />
            </LinearLayout>

            <!--税务小票这里显示-->
            <LinearLayout
                android:id="@+id/llPickUpNoWithInvoice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llPickUpNoWithInvoiceTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pick_up_no"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pick_up_no"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvPickUpNoWithInvoice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="001" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llReserveTimeWithInvoice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llReserveTimeWithInvoiceTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pre_order_time"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_pre_order_time"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvReserveTimeWithInvoice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="001" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llNumberOfPax"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llNumberOfPaxTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Number of Pax"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Number of Pax"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"

                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvNumberOfPax"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"

                    tools:text="001" />
            </LinearLayout>

            <!--2.13.2 新增 打印顾客名称-->
            <LinearLayout
                android:id="@+id/llCustomerName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_printer_default_margin_top"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llCustomerNameTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/customer_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/customer_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"

                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvCustomerName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"

                    tools:text="001" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/background_printer_border"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llItem"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="4px">

                    <LinearLayout
                        android:id="@+id/llItemIndex"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.6"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/print_title_item_index"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/print_title_item_index"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llItemName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.2"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:text="Item"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:text="Item"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llQty"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:gravity="center_horizontal"
                            android:text="Qty"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:gravity="center_horizontal"
                            android:text="Qty"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llUnitPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.4"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvUnitPrice0"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="Price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvUnitPrice1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="Price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

<!--                    <LinearLayout-->
<!--                        android:id="@+id/llDiscountPrice"-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_weight="1.4"-->
<!--                        android:orientation="vertical"-->
<!--                        android:visibility="gone"-->
<!--                        tools:visibility="visible">-->

<!--                        <TextView-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:gravity="center"-->
<!--                            android:text="@string/print_title_item_discount"-->
<!--                            android:textColor="@color/black"-->
<!--                            android:textSize="@dimen/_printer_default_sp" />-->

<!--                        <TextView-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:gravity="center"-->
<!--                            android:text="@string/print_title_item_discount"-->
<!--                            android:textColor="@color/black"-->
<!--                            android:textSize="@dimen/_printer_default_sp"-->

<!--                            android:visibility="gone"-->
<!--                            tools:visibility="visible" />-->
<!--                    </LinearLayout>-->

                    <LinearLayout
                        android:id="@+id/llItemTotal"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.4"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvItemTotal0"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="Price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvItemTotal1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="Price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_ticket_order_menu"
                    tools:visibility="visible" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvZplist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_ticket_order_menu"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/llCancelGoods"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <View
                        style="@style/commonDividerStyle"
                        android:layout_height="1dp"
                        android:background="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingHorizontal="4px"
                        android:paddingVertical="2px">

                        <!--  本来是用4个横杆 但是58的双语言 4个横杆放不下 就改成2个-->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="- - -"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <LinearLayout
                            android:id="@+id/llCancelGoodsTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="4px"
                            android:gravity="center_horizontal"
                            android:orientation="horizontal"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/rightStar"
                            app:layout_constraintStart_toEndOf="@id/leftStar"
                            app:layout_constraintTop_toTopOf="parent">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/cancel_dish"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"

                                android:visibility="gone"
                                tools:visibility="visible" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:text="@string/cancel_dish"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"

                                android:visibility="gone"
                                tools:visibility="visible" />

                        </LinearLayout>


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start"
                            android:text="- - -"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />


                    </LinearLayout>
                </LinearLayout>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCancelGoodList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_ticket_order_menu"
                    tools:visibility="visible" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llRemarkTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_remark"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_remark"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"

                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvRemark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_printer_default_margin_top"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"

                    tools:text="No Spicy! No chili, ginger, onion, garlic" />

                <View
                    style="@style/ticketDividerStyle"
                    android:layout_marginTop="0dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llSubtotal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llSubtotalTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvSubtotal0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_subtotal"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvSubtotal1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_subtotal"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvSubtotalUsdPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipSubtotalPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:text="-៛40731"
                            tools:visibility="visible" />
                    </LinearLayout>


                </LinearLayout>

                <View
                    android:id="@+id/lineSubtotal"
                    style="@style/ticketDividerStyle"
                    android:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPriceLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llCouponActivity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llCouponActivityTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/couponActivityTitle1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/couponActivityTitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountActivityAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipDiscountActivityAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:text="-៛40731" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPackPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llPackPriceTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_pack_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_pack_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvPackUsdPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvPackKhrPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:text="-៛40731" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llServiceFee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llServiceFeeTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvServiceFee0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvServiceFee1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvServiceFeePrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipServiceFeePrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:text="-៛40731"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llCoupon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llCouponTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCoupon0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_coupon"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvCoupon1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_coupon"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCouponPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipCouponPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:text="$10"
                            tools:visibility="visible" />

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llDiscountTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscount0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvDiscount1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvDiscountVipPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:text="$10"
                            tools:visibility="visible" />

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscountAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llDiscountAmountTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountAmount0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvDiscountAmount1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountAmountPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvDiscountAmountVipPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:text="$10"
                            tools:visibility="visible" />

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llVat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llVatTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvVat0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvVat1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvVatUsdPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"

                            tools:text="$10" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCommission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llCommissionTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCommission0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/commission"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvCommission1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/commission"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCommissionPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            tools:text="$10" />
                    </LinearLayout>
                </LinearLayout>

                <View
                    style="@style/ticketDividerStyle"
                    android:layout_marginVertical="0dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaidTotalLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:id="@+id/llTotalTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvTotal0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_total"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvTotal1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_total"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvTotalUsdPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="40px"
                        android:textStyle="bold"
                        tools:text="$10" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llConversionRatio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvConversionRatio"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="visible"
                        tools:text="USD 1=KHR 4,123"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvTotalKhrPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="40px"
                        android:textStyle="bold"
                        android:visibility="visible"
                        tools:text="៛40731"
                        tools:visibility="visible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTotalVipPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="40px"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="VIP $40731"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llKhrTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">


                    <LinearLayout
                        android:id="@+id/llKhrTotalTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_total"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_total"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvTotalKhrPriceWithInvoice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="40px"
                        android:textStyle="bold"
                        tools:text="$10" />
                </LinearLayout>

                <View
                    android:id="@+id/lineTotalPrice"
                    style="@style/ticketDividerStyle"
                    android:layout_marginVertical="0dp" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llPaidLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llPaymentBalance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llPaymentBalanceTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pay_by_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pay_by_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvPaymentBalance"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="$111100" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llAccountBalance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llAccountBalanceTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvAccountBalance"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="JiaJiaJia" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llReceived"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llReceivedTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvReceived0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_received"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:id="@+id/tvReceived1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_received"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvReceivedAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            tools:text="$10" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:text="៛40731" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llChange"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llChangeTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_change"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_change"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvChangeAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            tools:text="$10" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:text="៛40731"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>


                <View
                    android:id="@+id/linePaid"
                    style="@style/ticketDividerStyle" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOtherLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">
                <!--订单来源-->
                <LinearLayout
                    android:id="@+id/llSource"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llSourceTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_customer_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_customer_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvSource"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="JiaJiaJia" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llServiceLine"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/llServiceLineTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_line"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_line"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvServiceLine"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        tools:text="855 *********" />
                </LinearLayout>

                <View
                    android:id="@+id/lineOther"
                    style="@style/ticketDividerStyle"
                    android:visibility="visible" />
            </LinearLayout>


            <TextView
                android:id="@+id/tvThankYouWords"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                android:visibility="gone"
                tools:text="哈哈"
                tools:visibility="visible" />


            <LinearLayout
                android:id="@+id/llPaymentQrCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="gone">

                <View
                    style="@style/ticketDividerStyle"
                    android:layout_marginVertical="0dp" />

                <ImageView
                    android:id="@+id/ivOrderQr"
                    android:layout_width="250px"
                    android:layout_height="250px"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginVertical="10px" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPassAppCoupon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <View
                    android:id="@+id/linePassApp"
                    style="@style/ticketDividerStyle"
                    android:visibility="visible" />

                <LinearLayout

                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:id="@+id/llPassAppCouponTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pass_app_promo_code"
                            android:textColor="@color/black"
                            android:textSize="22px" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvPassAppCoupon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PASSMPOS"
                        android:textColor="@color/black"
                        android:textSize="26px" />

                </LinearLayout>
            </LinearLayout>


            <!--            <include-->
            <!--                layout="@layout/dot_divider"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="2dp"-->
            <!--                android:layout_marginVertical="0dp" />-->

            <include
                android:id="@+id/vFooter"
                layout="@layout/view_printer_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>