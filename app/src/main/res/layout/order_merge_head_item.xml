<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:visibility="visible">

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@color/black12" />

    <LinearLayout
        android:id="@+id/llOrderInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="4dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/tvOrderIndex"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            tools:text="订单1" />

        <TextView
            android:id="@+id/tvOrderId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            tools:text="订单号" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvNoGoods"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="10dp"
        android:text="@string/no_goods"
        android:textColor="@color/black60"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        android:visibility="gone" />

</LinearLayout>