<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="5dp"
    tools:layout_width="200dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="15dp"
        app:cardElevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imgImage"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_food" />

            <TextView
                android:id="@+id/tvName"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:layout_marginTop="11dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="Caramel Cake"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:layout_marginTop="5dp"
                android:orientation="vertical"
                android:layout_marginBottom="10dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primaryColorDark"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="$0.00" />

                <TextView
                    android:id="@+id/tvVipPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:drawablePadding="2dp"
                    android:textColor="@color/member_price_color"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    android:drawableStart="@drawable/icon_vip"
                    tools:text="$0.00"
                    tools:visibility="visible" />
                <TextView
                    android:id="@+id/tvOriginalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:drawablePadding="2dp"
                    android:ellipsize="end"
                    android:foreground="@drawable/strike_price"
                    android:maxLines="3"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_12ssp"
                    android:visibility="gone"
                    tools:text="12.99"
                    tools:visibility="visible" />


            </LinearLayout>


        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>
