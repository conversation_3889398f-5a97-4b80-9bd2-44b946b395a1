<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="5dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layoutMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:cardBackgroundColor="@color/white"
        app:strokeColor="@color/black20"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="10dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:id="@+id/tvFeedName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLength="50"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    tools:text="Lemonade32323232332323232323232对对发的发的辅导费方法大幅度发的发的发肥肥的的放大放大奋斗奋斗323方法热热3"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tvPrice"/>

                <TextView
                    android:id="@+id/tvPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="15dp"
                    android:ellipsize="end"
                    android:maxLines="3"
                    android:paddingHorizontal="8dp"
                    android:text="$12.99"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@id/tvFeedName"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="8dp"
                android:gravity="center">

                <ImageView
                    android:id="@+id/imgMinus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_minus"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/tvQTY"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLength="4"
                    android:paddingHorizontal="10dp"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_18ssp"
                    tools:text="1" />

                <ImageView
                    android:id="@+id/imgPlus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_add" />
            </LinearLayout>
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</FrameLayout>