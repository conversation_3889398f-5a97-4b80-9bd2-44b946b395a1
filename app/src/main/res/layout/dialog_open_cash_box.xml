<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

<!--    <LinearLayout-->
<!--        android:layout_width="450dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:background="@drawable/background_dialog"-->
<!--        android:orientation="vertical"-->
<!--        android:padding="24dp"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent">-->

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="end"-->
<!--            android:layout_marginBottom="10dp"-->
<!--            android:gravity="end|center_vertical"-->
<!--            tools:ignore="UseCompoundDrawables">-->

<!--            <TextView-->
<!--                android:id="@+id/tvDishedName"-->
<!--                style="@style/FontLocalization"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_weight="1"-->
<!--                android:gravity="start"-->
<!--                android:text="@string/open_cash_box"-->
<!--                android:textColor="@color/black"-->
<!--                android:textSize="20sp"-->
<!--                android:textStyle="bold" />-->

<!--            <ImageView-->
<!--                android:id="@+id/btnClose"-->
<!--                android:layout_width="40dp"-->
<!--                android:layout_height="40dp"-->
<!--                android:padding="5dp"-->
<!--                android:src="@drawable/ic_cross_closed"-->
<!--                tools:ignore="ContentDescription" />-->
<!--        </LinearLayout>-->

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="25dp"-->
<!--            android:orientation="vertical">-->

<!--            <TextView-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:text="@string/please_input_cash_box_pwd"-->
<!--                android:textColor="@color/black80"-->
<!--                android:textSize="16sp"-->
<!--                android:textStyle="bold" />-->


<!--            <com.maning.pswedittextlibrary.MNPasswordEditText-->
<!--                android:id="@+id/edtPassword"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="50dp"-->
<!--                android:layout_marginTop="10dp"-->
<!--                android:focusableInTouchMode="true"-->
<!--                android:inputType="number"-->
<!--                android:maxLength="6"-->
<!--                android:textSize="20sp"-->
<!--                app:psw_border_color="@color/black20"-->
<!--                app:psw_border_radius="12dp"-->
<!--                app:psw_border_selected_color="@color/black20"-->
<!--                app:psw_border_width="2dp"-->
<!--                app:psw_cover_circle_color="@color/black"-->
<!--                app:psw_cover_circle_radius="6dp"-->
<!--                app:psw_cover_text="*"-->
<!--                app:psw_cursor_width="2dp"-->
<!--                app:psw_item_margin="20dp"-->
<!--                app:psw_mode="Circle"-->
<!--                app:psw_show_cursor="false"-->
<!--                app:psw_style="StyleOneself"-->
<!--                app:psw_text_color="@color/black" />-->


<!--        </LinearLayout>-->


<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="horizontal">-->


<!--            <com.google.android.material.button.MaterialButton-->
<!--                android:id="@+id/btnYes"-->
<!--                style="@style/FontLocalization"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="50dp"-->
<!--                android:layout_marginTop="40dp"-->
<!--                android:layout_weight="1"-->
<!--                android:clickable="true"-->
<!--                android:focusable="true"-->
<!--                android:gravity="center"-->
<!--                android:orientation="horizontal"-->
<!--                android:text="@string/confirm2"-->
<!--                android:textAllCaps="false"-->
<!--                android:textColor="@color/white"-->
<!--                android:textSize="@dimen/_18ssp"-->
<!--                android:textStyle="bold"-->
<!--                app:backgroundTint="@color/primaryColor"-->
<!--                app:cornerRadius="15dp"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:strokeColor="@color/primaryColor"-->
<!--                app:strokeWidth="0dp" />-->

<!--        </LinearLayout>-->
<!--    </LinearLayout>-->

    <FrameLayout
        android:id="@+id/llActRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.metathought.food_order.casheir.ui.widget.blurview.ShapeBlurView
            android:id="@+id/vblurBg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:blur_overlay_color="@color/black60"
            app:blur_radius="25dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivLoc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="76dp"
                android:src="@drawable/icon_lock"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="10dp"
                android:gravity="center"
                android:text="@string/please_input_cash_box_pwd"
                android:textColor="@color/white"
                android:textSize="19sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivLoc" />


            <com.metathought.food_order.casheir.ui.widget.VerificationCodeViewPsw
                android:id="@+id/verificationCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="46dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle" />


<!--            <TextView-->
<!--                android:id="@+id/tvHint"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginStart="10dp"-->
<!--                android:layout_marginTop="20dp"-->
<!--                android:layout_marginEnd="10dp"-->
<!--                android:gravity="center"-->
<!--                android:textColor="@color/white"-->
<!--                android:textSize="13sp"-->

<!--                tools:text="密码错误" />-->

<!--            android:background="@drawable/background_cash_pwd_error"-->
            <LinearLayout
                android:id="@+id/llError"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/verificationCode">

                <TextView
                    android:id="@+id/tvError"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/pwd_error_input_again"
                    android:textColor="@color/main_yellow"
                    android:textSize="16sp" />

            </LinearLayout>


            <com.metathought.food_order.casheir.ui.widget.VerificationPinKeyboard2
                android:id="@+id/keyboard"
                android:layout_width="320dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="60dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/verificationCode" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
