<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        android:clipToPadding="false"
        android:padding="0dp"
        app:cardBackgroundColor="@color/color_e7f5ee"
        app:cardCornerRadius="12dp"
        app:cardElevation="10dp"
        app:strokeColor="@color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="25dp"
            android:paddingVertical="25dp">

            <TextView
                android:id="@+id/tvOnlinePayment"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:gravity="center"
                android:paddingVertical="15dp"
                android:text="@string/online_payment"
                android:textColor="@color/primaryColor"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvPayByCash"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"

                android:gravity="center"
                android:paddingVertical="15dp"
                android:text="@string/offline_payments"
                android:textColor="@color/primaryColor"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvBalance"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:paddingVertical="15dp"
                android:text="@string/pay_by_balance"
                android:textColor="@color/primaryColor"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
</FrameLayout>