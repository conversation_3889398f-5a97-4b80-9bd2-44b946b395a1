<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/please_input_product_weight"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvSinglePrice"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="单价：$99.99/KG" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutWeight"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/weight"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtWeight"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="numberDecimal"
                    android:maxLength="60"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>


            <TextView
                android:id="@+id/tvUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="KG" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/llWeightSeting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="@string/enable_manual_input"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/ivSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_switch_close" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:layout_marginBottom="10dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/white"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfirm"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm2"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent" />
        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>