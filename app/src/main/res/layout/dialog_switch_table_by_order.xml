<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/change_table_cue"
                android:textColor="@color/black"
                android:textSize="20sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/tvContent"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:layout_weight="1"
            android:gravity="center"
            android:minLines="3"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLeft"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="30dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/no"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/white"
                app:cornerRadius="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnRight"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="30dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/change"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/primaryColor"
                app:strokeWidth="0dp" />
        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
