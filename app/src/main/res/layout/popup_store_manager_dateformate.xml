<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="3dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:id="@+id/tvNone"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="@string/none"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvYYYY"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="yyyy"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvYY"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="yy"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvYYYYMM"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="yyyymm"
                android:textColor="@color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvYYMM"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:fontFamily="@font/roboto"
                android:gravity="start|center_vertical"
                android:text="yymm"
                android:textColor="@color/black"
                tools:visibility="visible" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>


</LinearLayout>