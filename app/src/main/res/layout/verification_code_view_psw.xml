<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layoutDirection="ltr">

    <FrameLayout
        android:id="@+id/container_zero"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_verification_code_psw"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/code_zero"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:visibility="invisible"
            tools:text="0" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/container_one"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:background="@drawable/selector_verification_code_psw"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/container_zero"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/code_one"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:visibility="invisible"
            tools:text="1" />

    </FrameLayout>


    <FrameLayout
        android:id="@+id/container_two"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:background="@drawable/selector_verification_code_psw"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/container_one"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/code_two"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:layout_gravity="center"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:visibility="invisible"
            tools:text="2" />

    </FrameLayout>


    <FrameLayout
        android:id="@+id/container_three"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:background="@drawable/selector_verification_code_psw"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/container_two"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/code_three"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:layout_gravity="center"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:visibility="invisible"
            tools:text="3" />

    </FrameLayout>


    <FrameLayout
        android:id="@+id/container_four"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:background="@drawable/selector_verification_code_psw"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/container_three"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/code_four"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:layout_gravity="center"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:visibility="invisible"
            tools:text="4" />

    </FrameLayout>


    <FrameLayout
        android:id="@+id/container_five"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:background="@drawable/selector_verification_code_psw"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/container_four"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/code_five"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:layout_gravity="center"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:visibility="invisible"
            tools:text="5" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>