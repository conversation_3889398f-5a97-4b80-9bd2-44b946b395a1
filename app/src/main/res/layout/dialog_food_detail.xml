<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end"
            android:paddingHorizontal="16dp"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDishedName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/item_detail"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                tools:text="Item Detail" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingHorizontal="16dp">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/nestedScrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15dp"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp">

                            <com.denzcoskun.imageslider.ImageSlider
                                android:id="@+id/imageSlider"
                                android:layout_width="300dp"
                                android:layout_height="150dp"
                                android:scaleType="centerCrop"
                                android:src="@drawable/icon_menu_default2"
                                app:iss_auto_cycle="true"
                                app:iss_delay="5000"
                                app:iss_error_image="@drawable/icon_menu_default2"
                                app:iss_period="5000"
                                app:iss_placeholder="@drawable/icon_menu_default2"
                                app:iss_text_align="CENTER" />

                        </androidx.cardview.widget.CardView>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvDetailInfo"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:paddingHorizontal="15dp"
                                android:textColor="@color/black"
                                android:textSize="16sp"
                                app:layout_constrainedHeight="true"
                                app:layout_constraintBottom_toTopOf="@id/llDesc"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_bias="0"
                                app:layout_constraintVertical_chainStyle="packed"
                                tools:text="@tools:sample/lorem/random" />
                            <!--                            tools:text="@tools:sample/lorem/random"-->
                            <LinearLayout
                                android:id="@+id/llDesc"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="15dp"
                                android:layout_marginTop="6dp"
                                android:orientation="horizontal"
                                app:layout_constraintBottom_toTopOf="@id/tvServiceChargePercentage"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tvDetailInfo"
                                app:layout_constraintVertical_chainStyle="packed">

                                <TextView
                                    android:id="@+id/tvDiscountRate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="6dp"
                                    android:background="@drawable/background_e7f5ee_radius_4"
                                    android:padding="2dp"
                                    android:textColor="@color/primaryColor"
                                    android:textSize="@dimen/_11ssp"
                                    android:visibility="gone"
                                    tools:text="70% OFF"
                                    tools:visibility="visible" />

                                <TextView
                                    android:id="@+id/tvDiscountActivity"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingHorizontal="4dp"
                                    android:paddingVertical="2dp"
                                    android:textColor="@color/primaryColor"
                                    android:textSize="@dimen/_11ssp"
                                    android:visibility="gone"
                                    tools:text="70% OFF"
                                    tools:visibility="visible" />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/tvServiceChargePercentage"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="15dp"
                                android:layout_marginTop="6dp"
                                android:textColor="@color/primaryColor"
                                android:visibility="gone"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/llDesc"
                                app:layout_constraintVertical_chainStyle="packed"
                                tools:text="需收取4%服务费"
                                tools:visibility="visible" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvMealSet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/meal_set_group_item" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerSpecification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/specification_main_item" />

                    <TextView
                        android:id="@+id/tvToppingsTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingBottom="5dp"
                        android:text="@string/toppings"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerTopping"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        tools:itemCount="6"
                        tools:listitem="@layout/topping_item" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <ProgressBar
                android:id="@+id/pbLoadSpecification"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginStart="10dp"
                android:indeterminate="true"
                android:indeterminateTint="@color/primaryColor"
                android:indeterminateTintMode="src_atop"
                android:progressTint="@color/primaryColor"
                android:visibility="visible" />
        </FrameLayout>

        <include
            android:id="@+id/layoutReachLimitPurchase"
            layout="@layout/reach_limit_purchase_layout" />

        <View
            style="@style/commonDividerStyle"
            android:background="@color/black12" />

        <LinearLayout
            android:id="@+id/layoutBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical"
            android:minHeight="60dp"
            android:paddingHorizontal="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:baselineAligned="true">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="3dp"
                        android:gravity="top"
                        android:text="@string/total"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        tools:text="@string/total" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="start"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvTotal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="bottom"
                            android:textColor="@color/black"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            tools:text="$11" />

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvOriginalPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:ellipsize="end"
                            android:foreground="@drawable/strike_price"
                            android:maxLines="3"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="gone"
                            tools:text="12.99"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvTimePriceSign"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:text="@string/time_price_parentheses"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                </LinearLayout>


            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:gravity="bottom">

                <ImageView
                    android:id="@+id/imgMinus"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_minus"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/tvQTY"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:maxLength="4"
                    android:paddingHorizontal="10dp"
                    android:text="1"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <ImageView
                    android:id="@id/imgPlus"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:alpha="0.5"
                    android:clickable="false"
                    android:src="@drawable/ic_add" />
            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAdd"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:alpha="0.5"
                android:enabled="false"
                android:text="@string/add"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
