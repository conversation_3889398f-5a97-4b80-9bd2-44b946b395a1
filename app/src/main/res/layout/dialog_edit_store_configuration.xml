<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:padding="25dp">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        app:dialog_title="@string/store_configuration"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:layout_weight="1"
        android:scrollbars="none"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/llButtom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBar"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/manager_store_status_required"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <RadioGroup
                android:id="@+id/radioStatus"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:checkedButton="@id/radioNormalBusiness"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <RadioButton
                    android:id="@+id/radioNormalBusiness"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/radiobutton_only_stroke_background"
                    android:button="@null"
                    android:checked="true"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/normal_business_hours"
                    android:textColor="@drawable/selector_language_color"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

                <RadioButton
                    android:id="@+id/radioOnlineNoOperating"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/radiobutton_only_stroke_background"
                    android:button="@null"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:minHeight="0dp"
                    android:paddingHorizontal="10dp"
                    android:text="@string/online_but_not_operating"
                    android:textColor="@drawable/selector_language_color"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

                <RadioButton
                    android:id="@+id/radioCloseStore"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/radiobutton_only_stroke_background"
                    android:button="@null"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:minHeight="0dp"
                    android:paddingHorizontal="10dp"
                    android:text="@string/close_store"
                    android:textColor="@drawable/selector_language_color"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

            </RadioGroup>

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/manager_store_payment_required"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <RadioGroup
                android:id="@+id/radioPayment"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:checkedButton="@id/radioPayFirst"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <RadioButton
                    android:id="@+id/radioPayFirst"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/radiobutton_only_stroke_background"
                    android:button="@null"
                    android:checked="true"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/pay_first"
                    android:textColor="@drawable/selector_language_color"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

                <RadioButton
                    android:id="@+id/radioPostPay"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/radiobutton_only_stroke_background"
                    android:button="@null"
                    android:drawableStart="@drawable/radiobutton_payment_icon"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:minHeight="0dp"
                    android:paddingHorizontal="10dp"
                    android:text="@string/post_pay"
                    android:textColor="@drawable/selector_language_color"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

            </RadioGroup>

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchPeopleNum"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:switch_title="@string/number_of_people_dining_is_required" />

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchTableShared"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:switch_title="@string/manager_store_table_service" />

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchShowTable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:switch_title="@string/manager_store_show_table" />

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchAutoAccept"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:switch_title="@string/manager_store_auto_accept" />

            <FrameLayout
                android:id="@+id/flAcceptTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutAcceptTime"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/order_auto_cancel_time"
                    android:textColorHint="@color/black60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtAcceptTime"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:inputType="text"
                        android:maxLength="30"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <ImageView
                    android:id="@+id/arrowAcceptTime"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical|end"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </FrameLayout>

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchShowMenuImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:switch_title="@string/manager_store_casher_menu_show_image" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginVertical="24dp"
                android:background="@drawable/imaginary_line"
                android:layerType="software" />

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchMealCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:switch_title="@string/manager_store_ticket_need_meal_code" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/llMealCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/every_day_from"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textInputLayoutStartMealCode"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_weight="1"
                        android:hint="@string/start_required"
                        android:textColorHint="@color/black60">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edtStartMealCode"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="10dp"
                            android:inputType="number"
                            android:maxLength="3"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textInputLayoutEndMealCode"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_weight="1"
                        android:hint="@string/end_required"
                        android:textColorHint="@color/black60">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edtEndMealCode"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="10dp"
                            android:inputType="number"
                            android:maxLength="3"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/loop"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvPickUpNoError"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/please_input_pickup_no_error"
                    android:textColor="@color/main_red"
                    android:textSize="@dimen/_12ssp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginVertical="24dp"
                android:background="@drawable/imaginary_line"
                android:layerType="software" />

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchInvoiceCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:switch_title="@string/manager_store_ticket_need_invoice_number" />

            <LinearLayout
                android:id="@+id/llInvoiceCodeInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutPrefix"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/prefix_required"
                    android:textColorHint="@color/black60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPrefix"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLength="5"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textInputLayoutDateType"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/date_type"
                        android:textColorHint="@color/black60">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edtDateType"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="10dp"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:inputType="text"
                            android:maxLength="30"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <ImageView
                        android:id="@+id/arrow"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical|end"
                        android:layout_marginEnd="20dp"
                        android:src="@drawable/ic_dropdown"
                        android:visibility="visible"
                        tools:ignore="ContentDescription" />
                </FrameLayout>


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutInvoiceCode"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/starting_invoice_serial_number"
                    android:textColorHint="@color/black60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtInvoiceCode"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="number"
                        android:maxLength="6"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/tvInvoiceCue"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/set_invoice_code_cue"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_14ssp" />

            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginVertical="24dp"
                android:background="@drawable/imaginary_line"
                android:layerType="software" />

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchTaxInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:switch_title="@string/manager_store_ticket_need_tax" />

            <LinearLayout
                android:id="@+id/llTaxInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCompanyName"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/company_name"
                    android:textColorHint="@color/black60"
                    app:counterEnabled="true"
                    app:counterMaxLength="100">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCompanyName"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLength="100"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCompanyVatTin"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/company_vat_tin"
                    android:textColorHint="@color/black60"
                    app:counterEnabled="true"
                    app:counterMaxLength="100">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCompanyVatTin"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLength="100"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCompanyAddress"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/company_address"
                    android:textColorHint="@color/black60"
                    app:counterEnabled="true"
                    app:counterMaxLength="200">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCompanyAddress"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLength="200"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCompanyPhone"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/company_phone"
                    android:textColorHint="@color/black60"
                    app:counterEnabled="true"
                    app:counterMaxLength="20">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCompanyPhone"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLength="20"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutCompanyEmail"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/company_email"
                    android:textColorHint="@color/black60"
                    app:counterEnabled="true"
                    app:counterMaxLength="40">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtCompanyEmail"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:inputType="text"
                        android:maxLength="40"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>


            </LinearLayout>

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchAutoCheckoutTicket"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:switch_desc="@string/auto_print_checkout_ticket_cue1"
                app:switch_title="@string/auto_print_checkout_ticket" />


            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginVertical="24dp"
                android:background="@drawable/imaginary_line"
                android:layerType="software" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/flConversion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutConversion"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/config_conversion_ratio_require"
                    android:textColorHint="@color/black60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clickable="false"
                        android:drawablePadding="10dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:inputType="text"
                        android:maxLength="30"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text="USD1=KHR"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />

                </com.google.android.material.textfield.TextInputLayout>

                <EditText
                    android:id="@+id/edtConversion"
                    android:layout_width="0dp"
                    android:layout_height="54dp"
                    android:layout_marginStart="100dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="100dp"
                    android:background="@null"
                    android:inputType="number"
                    android:maxLines="1"
                    android:text="4100"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black"
                    android:textSize="@dimen/_14ssp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textInputLayoutConversion" />

                <ImageView
                    android:id="@+id/ivConversionHistory"
                    android:layout_width="wrap_content"
                    android:layout_height="54dp"
                    android:layout_gravity="end"
                    android:layout_marginTop="4dp"
                    android:paddingHorizontal="20dp"
                    android:src="@drawable/icon_history"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/textInputLayoutConversion" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginVertical="24dp"
                android:background="@drawable/imaginary_line"
                android:layerType="software" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutRemark"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/remark"
                android:textColorHint="@color/black60"
                app:counterEnabled="true"
                app:counterMaxLength="100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtRemark"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="140dp"
                    android:drawablePadding="10dp"
                    android:gravity="top"
                    android:inputType="textMultiLine"
                    android:maxLength="100"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>
    </ScrollView>

    <ProgressBar
        android:id="@+id/pbLoading"
        android:layout_width="25dp"
        android:layout_height="25dp"
        app:layout_constraintBottom_toTopOf="@id/llButtom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBar" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llButtom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:divider="@drawable/shape_option_item_pading_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:showDividers="middle">

        <TextView
            android:id="@+id/btnDone"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_login_background"
            android:gravity="center"
            android:text="@string/done"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>