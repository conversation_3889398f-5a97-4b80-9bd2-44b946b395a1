<?xml version="1.0" encoding="utf-8"?>
<GridLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:columnCount="2"
    android:rowCount="3"
    android:useDefaultMargins="true">

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:background="@drawable/background_white_border_black12_radius_4">

        <TextView
            android:id="@+id/tvNumber0"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivEdit0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:padding="5dp"
            android:src="@drawable/icon_weight_edit" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:background="@drawable/background_white_border_black12_radius_4">

        <TextView
            android:id="@+id/tvNumber1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivEdit1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:padding="5dp"
            android:src="@drawable/icon_weight_edit" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:background="@drawable/background_white_border_black12_radius_4">

        <TextView
            android:id="@+id/tvNumber2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivEdit2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:padding="5dp"
            android:src="@drawable/icon_weight_edit" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:background="@drawable/background_white_border_black12_radius_4">

        <TextView
            android:id="@+id/tvNumber3"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivEdit3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:padding="5dp"
            android:src="@drawable/icon_weight_edit" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:background="@drawable/background_white_border_black12_radius_4">

        <TextView
            android:id="@+id/tvNumber4"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivEdit4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:padding="5dp"
            android:src="@drawable/icon_weight_edit" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:background="@drawable/background_white_border_black12_radius_4">

        <TextView
            android:id="@+id/tvNumber5"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivEdit5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:padding="5dp"
            android:src="@drawable/icon_weight_edit" />
    </FrameLayout>

</GridLayout>