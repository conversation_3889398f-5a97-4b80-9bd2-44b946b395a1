<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/mainBackground"
    android:orientation="vertical"
    android:padding="40dp">

    <LinearLayout
        android:id="@+id/layoutQR"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/top_khqr"
            android:padding="6dp"
            android:src="@drawable/ic_khqr_logo" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/info_khqr"
            android:orientation="vertical"
            android:paddingLeft="30dp"
            android:gravity="center_vertical"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvScanQRName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:lines="1"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:visibility="visible"
                app:autoSizeTextType="uniform"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvScanQRAmount"
                tools:text="$673.57"
                android:text="$673.57"
                android:layout_gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lines="1"
                android:textStyle="bold"
                style="@style/FontLocalization"
                android:textColor="@color/black"
                android:textSize="25sp"
                app:autoSizeTextType="uniform"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />


        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bottom_khqr"
            android:padding="30dp">

            <ImageView
                android:id="@+id/imgQR"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_qr_download" />
        </RelativeLayout>

    </LinearLayout>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_marginTop="40dp"
        android:id="@+id/tvInfo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/layoutQR"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="请在失效前扫码支付"
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        style="@style/FontLocalization"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:textSize="30sp"
        app:autoSizeTextType="uniform"
        app:layout_constrainedWidth="true"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDuration"
        android:layout_marginTop="10dp"
        android:background="@drawable/background_language_spiner"
        android:paddingHorizontal="30dp"
        android:paddingVertical="10dp"
        app:layout_constraintTop_toBottomOf="@id/tvInfo"
        app:layout_constraintStart_toEndOf="@+id/layoutQR"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:textStyle="bold"
        android:textColor="@color/khqr_red_color"
        android:textSize="30sp"
        app:autoSizeTextType="uniform"
        app:layout_constrainedWidth="true"
        />
    <ImageView
        android:padding="20dp"
        app:layout_constraintTop_toBottomOf="@id/tvDuration"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/layoutQR"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_centerInParent="true"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_secondary_info" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutAd"
        android:layout_width="match_parent"
        tools:visibility="gone"
        android:background="@color/mainBackground"
        android:layout_height="match_parent">
        <ImageView

            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:adjustViewBounds="true"
            android:src="@drawable/ic_banner_top2" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>