<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_dialog"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginBottom="10dp"
        android:gravity="center_vertical"
        tools:ignore="UseCompoundDrawables">

        <TextView
            android:id="@+id/tvDishedName"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="start"
            android:text="@string/request_refund"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="5dp"
            android:src="@drawable/ic_cross_closed"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/layoutMainOrdered"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo">

            <LinearLayout
                android:id="@+id/layoutFood"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/background_dialog_info"
                android:orientation="vertical"
                android:padding="10dp"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/layoutHeader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/items"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="2.6"
                        android:drawablePadding="3dp"
                        android:text="@string/items"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/quantity"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/quantity"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/amount"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/refundQty"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/refund_qty"
                        android:textColor="@color/black"
                        android:textSize="12sp" />


                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/height_bar"
                    android:background="@color/black08" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerOrderedFood"
                    android:layout_width="match_parent"
                    android:layout_height="180dp"
                    android:layout_weight="1"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:paddingBottom="10dp"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_second_refund_menu_item" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/layoutTotal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/refundQuantity"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_quantity"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundQTY"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPaymentMethod1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/paymentMethod1"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:text="@string/payment_method"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvPaymentMethod1"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        tools:text="U-Pay" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llBalanceRefund"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/balanceRefund"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_total_price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundTotalAmount1"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPaymentMethod"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/paymentMethod2"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:text="@string/payment_method"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvPaymentMethod"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        tools:text="U-Pay" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/layoutRefundPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/refundAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_amount"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundPrice"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutRefundPack"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/refundPack"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_pack_price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundPack"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutRefundService"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical"
                    android:visibility="visible"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/refundService"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_service_fee"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundService"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutRefundVat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/refundVat"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_vat"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundVat"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/totalRefundAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_total_price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvRefundTotalAmount"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />

                        <TextView
                            android:id="@+id/tvRefundTotalKhrAmount"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="៛0" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <View
                android:id="@+id/llFullView"
                android:layout_width="450dp"
                android:layout_height="1dp"
                android:visibility="gone" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
