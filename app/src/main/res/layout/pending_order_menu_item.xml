<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layoutBg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_menu_order_bg_color"
        android:orientation="vertical"
        android:padding="4dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.7" />

            <LinearLayout
                android:id="@+id/llGoodsInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="3dp"
                android:orientation="vertical"
                android:text="@string/items"
                android:textColor="@color/black50"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/guideline5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvFoodName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_weight="2"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    tools:text="Lemonade" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvTmpSign"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingHorizontal="4dp"
                        android:text="@string/temporary"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvDiscountActivity"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:paddingHorizontal="4dp"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_12ssp"
                        android:visibility="gone"
                        tools:text="第二份半价"
                        tools:visibility="visible" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvFoodSubName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_weight="1.2"
                    android:ellipsize="end"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_12ssp"
                    tools:text="Large Normal ice, Normal sugar" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginEnd="3dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toStartOf="@+id/guideline7"
                app:layout_constraintStart_toEndOf="@+id/guideline5"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvFoodCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="center_horizontal"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    tools:text="1" />

                <TextView
                    android:id="@+id/tvRefundMinusCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="center_horizontal"
                    android:textColor="@color/main_red"
                    android:textSize="@dimen/_16ssp"
                    android:visibility="gone"
                    tools:text="1"
                    tools:visibility="visible" />
            </LinearLayout>

            <include
                android:id="@+id/layoutPrice"
                layout="@layout/layout_item_price_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline7"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="llGoodsInfo,llCount" />

            <com.metathought.food_order.casheir.ui.widget.NonInterceptRecyclerView
                android:id="@+id/rvMealGoodsList"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:contextClickable="false"
                android:descendantFocusability="blocksDescendants"
                android:enabled="false"
                android:focusable="false"
                android:layout_marginTop="3dp"
                android:focusableInTouchMode="false"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                android:overScrollMode="never"
                android:scrollbars="none"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toStartOf="@+id/guideline7"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/barrier"
                tools:listitem="@layout/food_sub_table_item"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/tvRemark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="start"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/_12ssp"
            android:visibility="gone"
            tools:text="备注 : $9.9"
            tools:visibility="visible" />


    </LinearLayout>
</LinearLayout>
