<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/background_popup_dropdown"
    android:padding="10dp"
    android:gravity="center_horizontal">

    <TextView
        style="@style/FontLocalization"
        android:id="@+id/tvAll"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:textColor="@color/black"
        android:text="@string/all"/>
    <TextView
        android:id="@+id/tvPaid"
        android:gravity="center"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:textColor="@color/black"
        android:text="@string/consumption"/>
    <TextView
        style="@style/FontLocalization"
        android:id="@+id/tvRefund"
        android:gravity="center"
        android:textColor="@color/black"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/refund"/>
    <TextView
        style="@style/FontLocalization"
        android:id="@+id/tvTopup"
        android:gravity="center"
        android:textColor="@color/black"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/top_up"/>

</LinearLayout>