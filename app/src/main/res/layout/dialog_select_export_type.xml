<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/export" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginVertical="16dp"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llExportFormatGroupType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="2dp"
                        android:text="@string/export_formats_require"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/exportFormatsRadioType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/radioExcel"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radiobutton_only_stroke_background"
                            android:button="@null"
                            android:checked="true"
                            android:drawableStart="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="8dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/excel"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                        <RadioButton
                            android:id="@+id/radioPdf"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/radiobutton_only_stroke_background"
                            android:button="@null"
                            android:checked="false"
                            android:drawableStart="@drawable/radiobutton_payment_icon"
                            android:drawablePadding="8dp"
                            android:gravity="center"
                            android:paddingHorizontal="10dp"
                            android:text="@string/pdf"
                            android:textColor="@drawable/radio_payment_text_selector"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            app:buttonCompat="@null" />

                    </RadioGroup>
                </LinearLayout>

            </LinearLayout>
        </ScrollView>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnYes"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="24dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:text="@string/confirm2"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            app:backgroundTint="@color/primaryColor"
            app:cornerRadius="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:strokeColor="@color/primaryColor"
            app:strokeWidth="0dp" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
