<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="600dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDishedName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/cancel_dish"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/layoutMainOrdered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo">

                <LinearLayout
                    android:id="@+id/layoutFood"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_dialog_info"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <LinearLayout
                        android:id="@+id/layoutHeader"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="2"
                            android:drawablePadding="3dp"
                            android:text="@string/items"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:gravity="center_horizontal"
                            android:text="@string/quantity"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1.2"
                            android:gravity="center_horizontal"
                            android:text="@string/return_back_dish_num"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:gravity="center_horizontal"
                            android:text="@string/amount"
                            android:textColor="@color/black"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/height_bar"
                        android:background="@color/black08" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerOrderedFood"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_weight="1"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="10dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/refunds_menu_item" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="23dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/cancel_dish_num"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvCancelNum"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="1" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/cancel_dish_amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvCancelAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutCancelPackingAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/cancel_dish_packing_charge"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvCancelPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutCancelServiceAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/cancel_service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvCancelServiceAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutCancelVat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/cancel_dish_vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvCancelVat"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/cancel_dish_total_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvCancelTotalAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="24dp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llAutoInStore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="24dp"
                        android:background="@color/black12" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start"
                            android:text="@string/automatically_store_returned_goods"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp" />

                        <ImageView
                            android:id="@+id/ivSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_switch_close" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCancel"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:text="@string/cancel"
                        android:textAllCaps="false"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:strokeColor="@color/black20"
                        app:strokeWidth="1dp" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnConfirm"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:text="@string/confirm_cancel_dish"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        app:backgroundTint="@color/primaryColor"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent" />
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>