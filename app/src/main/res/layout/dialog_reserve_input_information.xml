<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"


    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:gravity="center_vertical"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/customer_info"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="16dp"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPeople"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/hint_number_of_people_required"
                android:textColorHint="@color/black60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtPeople"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="number"
                    android:maxLength="3"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutCustomerName"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/hint_customer_name_required"
                android:textColorHint="@color/black60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtCustomerName"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="@integer/name_max_length"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutPhoneNumber"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:hint="@string/hint_phone_number_required"
                    android:textColorHint="@color/black60"
                    app:startIconDrawable="@drawable/ic_rectangle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPhoneNumber"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:inputType="phone"
                        android:maxLength="15"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.hbb20.CountryCodePicker
                    android:id="@+id/countryCodeHolder"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:visibility="visible"
                    app:ccpDialog_keyboardAutoPopup="false"
                    app:ccp_defaultPhoneCode="855"
                    app:ccp_showFlag="false"
                    app:ccp_showNameCode="false"
                    app:ccp_textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent" />

            </FrameLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutDiningTime"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                android:hint="@string/hint_dining_time_require"
                android:textColorHint="@color/black60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtDiningTime"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawableEnd="@drawable/ic_calendar"
                    android:drawablePadding="10dp"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>


        <LinearLayout
            android:id="@+id/btnReserve"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/button_login_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save"
                android:textColor="@color/mainWhite"
                android:textSize="18sp" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
