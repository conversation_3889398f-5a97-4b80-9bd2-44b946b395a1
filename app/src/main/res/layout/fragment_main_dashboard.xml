<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainWhite"
    tools:context=".ui.app_dashbord.MainDashboardFragment">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="@color/primaryColor"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp">

            <LinearLayout
                android:id="@+id/dropdownUser"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:background="@drawable/background_language_spiner"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp">

                <cn.bingoogolapple.badgeview.BGABadgeImageView
                    android:id="@+id/imgLogo"
                    android:layout_width="24dp"
                    android:layout_height="24dp" />

                <TextView
                    android:id="@+id/tvStoreName"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/brand_name"
                    android:textColor="@color/black" />

                <ImageView
                    android:id="@+id/arrowUser"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="10dp"
                android:layout_weight="1">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/navigationMenu"
                    style="@style/Widget.MaterialComponents.NavigationRailView.Compact"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/primaryColor"
                    android:elevation="0dp"
                    android:overScrollMode="never"
                    android:padding="0dp"
                    android:scrollbars="none"
                    app:elevation="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:itemCount="6"
                    tools:listitem="@layout/features_items" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/tvContactUs"
                android:layout_width="wrap_content"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/background_language_spiner"
                android:drawablePadding="10dp"
                android:gravity="center"
                android:minWidth="80dp"
                android:paddingHorizontal="12dp"
                android:text="@string/contact_us"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:visibility="gone" />

            <!--        <LinearLayout-->
            <!--            android:id="@+id/dropdownLanguage"-->
            <!--            android:layout_width="120dp"-->
            <!--            android:layout_height="45dp"-->
            <!--            android:layout_marginEnd="20dp"-->
            <!--            android:background="@drawable/background_language_spiner"-->
            <!--            android:clickable="true"-->
            <!--            android:focusable="true"-->
            <!--            android:gravity="center"-->
            <!--            android:orientation="horizontal"-->
            <!--            android:paddingHorizontal="10dp"-->
            <!--            android:visibility="gone">-->

            <!--            <TextView-->
            <!--                android:id="@+id/tvLanguages"-->
            <!--                style="@style/FontLocalization"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_weight="1"-->
            <!--                android:gravity="center"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="16sp"-->
            <!--                tools:text="English" />-->

            <!--            &lt;!&ndash;            <ImageView&ndash;&gt;-->
            <!--            &lt;!&ndash;                android:id="@+id/arrow"&ndash;&gt;-->
            <!--            &lt;!&ndash;                android:layout_width="25dp"&ndash;&gt;-->
            <!--            &lt;!&ndash;                android:layout_height="25dp"&ndash;&gt;-->
            <!--            &lt;!&ndash;                android:src="@drawable/ic_dropdown"&ndash;&gt;-->
            <!--            &lt;!&ndash;                android:visibility="visible"&ndash;&gt;-->
            <!--            &lt;!&ndash;                tools:ignore="ContentDescription" />&ndash;&gt;-->
            <!--        </LinearLayout>-->

            <!--        <LinearLayout-->
            <!--          -->
            <!--            android:layout_width="wrap_content"-->
            <!--            android:layout_height="45dp"-->
            <!--            android:background="@drawable/background_language_spiner"-->
            <!--            android:clickable="true"-->
            <!--            android:focusable="true"-->
            <!--            android:gravity="center"-->
            <!--            android:minWidth="100dp"-->
            <!--            android:orientation="horizontal"-->
            <!--            android:paddingHorizontal="10dp">-->

            <!--            <ImageView-->
            <!--                android:id="@+id/imgUserProfile"-->
            <!--                android:layout_width="25dp"-->
            <!--                android:layout_height="25dp"-->
            <!--                android:layout_marginEnd="10dp"-->
            <!--                android:src="@drawable/ic_profile"-->
            <!--                android:visibility="visible"-->
            <!--                tools:ignore="ContentDescription" />-->

            <!--            <TextView-->
            <!--                android:id="@+id/tvUserName"-->
            <!--                style="@style/FontLocalization"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginEnd="10dp"-->
            <!--                android:layout_weight="1"-->
            <!--                android:gravity="center"-->
            <!--                android:text="@string/staff_name"-->
            <!--                android:textColor="@color/black"-->
            <!--                android:textSize="16sp" />-->

            <!--&lt;!&ndash;            <ImageView&ndash;&gt;-->
            <!--&lt;!&ndash;                android:id="@+id/arrowUser"&ndash;&gt;-->
            <!--&lt;!&ndash;                android:layout_width="25dp"&ndash;&gt;-->
            <!--&lt;!&ndash;                android:layout_height="25dp"&ndash;&gt;-->
            <!--&lt;!&ndash;                android:src="@drawable/ic_dropdown"&ndash;&gt;-->
            <!--&lt;!&ndash;                android:visibility="visible"&ndash;&gt;-->
            <!--&lt;!&ndash;                tools:ignore="ContentDescription" />&ndash;&gt;-->
            <!--        </LinearLayout>-->
        </LinearLayout>

        <!--    <com.google.android.material.navigationrail.NavigationRailView-->
        <!--        android:id="@+id/navigationMenu"-->
        <!--        style="@style/Widget.MaterialComponents.NavigationRailView.Compact"-->
        <!--        android:layout_width="100dp"-->
        <!--        android:layout_height="match_parent"-->
        <!--        android:layout_below="@id/toolbar"-->
        <!--        android:background="@color/white"-->
        <!--        android:elevation="0dp"-->
        <!--        android:padding="0dp"-->
        <!--        app:elevation="0dp"-->
        <!--        app:labelVisibilityMode="labeled"-->
        <!--        app:menu="@menu/features_menu" />-->

        <!--    <LinearLayout-->
        <!--        android:id="@+id/llMenu"-->
        <!--        android:layout_width="wrap_content"-->
        <!--        android:layout_height="wrap_content"-->
        <!--        android:layout_below="@+id/toolbar"-->
        <!--        android:layout_weight="1"-->
        <!--        android:orientation="horizontal">-->

        <!--        &lt;!&ndash;        <LinearLayout&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:id="@+id/llOpenCashBox"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:layout_width="match_parent"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:layout_height="wrap_content"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:layout_marginVertical="20dp"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:gravity="center"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:orientation="vertical"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:padding="5dp"&ndash;&gt;-->
        <!--        &lt;!&ndash;            android:visibility="gone"&ndash;&gt;-->
        <!--        &lt;!&ndash;            app:layout_constraintEnd_toEndOf="parent"&ndash;&gt;-->
        <!--        &lt;!&ndash;            app:layout_constraintStart_toStartOf="parent"&ndash;&gt;-->
        <!--        &lt;!&ndash;            app:layout_constraintTop_toTopOf="parent"&ndash;&gt;-->
        <!--        &lt;!&ndash;            tools:visibility="visible">&ndash;&gt;-->

        <!--        &lt;!&ndash;            <ImageView&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:layout_width="wrap_content"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:layout_height="wrap_content"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:src="@drawable/icon_cash_box" />&ndash;&gt;-->

        <!--        &lt;!&ndash;            <TextView&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:layout_width="wrap_content"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:layout_height="wrap_content"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:layout_marginTop="5dp"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:gravity="center_horizontal"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:maxLines="2"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:text="@string/open_cashbox"&ndash;&gt;-->
        <!--        &lt;!&ndash;                android:textSize="12sp"&ndash;&gt;-->
        <!--        &lt;!&ndash;                app:layout_constraintEnd_toEndOf="parent"&ndash;&gt;-->
        <!--        &lt;!&ndash;                app:layout_constraintStart_toStartOf="parent" />&ndash;&gt;-->

        <!--        &lt;!&ndash;        </LinearLayout>&ndash;&gt;-->

        <!--        <cn.bingoogolapple.badgeview.BGABadgeTextView-->
        <!--            android:id="@+id/tvVersion"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_gravity="center"-->
        <!--            android:layout_marginHorizontal="4dp"-->
        <!--            android:layout_marginBottom="15dp"-->
        <!--            android:gravity="center"-->
        <!--            android:textColor="@color/black40"-->
        <!--            android:textSize="@dimen/_12ssp"-->
        <!--            tools:text="v1.0.0\nVersion" />-->

        <!--    </LinearLayout>-->

        <LinearLayout
            android:id="@+id/llNotice"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:background="@color/color_e7f5ee"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:visibility="gone"
            tools:visibility="visible">

            <com.metathought.food_order.casheir.ui.widget.AlwaysMarqueeTextView
                android:id="@+id/tvNotice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:ellipsize="marquee"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_12ssp"
                android:textStyle="bold"
                tools:text="V2.9.1汉字" />
            <!--        <TextView-->
            <!--            android:id="@+id/tvNotice"-->
            <!--            android:layout_width="0dp"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:layout_marginHorizontal="10dp"-->
            <!--            android:layout_weight="1"-->
            <!--            android:ellipsize="marquee"-->
            <!--            android:maxLines="1"-->
            <!--            android:textColor="@color/black"-->
            <!--            android:textSize="@dimen/_12ssp"-->
            <!--            android:textStyle="bold" />-->

            <ImageView
                android:id="@+id/ivCloseNotice"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_cross_closed" />

        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <fragment
                android:id="@+id/dashboard_fragment"
                android:name="androidx.navigation.fragment.NavHostFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/mainWhite"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </FrameLayout>

    </LinearLayout>

    <!-- Gray Overlay - 覆盖整个屏幕包括toolbar -->
    <View
        android:id="@+id/grayOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone" />

    <!-- Payment Fragment Container - 覆盖整个屏幕包括toolbar -->
    <FrameLayout
        android:id="@+id/paymentFragmentContainer"
        android:layout_width="900dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:layout_marginStart="8dp"
        android:background="@android:color/transparent"
        android:visibility="gone" />

</FrameLayout>