<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_efefef_radius_10"
    android:minHeight="66dp"
    android:padding="10dp">

    <!-- 会员昵称 -->
    <TextView
        android:id="@+id/tvMemberName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvMemberBalance"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="张三" />

    <!-- 余额信息 -->
    <TextView
        android:id="@+id/tvMemberBalance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvMemberName"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="余额: ¥1,234.56" />

    <!-- 会员号 -->
    <TextView
        android:id="@+id/tvMemberNumber"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvMemberPhone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvMemberName"
        tools:text="138****8888" />

    <!-- 手机号 -->
    <TextView
        android:id="@+id/tvMemberPhone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvMemberNumber"
        app:layout_constraintTop_toTopOf="@id/tvMemberNumber"
        tools:text="138****8888" />


</androidx.constraintlayout.widget.ConstraintLayout>
