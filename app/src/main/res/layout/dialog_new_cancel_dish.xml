<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="560dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDishedName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/cancel_dish"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/layoutMainOrdered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo">


                <LinearLayout
                    android:id="@+id/layoutFood"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_dialog_info"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_weight="3"
                        android:orientation="vertical"
                        android:text="@string/items"
                        android:textColor="@color/black50"
                        android:textSize="14sp">

                        <TextView
                            android:id="@+id/tvFoodName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:ellipsize="end"
                            android:text="Lemonade"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvTmpSign"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="5dp"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:paddingHorizontal="4dp"
                                android:paddingVertical="2dp"
                                android:text="@string/temporary"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <TextView
                                android:id="@+id/tvDiscountActivity"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:paddingHorizontal="4dp"
                                android:paddingVertical="2dp"
                                android:textColor="@color/primaryColor"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:text="第二份半价"
                                tools:visibility="visible" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvFoodSubName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_weight="1.2"
                            android:ellipsize="end"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp"
                            android:visibility="gone"
                            tools:text="Large Normal ice, Normal sugar"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="top"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:orientation="vertical">-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/tvFoodCount"-->
                    <!--                            android:layout_width="match_parent"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_gravity="end"-->
                    <!--                            android:gravity="center_horizontal"-->
                    <!--                            android:textColor="@color/black"-->
                    <!--                            android:textSize="@dimen/_18ssp"-->
                    <!--                            tools:text="1" />-->


                    <!--                    </LinearLayout>-->

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_weight="1.2"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="center">

                            <ImageView
                                android:id="@+id/imgMinus"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_minus_disable"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/tvQTY"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:maxLength="4"
                                android:paddingHorizontal="10dp"
                                android:text="0"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_18ssp"
                                tools:text="0" />

                            <ImageView
                                android:id="@+id/imgPlus"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_add" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvRefundMinusCount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="8dp"
                            android:layout_marginEnd="20dp"
                            android:gravity="end"
                            android:textColor="@color/refund_text_color"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="gone"
                            tools:text="退1"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <include
                        android:id="@+id/layoutPrice"
                        layout="@layout/layout_item_price_view"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

<!--                    <LinearLayout-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_gravity="top"-->
<!--                        android:layout_weight="1"-->
<!--                        android:gravity="center_horizontal"-->
<!--                        android:orientation="vertical">-->

<!--                        <TextView-->
<!--                            android:id="@+id/tvFoodPrice"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:layout_marginTop="2dp"-->
<!--                            android:ellipsize="end"-->
<!--                            android:gravity="center"-->
<!--                            android:maxLines="2"-->
<!--                            android:textColor="@color/black"-->
<!--                            android:textSize="@dimen/_16ssp"-->
<!--                            tools:text="$9.9" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/tvWeight"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:ellipsize="end"-->
<!--                            android:gravity="center"-->
<!--                            android:maxLines="2"-->
<!--                            android:textColor="@color/black"-->
<!--                            android:textSize="@dimen/_16ssp"-->
<!--                            tools:text="(10kg)" />-->

<!--                        <TextView-->
<!--                            android:id="@+id/tvOriginalPrice"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center_horizontal"-->
<!--                            android:ellipsize="end"-->
<!--                            android:foreground="@drawable/strike_price"-->
<!--                            android:maxLines="3"-->
<!--                            android:textColor="@color/black60"-->
<!--                            android:textSize="@dimen/_12ssp"-->
<!--                            android:visibility="gone"-->
<!--                            tools:text="12.99"-->
<!--                            tools:visibility="visible" />-->
<!--                    </LinearLayout>-->
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llAutoInStore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <!--                    <View-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="1dp"-->
                    <!--                        android:layout_marginVertical="24dp"-->
                    <!--                        android:background="@color/black12" />-->

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start"
                            android:text="@string/automatically_store_returned_goods"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp" />

                        <ImageView
                            android:id="@+id/ivSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_switch_close" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCancel"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:text="@string/cancel"
                        android:textAllCaps="false"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:strokeColor="@color/black20"
                        app:strokeWidth="1dp" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnConfirm"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_weight="1"
                        android:alpha="0.5"
                        android:clickable="true"
                        android:enabled="false"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:text="@string/confirm_cancel_dish"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        app:backgroundTint="@color/primaryColor"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent" />
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>