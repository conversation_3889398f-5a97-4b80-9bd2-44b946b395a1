<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="0dp">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:background="@drawable/selector_rounded_rectangle_border"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="10dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/cardImage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:visibility="gone"
                app:cardCornerRadius="6dp"
                app:cardElevation="0dp"
                tools:visibility="visible">

                <FrameLayout

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@id/ivGood"
                        android:layout_width="44dp"
                        android:layout_height="44dp" />

                    <View
                        android:id="@+id/vDisable"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:background="@color/black40"
                        android:visibility="gone" />
                </FrameLayout>

            </androidx.cardview.widget.CardView>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvDishedName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="middle"
                    android:maxLines="2"
                    android:textColor="@drawable/selector_primary_color"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    tools:text="Hello $9.99" />

                <TextView
                    android:id="@+id/tvTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="Hello $9.99" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>