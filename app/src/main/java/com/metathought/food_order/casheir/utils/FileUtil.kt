package com.metathought.food_order.casheir.utils

import android.content.Context
import android.media.MediaScannerConnection
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.util.Locale


/**
 *<AUTHOR>
 *@time  2024/10/29
 *@desc
 **/

object FileUtil {
    fun getFileExtension(file: File?): String {
        if (file != null) {
            val name = file.name
            val lastIndex = name.lastIndexOf('.')
            if (lastIndex > 0 && lastIndex < name.length - 1) {
                return name.substring(lastIndex + 1)
            }
        }
        return ""
    }

    fun isFileSizeGreaterThan2MB(filePath: String?): Boolean {
        val file = File(filePath)
        if (file.exists() && file.isFile) {
            val fileSize = file.length()
            return fileSize >= 2 * 1024 * 1024 // 2MB in bytes
        }
        return false
    }

    fun isImageFile(file: File): Boolean {
        val fileName = file.name
        if (fileName.lastIndexOf(".") != -1 &&
            (fileName.lowercase(Locale.getDefault()).endsWith(".png") ||
                    fileName.lowercase(Locale.getDefault()).endsWith(".jpeg") ||
                    fileName.lowercase(Locale.getDefault()).endsWith(".jpg"))
        ) {
            return true
        }
        return false
    }

    private fun copyFile(sourceFile: File, destinationFile: File) {
        try {
            val inputStream = FileInputStream(sourceFile)
            val outputStream = FileOutputStream(destinationFile)
            val buffer = ByteArray(1024)
            var length: Int
            while (inputStream.read(buffer).also { length = it } > 0) {
                outputStream.write(buffer, 0, length)
            }
            inputStream.close()
            outputStream.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

   fun getFileNameInUrl(url: String): String {
        val lastIndex = url.lastIndexOf('/')
        if (lastIndex != -1) {
            val filename = url.substring(lastIndex + 1)
            return filename
        }
        return ""
    }

    fun scanFile(context: Context, file: File) {
        MediaScannerConnection.scanFile(
            context,
            arrayOf(file.absolutePath),
            null
        ) { path, uri ->
            // 扫描完成后的回调
        }
    }
}