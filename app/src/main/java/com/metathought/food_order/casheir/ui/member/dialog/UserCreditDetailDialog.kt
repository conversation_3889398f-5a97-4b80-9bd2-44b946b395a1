package com.metathought.food_order.casheir.ui.member.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecord
import com.metathought.food_order.casheir.databinding.DialogUserCreditDetailBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.CreditRecordsAdapter
import com.metathought.food_order.casheir.ui.adapter.PaymentRecordsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.ui.member.credit.MemberCreditViewModel
import dagger.hilt.android.AndroidEntryPoint


/**
 * 用户挂账详情弹窗
 */
@AndroidEntryPoint
class UserCreditDetailDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "UserCreditDetailDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            creditRecord: CreditRecord
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(creditRecord)
            fragment.show(fragmentManager, TAG)
        }

        fun showDialog(
            fragmentManager: FragmentManager,
            consumerId: String?,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    CreditRecord(
                        consumerId = consumerId?.toLongOrNull(),
                        null,
                        null,
                        null,
                        null,
                        null
                    )
                )
            fragment.show(fragmentManager, TAG)
        }


        fun getDialog(fragmentManager: FragmentManager): UserCreditDetailDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? UserCreditDetailDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? UserCreditDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            creditRecord: CreditRecord
        ): UserCreditDetailDialog {
            val fragment = UserCreditDetailDialog()
            fragment.creditRecord = creditRecord
            return fragment
        }
    }


    private val viewModel: MemberCreditViewModel by viewModels()

    private val creditRecordsAdapter = CreditRecordsAdapter().apply {
        maxItemCount = 5
        onDetailClick = { item, _ ->
            //跳转到挂账详情页
            CreditOrderDetailDialog.showDialog(parentFragmentManager, item)
        }
    }

    private val paymentRecordsAdapter = PaymentRecordsAdapter().apply {
        maxItemCount = 5
    }

    private var creditRecord: CreditRecord? = null

    private var binding: DialogUserCreditDetailBinding? = null

    fun getConsumerId(): Long? {
        return creditRecord?.consumerId
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogUserCreditDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        onTouchOutSide(binding?.mainLayout)
        initData()
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiCreditDetailsState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        state.data.let {
                            tvNickname.text = it.nickName
                            tvAccount.text = it.telephone
                            tvBalance.text = it.balanceAmount?.priceFormatTwoDigitZero4()
                            tvCreditAmount.text = it.amount?.priceFormatTwoDigitZero4()
                            tvRechargeNum.text = it.rechargeMembersNum?.toString() ?: "0"
                            tvConsumerNum.text = it.consumerMembersNum?.toString() ?: "0"

                            //挂账记录
                            creditRecordsAdapter.replaceData(it.creditRecordList ?: emptyList())
                            tvMoreCreditRecords.isVisible = creditRecordsAdapter.getItemSize() > 5
                            tvNoCreditRecords.isVisible = creditRecordsAdapter.itemCount == 0
                            creditRecordsLayout.isVisible = creditRecordsAdapter.itemCount > 0


                            //还款记录
                            paymentRecordsAdapter.replaceData(it.repaymentRecordList ?: emptyList())
                            tvMorePaymentRecords.isVisible = paymentRecordsAdapter.getItemSize() > 5
                            paymentRecordsLayout.isVisible = paymentRecordsAdapter.itemCount > 0
                            tvNoPaymentRecords.isVisible = paymentRecordsAdapter.itemCount == 0
                        }
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                    }

                }

                else -> {

                }
            }

        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            btnPrint.setOnClickListener {
                viewModel.printAllCreditUnpaidOrders(
                    requireContext(),
                    creditRecord?.consumerId ?: 0,
                    isRepayment = false
                )
            }
            tvMoreCreditRecords.setOnClickListener {
                //查看更多挂账记录
                CreditRecordsDialog.showDialog(
                    childFragmentManager,
                    creditRecord?.consumerId,
                    CreditRecordsDialog.SCENE.CREDIT_RECORDS
                )
            }
            tvMorePaymentRecords.setOnClickListener {
                //查看更多还款记录
                CreditRecordsDialog.showDialog(
                    childFragmentManager,
                    creditRecord?.consumerId,
                    CreditRecordsDialog.SCENE.PAYMENT_RECORDS
                )
            }
        }
    }

    private fun initData() {
        binding?.apply {
            tvNickname.text = creditRecord?.nickName
            tvAccount.text = creditRecord?.telephone
            tvBalance.text = 0L.priceFormatTwoDigitZero2()
            tvCreditAmount.text = creditRecord?.amount?.priceFormatTwoDigitZero4()
            tvRechargeNum.text = "0"
            tvConsumerNum.text = "0"

            viewModel.getCreditDetails(creditRecord?.consumerId)
        }
    }

    private fun initView() {
        binding?.apply {
            rvCreditRecords.adapter = creditRecordsAdapter

            rvPaymentRecords.adapter = paymentRecordsAdapter
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * PERCENT_85).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }
}