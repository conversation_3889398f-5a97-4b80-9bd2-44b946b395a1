package com.metathought.food_order.casheir.ui.widget.printer

import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.extension.formatTimestamp2
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import timber.log.Timber
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.TimeUnit


/**
 *<AUTHOR>
 *@time  2025/5/8
 *@desc  USB 打印队列 - 优化版本
 **/

/**
 * USB 打印任务数据类
 */
data class USBPrintTask(
    val taskId: String = UUID.randomUUID().toString(),
    val deviceName: String,
    val task: Runnable,
    var priority: Int = DEFAULT_PRIORITY,
    var retryCount: Int = 0,
    val timestamp: Long = System.currentTimeMillis(),
    val timeoutMs: Long = DEFAULT_TIMEOUT_MS
) : Comparable<USBPrintTask> {

    companion object {
        const val DEFAULT_PRIORITY = 5
        const val HIGH_PRIORITY = 1
        const val LOW_PRIORITY = 10
        const val MAX_RETRY = 3
        const val DEFAULT_TIMEOUT_MS = 30_000L // 30秒超时
    }

    override fun compareTo(other: USBPrintTask): Int {
        return when {
            priority != other.priority -> priority.compareTo(other.priority)
            retryCount != other.retryCount -> other.retryCount.compareTo(retryCount)
            else -> timestamp.compareTo(other.timestamp)
        }
    }
}

/**
 * 优化的 USB 打印队列管理器
 *
 * 主要改进：
 * 1. 支持多个 USB 打印机并发处理
 * 2. 任务优先级管理
 * 3. 错误处理和重试机制
 * 4. 打印机状态检查
 * 5. 任务超时处理
 * 6. 详细的日志记录
 */
object USBPrintQueueManager {

    // 按设备名分组的任务队列（设备名 -> 优先级队列）
    private val deviceQueues = ConcurrentHashMap<String, PriorityBlockingQueue<USBPrintTask>>()

    // 按设备名记录处理状态（设备名 -> 是否正在处理）
    private val processingStatus = ConcurrentHashMap<String, Boolean>()

    // 锁对象（按设备名隔离，避免不同打印机间的锁竞争）
    private val lockMap = ConcurrentHashMap<String, Any>()

    // 协程作用域
    private val coroutineScope = CoroutineScope(
        Dispatchers.IO +
                SupervisorJob() +
                CoroutineName("USBPrintQueueManager")
    )

    // 线程池执行器
    private val executorService = Executors.newCachedThreadPool { runnable ->
        Thread(runnable, "USB-Print-${System.currentTimeMillis()}").apply {
            isDaemon = true
            priority = Thread.NORM_PRIORITY
            uncaughtExceptionHandler = Thread.UncaughtExceptionHandler { _, e ->
                Timber.e(e, "USB打印线程异常")
            }
        }
    }

    /**
     * 获取或创建设备对应的锁对象
     */
    private fun getOrCreateLock(deviceName: String): Any {
        return lockMap.getOrPut(deviceName) { Any() }
    }

    /**
     * 添加打印任务到队列（兼容旧接口）
     */
//    fun addPrintTask(task: Runnable) {
//        val usbPrinterInfo = PrinterDeviceHelper.getUsbPrinterInfo()
//        val deviceName = usbPrinterInfo?.usbDevice?.deviceName ?: "default_usb"
//
//        addPrintTask(
//            USBPrintTask(
//                deviceName = deviceName,
//                task = task,
//                priority = USBPrintTask.DEFAULT_PRIORITY
//            )
//        )
//    }

    /**
     * 添加打印任务到队列（新接口）
     */
    fun addPrintTask(printTask: USBPrintTask) {
        Timber.d("添加USB打印任务: 设备=${printTask.deviceName}, 优先级=${printTask.priority}, ID=${printTask.taskId}")

        synchronized(getOrCreateLock(printTask.deviceName)) {
            // 初始化队列（如果不存在）
            val queue = deviceQueues.getOrPut(printTask.deviceName) {
                PriorityBlockingQueue<USBPrintTask>()
            }
            queue.add(printTask)
            processingStatus.getOrPut(printTask.deviceName) { false }

            // 开始处理队列
            processQueue(printTask.deviceName)
        }
    }

    /**
     * 处理指定设备的任务队列
     */
    private fun processQueue(deviceName: String) {
        synchronized(getOrCreateLock(deviceName)) {
            val isProcessing = processingStatus[deviceName] ?: false
            val queue = deviceQueues[deviceName] ?: return

            Timber.d("处理USB打印队列: 设备=$deviceName, 正在处理=$isProcessing, 队列长度=${queue.size}")

            if (isProcessing || queue.isEmpty()) return

            processingStatus[deviceName] = true
            val task = queue.poll()

            if (task != null) {
                executeTask(deviceName, task)
            } else {
                processingStatus[deviceName] = false
            }
        }
    }

    /**
     * 执行打印任务
     */
    private fun executeTask(deviceName: String, task: USBPrintTask) {
        coroutineScope.launch {
            try {
                Timber.d("开始执行USB打印任务: 设备=$deviceName, ID=${task.taskId}")

                // 检查任务是否超时
                if (isTaskExpired(task)) {
                    Timber.w("任务已超时，跳过执行: ID=${task.taskId}")
                    handleTaskCompleted(deviceName, task, false)
                    return@launch
                }

                // 检查打印机状态
                if (!isUSBPrinterAvailable(deviceName)) {
                    Timber.w("USB打印机不可用: $deviceName")
                    handleTaskCompleted(deviceName, task, false)
                    return@launch
                }

                // 执行打印任务（在IO线程池中执行）
                val success = withContext(Dispatchers.IO) {
                    try {
                        // 使用超时控制
                        withTimeout(task.timeoutMs) {
                            task.task.run()
                            true
                        }
                    } catch (e: TimeoutCancellationException) {
                        Timber.e(e, "USB打印任务超时: ID=${task.taskId}")
                        false
                    } catch (e: Exception) {
                        Timber.e(e, "USB打印任务执行失败: ID=${task.taskId}")
                        false
                    }
                }

                handleTaskCompleted(deviceName, task, success)

            } catch (e: Exception) {
                Timber.e(e, "执行USB打印任务异常: ID=${task.taskId}")
                handleTaskCompleted(deviceName, task, false)
            }
        }
    }

    /**
     * 处理任务完成
     */
    private fun handleTaskCompleted(deviceName: String, task: USBPrintTask, success: Boolean) {
        coroutineScope.launch {
            try {
                if (success) {
                    Timber.d("USB打印任务成功: 设备=$deviceName, ID=${task.taskId}")
                    // 成功后延迟一段时间再处理下一个任务，避免打印机过载
                    delay(1500)
                } else {
                    Timber.w("USB打印任务失败: 设备=$deviceName, ID=${task.taskId}, 重试次数=${task.retryCount}")
                    handleTaskFailure(deviceName, task)
                    delay(2000) // 失败后延迟更长时间
                }

                // 标记处理完成并继续处理队列
                synchronized(getOrCreateLock(deviceName)) {
                    processingStatus[deviceName] = false
                    processQueue(deviceName)
                }

            } catch (e: Exception) {
                Timber.e(e, "处理任务完成状态异常")
                synchronized(getOrCreateLock(deviceName)) {
                    processingStatus[deviceName] = false
                }
            }
        }
    }

    /**
     * 处理任务失败
     */
    private fun handleTaskFailure(deviceName: String, task: USBPrintTask) {
        synchronized(getOrCreateLock(deviceName)) {
            // 检查是否需要重试
            if (task.retryCount < USBPrintTask.MAX_RETRY && !isTaskExpired(task)) {
                Timber.i("重新加入USB打印队列: 设备=$deviceName, ID=${task.taskId}, 重试次数=${task.retryCount + 1}")

                val retryTask = task.copy(
                    retryCount = task.retryCount + 1,
                    priority = maxOf(1, task.priority - 1) // 提高优先级
                )

                deviceQueues[deviceName]?.add(retryTask)
            } else {
                Timber.e("USB打印任务最终失败: 设备=$deviceName, ID=${task.taskId}, 重试次数=${task.retryCount}")
                // 可以在这里添加失败通知逻辑
                notifyTaskFinalFailure(task)
            }
        }
    }

    /**
     * 检查任务是否已过期
     */
    private fun isTaskExpired(task: USBPrintTask): Boolean {
        val maxAge = 10 * 60 * 1000L // 10分钟
        return (System.currentTimeMillis() - task.timestamp) > maxAge
    }

    /**
     * 检查USB打印机是否可用
     */
    private fun isUSBPrinterAvailable(deviceName: String): Boolean {
        return try {
            val usbDevice = PrinterUsbDeviceHelper.usbDeviceMap[deviceName]
            usbDevice?.iDeviceConnection != null && usbDevice.pOSPrinter != null
        } catch (e: Exception) {
            Timber.e(e, "检查USB打印机状态失败: $deviceName")
            false
        }
    }

    /**
     * 通知任务最终失败
     */
    private fun notifyTaskFinalFailure(task: USBPrintTask) {
        try {
            val context = MyApplication.myAppInstance
            val message = "USB打印任务失败: ${task.deviceName}"

            // 在主线程显示错误信息
            coroutineScope.launch(Dispatchers.Main) {
                try {
                    MyApplication.myAppInstance.showToast(message)
                } catch (e: Exception) {
                    Timber.e(e, "显示错误信息失败")
                }
            }

            Timber.e("USB打印最终失败: 设备=${task.deviceName}, ID=${task.taskId}, 时间=${task.timestamp.formatTimestamp2()}")
        } catch (e: Exception) {
            Timber.e(e, "通知任务失败异常")
        }
    }

    /**
     * 打印任务完成回调（兼容旧接口）
     */
    fun onPrintTaskCompleted(delay: Long = 0, timeUnit: TimeUnit = TimeUnit.MILLISECONDS) {
        // 这个方法保持兼容性，但实际上新的队列管理器会自动处理任务完成
        Timber.d("收到打印任务完成回调: delay=$delay, timeUnit=$timeUnit")

        if (delay > 0) {
            coroutineScope.launch {
                delay(timeUnit.toMillis(delay))
                // 触发所有设备的队列处理
                deviceQueues.keys.forEach { deviceName ->
                    processQueue(deviceName)
                }
            }
        }
    }

    /**
     * 添加高优先级打印任务
     */
//    fun addHighPriorityTask(task: Runnable, deviceName: String? = null) {
//        val targetDevice = deviceName ?: run {
//            val usbPrinterInfo = PrinterDeviceHelper.getUsbPrinterInfo()
//            usbPrinterInfo?.usbDevice?.deviceName ?: "default_usb"
//        }
//
//        addPrintTask(
//            USBPrintTask(
//                deviceName = targetDevice,
//                task = task,
//                priority = USBPrintTask.HIGH_PRIORITY
//            )
//        )
//    }

    /**
     * 清空指定设备的打印队列
     */
    fun clearQueue(deviceName: String? = null) {
        if (deviceName.isNullOrEmpty()) {
            // 清空所有队列
            synchronized(this) {
                deviceQueues.clear()
                processingStatus.clear()
                lockMap.clear()
                Timber.i("已清空所有USB打印队列")
            }
        } else {
            // 清空指定设备的队列
            synchronized(getOrCreateLock(deviceName)) {
                deviceQueues.remove(deviceName)
                processingStatus.remove(deviceName)
                lockMap.remove(deviceName)
                Timber.i("已清空USB打印队列: $deviceName")
            }
        }
    }

    /**
     * 获取队列状态信息
     */
    fun getQueueStatus(): Map<String, QueueStatus> {
        return deviceQueues.mapValues { (deviceName, queue) ->
            QueueStatus(
                deviceName = deviceName,
                queueSize = queue.size,
                isProcessing = processingStatus[deviceName] ?: false,
                isDeviceAvailable = isUSBPrinterAvailable(deviceName)
            )
        }
    }

    /**
     * 暂停指定设备的打印队列
     */
    fun pauseQueue(deviceName: String) {
        synchronized(getOrCreateLock(deviceName)) {
            processingStatus[deviceName] = true // 设置为正在处理状态，阻止新任务执行
            Timber.i("已暂停USB打印队列: $deviceName")
        }
    }

    /**
     * 恢复指定设备的打印队列
     */
    fun resumeQueue(deviceName: String) {
        synchronized(getOrCreateLock(deviceName)) {
            processingStatus[deviceName] = false
            processQueue(deviceName)
            Timber.i("已恢复USB打印队列: $deviceName")
        }
    }

    /**
     * 关闭队列管理器
     */
    fun shutdown() {
        try {
            coroutineScope.cancel()
            executorService.shutdown()

            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow()
            }

            clearQueue()
            Timber.i("USB打印队列管理器已关闭")
        } catch (e: Exception) {
            Timber.e(e, "关闭USB打印队列管理器异常")
        }
    }
}

/**
 * 队列状态数据类
 */
data class QueueStatus(
    val deviceName: String,
    val queueSize: Int,
    val isProcessing: Boolean,
    val isDeviceAvailable: Boolean
)