package com.metathought.food_order.casheir.constant

const val FORMAT_DATE_TIME_REALIZED = "yyyy-MM-dd HH:mm:ss"

const val FORMAT_DATE_TIME_SHOW = "yyyy/MM/dd HH:mm"
const val FORMAT_DATE = "yyyy/MM/dd"
const val MAXIMUM_QTY = 999
const val FORMAT_DATE_TIME_SHOW_WITH_SECOND = "yyyy/MM/dd HH:mm:ss"
const val FORMAT_DATE_TIME_SHOW_WITHOUT_SECOND = "yyyy/MM/dd HH:mm"
const val FORMAT_DATE_REALIZED = "yyyy-MM-dd"
const val ONE_DAY_WITH_SECOND = 60 * 60 * 24 * 1000

const val FORMAT_DATE_EXPORT_NAME = "yyyy-MM-dd HH_mm_ss"

const val KIOSK = "KIOSK"