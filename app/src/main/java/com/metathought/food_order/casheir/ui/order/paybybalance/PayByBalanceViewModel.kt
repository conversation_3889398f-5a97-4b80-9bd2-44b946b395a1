package com.metathought.food_order.casheir.ui.order.paybybalance

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PayByBalanceViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {

    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    fun getMemberAccount(phoneNumber: String) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val tableResponse = repository.consumerPayAccount(phoneNumber)
                emitUiState(tableResponse)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(e.message))
            }
        }
    }


    fun emitUiState(memberAccountResult: ApiResponse<CustomerMemberResponse?>? = null) {
        val uiModel = UIModel(memberAccountResult)
        _uiState.value = uiModel
    }

    data class UIModel(val memberAccountResult: ApiResponse<CustomerMemberResponse?>?)

}