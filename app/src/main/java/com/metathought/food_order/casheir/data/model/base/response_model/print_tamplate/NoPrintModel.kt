package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate

import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.CreateTempTableCodeResponse


/**
 *<AUTHOR>
 *@time  2024/11/8
 *@desc  未打印的model
 **/
/**
 * No print model
 *
 * @property printTemplateResponseItem
 * @property currentOrderedInfo
 * @property isPrinterAgain
 * @property paymentQrCode
 * @property printerConfigInfo
 * @property isOrderMore
 * @property timeStamp
 * @constructor Create empty No print model
 */
data class NoPrintModel(
    /**
     * 模板
     */
    val printTemplateResponseItem: PrintTamplateResponseItem? = null,
    /**
     * 订单数据
     */
    val currentOrderedInfo: OrderedInfoResponse? = null,

    val isPrinterAgain: Boolean? = false,
    val paymentQrCode: String? = null,
    val printerConfigInfo: PrinterConfigInfo? = null,
    val isOrderMore: Boolean? = false,

    /**
     * 临时码数据
     */
    val createTempTableResponse: CreateTempTableCodeResponse? = null,
    /**
     * 商品报表数据
     */
    val productReport: ProductReportResponse? = null,
    /**
     * 门店报表数据
     */
    val paymentMethodReport: PaymentMethodReportResponse? = null,

    val timeStamp: Long,

    /**
     * 挂账还款信息
     */
    val repaymentResponse: RepaymentResponse? = null,
    val printerOrderStatus: OrderedStatusEnum? = null,

    )