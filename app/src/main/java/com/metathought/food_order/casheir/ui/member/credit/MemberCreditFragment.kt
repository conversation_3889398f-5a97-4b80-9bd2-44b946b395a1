package com.metathought.food_order.casheir.ui.member.credit

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.fastjson.JSON
import com.google.gson.Gson
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsRepaymentResponse
import com.metathought.food_order.casheir.databinding.FragmentMemberCreditBinding
import com.metathought.food_order.casheir.extension.getConsumePhoneNumber
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.ui.adapter.MemberCreditListAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.CreditDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.MixedPayDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import com.metathought.food_order.casheir.ui.member.dialog.UserCreditDetailDialog
import com.metathought.food_order.casheir.ui.order.payment.PaymentSuccessDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

@AndroidEntryPoint
class MemberCreditFragment : BaseFragment() {

    companion object {
        fun newInstance() = MemberCreditFragment()
    }

    private var orderedScreen: SecondaryScreenUI? = null

    private var _binding: FragmentMemberCreditBinding? = null
    private val binding get() = _binding
    private val memberMainViewModel: MemberMainViewModel by viewModels()
    private lateinit var memberCreditListAdapter: MemberCreditListAdapter
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMemberCreditBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initSecondary()
        initView()
        initObserver()
        initListener()

    }

    private fun initSecondary() {
        context?.let {
            orderedScreen = MyApplication.myAppInstance.orderedScreen
        }
    }

    private fun initObserver() {
        memberMainViewModel.cancelTopUpState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    showProgress()
                }

                is ApiResponse.Error -> {
                    dismissProgress()
                    it.message?.let { it1 -> showToast(it1) }
                }

                is ApiResponse.Success -> {
                    dismissProgress()
                    getCreditList()
                }

                else -> {}
            }
        }
        memberMainViewModel.uiCreditListState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        showProgress()
                }
                if (it.showEnd) {
                    dismissProgress()
                    if (it.isRefresh != false) {
                        layoutEmpty.root.isVisible = true
                        refreshLayout.finishRefresh()
                        memberCreditListAdapter.replaceData(arrayListOf())
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    dismissProgress()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    dismissProgress()
                    layoutEmpty.root.isVisible = false
                    if (it.isRefresh != false) {
                        response.records.let { it1 ->
                            memberCreditListAdapter.replaceData(it1)
                        }
                        refreshLayout.finishRefresh()

                    } else {
                        response.records.let { it1 -> memberCreditListAdapter.addData(it1) }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }

    }

    private var searchCreditJob: Job? = null

    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getCreditList(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getCreditList(false)
                }

            })

            edtSearch.setTextChangedListenerCallBack {
                searchCreditJob?.cancel()
                searchCreditJob = lifecycleScope.launch {
                    delay(500)
                    getCreditList()
                }
            }


            tvClearFilter.setOnClickListener {
                edtSearch.setSearchContent("")
                edtSearch.removeFocus()
//                activity?.hideKeyboard(edtSearch.)
            }
        }
    }


    private fun initView() {
        binding?.apply {

            context?.let {
                showProgress()
                getCreditList()
            }
            memberCreditListAdapter = MemberCreditListAdapter(arrayListOf(), {
                //详情
                UserCreditDetailDialog.showDialog(parentFragmentManager, it)
            }, { creditRecord ->
                val (areaCode, phoneNumber) = creditRecord.telephone.getConsumePhoneNumber()
                PayDialog.showDialog(
                    parentFragmentManager,
                    currentScene = SceneEnum.MEMBER_CREDIT.id,
                    totalPrice = creditRecord.amount?.toLong(),
                    menuOrderScreen = orderedScreen,
                    creditRecord = creditRecord,
                    countryCode = areaCode,
                    phone = phoneNumber,
                    conversionRatio = FoundationHelper.conversionRatio,
                    onCloseListener = {
                        dismissProgress()
                    },
                    repaymentResponse = { response ->
                        when (response) {
                            is ApiResponse.Loading -> {
                                showProgress()
                            }

                            is ApiResponse.Success -> {
                                when (response.data.payType) {
                                    PayTypeEnum.USER_BALANCE.id,
                                    PayTypeEnum.MIXED_PAYMENT.id,
                                    PayTypeEnum.CASH_PAYMENT.id -> {
                                        memberMainViewModel.printCreditOrders(
                                            requireContext(),
                                            response.data
                                        )
                                        getCreditList(true)

                                        PaymentSuccessDialog.showDialog(
                                            parentFragmentManager
                                        )
                                        MixedPayDialog.dismissDialog(parentFragmentManager)
                                        PayDialog.dismissDialog(parentFragmentManager)
                                    }


                                    else -> {}
                                }
                            }

                            is ApiResponse.Error -> {
                                dismissProgress()
                                showToast(response.message ?: "")
                                when (response.errorCode) {
                                    7637 -> {
                                        PayDialog.dismissDialog(parentFragmentManager)
                                        MixedPayDialog.dismissDialog(parentFragmentManager)
                                    }

                                    else -> {}
                                }

                            }
                        }
                    },
                    onlineRepaymentSuccessListener = { response ->
                        memberMainViewModel.printCreditOrders(
                            requireContext(),
                            response
                        )
                        getCreditList(true)

                        PaymentSuccessDialog.showDialog(
                            parentFragmentManager,
                        )
                    }
                )

            })
            rvMemberCredit.adapter = memberCreditListAdapter
        }
    }

    fun getCreditList(isRefresh: Boolean? = null) {
        memberMainViewModel.getCreditList(
            isRefresh,
            keyword = binding?.edtSearch?.getSearchContent().toString(),
        )
    }

    override fun onResume() {
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        super.onResume()
    }

    private fun wsHandel(event: WebSocket.Event) {

        when (event) {
            is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
            is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
            is WebSocket.Event.OnConnectionClosing -> Timber.e(
                "closing connection.."
            )

            is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
            is WebSocket.Event.OnMessageReceived -> {
                if (event.message is Message.Text) {
                    if ((event.message as Message.Text).value == "ping")
//                        viewModel.testingWebsocketSendMessage()
                    else {
                        try {
                            Timber.e("ws ==> ${(event.message as Message.Text).value}")
                            val socketModel = JSON.parseObject(
                                (event.message as Message.Text).value,
                                SocketModel::class.java
                            )
                            socketModel.cmd?.let { cmd ->
                                when (cmd) {
                                    WsCommand.ORDER_CREDIT, WsCommand.CANCEL_CREDIT -> {
                                        getCreditList(true)
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val orderNo = it.toString()
                                            }
                                        }
                                    }

                                    WsCommand.REPAYMENT -> {
                                        socketModel.data?.let { data ->
                                            val response = Gson().fromJson(
                                                data.toJson(),
                                                WsRepaymentResponse::class.java
                                            )
                                            val repaymentResponse = response.repaymentResult
                                            if (repaymentResponse != null) {
                                                PayDialog.getDialog(parentFragmentManager)
                                                    ?.getCreditRecord()?.let {
                                                    if (it.consumerId.toString() == repaymentResponse.consumerId) {
                                                        PayDialog.dismissDialog(
                                                            parentFragmentManager
                                                        )
                                                        MixedPayDialog.dismissDialog(
                                                            parentFragmentManager
                                                        )
                                                    }
                                                }
                                                //打印出小票在支付的场景需要关闭
                                                UserCreditDetailDialog.getDialog(
                                                    parentFragmentManager
                                                )?.getConsumerId()?.let {
                                                    if (it.toString() == repaymentResponse.consumerId) {
                                                        UserCreditDetailDialog.dismissDialog(
                                                            parentFragmentManager
                                                        )
                                                        CreditDialog.dismissDialog(
                                                            parentFragmentManager
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                        getCreditList(true)
                                    }

                                    else -> {}
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
    }

}