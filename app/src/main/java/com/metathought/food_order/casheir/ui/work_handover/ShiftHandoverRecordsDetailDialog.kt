package com.metathought.food_order.casheir.ui.work_handover

import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.databinding.DialogShiftHandoverRecordsDetailBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.AndroidEntryPoint
import java.math.BigDecimal

/**
 * 交班记录详情
 */
@AndroidEntryPoint
class ShiftHandoverRecordsDetailDialog : BaseDialogFragment() {
    private var binding: DialogShiftHandoverRecordsDetailBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    private val viewModel: ShiftHandoverViewModel by viewModels()
    private var logDeatil: CashRegisterHandoverLogVo? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogShiftHandoverRecordsDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.shiftReportPrint.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbLogout?.isVisible = true
                    binding?.btnPrint?.setEnable(false)
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLogout.isVisible = false
                        btnPrint.setEnable(true)
                    }
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        pbLogout.isVisible = false
                        btnPrint.setEnable(true)
                        it.data.data?.let { srPrint ->
//                           if( PrinterTemplateHelper.getPshiftHandoverTemplate()!=null) {
                            Printer.printPrinterClosingReport(requireContext(), srPrint)
//                           }else{
//                               Toast.makeText(requireContext(),"Printer Template not found",Toast.LENGTH_SHORT).show()
//                           }
                        }
                    }
                }
            }
        }
    }

    private fun initData() {
        val deatil: CashRegisterHandoverLogVo = arguments?.getParcelable(LOG_INFO) ?: return
        logDeatil = deatil
        binding?.apply {
            tvStaffName.text = deatil.userName
            tvClassStartTime.text = deatil.loginTime?.formatDate()
            tvClassCloseTime.text = deatil.logoutTime?.formatDate()
            //交班现金
            tvHandoverCash.text = deatil.getUsdAmount()
//                "${deatil.usdAmount?.priceFormatTwoDigitZero2("$")} + ${
//                    deatil.khrAmount?.priceFormatTwoDigit(
//                        "៛"
//                    )
//                }"

            //订单金额
            tvOrderAmount.text =
                if (deatil.totalOrderPrice == null) "$0" else "${
                    deatil.totalOrderPrice.priceFormatTwoDigitZero2(
                        "$"
                    )
                }"
            //订单数量
            tvOrderNum.text =
                if (deatil.totalOrderNumber == null) "0" else deatil.totalOrderNumber.toString()

            //线上收款
            tvOnlinePaymentAmount.text =
                if (deatil.onlinePaidPrice == null) "$0" else "${
                    deatil.onlinePaidPrice.priceFormatTwoDigitZero2(
                        "$"
                    )
                }"
            //线下收款
            tvOfflinePaymentAmount.text = if (deatil.offlinePaidPrice == null) "$0" else "${
                deatil.offlinePaidPrice?.priceFormatTwoDigitZero2("$")
            }"
//                "${deatil.offlinePaidPrice?.priceFormatTwoDigitZero2("$")} + ${
//                    deatil.offlinePaidKhrPrice?.priceFormatTwoDigit(
//                        "៛"
//                    )
//                }"

            labelOfflinePaymentAmountView.isVisible = (deatil.offlinePaidPrice ?: 0.0) > 0.0
            //现金收款
//            tvCashReceipts.text =
//                if (deatil.cashPaidPrice == null) "$0" else "${
//                    deatil.cashPaidPrice.priceFormatTwoDigitZero2(
//                        "$"
//                    )
//                }"


            //余额收款
            tvBalanceReceipts.text =
                if (deatil.balancePaidPrice == null) "$0" else deatil.balancePaidPrice.priceFormatTwoDigitZero2(
                    "$"
                )

            tvCreditReceipts.text =
                if (deatil.creditPaidPrice == null) "$0" else deatil.creditPaidPrice.priceFormatTwoDigitZero2(
                    "$"
                )

            //备用金
            tvImprest.text = deatil.getOpeningCashStr()
//                "${deatil.openingCashUsd?.priceFormatTwoDigitZero2("$")} + ${
//                    deatil.openingCashKhr?.priceFormatTwoDigit(
//                        "៛"
//                    )
//                }"

            //当班支出
            tvShiftExpenses.text = deatil.getAmountPaidUsd()
//                "${deatil.amountPaidUsd?.priceFormatTwoDigitZero2("$")} + ${
//                    deatil.amountPaidKhr?.priceFormatTwoDigit(
//                        "៛"
//                    )
//                }"

            //相差金额
//            val differenceAmountStr = SpannableString("$${deatil.discrepancyPrice}")
            val differenceAmountStr = SpannableString(
                if (deatil.discrepancyPrice.isNullOrEmpty()) "$0" else "${
                    deatil.discrepancyPrice?.toBigDecimalOrNull()?.priceFormatTwoDigitZero2(
                        "$"
                    )
                }"
            )
            differenceAmountStr.setSpan(
                ForegroundColorSpan(root.context.getColor(R.color.black)),
                0,
                differenceAmountStr.length,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            val diffenceAmount =
                deatil.discrepancyPrice?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            val color = if (diffenceAmount < BigDecimal.ZERO) {
                root.context.getColor(R.color.color_ff3141)
            } else if (diffenceAmount > BigDecimal.ZERO) {
                root.context.getColor(R.color.primaryColor)
            } else {
                root.context.getColor(R.color.black)
            }
            differenceAmountStr.setSpan(
                ForegroundColorSpan(
                    color
                ),
                0,
                differenceAmountStr.length,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            val spannableStringBuilder = SpannableStringBuilder()
            spannableStringBuilder.append(differenceAmountStr)
            tvDifferenceAmount.text = spannableStringBuilder


            //当班余额
            tvShiftBalance.text =
                if (deatil.balance == null) "$0" else deatil.balance.priceFormatTwoDigitZero2("$")

            tvRemark.text = deatil.remark
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnPrint.setOnClickListener {
                ConfirmDialog.showDialog(
                    parentFragmentManager,
                    content = getString(R.string.print_handover_report_tips),
                    positiveButtonTitle = getString(R.string.confirm),
                    negativeButtonTitle = getString(R.string.cancel),
                    isShowCancel = true
                ) {
                    viewModel.getShiftReportPrint(logDeatil?.id ?: 0)
                }
            }

            labelOfflinePaymentAmountView.setOnClickListener {
                if (logDeatil != null) {
                    ShiftHandoverOfflineRecordsDetailDialog.showDialog(
                        parentFragmentManager,
                        logDeatil!!
                    )
                }
            }

//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.8).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val SHIFT_HANDOVER_RECORDS_DETAIL_DIALOG =
            "SHIFT_HANDOVER_RECORDS_DETAIL_DIALOG"
        private const val LOG_INFO = "log_info"

        fun showDialog(
            fragmentManager: FragmentManager,
            log: CashRegisterHandoverLogVo,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(SHIFT_HANDOVER_RECORDS_DETAIL_DIALOG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener, log)
            fragment.show(fragmentManager, SHIFT_HANDOVER_RECORDS_DETAIL_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(SHIFT_HANDOVER_RECORDS_DETAIL_DIALOG) as? ShiftHandoverRecordsDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
            log: CashRegisterHandoverLogVo,
        ): ShiftHandoverRecordsDetailDialog {
            val args = Bundle()
            args.putParcelable(LOG_INFO, log)
            val fragment = ShiftHandoverRecordsDetailDialog()
            fragment.positiveButtonListener = iAgreeListener
            fragment.arguments = args
            return fragment
        }
    }

}
