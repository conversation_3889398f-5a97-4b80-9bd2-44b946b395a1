package com.metathought.food_order.casheir.ui.work_handover

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.data.model.base.request_model.logout.CashierLogoutRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLog
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.SaveOrUpdateOpeningCashRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list.StoreOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.PaymentMethodModel
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.store_dashbord.StoreDashboardViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class ShiftHandoverViewModel @Inject constructor(val repository: Repository) : ViewModel() {
    private val _cashRegisterHandoverLogState =
        MutableLiveData<UIOrderListModel>()
    val cashRegisterHandoverLogState get() = _cashRegisterHandoverLogState

    private val _shiftReportPrint =
        MutableLiveData<ApiResponse<BaseResponse<ShiftReportPrint>>>()
    val shiftReportPrint get() = _shiftReportPrint

    private var pagNo = 1
    private val pageSize = 20

    fun pageCashRegisterHandoverLog(
        isRefresh: Boolean? = null,
        startDate: String? = null,
        endDate: String? = null,
        keyword: String?= null,
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIOrderListState(showLoading = true)
                }
            }
            try {
                val response = repository.pageCashRegisterHandoverLog(
                    pagNo,
                    pageSize,
                    keyword,
                    startDate,
                    endDate
                )
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data
                        if (response.data.records.isNullOrEmpty()) {
                            emitUIOrderListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false,
                                keyword = keyword
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIOrderListState(
                            showSuccess = response.data,
                            isRefresh = isRefresh,
                            showLoading = false,
                            keyword = keyword
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUIOrderListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIOrderListState(showError = e.message, showLoading = false)
            }
        }
    }


    fun getShiftReportPrint(
        shiftId: Long,
    ) {
        viewModelScope.launch {
            _shiftReportPrint.value = ApiResponse.Loading
            _shiftReportPrint.value =
                repository.getShiftReportPrint(shiftId)
        }
    }


    private suspend fun emitUIOrderListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: CashRegisterHandoverLog? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
        keyword: String? = null,
    ) {
        val uiModel = UIOrderListModel(
            showLoading,
            showError,
            showSuccess,
            showEnd,
            isRefresh,
            keyword
        )
        withContext(Dispatchers.Main) {
            _cashRegisterHandoverLogState.value = uiModel
        }
    }

    data class UIOrderListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: CashRegisterHandoverLog?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
        val keyword: String? = null,
    )
}