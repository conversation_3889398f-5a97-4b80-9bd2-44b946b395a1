package com.metathought.food_order.casheir.helper

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponTemplateSDK
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber
import java.math.BigDecimal
import kotlin.math.min


/**
 *<AUTHOR>
 *@time  2024/9/2
 *@desc
 **/

object CouponHelper {
    //计算优惠券金额
    fun getPriceAfterCoupon(
        goodsRequest: List<GoodsRequest>,
        templateSDK: CouponTemplateSDK?,
        diningStyle: Int
    ): Pair<Pair<Long, Boolean>, Pair<Long, Boolean>> {

        if (templateSDK?.isLjCoupon() == true) {
            return calculateLjCouponPrice(goodsRequest, templateSDK, diningStyle)
        } else if (templateSDK?.isZkCoupon() == true) {
            return calculateZkCouponPrice(goodsRequest, templateSDK, diningStyle)
        } else if (templateSDK?.isZsCoupon() == true) {
            return calculateZPCouponPrice(goodsRequest, templateSDK, diningStyle)
        }

        return Pair(Pair(0L, false), Pair(0L, false))
    }

    //计算立减券的金额
    private fun calculateLjCouponPrice(
        goodsRequest: List<GoodsRequest>,
        templateSDK: CouponTemplateSDK?,
        diningStyle: Int
    ): Pair<Pair<Long, Boolean>, Pair<Long, Boolean>> {
        //现价优惠金额
        var couponPrice = 0L
        //现价优惠金额是否有效
        var couponPriceValid = false

        //会员价优惠金额
        var couponVipPrice = 0L
        //会员价优惠金额是否有效
        var couponVipPriceValid = false

//        //所有可用商品原价加起来的金额(不包括增值税) 门槛金额
//        var totalThresholdPrice = 0L
        //所有可用商品Vip价加起来的金额(不包括增值税) 门槛金额
        var totalVipThresholdPrice = 0L
        //所有可用商品现价加起来的金额(不包括增值税)门槛金额
        var totalDiscountThresholdPrice = 0L
        //所有可用商品加起来的数量
        var totalGoodsThresholdNum = 0

        //适用商品总金额（总结+增值税+服务费+打包费）
        var totalPrice = 0L
        //适用商品会员总金额（总结+增值税+服务费+打包费）
        var totalVipPrice = 0L
        //适用商品折扣价总金额（总结+增值税+服务费+打包费）
        var totalDiscountPrice = 0L

        var isShowVip = false
        //适用商品列表
        val usageGoodsMap = mutableMapOf<String, UsageGoods>()
        templateSDK?.usageGoods?.forEach {
            usageGoodsMap[it.id.toString()] = it
        }
        //部分适用的时候是否含有适用商品
        var isHasApplyToUseGoods = false
        for (it in goodsRequest) {
            /**
             * 售罄商品跳过计算
             */
            if (it.goods?.isSoldOut() == true) {
                continue
            }
            /**
             * 不参与活动跳过
             */
            if (it.goods?.isDiscountItemWhitelisting() == true) {
                continue
            }
            if (it.goods?.isShowVipPrice() == true) {
                isShowVip = true
            }
            //是否适用
            var isApplyToUse = false
            if (templateSDK?.isPartialGoods() == true) {
                if (usageGoodsMap.contains(it.goods?.id)) {
                    isApplyToUse = true
                    isHasApplyToUseGoods = true
                }
            } else {
                isApplyToUse = true
            }
            if (isApplyToUse) {
                //计算当前商品价格+服务费/打包费 (不受单品折扣影响)
//                val goodsTotalDiscountPrice =
//                    it.getTotalDiscountThresholdPrice(diningStyle)
//                var goodsTotalVipPrice =
//                    it.getTotalVipThresholdPrice(diningStyle)

//                if (diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
//                    //外带门槛加打包费
//                    goodsTotalDiscountPrice =
//                        it.totalDiscountPrice() + it.totalPackPrice()
//                    goodsTotalVipPrice =
//                        it.totalVipPrice() + it.totalPackPrice()
//                } else {
//                    //非外带加服务费
//                    goodsTotalDiscountPrice =
//                        it.totalDiscountPrice() + it.totalPackPrice()
//                    goodsTotalVipPrice =
//                        it.totalVipPrice() + it.totalPackPrice()
//                }
                //计算门槛到达没
                totalDiscountThresholdPrice += it.getTotalDiscountThresholdPrice(diningStyle)
                totalVipThresholdPrice += it.getTotalVipThresholdPrice(diningStyle)
                totalGoodsThresholdNum += it.num ?: 0
            }

            //计算使用总价
//            totalPrice += it.totalPrice()
            totalDiscountPrice += it.totalDiscountPrice()
            totalVipPrice += it.totalVipPrice()

            if (diningStyle != DiningStyleEnum.TAKE_AWAY.id) {
                //外带不计算服务费
//                totalPrice += it.totalServiceChargePrice()
                totalDiscountPrice += it.totalDiscountServiceChargePrice()
                totalVipPrice += it.totalVipServiceChargePrice()
            } else {
//                totalPrice += it.totalPackPrice()
                totalDiscountPrice += it.totalPackPrice()
                totalVipPrice += it.totalPackPrice()
            }
        }
        val quta = ((templateSDK?.getQuoTaPrice() ?: 0.0) * 100).toLong()
        if (templateSDK?.isThresholdCoupon() == true && templateSDK?.getThresholdNum() != null) {
            Timber.e("立减券 数量门槛")
            //有数量门槛
            if (totalGoodsThresholdNum >= (templateSDK.getThresholdNum() ?: 0)) {

                couponPrice = min(quta, totalDiscountPrice)
                couponPriceValid = true

                couponVipPrice = min(quta, totalVipPrice)
                couponVipPriceValid = isShowVip
            }

        } else if (templateSDK?.isThresholdCoupon() == true && templateSDK?.getThresholdPrice() != null) {
            //如果金额门槛到达了
            Timber.e("立减券 金额门槛")
            if (totalDiscountThresholdPrice.div(100.0) >= (templateSDK.getThresholdPrice()
                    ?: 0.0)
            ) {
                couponPrice = min(quta, totalDiscountPrice)
                couponPriceValid = true
            }

            if (totalVipThresholdPrice.div(100.0) >= (templateSDK.getThresholdPrice() ?: 0.0)) {
                couponVipPrice = min(quta, totalVipPrice)
                //会员价达到门槛 且不等于 会员价不等于原总价会员价有效
                couponVipPriceValid = isShowVip
            }
        } else if (templateSDK?.isNoThresholdCoupon() == true) {
            Timber.e("立减券 无门槛")
            var isCanUse = false
            if (templateSDK.isPartialGoods()) {
                if (isHasApplyToUseGoods) {
                    isCanUse = true
                }
            } else {
                isCanUse = true
            }
            if (isCanUse) {
                //无门槛
                couponPrice = min(quta, totalDiscountPrice)
                couponPriceValid = true

                couponVipPrice = min(quta, totalVipPrice)
                couponVipPriceValid = isShowVip
            }
        }
        Timber.e("totalDiscountThresholdPrice > totalVipPrice  :${totalDiscountThresholdPrice > totalVipThresholdPrice}      ${totalDiscountThresholdPrice} = ${totalVipThresholdPrice}")


        val couponPricePair = Pair(couponPrice, couponPriceValid)
        val couponVipPricePair = Pair(couponVipPrice, couponVipPriceValid)
        Timber.e("couponPrice :${couponPrice}  couponPriceValid:${couponPriceValid} ||| couponVipPrice:${couponVipPrice}   couponVipPriceValid:${couponVipPriceValid}")

        return Pair(couponPricePair, couponVipPricePair)
    }


    private fun calculateZkCouponPrice(
        goodsRequest: List<GoodsRequest>,
        templateSDK: CouponTemplateSDK?,
        diningStyle: Int
    ): Pair<Pair<Long, Boolean>, Pair<Long, Boolean>> {
        //现价优惠金额
        var couponPrice = 0L
        //现价优惠金额是否有效
        var couponPriceValid = false

        //会员价优惠金额
        var couponVipPrice = 0L
        //会员价优惠金额是否有效
        var couponVipPriceValid = false

        //所有可用商品原价加起来的金额(不包括增值税) 门槛价
//        var totalThresholdPrice = 0L
        //所有可用商品Vip价加起来的金额(不包括增值税) 门槛价
        var totalVipThresholdPrice = 0L
        //所有可用商品现价加起来的金额(不包括增值税) 门槛价
        var totalDiscountThresholdPrice = 0L
        //所有可用商品加起来的数量 门槛数量
        var totalGoodsThresholdNum = 0


        //适用商品总金额（总结+增值税+服务费+打包费）
        var totalPrice = 0L
        //适用商品会员总金额（总结+增值税+服务费+打包费）
        var totalVipPrice = 0L
        //适用商品折扣价总金额（总结+增值税+服务费+打包费）
        var totalDiscountPrice = 0L

        var isShowVip = false

        //适用商品列表
        val usageGoodsMap = mutableMapOf<String, UsageGoods>()
        templateSDK?.usageGoods?.forEach {
            usageGoodsMap[it.id.toString()] = it
        }
        var isHasApplyToUseGoods = false

        for (it in goodsRequest) {
            /**
             * 售罄商品跳过计算
             */
            if (it.goods?.isSoldOut() == true) {
                continue
            }
            /**
             * 不参与活动跳过
             */
            if (it.goods?.isDiscountItemWhitelisting() == true) {
                continue
            }

            if (it.goods?.isShowVipPrice() == true) {
                isShowVip = true
            }

            //是否适用
            var isApplyToUse = false
            if (templateSDK?.isPartialGoods() == true) {
                if (usageGoodsMap.contains(it.goods?.id)) {
                    isApplyToUse = true
                    isHasApplyToUseGoods = true
                }
            } else {
                isApplyToUse = true
            }
            if (isApplyToUse) {
//                //计算当前商品 价格+打包费
//                var goodsTotalPrice =
//                    it.totalPrice()
//                var goodsTotalDiscountPrice =
//                    it.totalDiscountPrice()
//                var goodsTotalVipPrice =
//                    it.totalVipPrice()
//
//                if (diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
//                    //打包费显示
//                    goodsTotalPrice =
//                        it.totalPrice() + it.totalPackPrice()
//                    goodsTotalDiscountPrice =
//                        it.totalDiscountPrice() + it.totalPackPrice()
//                    goodsTotalVipPrice =
//                        it.totalVipPrice() + it.totalPackPrice()
//                }
//
//                //计算门槛到达没
//                totalThresholdPrice += goodsTotalPrice
//                totalDiscountThresholdPrice += goodsTotalDiscountPrice
//                totalVipThresholdPrice += goodsTotalVipPrice
//                totalGoodsThresholdNum += it.num ?: 0
                totalDiscountThresholdPrice += it.getTotalDiscountThresholdPrice(diningStyle)
                totalVipThresholdPrice += it.getTotalVipThresholdPrice(diningStyle)
                totalGoodsThresholdNum += it.num ?: 0
            }

            //计算使用总价
//            totalPrice += it.totalPrice() //+ it.totalVatPrice()
            totalDiscountPrice += it.totalDiscountPrice() //+ it.totalDiscountVatPrice()
            totalVipPrice += it.totalVipPrice() //+ it.totalVipVatPrice()

            if (diningStyle != DiningStyleEnum.TAKE_AWAY.id) {
                //外带不计算服务费
//                totalPrice += it.totalServiceChargePrice()
                totalDiscountPrice += it.totalDiscountServiceChargePrice()
                totalVipPrice += it.totalVipServiceChargePrice()
            } else {
//                totalPrice += it.totalPackPrice()
                totalDiscountPrice += it.totalPackPrice()
                totalVipPrice += it.totalPackPrice()
            }
        }


        Timber.e("totalDiscountPrice11 : $totalDiscountPrice")
        //满足门槛就是整单折扣
        var quto = ((templateSDK?.getQuoTaPrice() ?: 0.0) * totalDiscountPrice).toLong()
        Timber.e("quto: ${quto}")
        var vipQuto = ((templateSDK?.getQuoTaPrice() ?: 0.0) * totalVipPrice).toLong()
        val upperLimitPrice = templateSDK?.rule?.discount?.upperLimitPrice
        if (upperLimitPrice != null) {
            //如果有上限，选择小的那个
            quto = min(quto, upperLimitPrice.times(100).toLong())
            vipQuto = min(vipQuto, upperLimitPrice.times(100).toLong())
        }
        if (templateSDK?.isThresholdCoupon() == true && templateSDK.getThresholdNum() != null) {
            Timber.e("折扣券 数量门槛")
            //有数量门槛
            if (totalGoodsThresholdNum >= (templateSDK.getThresholdNum() ?: 0)) {
                couponPrice = quto
                couponPriceValid = true

                couponVipPrice = vipQuto
                couponVipPriceValid = isShowVip
            }
        } else if (templateSDK?.isThresholdCoupon() == true && templateSDK.getThresholdPrice() != null) {
            //如果金额门槛到达了
            Timber.e("折扣券 金额门槛")
            if (totalDiscountThresholdPrice.div(100.0) >= (templateSDK.getThresholdPrice()
                    ?: 0.0)
            ) {
                couponPrice = quto
                couponPriceValid = true
            }

            if (totalVipThresholdPrice.div(100.0) >= (templateSDK.getThresholdPrice() ?: 0.0)) {
                couponVipPrice = vipQuto
                //会员价达到门槛 且不等于 会员价不等于原总价会员价有效
                couponVipPriceValid = isShowVip
            }
        } else if (templateSDK?.isNoThresholdCoupon() == true) {
            Timber.e("折扣券 无门槛")
            var isCanUse = false
            if (templateSDK.isPartialGoods() == true) {
                if (isHasApplyToUseGoods) {
                    isCanUse = true
                }
            } else {
                isCanUse = true
            }
            //无门槛
            couponPrice = quto
            couponPriceValid = isCanUse

            couponVipPrice = vipQuto
            couponVipPriceValid = isCanUse && isShowVip
        }
//        Timber.e("totalPrice > totalVipPrice  :${totalThresholdPrice > totalVipThresholdPrice}      $totalThresholdPrice = $totalVipThresholdPrice")

        Timber.e("couponPrice:${couponPrice}  couponVipPrice:${couponVipPrice} ")
//        if (couponPrice == 0L) {
//            couponPriceValid = false
//        }
//        if (couponVipPrice == 0L) {
//            couponVipPriceValid = false
//        }
        val couponPricePair = Pair(couponPrice, couponPriceValid)
        val couponVipPricePair = Pair(couponVipPrice, couponVipPriceValid)

        return Pair(couponPricePair, couponVipPricePair)
    }


    //赠品使用券是否满足
    private fun calculateZPCouponPrice(
        goodsRequest: List<GoodsRequest>,
        templateSDK: CouponTemplateSDK?,
        diningStyle: Int
    ): Pair<Pair<Long, Boolean>, Pair<Long, Boolean>> {
        //现价优惠金额
        var couponPrice = 0L
        //现价优惠金额是否有效
        var couponPriceValid = false

        //会员价优惠金额
        var couponVipPrice = 0L
        //会员价优惠金额是否有效
        var couponVipPriceValid = false

        //所有可用商品原价加起来的金额(不包括增值税) 门槛金额
//        var totalThresholdPrice = 0L
        //所有可用商品Vip价加起来的金额(不包括增值税) 门槛金额
        var totalVipThresholdPrice = 0L
        //所有可用商品现价加起来的金额(不包括增值税)门槛金额
        var totalDiscountThresholdPrice = 0L
        //所有可用商品加起来的数量
        var totalGoodsThresholdNum = 0

        //适用商品总金额（总结+增值税+服务费+打包费）
//        var totalPrice = 0L
        //适用商品会员总金额（总结+增值税+服务费+打包费）
        var totalVipPrice = 0L
        //适用商品折扣价总金额（总结+增值税+服务费+打包费）
        var totalDiscountPrice = 0L

        var isShowVip = false

        //适用商品列表
        val usageGoodsMap = mutableMapOf<String, UsageGoods>()
        templateSDK?.usageGoods?.forEach {
            usageGoodsMap[it.id.toString()] = it
        }
        //部分适用的时候是否含有适用商品
        var isHasApplyToUseGoods = false
        for (it in goodsRequest) {
            /**
             * 售罄商品跳过计算
             */
            if (it.goods?.isSoldOut() == true) {
                continue
            }
            /**
             * 不参与活动跳过
             */
            if (it.goods?.isDiscountItemWhitelisting() == true) {
                continue
            }

            if (it.goods?.isShowVipPrice() == true) {
                isShowVip = true
            }

            //是否适用
            var isApplyToUse = false
            if (templateSDK?.isPartialGoods() == true) {
                if (usageGoodsMap.contains(it.goods?.id)) {
                    isApplyToUse = true
                    isHasApplyToUseGoods = true
                }
            } else {
                isApplyToUse = true
            }
            if (isApplyToUse) {
//                //计算当前商品 价格+打包费
//                var goodsTotalPrice =
//                    it.totalPrice()
//                var goodsTotalDiscountPrice =
//                    it.totalDiscountPrice()
//                var goodsTotalVipPrice =
//                    it.totalVipPrice()
//
//                if (diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
//                    //打包费显示
//                    goodsTotalPrice =
//                        it.totalPrice() + it.totalPackPrice()
//                    goodsTotalDiscountPrice =
//                        it.totalDiscountPrice() + it.totalPackPrice()
//                    goodsTotalVipPrice =
//                        it.totalVipPrice() + it.totalPackPrice()
//                }
//
//                //计算门槛到达没
//                totalThresholdPrice += goodsTotalPrice
//                totalDiscountThresholdPrice += goodsTotalDiscountPrice
//                totalVipThresholdPrice += goodsTotalVipPrice
//                totalGoodsThresholdNum += it.num ?: 0
                totalDiscountThresholdPrice += it.getTotalDiscountThresholdPrice(diningStyle)
                totalVipThresholdPrice += it.getTotalVipThresholdPrice(diningStyle)
                totalGoodsThresholdNum += it.num ?: 0
            }

            //计算使用总价
//            totalPrice += it.totalPrice() + it.totalVatPrice()
            totalDiscountPrice += it.totalDiscountPrice() //+ it.totalDiscountVatPrice()
            totalVipPrice += it.totalVipPrice() //+ it.totalVipVatPrice()

            if (diningStyle != DiningStyleEnum.TAKE_AWAY.id) {
                //外带不计算服务费
//                totalPrice += it.totalServiceChargePrice()
                totalDiscountPrice += it.totalDiscountServiceChargePrice()
                totalVipPrice += it.totalVipServiceChargePrice()
            } else {
//                totalPrice += it.totalPackPrice()
                totalDiscountPrice += it.totalPackPrice()
                totalVipPrice += it.totalPackPrice()
            }
        }

        if (templateSDK?.isThresholdCoupon() == true && templateSDK.getThresholdNum() != null) {
            Timber.e("赠品券 数量门槛")
            //有数量门槛
            if (totalGoodsThresholdNum >= (templateSDK.getThresholdNum() ?: 0)) {
                couponPriceValid = true
                couponVipPriceValid = true
            }
        } else if (templateSDK?.isThresholdCoupon() == true && templateSDK.getThresholdPrice() != null) {
            //如果金额门槛到达了
            Timber.e("赠品券 金额门槛")
            if (totalDiscountThresholdPrice.div(100.0) >= (templateSDK.getThresholdPrice()
                    ?: 0.0)
            ) {
                couponPriceValid = true
            }

            if (totalVipThresholdPrice.div(100.0) >= (templateSDK.getThresholdPrice() ?: 0.0)) {
                //会员价达到门槛 且不等于 会员价不等于原总价会员价有效
                couponVipPriceValid = isShowVip
            }
        } else if (templateSDK?.isNoThresholdCoupon() == true) {
            Timber.e("赠品券 无门槛")
            if (templateSDK.isPartialGoods()) {
                if (isHasApplyToUseGoods) {
                    couponPriceValid = true
                    couponVipPriceValid = true
                }
            } else {
                //全部商品 无门槛
                couponPriceValid = true
                couponVipPriceValid = true
            }
        }

//        Timber.run { e("totalPrice > totalVipPrice  :${totalThresholdPrice > totalVipThresholdPrice}      ${totalThresholdPrice} = $totalVipThresholdPrice") }

        return Pair(Pair(0L, couponPriceValid), Pair(0L, couponVipPriceValid))
    }


    //获取充值优惠券描述
    fun getCouponDesc(
        context: Context,
        isCouponListEmpty: Boolean,
        coupInfo: CouponModel?,
        topupPrice: BigDecimal?,
        isTopUp: Boolean? = false
    ): SpannableStringBuilder {
        val desc = SpannableStringBuilder()

        if (coupInfo == null) {
            if (isCouponListEmpty) {
                val hint =
                    context.getString(if (isTopUp == true) R.string.hint_no_can_use_coupon else R.string.identify_coupons)
                desc.append(hint)
            } else {
                val hint = context.getString(R.string.hint_can_use_coupon)
                desc.append(hint)
                desc.setSpan(
                    ForegroundColorSpan(context.getColor(R.color.color_ff7f00)),
                    0,
                    hint.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        } else {
            var price = "-${coupInfo.couponPrice?.priceFormatTwoDigitZero2()}"
            if (topupPrice != null) {
                val tmpPrice =
                    coupInfo.couponPrice?.priceFormatTwoDigitZero()?.toDoubleOrNull() ?: 0.0
                price = "-${
                    BigDecimal.valueOf(min(topupPrice.toDouble(), tmpPrice))
                        .multiply(
                            BigDecimal.valueOf(100)
                        ).toLong()
                        .priceFormatTwoDigitZero2()
                }"
            }
            desc.append(price)
        }
        return desc
    }

}