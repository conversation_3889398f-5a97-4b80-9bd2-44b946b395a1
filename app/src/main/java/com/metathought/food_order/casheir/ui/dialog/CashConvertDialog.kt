//package com.metathought.food_order.casheir.ui.dialog
//
//import android.content.Context
//import androidx.core.view.isVisible
//import androidx.core.widget.addTextChangedListener
//import com.lxj.xpopup.XPopup
//import com.lxj.xpopup.core.BasePopupView
//import com.lxj.xpopup.interfaces.SimpleCallback
//import com.lxj.xpopup.util.KeyboardUtils
//import com.metathought.food_order.casheir.R
//import com.metathought.food_order.casheir.data.CashConvertModel
//import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
//import com.metathought.food_order.casheir.databinding.DialogCashConvertBinding
//import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
//import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
//import com.metathought.food_order.casheir.extension.getColor
//import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
//import com.metathought.food_order.casheir.extension.roundToTwoDecimalPlaces
//import com.metathought.food_order.casheir.extension.setNumberRange
//import com.metathought.food_order.casheir.helper.FoundationHelper
//import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
//import timber.log.Timber
//import java.math.BigDecimal
//import java.math.RoundingMode
//import kotlin.math.absoluteValue
//import kotlin.math.floor
//import kotlin.math.round
//
///**
// * 找零钱弹窗
// * Change amount Dialog
// * <AUTHOR>
// * @date 2024/5/912:31
// * @description
// */
//class CashConvertDialog(private var act: Context) : BaseCenterDialog(act) {
//
//    private var binding: DialogCashConvertBinding? = null
//
//    private var confirmClickListener: ((CashConvertModel?) -> Unit)? = null
//    private var onDismissCallback: (() -> Unit)? = null
//    private var totalPrice: Long? = null
//    private var offlineChannelModel: OfflineChannelModel? = null
//
//
//    private var cashConvertModel: CashConvertModel? = null
//
//    private var edtUsdAmountHint: String? = null
//    private var edtKhrAmountHint: String? = null
//
//    private var menuOrderScreen: SecondaryScreenUI? = null
//
//    fun showDialog(
//        totalPrice: Long,
//        offlineChannelModel: OfflineChannelModel,
//        menuOrderScreen: SecondaryScreenUI? = null,
//        onDismissCallback: (() -> Unit)? = null,
//        confirmClickListener: ((CashConvertModel?) -> Unit)
//    ): CashConvertDialog {
//        this.totalPrice = totalPrice
//        this.offlineChannelModel = offlineChannelModel
//        this.confirmClickListener = confirmClickListener
//        this.menuOrderScreen = menuOrderScreen
//        this.onDismissCallback = onDismissCallback
//
//        XPopup.Builder(act)
//            .dismissOnTouchOutside(false)
//            .setPopupCallback(object : SimpleCallback() {
//                override fun onClickOutside(popupView: BasePopupView?) {
//                    dismissOrHideSoftInput()
//                    super.onClickOutside(popupView)
//                }
//            })
//            .autoOpenSoftInput(true)
//            .autoFocusEditText(true)
//            .asCustom(this)
//            .show()
//        return this
//    }
//
//    override fun dismiss() {
//        Timber.e("隐藏 CashConvertDialog")
//        onDismissCallback?.invoke()
//        super.dismiss()
//    }
//
//    // 返回自定义弹窗的布局
//    override fun getImplLayoutId(): Int {
//        return R.layout.dialog_cash_convert
//    }
//
//    override fun getMaxWidth(): Int {
//        if (context != null) {
//            context.let {
//                val displayMetrics = getDisplayMetrics(it)
//                val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
//                return screenWidth
//            }
//        }
//        return 0
//    }
//
//    override fun onCreate() {
//        super.onCreate()
//        binding = DialogCashConvertBinding.bind(popupImplView)
//        initListener()
//        initData()
//
//        menuOrderScreen?.getSecondCashConvertDialog()
//    }
//
//
//    private fun initListener() {
//        binding?.apply {
//            btnCancel.setOnClickListener {
//                dismissOrHideSoftInput()
//            }
//
//            btnConfirm.setOnClickListener {
//                confirmClickListener?.invoke(cashConvertModel)
//                dismiss()
//            }
//        }
//    }
//
//    private fun calculateChange() {
//        binding?.apply {
//            totalPrice?.let { orderPrice ->
//                offlineChannelModel?.let { channel ->
//                    val usdAmountStr = edtUsdAmount.text?.toString()
//                    var paidAmountUSD = if (usdAmountStr.isNullOrEmpty()) {
//                        0
//                    } else {
//                        usdAmountStr.toLong()
//                    }
//                    //限制美元的范围0到99999 Limit the range of USD to 0 to 99999
//                    if (paidAmountUSD < 0 || paidAmountUSD > 99999999) {
//                        paidAmountUSD = 0
//                    }
//
//
//                    val khrAmountStr = edtKhrAmount.text?.toString()
//                    val paidAmountKHR = if (khrAmountStr.isNullOrEmpty()) {
//                        0L
//                    } else {
//                        khrAmountStr.toLong()
//                    }
//
//
//                    //汇率
////                    val exchangeRate = channel.conversionRatio!!
////                    // 将订单金额从USD转换为KHR Convert order amount from USD to KHR
////                    val orderAmountKHR =
////                        ceil(
////                            amountReceivableUSD.times(exchangeRate).toDouble()
////                        ).toLong() + amountReceivableKHR
////
////                    // 客户支付金额 计算总共支付的KHR金额（包括USD转换成的KHR） Calculate the total KHR amount paid (including KHR converted from USD)
////                    val totalPaidKHR = (paidAmountUSD * exchangeRate + paidAmountKHR)
////
////                    //计算找零的KHR金额 Calculate the KHR amount of change
////                    var changeKHR = totalPaidKHR - orderAmountKHR
////                    Timber.e("totalPaidKHR: ${totalPaidKHR}  orderAmountKHR:${orderAmountKHR}  changeKHR:${changeKHR}")
////                    // 计算找零的USD金额和KHR金额 Calculate the change in USD and KHR
////                    val changeUSD = changeKHR / exchangeRate
////                    Timber.e("changeUSD:${changeUSD}   changeKHR %= exchangeRate:${changeKHR % exchangeRate}")
////                    changeKHR %= exchangeRate
////
////                    var changeKHRAfterCeil = if (changeKHR < 0) {
////                        floor(changeKHR.div(100.0)).times(100).toLong()
////                    } else {
////                        floor(changeKHR.div(100.0)).times(100).toLong()
////                    }
////
////                    // 返回找零金额数[USD, KHR] Returns the change amount [USD, KHR]
////                    val changeAmountStr = if (changeUSD == 0L && changeKHRAfterCeil == 0L) {
////                        "$0"
////                    } else if (changeUSD == 0L) {
////                        "${
////                            if (changeKHRAfterCeil > 0L) {
////                                ""
////                            } else {
////                                "-"
////                            }
////                        } KHR${changeKHRAfterCeil.absoluteValue}"
////                    } else if (changeKHRAfterCeil == 0L) {
////                        "${
////                            if (changeUSD > 0L) {
////                                ""
////                            } else {
////                                "-"
////                            }
////                        } \$${changeUSD.absoluteValue}"
////                    } else {
////                        val pre = if (changeUSD > 0L || changeKHRAfterCeil > 0L) {
////                            ""
////                        } else {
////                            "-"
////                        }
////
////                        if (pre == "-") {
////                            "- (\$${changeUSD.absoluteValue} + KHR${changeKHRAfterCeil.absoluteValue})"
////                        } else {
////                            "\$${changeUSD.absoluteValue} + KHR${changeKHRAfterCeil.absoluteValue}"
////                        }
////                    }
//                    //汇率
//                    val exchangeRate = channel.conversionRatio!!
//
//                    //总的收取多少美元  整体计算完以后四舍五入
//                    val totalReceiveUsd =
//                        (paidAmountUSD + paidAmountKHR.times(1.0) / exchangeRate).roundToTwoDecimalPlaces()
//
//                    //应收金额 瑞尔->美元
////                    val price =
////                        BigDecimal(orderAmountKHR.times(1.0).div(exchangeRate)).setScale(
////                            2,
////                            RoundingMode.HALF_UP
////                        ).times(BigDecimal(100)).toLong()
//
//                    //找零的美元
//                    val changeUSD = (BigDecimal(totalReceiveUsd) - BigDecimal(
//                        (totalPrice ?: 0).div(100.0)
//                    )).toDouble().roundToTwoDecimalPlaces()
//
//                    //找零的美元换算成瑞尔
//                    val changeKHRAfterCeil = FoundationHelper.usdConverToKhr(
//                        exchangeRate.toLong(),
//                        changeUSD.times(100).toLong()
//                    )
////                    round((changeUSD * exchangeRate).div(100)).times(100).toLong()
//
//                    val changeAmountStr =
//                        if (changeUSD < 0) "-$${changeUSD.absoluteValue.decimalFormatTwoDigitZero()}\n=-KHR${changeKHRAfterCeil.absoluteValue.decimalFormatZeroDigit()}" else {
//                            "$${changeUSD.absoluteValue.decimalFormatTwoDigitZero()}\n=KHR${changeKHRAfterCeil.absoluteValue.decimalFormatZeroDigit()}"
//                        }
//
//                    if (edtUsdAmount.text.isNullOrEmpty() && edtKhrAmount.text.isNullOrEmpty()) {
//                        cashConvertModel = CashConvertModel(
//                            collectCash = edtKhrAmountHint?.toLongOrNull(),
//                            changeAmount = 0,
//                            collectCashDollar = edtUsdAmountHint?.toLongOrNull(),
//                            changeAmountDollar = 0.0
//                        )
//
//                        edtUsdAmount.hint = edtUsdAmountHint
//                        edtKhrAmount.hint = edtKhrAmountHint
//                        btnConfirm.alpha = 1f
//                        btnConfirm.isEnabled = true
//                        tvChangeAmount.text = "$0.00"
//                        tvChangeAmountTip.isVisible = false
//                        context?.let {
//                            tvChangeAmount.setTextColor(R.color.black.getColor(it))
//                        }
//                        menuOrderScreen?.getSecondCashConvertDialog()?.isErrorStatus(false)
//                    } else {
//                        cashConvertModel = CashConvertModel(
//                            collectCash = paidAmountKHR,
//                            changeAmount = 0,
//                            collectCashDollar = paidAmountUSD,
//                            changeAmountDollar = changeUSD
//                        )
//                        val enable = changeUSD >= 0 //&& changeKHRAfterCeil >= 0
//                        btnConfirm.alpha = if (enable) 1f else 0.5f
//                        btnConfirm.isEnabled = enable
//                        tvChangeAmountTip.isVisible = !enable
//                        if (!enable) {
//                            context?.let {
//                                tvChangeAmount.setTextColor(R.color.main_red.getColor(it))
//                            }
//                        } else {
//                            context?.let {
//                                tvChangeAmount.setTextColor(R.color.black.getColor(it))
//                            }
//                        }
//                        menuOrderScreen?.getSecondCashConvertDialog()?.isErrorStatus(!enable)
//                        tvChangeAmount.text = changeAmountStr
//                        edtUsdAmount.hint = ""
//                        edtKhrAmount.hint = ""
//                    }
//                }
//            }
//
//
//            menuOrderScreen?.getSecondCashConvertDialog()?.updatePrice(
//                tvOrderPrice.text.toString(),
//                tvConversionRatio.text.toString(),
//                tvAmountReceivable.text.toString(),
//                edtUsdAmount.text.toString(),
//                edtKhrAmount.text.toString(),
//                tvChangeAmount.text.toString()
//            )
//        }
//    }
//
////    //美元应收金额
////    private var amountReceivableUSD = 0L
////
////    //KHR应收金额
////    private var amountReceivableKHR = 0L
//
//    private var orderAmountKHR = 0L
//
//    private fun initData() {
//
//        binding?.apply {
//            totalPrice?.let { orderPrice ->
//                offlineChannelModel?.let { channel ->
//
//                    tvOrderPrice.text = orderPrice.priceFormatTwoDigitZero2()
//                    tvConversionRatio.text = channel.getRatio()
////
////                    //订单总金额
////                    val price = orderPrice.div(100.0)
////                    //先将订单金额取整，2.5=2 First round the order amount, 2.5=2
////                    val paidAmountUSD = floor(price).toLong()
////
////                    //获取小数位的金额 Get the amount in decimal places
////                    val pointUsdAmount =
////                        price.toBigDecimal().minus(paidAmountUSD.toBigDecimal()).toDouble()
//                    val exchangeRate = channel.conversionRatio!!
////
////                    //获取
////                    val paidAmountKHRTemp = round(pointUsdAmount.times(exchangeRate)).toLong()
////                    //最后2位要向上进位到100的倍数
////                    val paidAmountKHRTempAfterCeil =
////                        round(paidAmountKHRTemp.div(100.0)).times(100).toLong()
////
////                    // 将订单金额从USD转换为KHR Convert order amount from USD to KHR
////                    val orderAmountKHR = round(price.times(exchangeRate)).toLong()
////                    //最后2位要向上进位到100的倍数  2.12版本 改成四舍五入
////                    val orderAmountKHRAfterCeil =
////                        round(orderAmountKHR.div(100.0)).times(100).toLong()
////
////
////                    val paidAmountKHR = paidAmountKHRTempAfterCeil //- changeKHR
//
//                    //预值都显示成瑞尔  四舍五入后的结果
//                    orderAmountKHR =
//                        round(orderPrice.times(exchangeRate).div(10000.0)).times(100).toLong()
//
//
//                    tvAmountReceivable.text =
//                        "KHR${orderAmountKHR.decimalFormatZeroDigit()}"
//
//                    edtUsdAmountHint = ""
//                    //"${if (paidAmountUSD == 0L) "" else paidAmountUSD.decimalFormatZeroDigit()}"
//                    edtUsdAmount.hint = edtUsdAmountHint
////                    edtUsdAmount.setSelection(edtUsdAmount.text?.length ?: 0)
//                    edtKhrAmountHint =
//                        "${orderAmountKHR.decimalFormatZeroDigit()}"
//                    edtKhrAmount.hint = edtKhrAmountHint
////                    edtKhrAmount.setSelection(edtKhrAmount.text?.length ?: 0)
//                    cashConvertModel = CashConvertModel(
//                        collectCash = orderAmountKHR,
//                        changeAmount = 0L,
//                        collectCashDollar = 0,
//                        changeAmountDollar = 0.0
//                    )
//                    //找零金额 The amount of change
//                    val changeAmountStr = "$0.00"
//                    tvChangeAmount.text = changeAmountStr
//                    //限制输入范围 Limit input range
//                    edtUsdAmount.setNumberRange(0, 99999999)
//                    edtUsdAmount.addTextChangedListener {
//                        calculateChange()
//                    }
//                    edtKhrAmount.setNumberRange(0, 999999999)
//                    edtKhrAmount.addTextChangedListener {
//                        calculateChange()
//                    }
//                }
//            }
//
//
//            menuOrderScreen?.getSecondCashConvertDialog()?.updatePrice(
//                tvOrderPrice.text.toString(),
//                tvConversionRatio.text.toString(),
//                tvAmountReceivable.text.toString(),
//                edtUsdAmount.text.toString(),
//                edtKhrAmount.hint.toString(),
//                tvChangeAmount.text.toString()
//            )
//
//            menuOrderScreen?.getSecondCashConvertDialog()?.isErrorStatus(false)
//
//            KeyboardUtils.showSoftInput(edtUsdAmount)
//        }
//
//    }
//
//    override fun onDismiss() {
//        super.onDismiss()
//        menuOrderScreen?.dismissSecondCashConvertDialog()
//    }
//
//}