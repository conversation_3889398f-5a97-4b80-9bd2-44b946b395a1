package com.metathought.food_order.casheir.ui.widget

import android.content.Context

import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.view.isVisible

import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.ViewTitleContentTextBinding
import com.metathought.food_order.casheir.databinding.ViewTitleSwitchTextBinding


class TitleSwitchView(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {
    private var _binding: ViewTitleSwitchTextBinding? = null
    private var isOpen = false
    private var onClickListener: ((Boolean) -> Unit)? = null

    init {
        // 取得属性值数组
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.TitleSwitchView)

        // 获取尺寸属性
        val title = typedArray.getString(
            R.styleable.TitleSwitchView_switch_title
        )

        val desc = typedArray.getString(
            R.styleable.TitleSwitchView_switch_desc
        )

        // 最后需要回收数组
        typedArray.recycle()
        initView()

        _binding?.apply {
            tvTitle.text = title
            if (!desc.isNullOrEmpty()) {
                setDesc(desc)
            }
        }
    }

    private fun initView() {

        _binding = ViewTitleSwitchTextBinding.inflate(LayoutInflater.from(context), this, true)

        _binding?.ivSwitch?.setOnClickListener {
            setOpen(!isOpen)
            onClickListener?.invoke(isOpen)
        }

    }

    fun setTitle(title: String) {
        _binding?.apply {
            tvTitle.text = title
        }
    }

    fun setDesc(desc: String) {
        _binding?.apply {
            tvDesc.text = desc
            tvDesc.isVisible = !tvDesc.text.isNullOrEmpty()
        }
    }


    fun setOpen(isOpen: Boolean?) {
        this.isOpen = isOpen ?: false
        _binding?.apply {
            ivSwitch.setImageResource(if (isOpen!!) R.drawable.icon_switch_open else R.drawable.icon_switch_close)

        }
    }

    fun isSwitchOpen(): Boolean {
        return isOpen
    }

    fun setSwitchListener(onClickListener: ((Boolean) -> Unit)? = null) {
        this.onClickListener = onClickListener
    }

//    interface SetSwitchOpenListener{
//        fun
//    }
}