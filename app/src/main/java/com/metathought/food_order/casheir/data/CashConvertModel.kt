package com.metathought.food_order.casheir.data

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

/**
 * <AUTHOR>
 * @date 2024/5/913:25
 * @description
 */
data class CashConvertModel(
    @SerializedName("collectCash")
    val collectCash: Long? = null,
    //找零的金额
    @SerializedName("changeAmount")
    val changeAmount: Long? = null,
    //收款的美元
    @SerializedName("collectCashDollar")
    val collectCashDollar: BigDecimal? = null,
    //找零的美元
    @SerializedName("changeAmountDollar")
    val changeAmountDollar: Double? = null
)