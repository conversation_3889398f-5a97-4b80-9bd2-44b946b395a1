package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.AttachPopupView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ConsumerResponse
import com.metathought.food_order.casheir.databinding.DialogSearchNicknameViewBinding
import com.metathought.food_order.casheir.ui.adapter.FilterNicknameAdapter
import com.metathought.food_order.casheir.ui.ordered.OrderedViewModel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

class OrderFilterNicknameDialog(
    context: Context,
    val parentView: View,
    val viewModel: OrderedViewModel,
    val selecteAccount: ConsumerResponse? = null,
    val confirmListener: (selectedAccount: ConsumerResponse?) -> Unit,
    val dismissListener: () -> Unit
) : AttachPopupView(context) {

    fun showDialog(): AttachPopupView {
        XPopup.Builder(context)
            .isDestroyOnDismiss(true)
            .moveUpToKeyboard(false)
            .autoFocusEditText(false)
            .autoOpenSoftInput(false)
            .atView(parentView)
            .hasShadowBg(false)
//            .isViewMode(true)
            .asCustom(this)
            .show()
        return this
    }

    override fun doMeasure() {
        Timber.d("doMeasure")
//        super.doMeasure()
        /*
        复制BasePopupView的doMeasure方法，去除AttachPopupView中键盘弹出从新测量的逻辑
        否则键盘弹出时会从新播放动画
         */
        val act = activity ?: return
        var params: MarginLayoutParams? = layoutParams as? MarginLayoutParams
        val activityContent = activityContentView
        if (params == null) {
            params = MarginLayoutParams(activityContent.width, activityContent.height)
        } else {
            params.width = activityContent.width
            params.height = activityContent.height
        }
        params.leftMargin =
            if (popupInfo != null && popupInfo.isViewMode) activityContent.left else 0
        params.topMargin = activityContent.top
        layoutParams = params
    }


    private lateinit var binding: DialogSearchNicknameViewBinding

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_search_nickname_view
    }

    private var filterNicknameAdapter: FilterNicknameAdapter? = null
    private var searchAccountJob: Job? = null

    private val observer: (OrderedViewModel.UINickNameListModel) -> Unit = {
        binding.apply {
//                    it.showLoading?.let {
//                        if (it)
//                            showProgress()
//                    }
            if (it.showEnd) {
//                        dismissProgress()
                if (it.isRefresh != false) {
                    filterNicknameAdapter?.replaceData(arrayListOf())
                } else {
                    nickNameRefreshLayout.finishLoadMoreWithNoMoreData()
                }
            }
            it.showError?.let { error ->
//                        dismissProgress()
//                        showToast(error)
            }

            it.showSuccess?.let { response ->
//                        dismissProgress()
//                        layoutEmpty.root.isVisible = false
                if (it.isRefresh != false) {
                    filterNicknameAdapter?.replaceData(
                        response
                    )
                    nickNameRefreshLayout.finishRefresh()

                } else {
                    filterNicknameAdapter?.addData(response)
                    nickNameRefreshLayout.finishLoadMore()
                }
            }

            tvEmptyText.isVisible = (filterNicknameAdapter?.itemCount ?: 0) == 0
        }

    }

    override fun onCreate() {
        super.onCreate()
        val view: View = findViewById(R.id.rootView)
        binding = DialogSearchNicknameViewBinding.bind(view)
        binding.apply {
            edtNickNameSearch.getEditText()
                ?.setHint("${context.getString(R.string.customer_nickname)},${context.getString(R.string.customer_account)}")
            edtNickNameSearch.setTextChangedListenerCallBack {
                searchAccountJob?.cancel()
                searchAccountJob = lifecycleScope.launch {
                    delay(500)
                    viewModel.getConsumerList(keyword = edtNickNameSearch.getSearchContent())
                }
            }
            nickNameRefreshLayout.setEnableRefresh(true)
            nickNameRefreshLayout.setEnableLoadMore(false)
            nickNameRefreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    viewModel.getConsumerList(keyword = edtNickNameSearch.getSearchContent())
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    viewModel.getConsumerList(keyword = edtNickNameSearch.getSearchContent())
                }

            })
            filterNicknameAdapter = FilterNicknameAdapter(context) { record ->
                val selecteAccount = filterNicknameAdapter?.getSelecteAccount()
                confirmListener(selecteAccount)
            }
            filterNicknameAdapter?.setSelecteAccount(selecteAccount)
            rvNicnameList.adapter = filterNicknameAdapter

            viewModel.uiNickNameListState.observeForever(observer)
        }
        viewModel.getConsumerList()
    }

    override fun onDismiss() {
        viewModel.uiNickNameListState.removeObserver(observer)
        dismissListener()
        super.onDismiss()
    }
}