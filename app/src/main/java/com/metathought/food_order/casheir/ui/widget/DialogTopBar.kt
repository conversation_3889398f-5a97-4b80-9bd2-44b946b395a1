package com.metathought.food_order.casheir.ui.widget

import android.content.Context

import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout

import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.ViewDialogTopPartBinding


class DialogTopBar(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {
    private var _binding: ViewDialogTopPartBinding? = null

    init {

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.DialogTopBar)

        try {
            val title = typedArray.getString(
                R.styleable.DialogTopBar_dialog_title
            )

            initView()

            _binding?.apply {
                tvDialogName.text = title
            }
        } catch (e: Exception) {

        } finally {
            // 最后需要回收数组
            typedArray.recycle()
        }
    }

    private fun initView() {
        _binding = ViewDialogTopPartBinding.inflate(LayoutInflater.from(context), this, true)

    }

    fun getCloseBtn(): View? {
        return _binding?.btnClose
    }

    fun getIconAfterTitle(): ImageView? {
        return _binding?.tvDialogIcon
    }

    fun setTitle(title: String) {
        _binding?.apply {
            tvDialogName.text = title
        }
    }

//    fun setContent(content: String) {
//        _binding?.apply {
//            tvContent.text = content
//        }
//    }

}