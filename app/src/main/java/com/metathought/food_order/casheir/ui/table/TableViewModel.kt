package com.metathought.food_order.casheir.ui.table

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.TableStatusEnum
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.CreateTempTableRequest
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.table.CreateTempTableCodeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.websocket.ApiWebSocket
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.tinder.scarlet.Lifecycle
import com.tinder.scarlet.WebSocket
import com.tinder.scarlet.lifecycle.LifecycleRegistry
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ExecutionException
import javax.inject.Inject

@HiltViewModel
class TableViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {

    private val _tableResponse = MutableLiveData<TableResponse>()
    val tableResponseData get() = _tableResponse

    private val _filteredTableResponse = MutableLiveData<TableResponse>()
    val filteredTableResponse get() = _filteredTableResponse
    private val _uiState = MutableLiveData<UIModel>()

    val uiState get() = _uiState

    private val _uiSaveRecordState = MutableLiveData<UISaveRecordStateModel>()
    val uiSaveRecordState get() = _uiSaveRecordState

    private val _uiCreateTempleTableState =
        MutableLiveData<ApiResponse<CreateTempTableCodeResponse>>()

    val uiCreateTempleTableState = _uiCreateTempleTableState

    fun getRepository(): Repository {
        return repository
    }

    fun getTable(keyword: String) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val tableResponse = repository.getTable(keyword)
                if (tableResponse is ApiResponse.Success) {
                    val universalTableInfo =
                        tableResponse.data.firstOrNull { it.isUniversalQr() }
                    if (universalTableInfo != null) {
                        /**
                         * 本地缓存通用桌信息
                         */
                        PreferenceHelper.setUniversalTableInfo(universalTableInfo.toJson())
                    }
                }
                emitUiState(tableResponse, keyword = keyword)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(e.message))
            }
        }
    }

    fun reserverTable(reserveTableRequest: ReserveTableRequest) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val reserveTableRequest = repository.reserveTable(reserveTableRequest)
                emitUiState(resultReserve = reserveTableRequest)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(e.message))
            }

        }
    }

    fun cancelReserveTable(reserveTableRequest: String, tableUuid: String) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val cancelReserve = repository.cancelReserveTable(reserveTableRequest)
                emitUiState(cancelReserve = cancelReserve, tableUuid = tableUuid)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(e.message))
            }
        }
    }

    fun emitUiState(
        result: ApiResponse<TableResponse>? = null,
        resultReserve: ApiResponse<BaseBooleanResponse>? = null,
        cancelReserve: ApiResponse<TableResponseItem>? = null,
        tableUuid: String? = null,
        keyword: String? = null
    ) {
        val uiModel = UIModel(result, resultReserve, cancelReserve, tableUuid, keyword)
        _uiState.value = uiModel
    }

    data class UIModel(
        val result: ApiResponse<TableResponse>?,
        val resultReserve: ApiResponse<BaseBooleanResponse>?,
        val cancelReserve: ApiResponse<TableResponseItem>?,
        val tableUuid: String? = null,
        val keyword: String? = null
    )

    fun filterFloor(query: String, status: Int) {
        if (status != TableStatusEnum.ALL.id) {
            if (status == TableStatusEnum.TO_BE_PAID.id) {
                val filteredList = tableResponseData.value?.filter { table ->
                    if (table.location != null && query.isNotEmpty()) {
                        table.location == query && table.hasPrintPreSettlement == true
                    } else {
                        table.hasPrintPreSettlement == true
                    }
                }
                val tableResponse = TableResponse()
                filteredList?.let { tableResponse.addAll(it) }
                filteredTableResponse.value = tableResponse
            } else {
                val filteredList = tableResponseData.value?.filter { table ->
                    if (table.location != null && query.isNotEmpty()) {
                        table.location == query && table.status == status
                    } else {
                        table.status == status
                    }
                }
                val tableResponse = TableResponse()
                filteredList?.let { tableResponse.addAll(it) }
                filteredTableResponse.value = tableResponse
            }
        } else if (status == TableStatusEnum.ALL.id && query.isNotEmpty()) {
            val filteredList = tableResponseData.value?.filter { table ->
                table.location == query
            }
            val tableResponse = TableResponse()
            filteredList?.let { tableResponse.addAll(it) }
            filteredTableResponse.value = tableResponse
        } else {
            filteredTableResponse.value = tableResponseData.value
        }
    }

    fun filterStatus(status: Int): TableResponse {
        val filteredList = tableResponseData.value?.filter { table ->
            table.status == status
        }
        val tableResponse = TableResponse()
        filteredList?.let { tableResponse.addAll(it) }

        return tableResponse
    }

    fun filterToBePaidStatus(): TableResponse {
        val filteredList = tableResponseData.value?.filter { table ->
            table.hasPrintPreSettlement == true
        }
        val tableResponse = TableResponse()
        filteredList?.let { tableResponse.addAll(it) }

        return tableResponse
    }

    fun updateShoppingRecordForReserve(it: ReserveTableRequest, table: TableResponseItem) {
        viewModelScope.launch {
            emitUiSaveRecordState(showLoading = true)
            ShoppingHelper.del(0)
            ShoppingHelper.clearCoupon(0)
            ShoppingHelper.clearNote(0)
            ShoppingHelper.delAndCustomerAndTable(0)
            val shoppingRecord = ShoppingHelper.updateSelectTableAndCustomer(it, table, 0)
            withContext(Dispatchers.Main) {
                emitUiSaveRecordState(showSuccess = true, shoppingRecord = shoppingRecord)
            }
        }
    }

    fun updateShoppingRecordForTable(table: TableResponseItem) {
        viewModelScope.launch {
            emitUiSaveRecordState(showLoading = true)
//            ShoppingHelper.del(0)
//            ShoppingHelper.delAndCustomerAndTable(0)

            //这段删除逻辑为了修复3496  不知道会引起其他什么问题
            val currentShop = ShoppingHelper.get(0)
            if (currentShop?.tableUuid != table.uuid) {
                ShoppingHelper.clearNote(0)
                ShoppingHelper.clearCoupon(0)
                ShoppingHelper.delAndCustomerAndTable(0)
            }

            val shoppingRecord = ShoppingHelper.updateSelectTable(table, 0)
            withContext(Dispatchers.Main) {
                emitUiSaveRecordState(showSuccess = true, shoppingRecord = shoppingRecord)
            }
        }
    }

    private suspend fun emitUiSaveRecordState(
        showLoading: Boolean? = null,
        showSuccess: Boolean? = null,
        shoppingRecord: ShoppingRecord? = null
    ) {
        val uiModel =
            UISaveRecordStateModel(showLoading, showSuccess, shoppingRecord)
        withContext(Dispatchers.Main) {
            _uiSaveRecordState.value = uiModel
        }
    }

    data class UISaveRecordStateModel(
        val showLoading: Boolean?,
        val showSuccess: Boolean?,
        val shoppingRecord: ShoppingRecord?
    )

//    private val _liveDATA = MutableLiveData<WebSocket.Event>()
//    val liveDataRespose get() = _liveDATA
//    val lifecycleRegistry = LifecycleRegistry()
//    val apiWebSocketService = ApiWebSocket.provideSocketApi(lifecycleRegistry)
//
//    @SuppressLint("CheckResult")
//    fun connectWebsocket() {
//        apiWebSocketService.observeConnection().observeOn(Schedulers.io()).subscribe({ response ->
//            Log.d("WebSocket", response.toString())
//            viewModelScope.launch {
//                _liveDATA.value = response
//            }
//        }, { error ->
//            Log.e("WebSocket", error.message.orEmpty())
//        })
//    }
//
//    fun testingWebsocketSendMessage() {
//        apiWebSocketService.sendMessage("pong")
//        Log.d("WebSocket", "send back pong")
//    }
//
//    fun destroylifeCycle() {
//        lifecycleRegistry.onNext(Lifecycle.State.Destroyed)
//    }
//
//    fun startLifeCycle() {
//        lifecycleRegistry.onNext(Lifecycle.State.Started)
//    }


    fun printerTicket(context: Context, responseItem: TableResponseItem) {

        val connectUSB = PrinterUsbDeviceHelper.isPosPrinterConnectUSB()
        connectUSB.addListener(
            object : ListenableFuture.Listener<Boolean> {
                //是否连接了本地USB Printer打印机
                //Is a local USB Printer printer connected?
                override fun onSuccess(isConnected: Boolean) {
                    viewModelScope.launch {
                        //获取打印模板UI
                        try {
                            withContext(Dispatchers.Main) {
                                _uiCreateTempleTableState.value = ApiResponse.Loading
                            }
                            /**
                             * 这边简单校验wifi打印机是否有链接
                             */
                            var isHasWifiConnect = false
                            val printerList = PrinterDeviceHelper.getPrinterList()
                            printerList.forEach {
                                if (it.type == PrinterTypeEnum.WIFI.type && PrinterDeviceHelper.getWifiConnectState(
                                        it
                                    )
                                ) {
                                    isHasWifiConnect = true
                                }
                            }
                            val result = repository.createTempTableCode(
                                CreateTempTableRequest(
                                    refTableId = responseItem.id!!.toLong(),
                                    isPosPrint = isConnected || isHasWifiConnect
                                )
                            )

                            if (result is ApiResponse.Success) {
                                val response = result.data
                                Printer.printPrinterTempTable(context, response)
                            }
                            withContext(Dispatchers.Main) {
                                _uiCreateTempleTableState.value = result
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            withContext(Dispatchers.Main) {
                                _uiCreateTempleTableState.value = ApiResponse.Error(e.message)
                            }
                        }
                    }
                }

                override fun onFailure(e: ExecutionException) {
                }

            })

    }

}