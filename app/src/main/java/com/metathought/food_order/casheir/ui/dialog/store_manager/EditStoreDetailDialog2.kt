package com.metathought.food_order.casheir.ui.dialog.store_manager

import android.content.Context
import android.content.Intent
import androidx.core.app.ActivityCompat.startActivityForResult
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogEditStoreDetailBinding
import com.metathought.food_order.casheir.ui.dialog.BaseCenterDialog
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


@AndroidEntryPoint
class EditStoreDetailDialog2  //注意：自定义弹窗本质是一个自定义View，但是只需重写一个参数的构造，其他的不要重写，所有的自定义弹窗都是这样。
    (private val act: Context?) : BaseCenterDialog(act!!) {

    private var binding: DialogEditStoreDetailBinding? = null

    private var storeManagerViewModel: StoreManagerViewModel? = null

    fun showDialog(
        storeManagerViewModel: StoreManagerViewModel,
        onConfirmClickListener: ((String) -> Unit),
    ): EditStoreDetailDialog2 {
        this.storeManagerViewModel = storeManagerViewModel

        XPopup.Builder(act)
            .autoDismiss(false)
            .isViewMode(true)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }
            })
            .autoOpenSoftInput(true)
            .autoFocusEditText(true)
            .asCustom(this)
            .show()
        return this
    }


    // 返回自定义弹窗的布局
    override fun getImplLayoutId(): Int {
        return R.layout.dialog_edit_store_detail
    }

    // 执行初始化操作，比如：findView，设置点击，或者任何你弹窗内的业务逻辑
    override fun onCreate() {
        super.onCreate()
        binding = DialogEditStoreDetailBinding.bind(popupImplView)
        binding?.apply {
//            storeManagerViewModel?.getUploadToken()
            ivLogo.setOnClickListener {
                pickImageFromGallery()
            }
        }
    }

    private fun pickImageFromGallery() {
        val intent = Intent(Intent.ACTION_PICK)
        intent.setType("image/*")
        activity.startActivityForResult(intent, 100)

    }


    // 设置最大宽度，看需要而定，
    override fun getMaxWidth(): Int {
        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
                return screenWidth
            }
        }

        return super.getMaxWidth()
    }

    override fun getMaxHeight(): Int {

        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
                return screenHeight
            }
        }

        return super.getMaxHeight()
    }

    override fun onDismiss() {
        Timber.e("店铺编辑弹窗关闭")
        super.onDismiss()
    }
}

