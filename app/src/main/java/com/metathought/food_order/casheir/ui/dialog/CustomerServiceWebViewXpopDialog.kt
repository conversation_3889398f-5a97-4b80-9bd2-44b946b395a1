package com.metathought.food_order.casheir.ui.dialog


import android.content.Context
import android.webkit.ConsoleMessage
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogCustomerServiceWebviewBinding
import com.metathought.food_order.casheir.helper.XpopHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber


/**
 * 客服聊天页弹窗
 *
 * @property act
 * @constructor Create empty Customer service web view xpop dialog
 */
class CustomerServiceWebViewXpopDialog(private val act: Context?) : BaseCenterDialog(act!!) {

    private var binding: DialogCustomerServiceWebviewBinding? = null

    //    private var url = "https://chat.mos.me/#/chat?id=Mpos_service"
    private var url = "https://mos.me/Mpos_service"

//    private var url = "https://mos.me/lRbtMZv8l3D"  //测试

//    private val url = "https://chat-test.mos.me/#/chat?id=Mpos_service"
//    private val url = "https://www.baidu.com"

    // 返回自定义弹窗的布局
    override fun getImplLayoutId(): Int {
        return R.layout.dialog_customer_service_webview
    }

    fun showDialog(
    ): CustomerServiceWebViewXpopDialog {
        XPopup.Builder(act)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }

                override fun onDismiss(popupView: BasePopupView?) {
                    XpopHelper.removeToMap(popupView)
                    super.onDismiss(popupView)
                }
            })
            .asCustom(this)
            .show()
        XpopHelper.addToMap(this)
        return this
    }

    override fun onCreate() {
        super.onCreate()
        binding = DialogCustomerServiceWebviewBinding.bind(popupImplView)
        binding?.apply {
            url =
                "${url}?source=${MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()}"

            webView.webChromeClient = object : WebChromeClient() {
                override fun onConsoleMessage(consoleMessage: ConsoleMessage): Boolean {
                    if (consoleMessage.message() == "'webkitIndexedDB' is deprecated") {
                        // 可以在这里进行处理，例如记录日志或显示警告信息
                        println("警告：webkitIndexedDB 已弃用")
                    }
                    return super.onConsoleMessage(consoleMessage)
                }

            }
            webView.settings.javaScriptEnabled = true
            // 启用 DOM 存储
            webView.settings.domStorageEnabled = true
            webView.webViewClient = object : WebViewClient() {
                // 处理 Android 5.0+ (API 21+)
                override fun shouldOverrideUrlLoading(
                    view: WebView?,
                    request: WebResourceRequest
                ): Boolean {
                    Timber.e("request.getUrl().toString(): ${request.getUrl().toString()}")
                    return false;
                }
            }
            webView.loadUrl(url)
        }
    }


    // 设置最大宽度，看需要而定，
    override fun getPopupWidth(): Int {

        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val height = (displayMetrics.heightPixels * 0.8).toInt()
                val screenWidth = (height / 4 * 3).toInt()
                return screenWidth
            }
        }

        return super.getPopupWidth()
    }


    override fun getPopupHeight(): Int {
        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val height = (displayMetrics.heightPixels * 0.8).toInt()
                return height
            }
        }
        return super.getPopupHeight()
    }
}
