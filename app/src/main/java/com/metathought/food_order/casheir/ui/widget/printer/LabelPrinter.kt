package com.metathought.food_order.casheir.ui.widget.printer

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.databinding.LabelTicketPrinterBinding
import com.metathought.food_order.casheir.extension.getPrinterDiningStyleString
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import net.posprinter.TSPLConst
import net.posprinter.TSPLPrinter
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.ExecutionException
import androidx.core.graphics.createBitmap
import com.metathought.food_order.casheir.constant.PrinterFontSizeEnum
import com.metathought.food_order.casheir.utils.DisplayUtils

/**
 * 标签打印
 * <AUTHOR>
 * @date 2024/5/1717:46
 * @description
 */
object LabelPrinter {

    /**
     * 标签小票打印
     *
     */
    fun printLabelTicket(
        context: Context,
        labelTickerTemp: PrintTamplateResponseItem,
        orderedInfoResponse: OrderedInfoResponse?,
        printerConfigInfo: PrinterConfigInfo?,
    ) {
        try {
            /**
             *过滤出标签打印
             */
            val printer = PrinterUsbDeviceHelper.getLabelPrinter(printerConfigInfo?.usbDevice)
            printer.addListener(object : ListenableFuture.Listener<TSPLPrinter?> {
                override fun onSuccess(result: TSPLPrinter?) {
                    if (labelTickerTemp == null) {
                        return
                    }
                    result?.apply {
                        val isOrderMore =
                            orderedInfoResponse?.currentOrderMoreList?.isNotEmpty() == true
                        //判断订单中是否只有待定价的菜
                        var onlyToUnProcess = false
                        orderedInfoResponse?.let { orderInfo ->
                            val list = if (isOrderMore) {
                                orderInfo.currentOrderMoreList
                            } else {
                                orderInfo.goods
                            } ?: listOf()
                            val unProcessCount = list?.count { !it.isHasProcessed() }

                            Timber.e("isOrderMore: $isOrderMore   unProcessCount: $unProcessCount   goods:${orderInfo.goods?.size}")
                            onlyToUnProcess = unProcessCount == list?.size

                            if (onlyToUnProcess) {
                                //只有待定价的菜不打印
                                Timber.e("只有待定价的菜不打印")
                                return
                            }

                            var currentIndex = 0
                            var totalCount = 0
                            list.forEachIndexed { index, orderedGoods ->
                                /**
                                 * 是否打印标签
                                 */
                                if (orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                                    if (orderedGoods.printLabel == true) {
                                        /**
                                         * 已定价就打，不需要就打
                                         */
                                        if (orderedGoods.isHasProcessed()) {
                                            totalCount += (orderedGoods.num ?: 0)
                                        }
                                    }
                                } else {
                                    //加上套餐要打印的的数量
                                    orderedGoods.orderMealSetGoodsDTOList?.forEach {
                                        if (it.printLabel == true) {
                                            totalCount +=
                                                (it.num ?: 0) * (it.number ?: 0) * (orderedGoods.num
                                                    ?: 0)
                                        }
                                    }
                                }
                            }
                            //是否满足打印赠品
                            val isCanPrintZp =
                                orderedInfoResponse.getZsCouponValid() && !isOrderMore && orderedInfoResponse?.isAfterPayStatus() == true
                            var giftList: List<UsageGoods>? = null

                            if (isCanPrintZp) {
                                giftList = orderedInfoResponse.giftGoods?.filter {
                                    it.printLabel == true
                                }
                                giftList?.forEach { _ ->
                                    totalCount += 1
                                }
                            }


                            for (orderedGoods in list) {
                                if (!orderedGoods.isHasProcessed()) {
                                    continue
                                }
                                if (orderedGoods.printLabel == true) {
                                    for (i in 0 until (orderedGoods.num ?: 0)) {
                                        currentIndex++
                                        switchPrintLabel(
                                            context,
                                            result,
                                            printerConfigInfo,
                                            labelTickerTemp,
                                            orderedInfoResponse,
                                            orderedGoods,
                                            null,
                                            null,
                                            currentIndex,
                                            totalCount
                                        )
                                    }
                                } else {
                                    orderedGoods.orderMealSetGoodsDTOList?.forEach { orderMealSetGood ->
                                        if (orderMealSetGood.printLabel == true) {
                                            //套餐选择的数量
                                            for (i1 in 0 until (orderedGoods.num ?: 0)) {
                                                for (i2 in 0 until (orderMealSetGood.num ?: 0)) {
                                                    for (i3 in 0 until (orderMealSetGood.number
                                                        ?: 0)) {
                                                        currentIndex++
                                                        switchPrintLabel(
                                                            context,
                                                            result,
                                                            printerConfigInfo,
                                                            labelTickerTemp,
                                                            orderedInfoResponse,
                                                            null,
                                                            null,
                                                            orderMealSetGood,
                                                            currentIndex,
                                                            totalCount
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                            }


                            if (isCanPrintZp) {
                                giftList?.forEach { gift ->
                                    currentIndex++
                                    switchPrintLabel(
                                        context,
                                        result,
                                        printerConfigInfo,
                                        labelTickerTemp,
                                        orderedInfoResponse,
                                        null,
                                        gift,
                                        null,
                                        currentIndex,
                                        totalCount
                                    )
                                }
                            }
                        }

                    }
                }

                override fun onFailure(e: ExecutionException) {

                }
            })

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun switchPrintLabel(
        context: Context,
        result: TSPLPrinter? = null,
        printerConfigInfo: PrinterConfigInfo? = null,
        labelTickerTemp: PrintTamplateResponseItem? = null,
        orderedInfoResponse: OrderedInfoResponse? = null,
        goods: OrderedGoods? = null,
        giftGoods: UsageGoods? = null,
        orderMealSetGood: OrderMealSetGood? = null,
        currentIndex: Int? = null,
        totalNum: Int? = null,

        ) {
        result?.apply {
            if (printerConfigInfo?.model == "40*30") {
                /**
                 * 40*30
                 */
                create40WidthPrintLabelTicketBitmap(
                    context,
                    labelTickerTemp!!,
                    orderedInfoResponse,
                    goods = goods,
                    giftGoods = giftGoods,
                    currentIndex = currentIndex!!,
                    orderMealSetGood = orderMealSetGood,
                    totalNum = totalNum!!
                )?.let {
                    sizeMm(40.0, 30.0)
                        .gapMm(2.0, 0.0)
                        .reference(0, 0)
                        .direction(TSPLConst.DIRECTION_FORWARD)
                        .cls()
                        .bitmap(0, 0, TSPLConst.BMP_MODE_OVERWRITE, 290, it)
                        .print(1)

                }
            } else if (printerConfigInfo?.model == "60*40") {
                /**
                 * 60*40
                 */
                create60WidthPrintLabelTicketBitmap(
                    context,
                    labelTickerTemp!!,
                    orderedInfoResponse,
                    goods = goods,
                    giftGoods = giftGoods,
                    orderMealSetGood = orderMealSetGood,
                    currentIndex = currentIndex!!,
                    totalNum = totalNum!!
                )?.let {
                    sizeMm(60.0, 40.0)
                        .gapMm(2.0, 0.0)
                        .reference(0, 0)
                        .direction(TSPLConst.DIRECTION_FORWARD)
                        .cls()
                        .bitmap(0, 0, TSPLConst.BMP_MODE_OVERWRITE, 600, it)
                        .print(1)

                }
            }
        }
    }


    //标签小票 40*30
    private fun create40WidthPrintLabelTicketBitmap(
        context: Context,
        printTemplateResponseItem: PrintTamplateResponseItem,
        orderedInfoResponse: OrderedInfoResponse?,
        goods: OrderedGoods? = null,
        giftGoods: UsageGoods? = null,
        orderMealSetGood: OrderMealSetGood? = null,
        currentIndex: Int,
        totalNum: Int
    ): Bitmap? {
        var createBitmap: Bitmap? = null
        val binding = LabelTicketPrinterBinding.inflate(LayoutInflater.from(context))

        val localeList = mutableListOf<Locale>()
        val langList = printTemplateResponseItem.getLangList()
        langList.forEach {
            localeList.add(Locale(it.uppercase()))
        }

        if (localeList.isEmpty()) {
            localeList.add(Locale("EN"))
        }

        //是否是预订单
        val isReservation = orderedInfoResponse?.isReservation == true

        binding.apply {
            val lang = localeList.first()
            tvStoreName.text =
                MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(localeList.first())
            tvStoreName.isVisible =
                printTemplateResponseItem.informationShow?.showMerchantName == true
            tvPickUpNo.text = "${orderedInfoResponse?.pickupCode}"
            if (printTemplateResponseItem.informationShow?.showOrderType == true) {
                tvPickUpNo.text = "${tvPickUpNo.text}/ "
            }
            if (orderedInfoResponse?.pickupCode.isNullOrEmpty()) {
                tvPickUpNo.text = ""
            }
            tvNumber.text = "${currentIndex}/$totalNum"
            tvDingstyle.text = "${
                if (isReservation) {
                    context.getStringByLocale(R.string.print_title_pre_order, lang)
                } else {
                    orderedInfoResponse?.diningStyle?.getPrinterDiningStyleString(
                        context,
                        lang
                    )
                }
            }"

            goods?.let {
                tvGoodName.text = it.getNameByLocal(lang)
                if (printTemplateResponseItem.informationShow?.showGoodsCode == true && !it.number.isNullOrEmpty()) {
                    tvGoodName.text = "${it.number}-${tvGoodName.text}"
                }
                if (it.isToBeWeighed() && it.isHasProcessed() && !it.isMealSet()) {
                    tvGoodName.text =
                        "${tvGoodName.text}(${it.getWeightStr()})"
                }
                tvTag.text = it.getLocaleGoodsTagStrByTicket(lang)
                tvFeed.text = it.getLocaleFeedTagStr(lang)
                tvPrice.text = it.getCurrentFinalLabelPrintPrice()?.priceFormatTwoDigitZero2()
            }

            orderMealSetGood?.let {
                tvGoodName.text = it.getNameByLocale(lang)
                if (printTemplateResponseItem.informationShow?.showGoodsCode == true && !it.mealSetGoodsNumber.isNullOrEmpty()) {
                    tvGoodName.text = "${it.mealSetGoodsNumber}-${tvGoodName.text}"
                }
                tvTag.text = it.getTagStr(lang)
                tvFeed.text = ""
                tvPrice.text = it.calculateSingleTotalMarkUpPrice()?.priceFormatTwoDigitZero2()
            }

            giftGoods?.let {
                tvGoodName.text = it.getNameByLocal(lang)
                if (printTemplateResponseItem.informationShow?.showGoodsCode == true && !it.number.isNullOrEmpty()) {
                    tvGoodName.text = "${it.number}-${tvGoodName.text}"
                }
                tvTag.text = ""
                tvFeed.text = ""
                tvPrice.text = it.discountPrice?.priceFormatTwoDigitZero2()
            }

//                tvTag.text = "2222白日依山尽,黄河入海流,欲穷千里目,更上一层楼"
//                tvFeed.text = "3333白日依山尽,黄河入海流,欲穷千里目,更上一层楼"

            llTag.isVisible =
                tvTag.text.isNotEmpty()
            llFeed.isVisible =
                tvFeed.text.isNotEmpty()

            tvStoreName.isVisible =
                printTemplateResponseItem.informationShow?.showMerchantName == true
            tvDingstyle.isVisible = printTemplateResponseItem.informationShow?.showOrderType == true
            tvPickUpNo.isVisible = true
            tvNumber.isVisible =
                printTemplateResponseItem.informationShow?.showOrderComplete == true
            tvPrice.isVisible = printTemplateResponseItem.informationShow?.showGoodsPrice == true


            tvSlogan.text = printTemplateResponseItem.slogan
            llSlogan.isVisible =
                !printTemplateResponseItem.slogan.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showSlogan == true

            tvPayTime.text = orderedInfoResponse?.payTime
            tvPayTime.isInvisible = orderedInfoResponse?.isAfterPayStatus() != true



            if (printTemplateResponseItem.informationShow?.labelTicketGoodsNameFontSize == PrinterFontSizeEnum.STANDARD.id) {
                tvGoodName.textSize = DisplayUtils.px2sp(context, 24f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketGoodsNameFontSize == PrinterFontSizeEnum.BIG.id) {
                tvGoodName.textSize = DisplayUtils.px2sp(context, 27f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketGoodsNameFontSize == PrinterFontSizeEnum.BIGGER.id) {
                tvGoodName.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
            }


            if (printTemplateResponseItem.informationShow?.labelTicketTagNameFontSize == PrinterFontSizeEnum.STANDARD.id) {
                tvTag.textSize = DisplayUtils.px2sp(context, 16f).toFloat()
                tvFeed.textSize = DisplayUtils.px2sp(context, 16f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketTagNameFontSize == PrinterFontSizeEnum.BIG.id) {
                tvTag.textSize = DisplayUtils.px2sp(context, 19f).toFloat()
                tvFeed.textSize = DisplayUtils.px2sp(context, 19f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketTagNameFontSize == PrinterFontSizeEnum.BIGGER.id) {
                tvTag.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
                tvFeed.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            }

            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    290,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    235,
                    View.MeasureSpec.EXACTLY
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)

            createBitmap =
                createBitmap(root.measuredWidth, root.measuredHeight, Bitmap.Config.RGB_565)
            val canvas = Canvas(createBitmap!!).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)
        }
        return createBitmap
    }

    //标签小票 60*40
    private fun create60WidthPrintLabelTicketBitmap(
        context: Context,
        printTemplateResponseItem: PrintTamplateResponseItem,
        orderedInfoResponse: OrderedInfoResponse?,
        goods: OrderedGoods? = null,
        giftGoods: UsageGoods? = null,
        orderMealSetGood: OrderMealSetGood? = null,
        currentIndex: Int,
        totalNum: Int
    ): Bitmap? {
        var createBitmap: Bitmap? = null
        val binding = LabelTicketPrinterBinding.inflate(LayoutInflater.from(context))
        val localeList = mutableListOf<Locale>()
        val langList = printTemplateResponseItem.getLangList()
        langList.forEach {
            localeList.add(Locale(it.uppercase()))
        }

        if (localeList.isEmpty()) {
            localeList.add(Locale("EN"))
        }

        //是否是预订单
        val isReservation = orderedInfoResponse?.isReservation == true

        binding.apply {
            val lang = localeList.first()
            tvStoreName.text =
                MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(localeList.first())
            tvStoreName.isVisible =
                printTemplateResponseItem.informationShow?.showMerchantName == true
            tvPickUpNo.text = "${orderedInfoResponse?.pickupCode}"
            if (printTemplateResponseItem.informationShow?.showOrderType == true) {
                tvPickUpNo.text = "${tvPickUpNo.text}/ "
            }
            if (orderedInfoResponse?.pickupCode.isNullOrEmpty()) {
                tvPickUpNo.text = ""
            }
            tvNumber.text = "${currentIndex}/$totalNum"
            tvDingstyle.text = "${
                if (isReservation) {
                    context.getStringByLocale(R.string.print_title_pre_order, lang)
                } else {
                    orderedInfoResponse?.diningStyle?.getPrinterDiningStyleString(
                        context,
                        lang
                    )
                }
            }"


            goods?.let {
                tvGoodName.text = it.getNameByLocal(lang)
                if (printTemplateResponseItem.informationShow?.showGoodsCode == true && !it.number.isNullOrEmpty()) {
                    tvGoodName.text = "${it.number}-${tvGoodName.text}"
                }
                if (it.isToBeWeighed() && it.isHasProcessed() && !it.isMealSet()) {
                    tvGoodName.text =
                        "${tvGoodName.text}(${it.getWeightStr()})"
                }
                tvTag.text = goods.getLocaleGoodsTagStrByTicket(lang)
                tvFeed.text = goods.getLocaleFeedTagStr(lang)
                tvPrice.text = it.getCurrentFinalLabelPrintPrice()?.priceFormatTwoDigitZero2()

            }

            orderMealSetGood?.let {
                tvGoodName.text = it.getNameByLocale(lang)
                if (printTemplateResponseItem.informationShow?.showGoodsCode == true && !it.mealSetGoodsNumber.isNullOrEmpty()) {
                    tvGoodName.text = "${it.mealSetGoodsNumber}-${tvGoodName.text}"
                }
                tvTag.text = it.getTagStr(lang)
                tvFeed.text = ""

                tvPrice.text = it.calculateSingleTotalMarkUpPrice()?.priceFormatTwoDigitZero2()
            }

            giftGoods?.let {
                tvGoodName.text = it.getNameByLocal(lang)
                if (printTemplateResponseItem.informationShow?.showGoodsCode == true && !it.number.isNullOrEmpty()) {
                    tvGoodName.text = "${it.number}-${tvGoodName.text}"
                }
                tvTag.text = ""
                tvFeed.text = ""

                tvPrice.text = it.discountPrice?.priceFormatTwoDigitZero2()
            }


            llTag.isVisible =
                tvTag.text.isNotEmpty()

            llFeed.isVisible =
                tvFeed.text.isNotEmpty()


            tvStoreName.isVisible =
                printTemplateResponseItem.informationShow?.showMerchantName == true
            tvDingstyle.isVisible = printTemplateResponseItem.informationShow?.showOrderType == true
            tvPickUpNo.isVisible = true
            tvNumber.isVisible =
                printTemplateResponseItem.informationShow?.showOrderComplete == true

            tvSlogan.text = printTemplateResponseItem.slogan
            llSlogan.isVisible =
                !printTemplateResponseItem.slogan.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showSlogan == true

            tvPayTime.text = orderedInfoResponse?.payTime
            tvPayTime.isInvisible = orderedInfoResponse?.isAfterPayStatus() != true
            tvPrice.isVisible = printTemplateResponseItem.informationShow?.showGoodsPrice == true


            tvStoreName.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            tvPickUpNo.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
            tvDingstyle.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
            tvNumber.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            tvGoodName.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
            tvTag.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            tvFeed.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            tvSlogan.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            tvPayTime.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            tvPrice.textSize = DisplayUtils.px2sp(context, 30f).toFloat()


            if (printTemplateResponseItem.informationShow?.labelTicketGoodsNameFontSize == PrinterFontSizeEnum.STANDARD.id) {
                tvGoodName.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketGoodsNameFontSize == PrinterFontSizeEnum.BIG.id) {
                tvGoodName.textSize = DisplayUtils.px2sp(context, 34f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketGoodsNameFontSize == PrinterFontSizeEnum.BIGGER.id) {
                tvGoodName.textSize = DisplayUtils.px2sp(context, 38f).toFloat()
            }


            if (printTemplateResponseItem.informationShow?.labelTicketTagNameFontSize == PrinterFontSizeEnum.STANDARD.id) {
                tvTag.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
                tvFeed.textSize = DisplayUtils.px2sp(context, 22f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketTagNameFontSize == PrinterFontSizeEnum.BIG.id) {
                tvTag.textSize = DisplayUtils.px2sp(context, 26f).toFloat()
                tvFeed.textSize = DisplayUtils.px2sp(context, 26f).toFloat()
            } else if (printTemplateResponseItem.informationShow?.labelTicketTagNameFontSize == PrinterFontSizeEnum.BIGGER.id) {
                tvTag.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
                tvFeed.textSize = DisplayUtils.px2sp(context, 30f).toFloat()
            }

            binding.root.setPadding(5, 10, 0, 12)
        }

        binding.root.requestLayout()

        binding.apply {
            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    450,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    330,
                    View.MeasureSpec.EXACTLY
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)

            createBitmap =
                createBitmap(root.measuredWidth, root.measuredHeight, Bitmap.Config.RGB_565)
            val canvas = Canvas(createBitmap!!).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)
        }
        return createBitmap
    }


}