package com.metathought.food_order.casheir.ui.ordered.printer


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.KitchenCheckTicketType
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogConfirmPrinterBinding
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PrinterTemplateHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


/**
 * <AUTHOR>
 * @date 2024/3/1920:20
 * @description
 */
@AndroidEntryPoint
class PrinterConfirmDialog : BaseDialogFragment() {

    private var binding: DialogConfirmPrinterBinding? = null
    private var confirmButtonListener: ((Boolean, Int, Boolean, Boolean) -> Unit)? =
        null

    private val viewModel: PrinterConfirmViewModel by viewModels()
    private var orderID = ""
    private var orderInfo: OrderedInfoResponse? = null

    companion object {
        private const val TAG = "PrinterConfirmDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderID: String,
            orderInfo: OrderedInfoResponse?,
            confirmButtonListener: ((Boolean, Int, Boolean, Boolean) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return

            fragment =
                newInstance(orderID, orderInfo, confirmButtonListener = confirmButtonListener)

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            orderID: String,
            orderInfo: OrderedInfoResponse?,
            confirmButtonListener: ((Boolean, Int, Boolean, Boolean) -> Unit)
        ): PrinterConfirmDialog {
            val fragment = PrinterConfirmDialog()
            fragment.confirmButtonListener = confirmButtonListener
            fragment.orderID = orderID
            fragment.orderInfo = orderInfo
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogConfirmPrinterBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.multiplePrinterState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.progressBar?.isVisible = true
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        layoutPrinter.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        tvRemindView.isVisible = it.data.multiplePrint == true
                        layoutPrinter.isVisible = true
                    }
                }
            }
        }
    }

    private fun initView() {
        viewModel.getMultiplePrinter(orderID)
        binding?.apply {

            var isShowKitchenPart = OrderHelper.getLastAddGoods(orderInfo).isNotEmpty()
            if (orderInfo?.payStatus == OrderedStatusEnum.CREDIT_UNPAID.id) {
                //如果是挂账-未支付
                tvCheckboxCashier.text = getString(R.string.checkout_bill)
                checkboxCashier.text = getString(R.string.normal_ticket)

                checkboxCashier.isSelected = false
                llCheckboxCashier.isVisible = true

                isShowKitchenPart = false

            } else if (orderInfo?.payStatus == OrderedStatusEnum.UNPAID.id) {
                checkboxCashier.isSelected = false
                llCheckboxCashier.isVisible = false
                checkboxPreSettlement.isSelected = false
                llToBePaid.isVisible = true
                if (orderInfo?.isHasNeedProcess() == true) {
                    checkboxPreSettlement.isSelected = false
                    llToBePaid.isVisible = false
                }
            } else if (orderInfo?.isOrderSuccess() == true) {
                // 挂账-未支付  不走这里
//                val temp = PrinterTemplateHelper.getCheckoutReceiptTemplate()
                val isTaxAdministrationTicket =
                    orderInfo?.isOrderSuccess() == true && MainDashboardFragment.STORE_INFO?.ticketShowTaxInfo == true && MainDashboardFragment.STORE_INFO?.isNeedInvoiceNumber == true

                tvCheckboxCashier.text = getString(R.string.checkout_bill)
                checkboxCashier.text = getString(R.string.normal_ticket)
                if (isTaxAdministrationTicket) {
                    checkboxCashier.text = getString(R.string.tax_ticket)
                }

                checkboxCashier.isSelected = false
                llCheckboxCashier.isVisible = true

                isShowKitchenPart = false


            } else if (orderInfo?.payStatus == OrderedStatusEnum.BE_CONFIRM.id) {
                //已确认的时候判断一下是否有待称重的，有待称重的不显示打预结算小票
                if (orderInfo?.isHasNeedProcess() != true) {
                    checkboxPreSettlement.isSelected = false
                    llToBePaid.isVisible = true
                }
            }
            radioPart.isVisible = isShowKitchenPart
            checkEnableConfirmButton()
        }
    }


    private fun initListener() {
        binding?.apply {
            context?.let {

            }
            topBar?.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnConfirm.setOnClickListener {
                if (orderInfo?.isPreOrder() == true) {
                    //这边判断一下如果预定时间没到不能打印
                    if (!orderInfo?.getDingTime().isNullOrEmpty()) {
                        //是否有预定模板
                        val preOrderTemplate = PrinterTemplateHelper.getPerOrderTemplate()

                        if (!PrinterTemplateHelper.isTimeToPrinterPerOrderTicket(
                                preOrderTemplate,
                                orderInfo
                            )
                        ) {
                            Toast.makeText(
                                requireContext(),
                                getString(R.string.no_time_to_print_pre_order),
                                Toast.LENGTH_LONG
                            ).show()

                            return@setOnClickListener
                        }
                    }
                }
                val kitchenType = if (radioPart.isSelected && !radioAll.isSelected) {
                    KitchenCheckTicketType.PART.id
                } else if (!radioPart.isSelected && radioAll.isSelected) {
                    KitchenCheckTicketType.ALL.id
                } else {
                    KitchenCheckTicketType.NONE.id
                }
                confirmButtonListener?.invoke(
                    checkboxCashier.isSelected,
                    kitchenType,
                    checkboxPreSettlement.isSelected,
                    radioLabelStickers.isSelected
                )

                dismissAllowingStateLoss()
            }

            checkboxCashier.setOnClickListener {
                checkboxPreSettlement.isSelected = false
                radioPart.isSelected = false
                radioAll.isSelected = false
                radioLabelStickers.isSelected = false

                checkboxCashier.isSelected = !checkboxCashier.isSelected
                checkEnableConfirmButton()
            }

            checkboxPreSettlement.setOnClickListener {
                checkboxCashier.isSelected = false
                radioPart.isSelected = false
                radioAll.isSelected = false
                radioLabelStickers.isSelected = false

                checkboxPreSettlement.isSelected = !checkboxPreSettlement.isSelected
                checkEnableConfirmButton()
            }

            radioPart.setOnClickListener {
                checkboxCashier.isSelected = false
                checkboxPreSettlement.isSelected = false
                radioLabelStickers.isSelected = false
                radioAll.isSelected = false

                radioPart.isSelected = !radioPart.isSelected
                checkEnableConfirmButton()
            }
            radioAll.setOnClickListener {
                checkboxCashier.isSelected = false
                checkboxPreSettlement.isSelected = false
                radioPart.isSelected = false
                radioLabelStickers.isSelected = false

                radioAll.isSelected = !radioAll.isSelected
                checkEnableConfirmButton()
            }

            radioLabelStickers.setOnClickListener {
                checkboxCashier.isSelected = false
                checkboxPreSettlement.isSelected = false
                radioPart.isSelected = false
                radioAll.isSelected = false


                radioLabelStickers.isSelected = !radioLabelStickers.isSelected
                checkEnableConfirmButton()
            }
        }
    }

    private fun unCheckOther() {

    }

    private fun checkEnableConfirmButton() {
        binding?.apply {
            btnConfirm.setEnableWithAlpha(checkboxCashier.isSelected || radioPart.isSelected || radioAll.isSelected || checkboxPreSettlement.isSelected || radioLabelStickers.isSelected)
        }

    }

}