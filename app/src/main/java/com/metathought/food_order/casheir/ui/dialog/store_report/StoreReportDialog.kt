package com.metathought.food_order.casheir.ui.dialog.store_report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogStoreReportBinding
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils


/**
 *<AUTHOR>
 *@time  2025/4/14
 *@desc 门店报表
 **/

class StoreReportDialog : BaseDialogFragment() {
    private var binding: DialogStoreReportBinding? = null

    companion object {
        private const val TAG = "StoreReportDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? StoreReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): StoreReportDialog {
            val args = Bundle()
            val fragment = StoreReportDialog()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogStoreReportBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initListener()
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            llSaleReport.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    SalesReportDialog.showDialog(parentFragmentManager)
//                    PrintReportDialog.showDialog(
//                        parentFragmentManager,
//                        ReportExportEnum.SALES_REPORT.id
//                    )
                }
            }
            llSaleItemReport.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    ProductReportDialog.showDialog(parentFragmentManager)
//                    PrintReportDialog.showDialog(
//                        parentFragmentManager,
//                        ReportExportEnum.PRODUCT_REPORT.id
//                    )
                }
            }

            llPaymentMethodReport.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    PayChannelReportDialog.showDialog(parentFragmentManager)
//                    PrintReportDialog.showDialog(
//                        parentFragmentManager,
//                        ReportExportEnum.OFFLINE_CHANNEL_REPORT.id
//                    )
                }
            }


        }
    }

}