package com.metathought.food_order.casheir.ui.member

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.ChartTimeType
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.RepaymentRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeTier
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.MemberListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeDetailResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.BalanceListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.MemberStatisticResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.RechargeAmountValue
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.RechargeMembersNumValue
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class MemberMainViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private var pagNo = 1
    private val pageSize = 20
    private val _uiListMemberState = MutableLiveData<UIListModel>()
    val uiListMemberState get() = _uiListMemberState
    private val _uiBalanceListState = MutableLiveData<UIBalanceListModel>()

    val uiCreditListState get() = _uiCreditListState
    private val _uiCreditListState = MutableLiveData<UICreditListModel>()
    val uiBalanceListState get() = _uiBalanceListState

    private val _statisticState = MutableLiveData<ApiResponse<MemberStatisticResponse>>()
    val statisticState get() = _statisticState

    private val _cancelTopUpState = MutableLiveData<ApiResponse<BaseBooleanResponse>>()
    val cancelTopUpState get() = _cancelTopUpState


    private val _uiCouponListState = MutableLiveData<ApiResponse<List<CouponModel>>>()
    val uiCouponListState get() = _uiCouponListState


    //充值等级列表
    private val _uiRechargeDetailState = MutableLiveData<ApiResponse<RechargeDetailResponse>>()
    val uiRechargeDetailState get() = _uiRechargeDetailState


    data class RechargeData(
        var rechargeTier: ConsumerRechargeTier?,
        var currentCouponInfo: CouponModel?,
        var couponList: List<CouponModel>? = null
    )

    //当前充值档位
    private val _uiRechargeData = MutableLiveData(RechargeData(null, null))
    val uiRechargeData get() = _uiRechargeData

    fun getMemberList(
        isRefresh: Boolean? = null,
        keyword: String? = null,
        startDate: String? = null,
        endDate: String? = null
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIListState(showLoading = true)
                }
            }
            try {
                val response = repository.getMemberList(
                    pageSize = pageSize,
                    page = pagNo,
                    userType = "1",
                    upayAccount = keyword,
                    startTime = startDate,
                    endTime = endDate
                )
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data
                        if (response.records.isNullOrEmpty()) {
                            emitUIListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUIListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIListState(showError = e.message, showLoading = false)
            }
        }
    }

    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: MemberListResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel =
            UIListModel(
                showLoading,
                showError,
                showSuccess,
                showEnd,
                isRefresh
            )
        withContext(Dispatchers.Main) {
            _uiListMemberState.value = uiModel
        }
    }

    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: MemberListResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )

    fun getMemberBalanceList(
        isRefresh: Boolean? = null,
        keyword: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: String? = null,
        exactMatch: Boolean? = false
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIBalanceListState(showLoading = true)
                }
            }
            try {
                val response = repository.getMemberBalanceList(
                    pageSize = pageSize,
                    page = pagNo,
                    type = type,
                    upayAccount = keyword,
                    startTime = startDate,
                    endTime = endDate,
                    exactMatch = exactMatch
                )
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data
                        if (response.records.isNullOrEmpty()) {
                            emitUIBalanceListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIBalanceListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUIBalanceListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIBalanceListState(showError = e.message, showLoading = false)
            }
        }
    }

    private suspend fun emitUIBalanceListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: BalanceListResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel =
            UIBalanceListModel(
                showLoading,
                showError,
                showSuccess,
                showEnd,
                isRefresh
            )
        withContext(Dispatchers.Main) {
            _uiBalanceListState.value = uiModel
        }
    }

    data class UIBalanceListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: BalanceListResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )

    fun getStatisticMember(type: String?, startDate: String?, endDate: String?) {
        _statisticState.value = ApiResponse.Loading
        viewModelScope.launch {
            val response = repository.getMemberStatistic(type, startDate, endDate)
            if (response is ApiResponse.Success) {
                when (type) {
                    ChartTimeType.QUARTER.type,
                    ChartTimeType.YEAR.type -> {
                        //会员人数 年，按月加总
                        val list = ArrayList<RechargeMembersNumValue?>()
                        response.data.memberOverviewLineChartVo?.rechargeMembersNumValueList?.groupBy {
                            //返回数据按月进行groupBy
                            it?.statisticDate?.substring(0, 2)
                        }?.mapValues {
                            // 计算每个月的 rechargeValue 总和
                                it ->
                            it.value.sumOf { it?.rechargeValue ?: 0 }
                        }?.map {
                            // 将结果转换为 Statistic 对象列表
                            list.add(RechargeMembersNumValue(it.value, it.key))
                        }
                        response.data.memberOverviewLineChartVo?.rechargeMembersNumValueList = list

                        //会员金额
                        val list2 = ArrayList<RechargeAmountValue?>()
                        response.data.memberOverviewLineChartVo?.rechargeAmountValueList?.groupBy {
                            //返回数据按月进行groupBy
                            it?.statisticDate?.substring(0, 2)
                        }?.mapValues {
                            // 计算每个月的 rechargeValue 总和
                                it ->
                            it.value.sumOf { it?.rechargeValue ?: 0 }
                        }?.map {
                            // 将结果转换为 Statistic 对象列表
                            list2.add(RechargeAmountValue(it.value, it.key))
                        }
                        response.data.memberOverviewLineChartVo?.rechargeAmountValueList = list2
                    }
                }
            }
            _statisticState.value = response
        }
    }

    fun cancelTopUp(topUpId: String) {
        _cancelTopUpState.value = ApiResponse.Loading
        viewModelScope.launch {
            _cancelTopUpState.value = repository.putCancelTopUp(topUpId)
        }
    }


    fun findTopUpCanUseCoupon(
        addNum: BigDecimal? = null,
        id: String? = null
    ) {
        viewModelScope.launch {
            try {
                uiCouponListState.value = ApiResponse.Loading
                val response = repository.findTopUpCanUseCoupon(addNum, id)
                if (response is ApiResponse.Success) {
                    uiCouponListState.value = response
                } else if (response is ApiResponse.Error) {
                    uiCouponListState.value = response
                }
            } catch (e: Exception) {
                uiCouponListState.value = ApiResponse.Error("")
            }
        }
    }


    fun getRechargeDetailPage(consumerPayAccountId: String?) {
        viewModelScope.launch {
            try {
                _uiRechargeDetailState.value = ApiResponse.Loading
                val response = repository.getRechargeDetailPage(consumerPayAccountId)
                Timber.d("response: $response")
                if (response is ApiResponse.Success) {
                    _uiRechargeDetailState.value = response
                } else if (response is ApiResponse.Error) {
                    _uiRechargeDetailState.value = response
                }
            } catch (e: Exception) {
                _uiRechargeDetailState.value = ApiResponse.Error("")
            }
        }
    }

    fun updateRechargeData(
        rechargeTier: ConsumerRechargeTier? = null,
        currentCouponInfo: CouponModel? = null,
        couponList: List<CouponModel>? = null
    ) {
        val rechargeData = _uiRechargeData.value ?: RechargeData(null, null)
        if (rechargeTier != null) {
            rechargeData.rechargeTier = rechargeTier
        }
        if (currentCouponInfo != null) {
            rechargeData.currentCouponInfo = currentCouponInfo
        }
        if (couponList != null) {
            rechargeData.couponList = couponList
        }
        _uiRechargeData.value = rechargeData
    }

    fun cleanRechargeTier() {
        val rechargeData = _uiRechargeData.value ?: RechargeData(null, null)
        rechargeData.rechargeTier = null
        _uiRechargeData.value = rechargeData
    }

    fun cleanCurrentCoupon() {
        val rechargeData = _uiRechargeData.value ?: RechargeData(null, null)
        rechargeData.currentCouponInfo = null
        _uiRechargeData.value = rechargeData
    }


    private var creditListPagNo = 1
    fun getCreditList(
        isRefresh: Boolean? = null,
        keyword: String? = null,
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                creditListPagNo = 1
                if (isRefresh == null) {
                    emitUICreditListState(showLoading = true)
                }
            }
            try {
                val response = repository.getCreditList(
                    pageSize = pageSize,
                    page = creditListPagNo,
                    keyword = keyword,
                )
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data

                        if (response.records.isNullOrEmpty()) {
                            emitUICreditListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        creditListPagNo++
                        emitUICreditListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUICreditListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUICreditListState(showError = e.message, showLoading = false)
            }
        }
    }


    private suspend fun emitUICreditListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: CreditListResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel =
            UICreditListModel(
                showLoading,
                showError,
                showSuccess,
                showEnd,
                isRefresh
            )
        withContext(Dispatchers.Main) {
            _uiCreditListState.value = uiModel
        }
    }



    data class UICreditListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: CreditListResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )



    fun printCreditOrders(
        context: Context,
        repaymentResponse: RepaymentResponse,
    ) {
        viewModelScope.launch {
            try {
                //走还款在线支付获取支付二维码
                Printer.printPrinterCreditRecordReport(
                    context,
                    OrderedStatusEnum.CREDIT_PAID,
                    repaymentResponse,
                )
            } catch (e: Exception) {

            }
        }
    }
}