package com.metathought.food_order.casheir.ui.dialog.store_report

import android.app.DatePickerDialog
import android.content.DialogInterface
import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker
import android.widget.EditText
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.ReportDateType
import com.metathought.food_order.casheir.constant.ReportFileType
import com.metathought.food_order.casheir.databinding.DialogReportDateSelectBinding
import com.metathought.food_order.casheir.databinding.DialogSelectExportTypeBinding
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.parseDate
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.ui.common.RangeTimePickerDialog
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import java.util.Date


/**
 *<AUTHOR>
 *@time  2025/4/15
 *@desc  报表时间选择
 **/

@AndroidEntryPoint
class SelectExportTypeDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "ReportDateSelectDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            onConfirmCallBack: ((type: Int) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(onConfirmCallBack)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SelectExportTypeDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            onConfirmCallBack: ((type: Int) -> Unit)? = null
        ): SelectExportTypeDialog {
            val args = Bundle()
            val fragment = SelectExportTypeDialog()
            fragment.arguments = args
            fragment.onConfirmCallBack = onConfirmCallBack
            return fragment
        }
    }

    private var binding: DialogSelectExportTypeBinding? = null

    private var onConfirmCallBack: ((type: Int) -> Unit)? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSelectExportTypeBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()

    }

    private fun initData() {

    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnYes.setOnClickListener {
                onConfirmCallBack?.invoke(if (exportFormatsRadioType.checkedRadioButtonId == R.id.radioExcel) ReportFileType.EXCEL.id else ReportFileType.PDF.id)
                dismissAllowingStateLoss()
            }

        }
    }


}