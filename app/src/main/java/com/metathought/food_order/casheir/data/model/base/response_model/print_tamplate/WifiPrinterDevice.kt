package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate

import android.hardware.usb.UsbDevice
import net.posprinter.IDeviceConnection
import net.posprinter.POSPrinter
import net.posprinter.TSPLPrinter


/**
 *<AUTHOR>
 *@time  2024/11/14
 *@desc  usb打印配置
 **/

class WifiPrinterDevice {
    /**
     *USB Device
     */
    var ipAddress: String? = null

    /**
     *USB Connection
     */
    var iDeviceConnection: IDeviceConnection? = null

    /**
     *wifi 小票打印
     */
    var pOSPrinter: POSPrinter? = null

    /**
     *wifi 标签打印
     */
    var tSPLPrinter: TSPLPrinter? = null


    var isConnect: Boolean? = false
}