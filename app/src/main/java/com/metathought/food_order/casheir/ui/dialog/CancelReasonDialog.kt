package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogCancelReasonBinding


/**
 *
 * 取消原因弹窗
 *
 * **/

class CancelReasonDialog : BaseDialogFragment() {
    private var binding: DialogCancelReasonBinding? = null

    private var reason: String? = ""

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCancelReasonBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }


    private fun initData() {

        binding?.apply {
            tvReason.text = reason

        }

    }

    private fun initListener() {
        binding?.apply {

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }

        }
    }

    companion object {
        private const val TAG = "CancelReasonDialog"

        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            reason: String?
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(reason)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment = fragmentManager.findFragmentByTag(TAG) as? CancelReasonDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            reason: String?
        ): CancelReasonDialog {
            val args = Bundle()
            val fragment = CancelReasonDialog()
            fragment.arguments = args
            fragment.reason = reason

            return fragment
        }
    }

}
