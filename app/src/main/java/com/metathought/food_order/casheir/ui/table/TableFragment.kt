package com.metathought.food_order.casheir.ui.table

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import com.alibaba.fastjson.JSON
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.constant.TableStatusEnum
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.FragmentTableBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.ui.adapter.FloorAdapter
import com.metathought.food_order.casheir.ui.adapter.TableAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.order.MenuOrderFragment
import com.metathought.food_order.casheir.ui.ordered.OrderedFragment
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.table.occupied.OccupiedTableDialog
import com.metathought.food_order.casheir.ui.table.occupied.SharedOccupiedTableDialog
import com.metathought.food_order.casheir.ui.table.reserve.AvailableTableDialog
import com.metathought.food_order.casheir.ui.table.reserve.ReserveInformationDialog
import com.metathought.food_order.casheir.ui.table.reserve.ReserveInputInfoDialog
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

//桌台fragment
@AndroidEntryPoint
class TableFragment : BaseFragment() {

    companion object {
        fun newInstance() = TableFragment()
        val TAG = TableFragment::class.java.simpleName
    }

    private var _binding: FragmentTableBinding? = null
    private val binding get() = _binding
    private val viewModel: TableViewModel by viewModels()
    private var floorAdapter: FloorAdapter? = null
    private var tableAdapter: TableAdapter? = null

    private var secondaryScreenUI: SecondaryScreenUI? = null

    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                viewModel.getTable(edtSearch.getSearchContent())
            }
        } catch (e: Exception) {

        }
    }

    private fun postSearch(duration: Int) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentTableBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onLoad() {
        super.onLoad()
        dismissProgress()
        viewModel.getTable("")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initSecondary()
        initView()
        initListener()
        initObserver()

//        if (MainDashboardFragment.CURRENT_USER?.isTableService == true)
//        initObserverSocket()
    }

    private fun initObserver() {
        context?.let { context ->
            viewModel.uiState.observe(viewLifecycleOwner) {
                it.cancelReserve?.let { res ->
                    when (res) {
                        is ApiResponse.Loading -> {
                            showProgress()
                        }

                        is ApiResponse.Error -> {
                            dismissProgress()
                            res.message?.let { it1 ->
                                if (res.errorCode == 7103) {
                                    // 检查 Fragment 是否已经添加到 Activity 并且没有被销毁
                                    if (isAdded && !isDetached && !isRemoving) {
                                        ConfirmDialog.showDialog(
                                            parentFragmentManager,
                                            content = it1,
                                            positiveButtonTitle = getString(R.string.view_ordered),
                                        ) {
                                            //如果存在未完成的订单，则跳转到订单页，查询当前桌台下的订单
                                            jumpOrdered(it.tableUuid)
                                        }
                                    } else {
                                        Timber.w("TableFragment is not in a valid state to show dialog")
                                    }
                                } else {
                                    showErrorPopup(it1)
                                }
                            }
                        }

                        is ApiResponse.Success -> {
                            dismissProgress()
                            viewModel.getTable("")
                        }
                    }
                }
                it.resultReserve?.let { res ->
                    when (res) {
                        is ApiResponse.Loading -> {
                            showProgress()

                        }

                        is ApiResponse.Error -> {
                            dismissProgress()
                            res.message?.let { it1 -> showErrorPopup(it1) }
                        }

                        is ApiResponse.Success -> {
                            dismissProgress()
                            viewModel.getTable("")
                        }
                    }
                }
                it.result?.let { res ->
                    when (res) {
                        is ApiResponse.Loading -> {
                            showProgress()

                        }

                        is ApiResponse.Error -> {
                            dismissProgress()
                            res.message?.let { it1 -> if (it1.isNotEmpty()) showErrorPopup(it1) }
                        }

                        is ApiResponse.Success -> {
                            dismissProgress()
                            binding?.apply {
                                if (!it.keyword.isNullOrEmpty() && edtSearch.getSearchContent() != it.keyword) {
                                    return@observe
                                }
                            }


                            var floorArray: ArrayList<String> = arrayListOf()
                            floorArray.add(getString(R.string.all))
                            val tableList = res.data
                            val commontable =
                                tableList.firstOrNull { it.type == 2 && it.status == TableStatusEnum.AVAILABLE.id }
                            tableList.remove(commontable)
                            commontable?.let { it1 -> tableList.add(0, it1) }
                            for (table in tableList) {
                                if (!floorArray.contains(table.location)) {
                                    floorArray.add(table.location ?: "")
                                }
                            }
                            floorAdapter = FloorAdapter(floorArray, context, 0) { floor ->
                                viewModel.filterFloor(
                                    if (floor == 0) "" else floorArray[floor],
                                    getStatus()
                                )
                            }
                            binding?.recyclerViewPage?.adapter = floorAdapter
                            dismissProgress()


                            viewModel.tableResponseData.value = tableList
                            viewModel.filteredTableResponse.value =
                                viewModel.tableResponseData.value!!.clone() as TableResponse
                            //空闲
                            val available = viewModel.filterStatus(TableStatusEnum.AVAILABLE.id)
                            //有客
                            val occupied = viewModel.filterStatus(TableStatusEnum.OCCUPIED.id)
                            //预结
                            val reserve = viewModel.filterToBePaidStatus()

                            binding?.apply {
                                radioAll.text = getString(
                                    R.string.filter_all,
                                    viewModel.tableResponseData.value!!.size
                                )
                                radioAvailable.text =
                                    getString(R.string.filter_available, available.size)
                                radioOccupied.text =
                                    getString(R.string.filter_dining, occupied.size)
                                radioReserve.text =
                                    getString(R.string.filter_reserved, reserve.size)

                                if (radioGroupFilter.checkedRadioButtonId != R.id.radioAll) {
                                    viewModel?.filterFloor("", getStatus())
                                }
                            }

                        }

                        else -> {}
                    }
                }
            }
            viewModel.filteredTableResponse.observe(viewLifecycleOwner) { it ->
                try {
                    it.sortedBy {
                        it.id
                    }.let {
                        tableAdapter?.updateItems(it)
                    }
                } catch (e: Exception) {

                }
            }
            viewModel.uiCreateTempleTableState.observe(viewLifecycleOwner) {
                binding?.apply {

                    it?.let {
                        when (it) {
                            is ApiResponse.Loading -> {
                                pbTableLoading.isVisible = true
                            }

                            is ApiResponse.Error -> {
                                pbTableLoading.isVisible = false
                                showToast(it.message ?: "")
                            }

                            is ApiResponse.Success -> {
                                pbTableLoading.isVisible = false
                            }
                        }
                    }
                }

            }
        }
        viewModel.uiSaveRecordState.observe(viewLifecycleOwner) {
            it.showLoading?.let {
                showProgress()
            }
            it.showSuccess?.let {
                dismissProgress()

                // 检查 Fragment 是否已经添加到 Activity 并且没有被销毁
                if (!isAdded || isDetached || isRemoving) {
                    Timber.w("TableFragment is not in a valid state to perform fragment transaction")
                    return@observe
                }

                // 检查 Activity 是否还存在且没有被销毁
                val activity = activity
                if (activity == null || activity.isFinishing || activity.isDestroyed) {
                    Timber.w("Activity is not in a valid state to perform fragment transaction")
                    return@observe
                }

                // 确保在主线程中执行
                activity.runOnUiThread {
                    try {
                        val currentFragment = parentFragmentManager.fragments.firstOrNull()
                        if (currentFragment is MainDashboardFragment) {
                            currentFragment.replaceFragmentFromOtherFragment(
                                FeatureMenuEnum.ORDER.id,
                                MenuOrderFragment(),
                                bundle = Bundle().apply {
                                    putBoolean("IS_TABLE", true)
                                }
                            )
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "Error in uiSaveRecordState fragment transaction")
                    }
                }
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            radioGroupFilter.setOnCheckedChangeListener { group, checkedId ->
                var floor = ""
                floorAdapter?.let {
                    floor = if (it.getCheckedIndex() == 0) "" else it.getValueCheckedIndex()
                }
                viewModel.filterFloor(floor, getStatus())
            }

            edtSearch.setTextChangedListenerCallBack {
                postSearch(500)
            }

//            edtSearch.setOnEditorActionListener { textView, i, keyEvent ->
//                if (i == EditorInfo.IME_ACTION_SEARCH) {
//                    context.hideKeyboard(edtSearch)
//                    true
//                }
//                false
//            }
        }
    }

    private fun initView() {
        binding?.apply {
            radioAll.text = getString(R.string.filter_all, 0)
            radioAvailable.text = getString(R.string.filter_available, 0)
            radioReserve.text = getString(R.string.filter_reserved, 0)
            radioOccupied.text = getString(R.string.filter_dining, 0)
            context?.let { context ->
                tableAdapter = TableAdapter(TableResponse(), context) {
                    onTableClick(it)
                }
//                SecondaryManager.showDefault(context)
//                SecondaryManagerV2.showDefault(context)
                secondaryScreenUI?.showDefault()
            }
            recyclerViewTable.adapter = tableAdapter

        }
    }

    private fun initSecondary() {
        context?.let {
            secondaryScreenUI = MyApplication.myAppInstance.orderedScreen
        }
    }


    private fun getStatus(): Int {
        binding?.apply {
            when (radioGroupFilter?.checkedRadioButtonId) {
                R.id.radioAll -> {
                    return TableStatusEnum.ALL.id
                }

                R.id.radioAvailable -> {
                    return TableStatusEnum.AVAILABLE.id
                }

                R.id.radioReserve -> {
                    return TableStatusEnum.TO_BE_PAID.id
                }

                R.id.radioOccupied -> {
                    return TableStatusEnum.OCCUPIED.id
                }
            }
        }
        return TableStatusEnum.AVAILABLE.id
    }

    private fun onTableClick(responseItem: TableResponseItem) {
        // 检查 Fragment 是否已经添加到 Activity 并且没有被销毁
        if (!isAdded || isDetached || isRemoving) {
            Timber.w("TableFragment is not in a valid state to show dialog")
            return
        }

        when (responseItem.status) {
            TableStatusEnum.OCCUPIED.id, TableStatusEnum.TO_BE_PAID.id -> {
                if (MainDashboardFragment.CURRENT_USER?.isTableService == false) {
                    //非共享
                    SharedOccupiedTableDialog.showDialog(
                        parentFragmentManager,
                        responseItem.toJson(),
                        menuButtonListener = {
                            viewModel.updateShoppingRecordForTable(responseItem)
                        },
                        viewOrderedButtonListener = {
                            jumpOrdered(responseItem.uuid)
                        },
                        printerButtonListener = {
                            context?.let {
                                viewModel.printerTicket(it, responseItem)
                            }
                        })
                } else {
                    OccupiedTableDialog.showDialog(
                        parentFragmentManager,
                        responseItem.toJson(),
                        clearTableButtonListener = {
                            ConfirmDialog.showDialog(
                                parentFragmentManager,
                                content = getString(R.string.there_are_unfinished_orders),
                                positiveButtonTitle = getString(R.string.view_ordered),
                                negativeButtonTitle = getString(R.string.cancel),
                            ) {
                                jumpOrdered(responseItem.uuid)
                            }
                        },
                        viewOrderedButtonListener = {
                            jumpOrdered(responseItem.uuid)
                        },
                        printerButtonListener = {
                            context?.let {
                                viewModel.printerTicket(it, responseItem)
                            }
                        })
                }

            }

            TableStatusEnum.AVAILABLE.id -> {
                //正常
                AvailableTableDialog.showDialog(
                    fragmentManager = parentFragmentManager,
                    responseItem.toJson(),
                    responseItem.id,
                    orderButtonListener = {
                        viewModel.updateShoppingRecordForTable(responseItem)
                    },
                    reserveButtonListener = {
                        ReserveInputInfoDialog.showDialog(
                            parentFragmentManager,
                            tableId = it
                        ) { reserveTableRequest ->
                            viewModel.reserverTable(reserveTableRequest)
                        }
                    },
                    printerButtonListener = {
                        context?.let {
                            viewModel.printerTicket(it, responseItem)
                        }
                    })
            }


            TableStatusEnum.RESERVED.id -> {
                //占桌点击
                ReserveInformationDialog.showDialog(
                    fragmentManager = parentFragmentManager,
                    responseItem.toJson(),
                    orderButtonListener = {
                        moveToMenuData(responseItem)
                    },
                    cancelButtonListener = {
                        ConfirmDialog.showDialog(
                            parentFragmentManager,
                            content = context?.getString(R.string.cancel_reservation_question)
                        ) {
                            viewModel.cancelReserveTable(
                                responseItem.id ?: "",
                                responseItem.uuid ?: ""
                            )
                        }
                    },
                    printerButtonListener = {
                        context?.let {
                            viewModel.printerTicket(it, responseItem)
                        }
                    })
            }
        }
    }

    private fun jumpOrdered(uuid: String?) {
        try {
            // 检查 Fragment 是否已经添加到 Activity 并且没有被销毁
            if (!isAdded || isDetached || isRemoving) {
                Timber.w("TableFragment is not in a valid state to perform fragment transaction")
                return
            }

            // 检查 Activity 是否还存在且没有被销毁
            val activity = activity
            if (activity == null || activity.isFinishing || activity.isDestroyed) {
                Timber.w("Activity is not in a valid state to perform fragment transaction")
                return
            }

            // 确保在主线程中执行
            activity.runOnUiThread {
                try {
                    val currentFragment = parentFragmentManager.fragments.firstOrNull()
                    if (currentFragment is MainDashboardFragment) {
                        currentFragment.replaceFragmentFromOtherFragment(
                            FeatureMenuEnum.ORDER_MANAGEMENT.id,
                            OrderedFragment(),
                            bundle = Bundle().apply {
                                putString(OrderedFragment.TABLE_UUID, uuid)
                                putBoolean(OrderedFragment.PAY_STATUS, true)
                            }
                        )
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error in jumpOrdered fragment transaction")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in jumpOrdered")
        }
    }

    private fun moveToMenuData(table: TableResponseItem) {
        val reserveTableRequest = ReserveTableRequest(
            diningTime = table.getCustomerJson()?.diningTime ?: "",
            diningNumber = table.getCustomerJson()?.diningNumber ?: 0,
            areaCode = table.getCustomerJson()?.areaCode ?: "",
            mobile = table.getCustomerJson()?.mobile ?: "",
            name = table.getCustomerJson()?.name ?: "",
            tableId = ""
        )
        viewModel.updateShoppingRecordForReserve(reserveTableRequest, table)
    }


    private fun gotoMenu() {
        try {
            // 检查 Fragment 是否已经添加到 Activity 并且没有被销毁
            if (!isAdded || isDetached || isRemoving) {
                Timber.w("TableFragment is not in a valid state to perform fragment transaction")
                return
            }

            // 检查 Activity 是否还存在且没有被销毁
            val activity = activity
            if (activity == null || activity.isFinishing || activity.isDestroyed) {
                Timber.w("Activity is not in a valid state to perform fragment transaction")
                return
            }

            // 确保在主线程中执行
            activity.runOnUiThread {
                try {
                    val currentFragment = parentFragmentManager.fragments.firstOrNull()
                    if (currentFragment is MainDashboardFragment) {
                        currentFragment.replaceFragmentFromOtherFragment(
                            FeatureMenuEnum.ORDER.id,
                            MenuOrderFragment()
                        )
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error in gotoMenu fragment transaction")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in gotoMenu")
        }
    }

    override fun onResume() {
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
//        viewModel.startLifeCycle()
//        viewModel.connectWebsocket()
        super.onResume()
    }

    override fun onPause() {
//        viewModel.destroylifeCycle()
        super.onPause()
    }

    override fun onDestroy() {
        // 清理 WebSocket 监听器以防止内存泄漏
        try {
            NetworkHelper.setWsMessageListener(null)
        } catch (e: Exception) {
            Timber.e(e, "Error clearing WebSocket listener")
        }
        super.onDestroy()
    }

//
//    private fun initObserverSocket() {
//        viewModel.liveDataRespose.observe(viewLifecycleOwner) {
//            wsHandel(it)
//        }
//    }

    private fun wsHandel(event: WebSocket.Event) {
        when (event) {
            is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
            is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
            is WebSocket.Event.OnConnectionClosing -> Timber.e(
                "closing connection.."
            )

            is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
            is WebSocket.Event.OnMessageReceived -> {
                if (event.message is Message.Text) {
                    if ((event.message as Message.Text).value == "ping")
//                        viewModel.testingWebsocketSendMessage()
                    else {
                        try {
                            Timber.e("${(event.message as Message.Text).value}")
                            val socketModel = JSON.parseObject(
                                (event.message as Message.Text).value,
                                SocketModel::class.java
                            )
                            socketModel.cmd?.let { cmd ->
                                if (cmd in (3..6) || cmd == 12 || cmd == 14) {
//                                    viewModel.getTable()
                                    postSearch(500)
                                }

                                if (cmd == WsCommand.PRINT_PRE_SETTLEMENT_STATUS_CHANGE) {
                                    postSearch(500)
                                }
//                                if (cmd == 29) {
//                                    //获取Usb 打印配置
//                                    val currentFragment =
//                                        parentFragmentManager.fragments.firstOrNull()
//                                    if (currentFragment != null) {
//                                        (currentFragment as MainDashboardFragment).getUsbPrinterInfo()
//                                    }
//                                }

                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }

            else -> {

            }
        }

    }


}