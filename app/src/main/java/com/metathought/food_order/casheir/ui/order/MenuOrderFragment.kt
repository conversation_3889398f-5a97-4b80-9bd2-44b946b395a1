package com.metathought.food_order.casheir.ui.order

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSON
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.constant.WsCommand.CART_CHANGE
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateCustomerInfoRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ReduceDiscountDetailModel
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.WsTakeOutPlatformChangeResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.database.dao.GoodsListHelper
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper
import com.metathought.food_order.casheir.databinding.FragmentMenuOrderBinding
import com.metathought.food_order.casheir.databinding.PopupDingstyleBinding
import com.metathought.food_order.casheir.databinding.PopupMenuMoreActionBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getScrollPosition
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.CouponHelper
import com.metathought.food_order.casheir.helper.DiscountActivityHelper
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.ShoppingCartHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.CLASSIFICATION_LIMIT_MAX_ERROR
import com.metathought.food_order.casheir.network.CLASSIFICATION_LIMIT_MIN_ERROR
import com.metathought.food_order.casheir.network.COUPON_LOCKED
import com.metathought.food_order.casheir.network.COUPON_USED
import com.metathought.food_order.casheir.network.CUSTOMER_INFO_REQUIRE
import com.metathought.food_order.casheir.network.GOODS_HAS_BEEN_MERGE_OR_SPLIT
import com.metathought.food_order.casheir.network.GOODS_OFF_SHELF
import com.metathought.food_order.casheir.network.GOODS_OFF_SHELF_2
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.network.TABLE_INFO_REQUIRE
import com.metathought.food_order.casheir.network.TAKE_OUT_PLATFORM_NOT_EXIST
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketGoods
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.ui.adapter.FoodCategoryAdapter
import com.metathought.food_order.casheir.ui.adapter.MenuAdapter
import com.metathought.food_order.casheir.ui.adapter.MenuOrderFoodAdapter
import com.metathought.food_order.casheir.ui.adapter.PrevoiusOrderedAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.dialog.CreditDialog
import com.metathought.food_order.casheir.ui.dialog.DiscountActivityDialog
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetail
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetailDialog
import com.metathought.food_order.casheir.ui.dialog.PackPriceDetailDialog
import com.metathought.food_order.casheir.ui.dialog.ServiceFeeDetailDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.MixedPayDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.ui.dialog.take_out.TakeOutPlatformDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.EditSingleDiscountDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountDetailDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import com.metathought.food_order.casheir.ui.dialog.tmp_good.TmpGoodDialog
import com.metathought.food_order.casheir.ui.member.balace.topup.TopUpSuccessDialog
import com.metathought.food_order.casheir.ui.member.balace.topup.TopupBalanceDialog
import com.metathought.food_order.casheir.ui.order.change_num.EditGoodNumDialog
import com.metathought.food_order.casheir.ui.order.food_detail.FoodDetailDialog
import com.metathought.food_order.casheir.ui.order.food_detail.SubDetailDialog
import com.metathought.food_order.casheir.ui.order.payment.PaymentQrDialog
import com.metathought.food_order.casheir.ui.order.payment.PaymentSuccessDialog
import com.metathought.food_order.casheir.ui.order.table_available.AvailableTableListDialog
import com.metathought.food_order.casheir.ui.ordered.OrderedFragment
import com.metathought.food_order.casheir.ui.ordered.coupon.CouponListDialog
import com.metathought.food_order.casheir.ui.ordered.coupon.IdentifyCouponDialog
import com.metathought.food_order.casheir.ui.ordered.discount.NewModifyDiscountDialog
import com.metathought.food_order.casheir.ui.ordered.note.EditRemarkDialog
import com.metathought.food_order.casheir.ui.ordered.time_price.EditTimePriceDialog
import com.metathought.food_order.casheir.ui.ordered.weight.EditWeightDialog
import com.metathought.food_order.casheir.ui.ordered.weight.MealSetWeightGoodsDialog
import com.metathought.food_order.casheir.ui.ordered.weight.WeightData
import com.metathought.food_order.casheir.ui.pending_order.PendingOrderFragmentDialog
import com.metathought.food_order.casheir.ui.pending_order.confirm_dialog.ConfirmPendingDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.table.reserve.ReserveInputInfoDialog
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import java.math.BigDecimal


class MenuOrderFragment : BaseFragment() {
    private var _binding: FragmentMenuOrderBinding? = null
    private val binding get() = _binding
    private val viewModel: MenuOrderViewModel by viewModels()
    private var menuAdapter: MenuAdapter? = null
    private var adapterCategory: FoodCategoryAdapter? = null
    private var menuOrderFoodAdapter: MenuOrderFoodAdapter? = null
    private var prevoiusOrderedAdapter: PrevoiusOrderedAdapter? = null

    private var goodTotalNum: Int = 0

    //加购前购物车的总金额
    private var finalTotalPrice: Long = 0

    //整单减免金额
    private var wholeDiscountPrice: Long = 0

    //vat
    private var totalVatPirce: Long = 0

    //vipvat
    private var totalVipVatPirce: Long = 0

    private var commissionPair: Pair<BigDecimal, Map<String, BigDecimal>>? = null

    //不带优惠券金额的 总金额
    private var finalTotalPriceWithoutCoupon: Long = 0
    private var totalVipPrice: Long = 0
    private var totalDiscountPrice: Long = 0

    /**
     * 是否有待定价商品
     */
    private var hasUnProcess: Boolean = false
    private var isOrderMore: Boolean = false
    private var takeOutPlatformModel: TakeOutPlatformModel? = null


    //    private var menuOrderScreen: MenuOrderScreenUI? = null
    private var menuOrderScreen: SecondaryScreenUI? = null

    private var orderAmountDetail = OrderAmountDetail()

    private var discountActivityList = listOf<CouponActivityModel>()

    /**
     * 是否去支付
     */
    private var isPayNow = false

    /**
     * 备注
     */
    private var remark = ""

    /**
     * 搜索
     */
    private fun postSearch() {
        binding?.apply {
            lifecycleScope.launch {
                viewModel.getGoodsReserveList(
                    viewModel.getDingingStyle()!!,
                    binding?.edtSearch?.getSearchContent() ?: "",
                    requireActivity()
                )
//                viewModel.searchQueryFlow.emit(edtSearch.getSearchContent())
            }
        }
    }

    //注释
    private val updateClassificationRunnable = Runnable {
        binding?.apply {
            val position =
                (recyclerViewMenu.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
            //优化滑动的时候不会频繁去修复分类
            if (position == lastPosition) {
                lastPosition = position
                return@Runnable
            }

            lastPosition = position

            menuAdapter?.let {
                if (position >= 0) {
                    when (val item = it.list[position]) {
                        is HeaderGoods -> {
                            adapterCategory?.let { adapterCategory ->
                                for (i in 0..<adapterCategory.categories.size) {
                                    if (adapterCategory.categories[i].id == item.id) {
                                        adapterCategory.setCheckedIndex(i)
                                        //update secondary as well
                                        menuOrderScreen?.updateCategoryAdapter(adapterCategory.categories[i])
                                        binding?.run {
                                            recyclerViewCategories.scrollToPosition(i)
                                        }
                                        break
                                    }
                                }
                            }
                        }

                        is Goods -> {
                            adapterCategory?.let { adapterCategory ->
                                for (i in 0..<adapterCategory.categories.size) {
                                    if (item.groupID == adapterCategory.categories[i].id) {
                                        adapterCategory.setCheckedIndex(i)
                                        //update secondary as well
                                        menuOrderScreen?.updateCategoryAdapter(adapterCategory.categories[i])
                                        binding?.run {
                                            recyclerViewCategories.scrollToPosition(i)
                                        }
                                        break
                                    }
                                }
                            }
                        }

                        else -> {}
                    }
                }
            }
        }
    }

    private fun postUpdateClassification(duration: Int) {
        binding?.apply {
            recyclerViewCategories.removeCallbacks(updateClassificationRunnable)
            if (duration <= 0) {
                updateClassificationRunnable.run()
            } else {
                recyclerViewCategories.postDelayed(updateClassificationRunnable, duration.toLong())
            }
        }
    }

    companion object {
        fun newInstance() = MenuOrderFragment()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initEventBus()
        initListener()
        initObserver()

        initData()
        initView()
        initAdapter()
    }

    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDestroy() {
//        cashPopDialog?.dismiss()
        try {
            PayDialog.dismissDialog(parentFragmentManager)
        } catch (e: Exception) {

        }

        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    private fun initSecondary() {
        context?.let {
//            menuOrderScreen = SecondaryManager.getMenuOrderScreen(it)
//            menuOrderScreen = SecondaryManagerV2.getMenuOrderScreen(it)

            menuOrderScreen = MyApplication.myAppInstance.orderedScreen
            menuOrderScreen?.showMenuOrder()
        }
    }

    private fun initData() {

        lifecycleScope.launch {
            PreferenceHelper.getStoreInfo()?.conversionRatio?.let {
                FoundationHelper.conversionRatio = it
            }

            PreferenceDataStoreHelper.getInstance().apply {
                val diningStyle = getFirstPreference(
                    PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_DINING_STYLE,
                    DiningStyleEnum.DINE_IN.id
                )
                val isPaymentInAdvance = MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance
                binding?.run {
                    tvPreviousOrderCount.text = getString(R.string.old_order_items, 0)
                    if (diningStyle == DiningStyleEnum.DINE_IN.id && isPaymentInAdvance != true) {
                        //后付款且堂食的情况 Pay after and dine in
                        btnPayNow.text = getString(R.string.confirmation)
                    }
                }

                resumeOrder()
            }
        }
    }

    private fun initView() {
//        val localDingingStyle = viewModel.getDingingStyle()!!
//        val shoppingRecord = ShoppingHelper.get(localDingingStyle)

    }


    @SuppressLint("SetTextI18n")
    private fun initObserver() {
        viewModel.uiLoading.observe(viewLifecycleOwner) {
            binding?.apply {
                pdLoading.isVisible = it.isLoading
            }
        }

        viewModel.paymentChannel.observe(viewLifecycleOwner) {
            binding?.apply {
                showPayDialog()
            }
        }

        viewModel.orderMoreDataResponse.observe(viewLifecycleOwner) {
            binding?.apply {

                var itemCount = 0
                it.goodsJsonList?.goodsList?.forEach {
                    it.num?.let { num ->
                        itemCount += num
                    }
                }

                prevoiusOrderedAdapter?.replaceData(
                    ArrayList(
                        it.goodsJsonList?.goodsList ?: listOf()
                    )
                )
                tvPreviousOrderCount.text = getString(R.string.old_order_items, itemCount)
                tvPricePreviousOrder.text = it.getOrderPriceNoVat(requireContext())

                //update secondary as well
                menuOrderScreen?.updateByOrderMoreData(
                    prevoiusOrderedAdapter?.list!!,
                    tvPreviousOrderCount.text.toString(),
                    tvPricePreviousOrder.text.toString()
                )
            }
        }

        viewModel.listReserveGoods.observe(viewLifecycleOwner) {
            dismissProgress()
            binding?.apply { pdMenu.isVisible = false }
            val requestDingingStyle = it.localDingingStyle!!

            binding?.apply {
                //防止列表加载出不当前tab
                if (viewModel.getDingingStyle() != requestDingingStyle) {
                    return@observe
                }
            }

            menuAdapter?.updateItems(it.list ?: ArrayList())
            binding?.recyclerViewMenu?.layoutManager?.scrollToPosition(0)

            menuOrderScreen?.updateMenuAdapterAndScrollPosition(it.list ?: ArrayList())

        }

        viewModel.listGroup.observe(viewLifecycleOwner) {
            it.apply {
                binding?.apply {
                    val requestDingingStyle = it.localDingingStyle!!

                    //防止列表加载出不当前tab
                    if (viewModel.getDingingStyle() != requestDingingStyle) {
                        return@observe
                    }
                }

                it.groupList?.firstOrNull()?.checked = true
                Timber.e("localGoodsLoading--> ${it.groupList?.size}")
                adapterCategory?.updateItems(ArrayList(it.groupList ?: listOf()))
                //update secondary
                menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)
                binding?.apply {
                    layoutMenuCategories.isVisible =
                        edtSearch.getSearchContent().isEmpty()
                    refreshLayout.finishRefresh()
                }
            }

            dismissProgress()
        }


        val isTable = arguments?.getBoolean("IS_TABLE")
        viewModel.uiState.observe(viewLifecycleOwner) { response ->

            response.goBackOrdered?.let {
                //取消加购回到订单页面
                if (isFromOrderList) {
                    val currentFragment =
                        parentFragmentManager.fragments.firstOrNull()

                    if (currentFragment is MainDashboardFragment) {
                        currentFragment.replaceFragmentFromOtherFragment(
                            FeatureMenuEnum.ORDER_MANAGEMENT.id,
                            OrderedFragment(),
                            bundle = Bundle().apply {
                                putString(OrderedFragment.LOCAL_ORDER_ID, it)
                            }
                        )
                    }
                }
            }

            response.localDingingStyle?.let {
                binding?.apply {
                    //如果请求的已经不是当前类型 后面就不处理了
                    if (it != viewModel.getDingingStyle()) {
                        Timber.e("请求的 localDingingStyle:${it}  当前的:${viewModel.getDingingStyle()}")
                        return@observe
                    }
                }

                if (it >= TakeOutPlatformToDiningHelper.BASE_INDEX) {
                    menuOrderScreen?.showDefault()
                } else {
                    menuOrderScreen?.showMenuOrder(isInit = false)
                }
                menuAdapter?.localDiningStyle = it
                menuAdapter?.notifyDataSetChanged()
//                resumeOrder()

                //update secondary as well
                menuOrderScreen?.updateMenuAdapterAndLocalDiningStyle(it)

                isOrderMore = ShoppingHelper.get(it)?.isOrderMore == true
                Timber.e("isOrderMore ===== >${isOrderMore}")
//                prevoiusOrderedAdapter?.clearData()
//                clearOrderMore()
                if (isOrderMore) {
                    viewModel.getOrderMoreProduct(requireActivity())
                    binding?.btnPending?.text = getString(R.string.cancel_add_order)
                    binding?.layoutOrderMore?.isVisible = true
                    binding?.layoutNewOrderTitle?.isVisible = true
                    binding?.btnPending?.setEnable(false)
                } else if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                    clearOrderMore()
                    val shoppingRecord = ShoppingHelper.get(it)
                    val goodsList = shoppingRecord?.getGoodsVoList()
                    if (goodsList
                            .isNullOrEmpty() && shoppingRecord?.tableUuid.isNullOrEmpty()
                        && isTable != true
                    ) {
                        viewModel.getCartInfo()
                    } else if (isTable == true) {
                        viewModel.switchTable(
                            shoppingRecord?.tableUuid,
                            shoppingRecord?.tableLabel ?: "",
                            shoppingRecord?.tableType
                        )
                    } else {
                        Timber.e("所以走的是 it=>>>>>${it}    isUniversalQr: ${shoppingRecord?.isUniversalQr()}")
                        //非通用桌才去请求，通用桌 请求菜品会返回空的导致本地清掉
                        if (shoppingRecord?.isUniversalQr() != true) {
                            viewModel.getCartInfo()
                        }
                    }
                } else {
                    clearOrderMore()
                }

                //update Secondary
                menuOrderScreen?.updateFromObserve(it)
            }

            response.hideEmptyDataList?.let {
                binding?.apply {
                    Timber.e("hideEmptyDataList  :${it}")
                    layoutEmptyFood.root.setVisibleGone(!it)
                }
            }

            response.result?.let { reserveGoodListResponse ->
                when (reserveGoodListResponse) {
                    is ApiResponse.Loading -> showProgress()
                    is ApiResponse.Error -> {
                        binding?.apply {
                            refreshLayout.finishRefresh()
                        }
//                        clearMenu()
                        dismissProgress()
                    }

                    is ApiResponse.Success -> {
                        binding?.apply {
                            refreshLayout.finishRefresh()
                        }
                        val requestDingingStyle = response.localDingingStyle!!
                        binding?.apply {
                            //防止列表加载出不当前tab
                            if (requestDingingStyle != viewModel.getDingingStyle()) {
                                Timber.e("不是请求返回的菜单 当前选择的tab 不处理  request.localDingingStyle:${requestDingingStyle}  current.localDingingStyle: ${viewModel.getDingingStyle()}")
                                return@observe
                            }
                        }

                        if (requestDingingStyle >= TakeOutPlatformToDiningHelper.BASE_INDEX) {
                            menuOrderScreen?.showDefault()
                        } else {
                            menuOrderScreen?.showMenuOrder(isInit = false)
                        }


                        reserveGoodListResponse.data?.let {
                            //获取当前语言的菜品以及分类
                            val goodsInfo =
                                it.goodsInfo[LocaleHelper.getLang(requireContext()).uppercase()]

                            //如果登录的服务费不等于菜单返回的
                            if (MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage() != goodsInfo?.store?.vatPercentage) {
                                viewModel.updateVatPercentage(goodsInfo?.store?.vatPercentage)
                            }
                            if (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != goodsInfo?.store?.serviceChargePercentage) {
                                viewModel.updateServicePercentage(goodsInfo?.store?.serviceChargePercentage)
                            }

                            viewModel.updateGoodsList(
                                requestDingingStyle,
                                it.goodsInfo.toJson(),
                                reserveGoodListResponse.data.lastChangeDate ?: ""
                            )


                            goodsInfo?.groupList?.firstOrNull()?.checked = true

                            val newListCategory =
                                goodsInfo?.groupList?.filter { group -> group.status == 1 }
                                    ?: listOf()

                            adapterCategory?.updateItems(ArrayList(newListCategory))

                            //update secondary as well
                            menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)

                            if (goodsInfo?.goodsReserveList != null) {
                                val jsonObjectGoods = Gson().fromJson<JsonObject>(
                                    goodsInfo?.goodsReserveList!!.toJson(),
                                    JsonObject::class.java
                                )
                                viewModel.parseListGood(
                                    jsonObjectGoods,
                                    newListCategory,
                                    "",
                                    requestDingingStyle,
                                )
                            }

                            binding?.apply {
                                layoutEmptyFood.root.setVisibleGone(false)
                            }

                            if (goodsInfo?.groupList.isNullOrEmpty()) {
                                binding?.apply {
                                    layoutEmptyFood.tvEmptyText.text =
                                        getString(R.string.no_result_found)
                                    layoutEmptyFood.root.setVisibleInvisible(true)
                                    menuAdapter?.updateItems(arrayListOf())
                                    adapterCategory?.updateItems(arrayListOf())

                                    //update secondary as well
                                    menuOrderScreen?.updateMenuAdapter(menuAdapter?.list)
                                    menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)
                                }
                            }
                        }
                        val goodsListRecord = GoodsListHelper.get(requestDingingStyle)
                        viewModel.getLoadGoodsList(
                            goodsListRecord,
                            requestDingingStyle,
                            "",
                        )

                        dismissProgress()
                    }

                    else -> {}
                }
            }

            response.refreshEnd?.let {
                Timber.e("response.refreshEnd。${response.refreshEnd}")
                binding?.apply {
                    refreshLayout.finishRefresh()
                }
            }

            response.shoppingRecord?.let {
                val startTime = System.nanoTime()
                //main
                binding?.apply {
                    Timber.e("当前类型:${it.diningStyle}")
                    isOrderMore = it.isOrderMore ?: false
                    takeOutPlatformModel = it.getTakeOutPlatformModel()

                    FoundationHelper.useConversionRatio = FoundationHelper.conversionRatio!!
                    FoundationHelper.isKrh = takeOutPlatformModel?.isKhr() == true
                    FoundationHelper.isTakeOut = takeOutPlatformModel != null

                    btnRemarkEnable()

                    var conversionRatio = FoundationHelper.useConversionRatio


                    val note = it.note ?: ""
                    remark = note
                    Timber.e("remark = $note")

                    if (isOrderMore) {
                        binding?.btnPending?.text = getString(R.string.cancel)
                        binding?.layoutOrderMore?.isVisible = true
                        binding?.layoutNewOrderTitle?.isVisible = true
                        binding?.btnPending?.setEnable(false)
                        llSetTimePrice.isVisible = false
                    } else if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                        clearOrderMore()
                    } else {
                        clearOrderMore()
                    }
                    var goodsVoList = it.getGoodsVoList().toList()

                    layoutEmpty.let {
                        it.tvEmptyText.text = getString(R.string.please_select_item)
                        it.root.setVisibleInvisible(goodsVoList.isEmpty())
                        it.imgError.setImageDrawable(context?.let { it1 ->
                            ContextCompat.getDrawable(
                                it1, R.drawable.ic_empty_food_order
                            )
                        })
                    }

                    recyclerOrderedFood.setVisibleInvisible(goodsVoList.isNotEmpty())
                    layoutTotal.setVisibleInvisible(goodsVoList.isNotEmpty())
                    if (recyclerOrderedFood.isVisible) {
                        flOrderedFood.isVisible = true
                    }
                    btnPayNow.setEnable(goodsVoList.isNotEmpty())

                    if (goodsVoList.isEmpty()) {
                        btnPending.text = getString(R.string.resume_order)
                    } else {
                        btnPending.text = getString(R.string.pending)
                    }
                    btnPayNow.alpha = if (goodsVoList.isNotEmpty()) 1.0f else 0.5f
                    llTmpGood.isVisible =
                        viewModel.getDingingStyle() != DiningStyleEnum.PRE_ORDER.id
                    goodsVoList =
                        viewModel.updateShopRecordByMenu(goodsVoList, menuAdapter?.list)

                    viewModel.updateLocalHashCode(goodsVoList)
                    Log.e(
                        "购物车耗时3333",
                        "Time: ${(System.nanoTime() - startTime) / 1_000_000} ms"
                    )
                    menuOrderFoodAdapter?.updateItems(goodsVoList)
                    Log.e(
                        "购物车耗时4444",
                        "Time: ${(System.nanoTime() - startTime) / 1_000_000} ms"
                    )
                    viewModel.getDingingStyle()?.let { it1 ->
                        menuOrderFoodAdapter?.updateDiningStyle(
                            it1
                        )
                        //update secondary adapter
                        menuOrderScreen?.updateMenuOrderFoodAdapter(menuOrderFoodAdapter?.list)
                        menuOrderScreen?.updateMenuOrderFoodAdapterDining(it1)
                        menuOrderScreen?.updateMenuAdapter()
                        context?.let { context ->
                            menuOrderScreen?.updateShoppingRecord(
                                it,
                                it1,
                                viewModel.orderMoreDataResponse.value
                            )
                        }
                        //update secondary
                    }

                    //set customer
                    if ((it.diningNumber ?: 0) == 0) {
                        tvCustomerInfo.text =
                            "${getString(R.string.people)}:${getString(R.string.none)}"
                    } else {
                        tvCustomerInfo.text =
                            "${getString(R.string.people)}:${it.diningNumber}"
                    }

                    Timber.e("it.tableLabel:${it.tableLabel}")
                    if (!isTakeOut()) {
                        if (it.tableLabel.isNullOrEmpty()) {
                            lifecycleScope.launch {
                                val universalTableInfo = PreferenceHelper.getUniversalTableInfo()
                                if (universalTableInfo.isNotEmpty()) {
                                    val tableInfo = Gson().fromJson(
                                        universalTableInfo,
                                        TableResponseItem::class.java
                                    )
                                    viewModel.updateSelectTable(
                                        tableInfo.uuid,
                                        tableInfo.name,
                                        tableInfo.type,
                                        viewModel.getDingingStyle()!!
                                    )
                                    Timber.e("设置为default 桌台")
                                } else {
                                    tvSelectTable.text = getString(R.string.select_table)
                                }
                            }
                        } else {
                            tvSelectTable.text = it.tableLabel
                        }
                    } else {
                        tvSelectTable.text = takeOutPlatformModel?.name
                    }

                    val totalPrice = it.totalPrice ?: 0
                    hasUnProcess = false

                    val sub =
                        (it.totalPrice ?: 0L) - (it.serviceFeeCharge ?: 0L)
//                    var vipSub = 0L
                    //商品总数
                    goodTotalNum = 0
                    //总商品vip价格(不包括服务费和打包费)
                    totalVipPrice = 0L
                    //计算商品折扣价(不包括服务费和打包费)
                    totalDiscountPrice = 0L
                    //计算总的折扣价(包括增值税,服务费和打包费)
                    var finalTotalDiscountPrice = 0L
                    //是否有可以使用vip的
                    var isHasVipPrice = false
                    //计算会员税费
                    var totalVipVatPrice = 0L
                    //计算会员服务费
                    var totalVipServiceFeePrice = 0L
                    //计算折扣后的服务费
                    var totalDiscountServiceFeePrice = 0L
                    //佣金
                    var totalCommissionPrice = 0L

                    //计算打包费
                    var totalPackPrice = 0L

                    //未称重商品数量
                    var unProcessNum = 0
                    //未称重 服务费白名单商品数量
                    var unProcessServiceWhiteGoodsNum = 0


                    val currentSelectItem = menuOrderFoodAdapter?.getSelectItem()
                    var currentSelectItemHashCode: String? = currentSelectItem?.getHash()
                    //保险判断当前选择的item 是否存在
                    var isSelectItemExit = false
                    for (goodsRequest in goodsVoList) {
                        /**
                         * 更新填空输入框
                         * **/
                        if (currentSelectItemHashCode != null && goodsRequest.getHash() == currentSelectItemHashCode) {
                            isSelectItemExit = true
                            updateAddGoodNumView(goodsRequest)
                        }

                        val isSoldOut = goodsRequest.goods?.isSoldOut()
                        if (isSoldOut == true) {
                            continue
                        }

                        /**
                         * 计算商品总数
                         */
                        goodsRequest.num?.let {
                            goodTotalNum += it
                        }

                        totalVipPrice += goodsRequest.totalVipPrice()
                        totalDiscountPrice += goodsRequest.totalDiscountPrice()


                        if (goodsRequest.goods?.isShowVipPrice() == true) {
                            isHasVipPrice = true
                        }


                        if (it.diningStyle == DiningStyleEnum.TAKE_AWAY.id) {
                            //打包费显示
                            totalPackPrice += goodsRequest.totalPackPrice()
                        } else if (it.diningStyle == DiningStyleEnum.PRE_ORDER.id || it.diningStyle == DiningStyleEnum.DINE_IN.id) {
                            //堂食预定才有服务费
                            totalVipServiceFeePrice += goodsRequest.totalVipServiceChargePrice()
                            totalDiscountServiceFeePrice += goodsRequest.totalDiscountServiceChargePrice()
                        } else if (it.diningStyle >= TakeOutPlatformToDiningHelper.BASE_INDEX) {
                            //外带也要计算打包费
                            totalPackPrice += goodsRequest.totalPackPrice()
                            //外卖计算菜品佣金
//                            totalCommissionPrice += goodsRequest.totalDiscountCommissionPrice()
                        }

                        //是否有待定价
//                        val isHasProcessed = if (goodsRequest.goods?.isMealSet() == true) {
//                            !goodsRequest.isMealSetHasCompleteWeight()
//                        } else {
//                            goodsRequest.goods?.isHasProcessed() == false
//                        }

                        if (!goodsRequest.isProcessed()) {
                            hasUnProcess = true
                            unProcessNum += 1
                            //服务费的只需要判断堂食 预定
                            if (it.diningStyle == DiningStyleEnum.PRE_ORDER.id || it.diningStyle == DiningStyleEnum.DINE_IN.id) {
                                if (goodsRequest.goods?.serviceChargeWhitelisting == true) {
                                    unProcessServiceWhiteGoodsNum += 1
                                }
                            }
                        }
                    }
                    if (!isSelectItemExit) {
                        Timber.e("所选的已经不在购物车内")
                        menuOrderFoodAdapter?.clearSelect()
                    }

                    updateActionBtnState(goodsVoList.isNullOrEmpty())

                    //总价
                    finalTotalPrice = totalPrice + totalPackPrice


                    //折扣后的总价 菜品折扣总价+服务费+打包费
                    finalTotalDiscountPrice =
                        totalDiscountPrice + totalPackPrice + totalDiscountServiceFeePrice

                    //显示的小计
                    var showSubPrice = sub
                    //显示的会员小计
                    var showVipSubPrice = totalVipPrice
                    //显示的打包费
                    var showPackPrice = totalPackPrice
//                    //显示的vat
//                    var showVatPrice = 0L
                    //显示的服务费
                    var showServiceFeePrice = it.serviceFeeCharge ?: 0


                    //会员价  菜品总价+打包费+服务费
                    totalVipPrice += totalPackPrice
                    totalVipPrice += totalVipServiceFeePrice

                    finalTotalPrice = finalTotalDiscountPrice
                    showSubPrice = totalDiscountPrice
                    showServiceFeePrice = totalDiscountServiceFeePrice

                    //如果有会员价
                    tvVipPrice.isVisible = isHasVipPrice

                    //新购物车的总计
                    val totalPriceFormatter =
                        finalTotalPrice.priceFormatTwoDigitZero2()

                    isCanSwitchTable(true)

                    layoutEmpty.imgError.isVisible = !isOrderMore

                    val totalGoodsList = goodsVoList.map {
                        it.copy()
                    }.toMutableList()

                    if (isOrderMore) {
                        val dataOrderMore = viewModel.orderMoreDataResponse.value
                        dataOrderMore?.apply {
                            dataOrderMore.conversionRatio?.let {
                                FoundationHelper.useConversionRatio = dataOrderMore.conversionRatio
                                conversionRatio = FoundationHelper.useConversionRatio
                            }

                            /**
                             * 这边的总计要把原订单的优惠券 优惠金额加回去
                             */
//                            //加购前订单不带vat的总计
                            val oldOrderTotal =
                                ordersGoodsPriceDTO?.price?.getNoCouponPayableAmountNoVatToLong()
                                    ?: 0
//                            //加购前订单不带vat的会员价总计
                            val oldOrderVipTotal =
                                ordersGoodsPriceDTO?.vipPrice?.getNoCouponPayableAmountNoVatToLong()
                                    ?: 0

                            //加购前订单的小计
                            val oldOrderSubtotal =
                                ordersGoodsPriceDTO?.price?.getSubTotalToLong() ?: 0
                            //加购前订单的会员小计
                            val oldOrderVipSubtotal =
                                ordersGoodsPriceDTO?.vipPrice?.getSubTotalToLong() ?: 0

                            //加购前订单的打包费
                            val oldOrderPackPrice =
                                ordersGoodsPriceDTO?.price?.getTotalPackingAmountToLong() ?: 0


                            Timber.e("showSubPrice :${showSubPrice}  oldOrderSubtotal:${oldOrderSubtotal}")
                            //新的会员价 加旧的会员价
                            val orderTotalVipPrice = totalVipPrice.plus(oldOrderVipTotal)
                            //新的小计加旧的小计
                            val orderTotalSubPrice = showSubPrice.plus(oldOrderSubtotal)
                            //新的会员小计加旧的会员小计
                            val orderTotalVipSubPrice = showVipSubPrice.plus(oldOrderVipSubtotal)

                            val orderTotalPrice = finalTotalPrice.plus(oldOrderTotal)
                            val orderTotalServiceFee = showServiceFeePrice.plus(
                                ordersGoodsPriceDTO?.price?.getTotalServiceChargeAmountToLong() ?: 0
                            )

                            showSubPrice = orderTotalSubPrice
                            showVipSubPrice = orderTotalVipSubPrice
                            showPackPrice += oldOrderPackPrice
                            showServiceFeePrice = orderTotalServiceFee
                            finalTotalPrice = orderTotalPrice
                            totalVipPrice = orderTotalVipPrice

                            totalVipServiceFeePrice = totalVipServiceFeePrice.plus(
                                ordersGoodsPriceDTO?.vipPrice?.getTotalServiceChargeAmountToLong()
                                    ?: 0
                            )

                            goodsJsonList?.goodsList?.forEach {
                                totalGoodsList.add(it.toGoodsRequestModel())
                                if (it.isShowVipPrice()) {
                                    isHasVipPrice = true
                                }

                                if (!it.isHasProcessed()) {
                                    hasUnProcess = true
                                    unProcessNum += 1
                                    if (it.serviceChargeWhitelisting == true) {
                                        unProcessServiceWhiteGoodsNum += 1
                                    }
                                }
                            }
                        }
                        tvVipPrice.isVisible = isHasVipPrice
                    }

                    vBottomLine.isVisible = totalGoodsList.isNotEmpty()

                    //计算减免金额前先把是否显示vip价格记下来
//                    val vipSubtotalPrice =
//                        totalVipPrice - showPackPrice - totalVipServiceFeePrice

                    //==========计算优惠活动金额相关=====================
                    //优惠活动列表
                    discountActivityList = DiscountActivityHelper.calculateAllDiscountAct(
                        totalGoodsList,
                    )
                    var discountActivityAmount = 0L
                    var discountActivityVipAmount = 0L
                    if (!discountActivityList.isNullOrEmpty()) {
                        val discountActivityUnWeightList =
                            discountActivityList.filter { it.weightMark == true }
                        if (discountActivityUnWeightList.isNullOrEmpty()) {
                            discountActivityAmount = discountActivityList.fold(0L) { acc, element ->
                                acc + (element.activityCouponAmount ?: 0)
                            }
                            discountActivityVipAmount =
                                discountActivityList.fold(0L) { acc, element ->
                                    acc + (element.activityVipCouponAmount ?: 0)
                                }

                            finalTotalPrice -= discountActivityAmount
                            totalVipPrice -= discountActivityVipAmount
                        }
                    }

                    //================================================

                    //======赠品活动显示======
                    //TODO 先注释掉
//                    val promotionActivityList =
//                        GoodsHelper.getOngoingGiftPromotionList(it.diningStyle)
//                    if (!promotionActivityList.isNullOrEmpty()) {
//                        val promotionActivity = promotionActivityList.firstOrNull()
//                        val isEffect = GiftPromotionHelper.handlePromotionEffect(
//                            totalGoodsList,
//                            promotionActivity,
//                            it.diningStyle
//                        )
//                        if (isEffect) {
//                            viewOrderGiftList.setGiftPromotionData(
//                                requireContext(),
//                                promotionActivity
//                            )
//                        }
//                        viewOrderGiftList.isVisible = isEffect
//
//                    } else {
//                        viewOrderGiftList.isVisible = false
//                    }
                    //=======================

                    //===================优惠券相关=================
                    finalTotalPriceWithoutCoupon = finalTotalPrice
                    val coupon = it.getCouponInfo()
                    val couponPair =
                        CouponHelper.getPriceAfterCoupon(
                            totalGoodsList,
                            coupon?.templateSDK,
                            it.diningStyle
                        )
                    val couponDesc = it.getCouponDesc(
                        requireContext(),
                        true,
                        isHasVipPrice,
                        hasUnProcess,
                        couponPair
                    )

                    if (coupon != null) {
                        if (!coupon.isZsCoupon()) {
                            //支付前 总计要自己再计算一遍优惠券的金额
                            totalVipPrice -= couponPair.second.first

                            if (totalVipPrice < 0L) {
                                totalVipPrice = 0
                            }

                            finalTotalPrice -= couponPair.first.first
                            if (finalTotalPrice < 0L) {
                                finalTotalPrice = 0
                            }
                            coupon.couponPrice = couponPair.first.first
                            coupon.vipCouponPrice = couponPair.second.first
                            coupon.isValid = couponPair.first.second
                            coupon.isVipValid = couponPair.second.second
                            ShoppingHelper.updateCoupon(it.diningStyle, coupon)
                        }
                    }
                    Timber.e("finalTotalPrice22222 ${finalTotalPrice}")
                    //====================================================

                    /**
                     * 加购的时候不计算vat ，也不显示
                     */
//                    var showVipVatPrice = 0L
                    if (!isOrderMore) {
                        /**
                         * 计算普通价格增值税    小计-优惠活动减免的金额+服务费/打包费-优惠券金额-折扣减免金额+VAT
                         */
                        totalVatPirce = BigDecimal(
                            finalTotalPrice.times(
                                MainDashboardFragment.CURRENT_USER?.vatPercentage ?: 0
                            ).div(100.0)
                        ).halfUp(0).toLong()
//                        showPriceWithOutVat = finalTotalPrice
                        //最后算上增值税
                        finalTotalPrice += totalVatPirce
                        finalTotalPriceWithoutCoupon += totalVatPirce
                        /**
                         * 计算会员价格增值税    小计-优惠活动减免的金额+服务费/打包费-优惠券金额-折扣减免金额+VAT
                         */
                        totalVipVatPirce = BigDecimal(
                            totalVipPrice.times(
                                MainDashboardFragment.CURRENT_USER?.vatPercentage ?: 0
                            ).div(100.0)
                        ).halfUp(0).toLong()
//                        showVipPriceWithOutVat = totalVipPrice
                        //最后算上增值税
                        totalVipPrice += totalVipVatPirce
                    } else {
                        totalVatPirce = 0L
                        totalVipVatPirce = 0L
                    }

                    wholeDiscountPrice = finalTotalPrice
                    if (!isTakeOut()) {
                        //总计
                        tvTotalPrice.text = finalTotalPrice.priceFormatTwoDigitZero2()
                        tvTotalKhrPrice.isVisible = true

                        //会员价
                        tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()
                        tvVipPrice.isVisible = isHasVipPrice

                        commissionPair = null
                    } else {
                        commissionPair = ShoppingCartHelper.calculateCommissionAmount(
                            dinningStyle = it.diningStyle,
                            goodsVoList,
                            MainDashboardFragment.CURRENT_USER?.getWholeDiscountCalculationType()!!.id
                        )

                        totalCommissionPrice = (commissionPair?.first ?: BigDecimal.ZERO).toLong()

                        Timber.e("totalCommissionPrice  ${totalCommissionPrice}")
                        //计算减去佣金后的价格
                        finalTotalPrice -= totalCommissionPrice
                        tvTotalKhrPrice.isVisible = takeOutPlatformModel?.isKhr() != true
                        //外卖的显示逻辑
                        tvTotalPrice.text = FoundationHelper.getPriceStrByUnit(
                            conversionRatio,
                            finalTotalPrice,
                            takeOutPlatformModel?.isKhr() == true
                        )
                    }

                    tvTotalKhrPrice.text =
                        "៛${
                            FoundationHelper.usdConverToKhr(
                                conversionRatio,
                                finalTotalPrice
                            )
                                .decimalFormatZeroDigit()
                        }"
                    Timber.e("购物车 totalVipPrice:${totalVipPrice}   finalTotalPrice:${finalTotalPrice}")


                    //==========
                    //金额详情
                    orderAmountDetail.subtotalPrice = showSubPrice
                    orderAmountDetail.vipSubtotalPrice = showVipSubPrice
                    orderAmountDetail.packagePrice = showPackPrice

                    orderAmountDetail.vatPrice = totalVatPirce
                    orderAmountDetail.vipVatPrice = totalVipVatPirce

                    orderAmountDetail.servicePrice = showServiceFeePrice
                    orderAmountDetail.vipServicePrice = totalVipServiceFeePrice

                    orderAmountDetail.coupon = it.getCouponInfo()
                    orderAmountDetail.couponDesc = couponDesc

                    orderAmountDetail.conversionRatio = conversionRatio

                    orderAmountDetail.totalCommissionPrice = totalCommissionPrice
                    orderAmountDetail.takeOutModel = it.getTakeOutPlatformModel()

                    orderAmountDetail.reduceType = null
                    orderAmountDetail.reduceRate = null
                    orderAmountDetail.reduceAmount = null
                    orderAmountDetail.reduceDollar = null
                    orderAmountDetail.reduceKhr = null

                    orderAmountDetail.totalPrice = finalTotalPrice
                    orderAmountDetail.totalVipPrice = totalVipPrice

                    orderAmountDetail.hasVipPrice = isHasVipPrice
                    orderAmountDetail.isNeedProcess = hasUnProcess

                    orderAmountDetail.diningStyle = it.diningStyle

                    orderAmountDetail.couponActivityModel = discountActivityList
                    //=======

                    isPayNow = false
                    val isPaymentInAdvance = MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance
                    if (it.diningStyle == DiningStyleEnum.DINE_IN.id && isPaymentInAdvance != true && !it.isUniversalQr()) {
                        //后付款且堂食的情况 Pay after and dine in
                        btnPayNow.text = getString(R.string.confirmation)
                    } else {
                        isPayNow = true
                        if (!isTakeOut()) {
                            if (goodsVoList?.filter { it.isSetSingleItemDiscount() }
                                    .isNullOrEmpty()) {
                                btnPayNow.text = getString(R.string.pay_now)
                            } else {
                                btnPayNow.text = getString(R.string.confirmation)
                            }
                        } else {
                            btnPayNow.text = getString(R.string.submit)
                        }
                    }

                    //有待称重的这里统一显示
                    if (hasUnProcess) {
                        isPayNow = false
                        btnPayNow.text = getString(R.string.submit)
                        tvTotalPrice.text = getString(R.string.to_be_confirmed)
                        tvTotalKhrPrice.isVisible = false
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                    }
                    llPending.isVisible = true
                    llChangeTakeOutPlatform.isVisible = false
                    if (isTakeOut()) {
                        //如果是外卖 功能隐藏
                        llPending.isVisible = false
                        llTmpGood.isVisible = false
                        llChangeTable.isVisible = false
                        llChangeTakeOutPlatform.isVisible = true
                    }

                    if (isOrderMore) {
                        isPayNow = false
                        //加购的时候不让选择桌台
                        isCanSwitchTable(false)
                        btnPayNow.text = getString(R.string.confirmation)
//                        layoutHeader.isVisible = true
                        layoutTotal.isVisible = true
                        btnPending.setEnable(true)
                        btnPending.text = getString(R.string.cancel_add_order)
                        btnPending.alpha = 1.0f
//                        layoutMainOrdered.setBackgroundResource(R.drawable.background_round_top_white)
                        if (goodsVoList.isNotEmpty()) {
                            recyclerPreviousOrderedFood.isVisible = false
                        }

                        tvNewOrderTotalPrice.text = totalPriceFormatter
                        if (it.isHasNeedProcess()) {
                            tvNewOrderTotalPrice.text = getString(R.string.to_be_confirmed)
                        }
                        tvOrderMoreCount.text = getString(R.string.new_order, goodTotalNum)
                    }
                    btnClearGood.isEnabled = totalGoodsList.isNotEmpty()
                    btnCreditShow(totalGoodsList.isEmpty(), it)
                }
            }


            response.paymentResponse?.let {
                when (it) {
                    is ApiResponse.Loading -> {
                        binding?.btnPayNow?.setEnable(false)
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        dismissProgress()
                        if (it.data.payType == PayTypeEnum.PAY_OTHER.id) {
                            val currentFragment =
                                parentFragmentManager.fragments.firstOrNull()
                            if (currentFragment is MainDashboardFragment) {
                                currentFragment.replaceFragmentFromOtherFragment(
                                    FeatureMenuEnum.ORDER_MANAGEMENT.id,
                                    OrderedFragment(),
                                    bundle = Bundle().apply {
                                        putString(
                                            OrderedFragment.LOCAL_ORDER_ID,
                                            it.data.orderNo
                                        )
                                        putBoolean(OrderedFragment.IS_NEED_PRINT, true)
                                        putBoolean(OrderedFragment.IS_FINISH, true)
                                    }
                                )
                            }
                            return@observe
                        }
                        if (response.reduceDiscountDetailRequest != null || !response.singleItemDiscountList.isNullOrEmpty()) {
                            //减免折扣后直接跳转
                            val currentFragment =
                                parentFragmentManager.fragments.firstOrNull()
                            if (currentFragment is MainDashboardFragment) {
                                currentFragment.replaceFragmentFromOtherFragment(
                                    FeatureMenuEnum.ORDER_MANAGEMENT.id,
                                    OrderedFragment(),
                                    bundle = Bundle().apply {
                                        putString(
                                            OrderedFragment.LOCAL_ORDER_ID,
                                            it.data.orderNo
                                        )
                                    }
                                )
                            }
                            return@observe
                        }

                        if (it.data.payType == PayTypeEnum.ONLINE_PAYMENT.id) {
                            PaymentQrDialog.showDialog(
                                parentFragmentManager,
                                it.data,
                                menuOrderScreen,
                                { res ->
                                    PaymentSuccessDialog.showDialog(
                                        parentFragmentManager,
                                        orderedInfo = res,
                                        menuOrderScreen,
                                    ) {
                                        //展示回副屏
                                        initSecondary()
                                        menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)
                                        menuOrderScreen?.updateMenuAdapter(menuAdapter?.list)
                                        viewModel.updateShoppingRecord()
                                    }
                                    //在线支付完成去打印小票
                                    context?.let { context ->
                                        viewModel.printTicket(context, res)
                                    }

                                },
                                {
                                    try {
                                        //try一下防止弹窗还没弹出时快速切换fragment 关闭弹窗这里闪退
                                        val currentFragment =
                                            parentFragmentManager.fragments.firstOrNull()
                                        if (currentFragment is MainDashboardFragment) {
                                            currentFragment.replaceFragmentFromOtherFragment(
                                                FeatureMenuEnum.ORDER_MANAGEMENT.id,
                                                OrderedFragment(),
                                                bundle = Bundle().apply {
                                                    putString(
                                                        OrderedFragment.LOCAL_ORDER_ID,
                                                        it.data.orderNo
                                                    )
                                                }
                                            )
                                        }
                                    } catch (e: Exception) {

                                    }

                                })

                        } else if (it.data.payType == PayTypeEnum.CREDIT.id) {
                            context?.let { context ->
                                viewModel.printTicketByOrderNo(context, it.data.orderNo ?: "")
                            }
                            PaymentSuccessDialog.showDialog(
                                parentFragmentManager,
                                successText = getString(R.string.credit_success)
                            )
                        } else if (it.data.payType == PayTypeEnum.CASH_PAYMENT.id) {
                            context?.let { context ->
                                viewModel.printTicketByOrderNo(context, it.data.orderNo ?: "")
                            }
                            PaymentSuccessDialog.showDialog(parentFragmentManager)
                        } else if (it.data.payType == PayTypeEnum.USER_BALANCE.id) {
                            context?.let { context ->
                                viewModel.printTicketByOrderNo(context, it.data.orderNo ?: "")
                            }
                            PaymentSuccessDialog.showDialog(parentFragmentManager)
                        } else if (it.data.payType == PayTypeEnum.PAY_AFTER.id) {
                            context?.let { context ->
                                viewModel.printTicketByOrderNo(context, it.data.orderNo ?: "")
                            }
                            context?.let { context ->
                                val currentFragment =
                                    parentFragmentManager.fragments.firstOrNull()
                                if (currentFragment is MainDashboardFragment) {
                                    currentFragment.replaceFragmentFromOtherFragment(
                                        FeatureMenuEnum.ORDER_MANAGEMENT.id,
                                        OrderedFragment(),
                                        bundle = Bundle().apply {
                                            putString(
                                                OrderedFragment.CONFIRMED_ORDER,
                                                it.data.orderNo
                                            )
                                        }
                                    )
                                }
                            }
                        }
                        binding?.btnPayNow?.setEnable(true)
                    }

                    is ApiResponse.Error -> {
                        binding?.btnPayNow?.setEnable(true)
                        dismissProgress()
                        Timber.e("it.errorCode  ${it.errorCode}")
                        paymentError(it)
                    }

                    else -> {}
                }
            }

            response.removeAll?.let {
                Timber.e("menuAdapter 刷新111111")
//                menuAdapter?.notifyDataSetChanged()

                menuAdapter?.notifyItemRangeChanged(0, menuAdapter?.list?.size ?: 0, "update")
                clearProgress()

                OrderAmountDetailDialog.dismissDialog(parentFragmentManager)
                DiscountActivityDialog.dismissDialog(parentFragmentManager)
                ServiceFeeDetailDialog.dismissDialog(parentFragmentManager)
                PackPriceDetailDialog.dismissDialog(parentFragmentManager)
                //update secondary
//                menuOrderScreen?.updateMenuAdapter()
            }
            response.goods?.let {
                menuAdapter?.updateItem(it)
//                menuAdapter?.notifyDataSetChanged()
                //update secondary
//                menuOrderScreen?.updateMenuAdapter()
            }

//            response.localGoodsLoading?.let {
//                it.apply {
//                    firstOrNull()?.checked = true
//                    Timber.e("localGoodsLoading--> ${it.size}")
//                    adapterCategory?.updateItems(ArrayList(it))
//                    //update secondary
//                    menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)
//                    binding?.apply {
//                        layoutMenuCategories.isVisible =
//                            edtSearch.getSearchContent().isEmpty()
//                        refreshLayout.finishRefresh()
//                    }
//                }
//
//                dismissProgress()
//            }
            response.errorResponse?.let {
                when (it) {
                    is ApiResponse.Error -> {
                        dismissProgress()
                        Timber.e("it.errorCode ==> ${it.errorCode}")
                        if (it.errorCode == GOODS_OFF_SHELF) {
                            showToast(getString(R.string.goods_off_shelf))
                        } else {
                            showToast(it.message ?: "")
                        }
                    }

                    else -> {}
                }
            }
            response.pendingResponse?.let {
                Timber.e("pendingResponse  ${it}")
                when (it) {
                    is ApiResponse.Loading -> showProgress()
                    is ApiResponse.Success -> {
                        clearProgress()
                        showToast(getString(R.string.pending_success))
                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
                        paymentError(it)
//                        when (it.status) {
//                            CUSTOMER_INFO_REQUIRE -> {
//                                showToast(getString(R.string.please_input_customer_information))
//                                openCustomerDialog()
//                            }
//
//                            TABLE_INFO_REQUIRE -> {
//                                showTableDialog()
//                            }
//
//                            else -> {
//                                showToast(it.message ?: "")
//                            }
//                        }
                    }

                    else -> {}
                }
            }

            response.orderMoreResponse?.let {
                when (it) {
                    is ApiResponse.Loading -> showProgress()
                    is ApiResponse.Success -> {
                        dismissProgress()
                        val currentFragment =
                            parentFragmentManager.fragments.firstOrNull()
                        if (currentFragment is MainDashboardFragment) {
                            currentFragment.replaceFragmentFromOtherFragment(
                                FeatureMenuEnum.ORDER_MANAGEMENT.id,
                                OrderedFragment(),
                                bundle = Bundle().apply {
                                    putString(OrderedFragment.LOCAL_ORDER_ID, it.data.orderNo)
//                                    putParcelableArrayList(
//                                        OrderedFragment.ORDER_MORE,
//                                        it.data.currentOrderMore
//                                    )
                                }
                            )
                        }
                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
                        paymentError(it)
                    }

                    else -> {}
                }
            }

        }
    }

    //清空菜单
    private fun clearMenu() {
        adapterCategory?.updateItems(arrayListOf())
        menuAdapter?.updateItems(arrayListOf())
        //update secondary as well
        menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)
        menuOrderScreen?.updateMenuAdapter(menuAdapter?.list)
    }


    private fun paymentError(it: ApiResponse.Error) {
//        when (it.status) {
//            CUSTOMER_INFO_REQUIRE -> {
//                showToast(getString(R.string.please_input_customer_information))
//                openCustomerDialog()
//            }
//
//            TABLE_INFO_REQUIRE -> {
//                showTableDialog()
//            }
//
//            else -> {
//                showToast(it.message ?: "")
//            }
//        }


        when (it.status) {

            CUSTOMER_INFO_REQUIRE -> {
                showToast(getString(R.string.please_input_customer_information))
                openCustomerDialog()
            }

            TABLE_INFO_REQUIRE -> {

                showTableDialog()
            }

            else -> {
                when (it.errorCode) {
                    CLASSIFICATION_LIMIT_MIN_ERROR, CLASSIFICATION_LIMIT_MAX_ERROR -> {
                        try {
                            //去掉提示后面带的商品id
                            val index =
                                it.message?.indexOfFirst { char -> char == '|' }
                                    ?: -1
                            if (index != -1) {
                                val msg = it.message?.substring(0, index)
                                val groupId = it.message?.substring(index + 1)
                                showToast(msg ?: "")
                                jumpToCategorizeAndHighLight(groupId)
                            } else {
                                showToast(it.message ?: "")
                            }
                        } catch (e: Exception) {
                            showToast(it.message ?: "")
                        }
                    }

                    GOODS_OFF_SHELF_2 -> {
                        try {
                            //去掉提示后面带的商品id
                            val index =
                                it.message?.indexOfFirst { char -> char == '|' }
                                    ?: -1
                            if (index != -1) {
                                val msg = it.message?.substring(0, index)
                                showToast(msg ?: "")
                            } else {
                                showToast(it.message ?: "")
                            }
                        } catch (e: Exception) {
                            showToast(it.message ?: "")
                        }
                    }

                    COUPON_USED, COUPON_LOCKED -> {
                        viewModel.getDingingStyle()?.apply {
                            viewModel.clearCoupon(this)
                        }

                        showToast(it.message ?: "")
                    }

                    TAKE_OUT_PLATFORM_NOT_EXIST -> {
                        changeToDineIn()
                        showToast(it.message ?: "")
                    }

                    else -> {
                        showToast(it.message ?: "")
                    }
                }

            }
        }


    }

    override fun onLoad() {
        super.onLoad()
        binding?.apply { pdMenu.isVisible = true }
        viewModel.getGoodsReserveList(
            viewModel.getDingingStyle() ?: DiningStyleEnum.DINE_IN.id,
            binding?.edtSearch?.getSearchContent() ?: "",
            requireActivity()
        )
    }

//    private fun Int.getPayTypeByRadioId(): DiningStyleEnum {
//        return when (this) {
//            R.id.radioDineIn -> DiningStyleEnum.DINE_IN
//            R.id.radioTakeAway -> DiningStyleEnum.TAKE_AWAY
//            R.id.radioReserve -> DiningStyleEnum.PRE_ORDER
//            else -> DiningStyleEnum.DINE_IN
//        }
//    }

    private fun updateDingStyleView(dingStyleEnum: Int) {
        binding?.apply {
            when (dingStyleEnum) {
                DiningStyleEnum.DINE_IN.id -> {
                    tvDingStyle.text = getString(R.string.dine_in)
                }

                DiningStyleEnum.TAKE_AWAY.id -> {
                    tvDingStyle.text = getString(R.string.take_away)
                }

                DiningStyleEnum.PRE_ORDER.id -> {
                    tvDingStyle.text = getString(R.string.pre_order)
                }

                else -> {
                    tvDingStyle.text = getString(R.string.take_out)
                }
            }
        }
    }


    private fun initListener() {
        binding?.apply {

            refreshLayout.setEnableRefresh(true)
            refreshLayout.setEnableLoadMore(false)
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    postSearch()
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                }

            })

            btnChangeTakeOutPlatform.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val shoppingRecord = ShoppingHelper.getDefault(viewModel.getDingingStyle()!!)
                    TakeOutPlatformDialog.showDialog(
                        parentFragmentManager,
                        shoppingRecord.getTakeOutPlatformModel(),
                        shoppingRecord.takeOutOrderId
                    ) { takeOutPlatformModel, dinStyle, takeOutOrderId ->

                        //旧的购物车信息
                        val oldShoppingRecord =
                            ShoppingHelper.getDefault(viewModel.getDingingStyle()!!)
                        changeToTakeOutPlatform(
                            takeOutPlatformModel,
                            dinStyle,
                            takeOutOrderId,
                            oldShoppingRecord
                        )
                    }
                }
            }

            btnMore.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    btnMore.isSelected = true
                    showPopupWindowMoreAction(btnMore)
                }
            }

//            btnSingleDiscount.setOnClickListener {
//                SingleClickUtils.isFastDoubleClick {
//                    val shareRecord = ShoppingHelper.get(viewModel.getDingingStyle()!!)
//                    if (shareRecord?.tableUuid.isNullOrEmpty()) {
//                        paymentError(
//                            ApiResponse.Error(
//                                "Please select a table number",
//                                TABLE_INFO_REQUIRE
//                            )
//                        )
//                        return@isFastDoubleClick
//                    }
//
//                    val singleDiscountData =
//                        ShoppingHelper.get(viewModel.getDingingStyle() ?: 0)?.getGoodsVoList()
//                            ?: emptyList()
//                    Timber.d("singleDiscountData: ${singleDiscountData.toJson()}")
//                    val singleDiscountGoods =
//                        singleDiscountData
//                            .filter { it.goods?.goodsType != GoodTypeEnum.TEMPORARY.id }
//                            .map {
//                                SingleDiscountGoods(
//                                    name = it.goods?.name,
//                                    num = it.num,
//                                    totalPrice = it.totalDiscountPrice(),
//                                    vipPrice = it.totalVipPrice(),
//                                    isShowVipPrice = it.goods?.isShowVipPrice() ?: false,
//                                    goodsId = it.goods?.id,
//                                    goodsHashKey = HashHelper.getHash(
//                                        it.feedInfoList,
//                                        it.goodsTagItems,
//                                        it.goods?.id!!
//                                    ),
//                                )
//                            }
//                    if (singleDiscountGoods.isEmpty()) {
//                        showToast(getString(R.string.no_good_can_set_single_discount))
//                        return@isFastDoubleClick
//                    }
//                    SingleDiscountDialog.showDialog(
//                        parentFragmentManager,
//                        SingleDiscountData(null, singleDiscountGoods)
//                    ) { data, remark ->
//                        viewModel.payment(
//                            PayTypeEnum.ONLINE_PAYMENT,
//                            singleReduceReason = remark,
//                            singleItemDiscountList = data?.goods?.map {
//                                SingleDiscount(
//                                    type = it.type,
//                                    goodsId = it.goodsId,
//                                    goodsHashKey = it.goodsHashKey,
//                                    reduceRatio = it.reduceRatio,
//                                    saleReduce = it.saleReduce,
//                                    vipReduce = it.vipReduce,
//                                )
//                            }
//                        )
//                        SingleDiscountDialog.dismissDialog(parentFragmentManager)
//                    }
//                }
//            }
//
//
//            btnWholeDiscount.setOnClickListener {
//                val shareRecord = ShoppingHelper.get(viewModel.getDingingStyle()!!)
//                if (shareRecord?.tableUuid.isNullOrEmpty()) {
//                    paymentError(
//                        ApiResponse.Error(
//                            "Please select a table number",
//                            TABLE_INFO_REQUIRE
//                        )
//                    )
//                    return@setOnClickListener
//                }
//                val model = ReduceDiscountDetailModel()
//                //美元转瑞尔汇率
//                model.conversionRatio = null
//
//                //	减免折扣率价格
//                model.reduceAmount = null
//                //	销售价：减免金额（美元）
//                model.reduceDollar = null
//                //销售价：减免金额（瑞尔）
//                model.reduceKhr = null
//                //销售价：减免百分比
//                model.reduceRate = null
//                //减免后实际金额
//                model.reduceRealPrice = null
//                //减免后实际金额(瑞尔)
//                model.reduceRealPriceKhr = null
//
//                //会员价：减免折扣率价格
//                model.reduceVipAmount = null
//                //会员价：减免金额（美元）
//                model.reduceVipDollar = null
//                //会员价：减免金额（瑞尔）
//                model.reduceVipKhr = null
//                //会员价：减免百分比
//                model.reduceVipRate = null
//                //会员价：减免后实际金额
//                model.reduceVipRealPrice = null
//                //会员价：减免后实际金额(瑞尔)
//                model.reduceVipRealPriceKhr = null
//
//                //销售价：订单实付总金额
//                model.totalAmount = finalTotalPrice
//                //销售价：订单实付总金额（瑞尔）
//                model.totalAmountKhr = null
//                //会员价：订单实付总金额，如果有值则需展示会员价tab
//                model.totalVipAmount = totalVipPrice
//                //会员价：订单实付总金额（瑞尔）
//                model.totalVipAmountKhr = null
//                binding?.apply {
//                    //是否有会员价tab
//                    model.vipTabFlag = tvVipPrice.isVisible
//                }
//
//                //当前设置的类型
//                model.type = null
//
//                Timber.e(model.toString())
//
//                ModifyDiscountDialog.showDialog(
//                    parentFragmentManager,
//                    null,
//                    model,
//                    { reduceType, reduceRate, reduceDollar, reduceKhr ->
//                        Timber.e("刷新订单")
//
//                        viewModel.payment(
//                            PayTypeEnum.ONLINE_PAYMENT,
//                            reduceDiscountDetailRequest = ReduceDiscountDetailRequest(
//                                type = reduceType,
//                                reduceKhr = reduceKhr,
//                                reduceDollar = reduceDollar,
//                                reduceRate = reduceRate
//                            )
//                        )
//                    }, {})
//            }
            btnWeight.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val item = menuOrderFoodAdapter?.getSelectItem()
                    if (item != null) {
                        //判断是套餐还是商品
                        if (item.orderMealSetGoodList.isNullOrEmpty()) {
                            EditWeightDialog(requireContext()).showDialog(
                                WeightData(
                                    weight = item.goods?.weight,
                                    weightUnit = item.goods?.getWeightUnit(),
                                    singleDiscount = item.goods?.getCalculateDiscountPrice(),
                                )
                            ) { weight ->
                                if (weight.toDoubleOrNull() != item.goods?.weight || item.isSetSingleItemDiscount()) {
                                    viewModel.confirmGoodsWeight(
                                        requireContext(),
                                        item,
                                        weight = weight
                                    )
                                }
                            }
                        } else {
                            //套餐子商品选择弹窗
                            MealSetWeightGoodsDialog.showDialog(
                                parentFragmentManager,
                                item.orderMealSetGoodList,
                            ) { orderGoods ->
                                viewModel.confirmGoodsWeight(
                                    requireContext(),
                                    item,
                                    orderGoods = orderGoods,
                                )
                            }
                        }
                    }
                }
            }

            btnAdd.setOnClickListener {
//                SingleClickUtils.isFastDoubleClick(200) {
                val item = menuOrderFoodAdapter?.getSelectItem()
                if (item != null) {
                    viewModel.plus(
                        item.goods!!,
                        feedList = item.feedInfoList,
                        goodsTagItemList = item.goodsTagItems,
                        orderMealSetGoodList = item.orderMealSetGoodList,
                        singleDiscountGoods = item.singleDiscountGoods,
                        note = item.note,
                        goodsReq = item,
                    )
                }
//                }
            }

            btnReduce.setOnClickListener {
                //sub

//                SingleClickUtils.isFastDoubleClick(200) {
                val item = menuOrderFoodAdapter?.getSelectItem()
                if (item != null) {
                    viewModel.sub(
                        item.goods!!,
                        feedList = item.feedInfoList,
                        goodsTagItemList = item.goodsTagItems,
                        orderMealSetGoodList = item.orderMealSetGoodList,
                        singleDiscountGoods = item.singleDiscountGoods,
                        note = item.note
                    )
                }
//                }
            }

            tvGoodNum.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val selectGood = menuOrderFoodAdapter?.getSelectItem()
                    //称重菜不可以直接编辑数量
                    if (selectGood != null && selectGood.goods?.isToBeWeighed() != true) {
                        modifyGoodNum(selectGood)
                    }
                }
            }

            btnRemark.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    EditRemarkDialog.showDialog(
                        parentFragmentManager,
                        cartGood = menuOrderFoodAdapter?.getSelectItem(),
                        remark = if (menuOrderFoodAdapter?.getSelectItem() == null) remark else menuOrderFoodAdapter?.getSelectItem()?.note,
                        updateCartCallBackListener = { note, cartGood ->
                            if (cartGood == null) {
                                //设置整单备注
                                remark = note
                                binding?.apply {
                                    ShoppingHelper.updateNote(
                                        note,
                                        viewModel.getDingingStyle()
                                    )
                                }
                            } else {
                                //设置菜品备注
                                viewModel.updateGoodsRemark(cartGood, note)
                            }
                        }
                    )
                }
            }

            layoutOrderMore.setOnClickListener {
                val isNullOrEmpty = ShoppingHelper.get(0)?.getGoodsVoList().isNullOrEmpty()
                if (recyclerPreviousOrderedFood.isVisible) {
                    arrowNewOrder.rotation = 180f
                    arrowOldOrder.rotation = 0f
                    recyclerOrderedFood.isVisible = true
                    recyclerPreviousOrderedFood.isVisible = false
                    layoutEmpty.root.isVisible = isNullOrEmpty
//                    vBottomLine.isVisible = !isNullOrEmpty
                } else {
                    layoutEmpty.root.isVisible = false
                    arrowNewOrder.rotation = 0f
                    arrowOldOrder.rotation = 180f
                    recyclerOrderedFood.isVisible = false
                    recyclerPreviousOrderedFood.isVisible = true
//                    vBottomLine.isVisible = true
                }
                flOrderedFood.isVisible =
                    recyclerOrderedFood.isVisible || layoutEmpty.root.isVisible
                //update secondary
                menuOrderScreen?.setListenerOrderMore(isNullOrEmpty)
            }

            layoutNewOrderTitle.setOnClickListener {
                val isNullOrEmpty =
                    ShoppingHelper.get(viewModel.getDingingStyle() ?: 0)?.getGoodsVoList()
                        .isNullOrEmpty()

                layoutEmpty.root.isVisible = isNullOrEmpty
//                vBottomLine.isVisible = !isNullOrEmpty
                recyclerOrderedFood.isVisible = true
                recyclerPreviousOrderedFood.isVisible = false
                //update secondary
                menuOrderScreen?.setListenerOrderTitle(isNullOrEmpty)
                flOrderedFood.isVisible =
                    recyclerOrderedFood.isVisible || layoutEmpty.root.isVisible
            }

            recyclerOrderedFood.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    recyclerView.layoutManager?.getScrollPosition(dy)?.let {
                        if (it != -1) {
                            menuOrderScreen?.updateRecyclerOrderedFood(it)
                        }
                    }


                }
            })

            recyclerPreviousOrderedFood.addOnScrollListener(object :
                RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    recyclerView.layoutManager?.getScrollPosition(dy)?.let {
                        if (it != -1) {
                            menuOrderScreen?.updateRecyclerPreviousOrderedFood(it)
                        }
                    }
                }
            })

            recyclerViewMenu.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    recyclerView.layoutManager?.getScrollPosition(dy)?.let {
                        if (it != -1) {
                            menuOrderScreen?.updateRecyclerMenu(it)
                        }
                    }
                }

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    when (newState) {
                        RecyclerView.SCROLL_STATE_DRAGGING -> {
                            // 用户开始拖动，暂停Glide加载
//                            Glide.with(requireContext()).pauseRequests()
                        }

                        RecyclerView.SCROLL_STATE_IDLE -> {
                            // 滚动停止，恢复加载
//                            Glide.with(requireContext()).resumeRequests()
                        }
                    }
                }
            })

            recyclerViewCategories.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    recyclerView.layoutManager?.getScrollPosition(dy)?.let {
                        if (it != -1) {
                            menuOrderScreen?.updateRecyclerViewCategories(it)
                        }
                    }
                }
            })

            btnChangeTable.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    showTableDialog()
                }
            }

            tvCustomerInfo.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    openCustomerDialog()
                }
            }

            btnClearGood.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    Timber.e("goodTotalNum ${goodTotalNum}")
                    val goodsNum = (menuOrderFoodAdapter?.list?.size ?: 0)
                    if (goodsNum > 0) {
                        ConfirmDialog.showDialog(
                            parentFragmentManager,
                            content = getString(R.string.remove_num_items, goodsNum),
                            positiveButtonTitle = getString(R.string.remove),
                            negativeButtonTitle = getString(R.string.no),
                        ) {
                            viewModel.removeAllGoods()
                        }
                    }
                }
            }

            context?.let {
                menuOrderFoodAdapter = MenuOrderFoodAdapter(arrayListOf(), it, { plus ->
                    //plus
//                    viewModel.plus(
//                        plus.goods!!,
//                        feedList = plus.feedInfoList,
//                        goodsTagItemList = plus.goodsTagItems,
//                        orderMealSetGoodList = plus.orderMealSetGoodList,
//                        singleDiscountGoods = plus.singleDiscountGoods,
//                        note = plus.note,
//                    goodsReq = plus
//                    )
                }, { sub ->
                    //sub
//                    viewModel.sub(
//                        sub.goods!!,
//                        feedList = sub.feedInfoList,
//                        goodsTagItemList = sub.goodsTagItems,
//                        orderMealSetGoodList = sub.orderMealSetGoodList,
//                        singleDiscountGoods = sub.singleDiscountGoods,
//                        note = sub.note,
//                    )
                }, { good ->
//                    val diningStyle = viewModel.getDingingStyle()!!
//                    val record = GoodsHelper.get(good.goods!!.id, viewModel.getDingingStyle()!!)
//                    var maxNum = GOOD_MAX_NUM
//                    if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
//                        //预定数量限制
//                        maxNum = (good.goods?.restrictNum
//                            ?: 0)
//                    }
//                    //剩余最多可加数量
//                    var remainingNum = maxNum - (record?.num ?: 0)
//                    val shoppingRecord = ShoppingHelper.get(diningStyle)
//                    shoppingRecord?.getGoodsVoList()?.forEach {
//                        if (HashHelper.getHash(
//                                it.feedInfoList,
//                                it.goodsTagItems,
//                                it.goods!!.id
//                            ) == HashHelper.getHash(
//                                good.feedInfoList,
//                                good.goodsTagItems,
//                                it.goods!!.id
//                            )
//                        ) {
//                            remainingNum += it.num ?: 0
//                        }
//                    }
//                    EditGoodNumDialog(requireContext()).showDialog(
//                        good.num ?: 0,
//                        remainingNum,
//                        false
//                    ) { num ->
//                        viewModel.updateGoodsNum(good, num)
//                    }
                }, onSelectCallback = { selectItem ->
                    updateAddGoodNumView(selectItem)
                }, onWarnClick = { view, item ->
//                    val singleReduceReason = item.singleDiscountGoods?.remark
                    SingleDiscountDetailDialog.showDialog(
                        parentFragmentManager,
                        cartGood = item
                    )
//                    if (!singleReduceReason.isNullOrEmpty()) {
//                        XPopup.Builder(requireContext())
//                            .hasShadowBg(false)
//                            .isTouchThrough(true)
//                            .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
//                            .atView(view)
//                            .isCenterHorizontal(true)
//                            .hasShadowBg(false) // 去掉半透明背景
//                            .asCustom(
//                                CustomBubbleAttachPopup(
//                                    requireContext(),
//                                    singleReduceReason
//                                )
//                            )
//                            .show()
//                    }
                })
                recyclerOrderedFood.adapter = menuOrderFoodAdapter

                //recyclerOrderedFood.addItemDecoration(StickyHeaderItemDecoration(recyclerOrderedFood, adapter)

                prevoiusOrderedAdapter = PrevoiusOrderedAdapter()
                recyclerPreviousOrderedFood.adapter = prevoiusOrderedAdapter

            }

            tvDingStyle.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    activity?.hideKeyboard(edtSearch.getEditText()!!)
                    showPopupWindowDingStyle(tvDingStyle)
                }
            }


            btnPayNow.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    //防止服务端炸了这个 是null 闪退
                    if (viewModel.getDingingStyle() == null) {
                        return@isFastDoubleClick
                    }

//                    val payNow = isPayNow
                    Timber.e("payNow  $isPayNow")
                    if (!isPayNow && isOrderMore) {
                        viewModel.orderMore(requireContext(), remark)
                        return@isFastDoubleClick
                    }
                    onPay()
                }
            }

            edtSearch.setTextChangedListenerCallBack {
                postSearch()

            }

            btnPending.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val localDingingStyle = viewModel.getDingingStyle()

                    if (menuOrderFoodAdapter?.list.isNullOrEmpty() && !isOrderMore) {
                        PendingOrderFragmentDialog.showDialog(parentFragmentManager, {
                            binding?.apply {
                                updateDingStyleView(it)
                                viewModel.getGoodsReserveList(
                                    viewModel.getDingingStyle()!!,
                                    "",
                                    requireActivity()
                                )
                                binding?.apply {
                                    if (!edtSearch.getSearchContent().isNullOrEmpty()) {
                                        edtSearch.setSearchContent("")
                                    }
                                }
                            }
                        }, {
                            binding?.apply {
                                updateDingStyleView(it)
                                viewModel.getGoodsReserveList(
                                    viewModel.getDingingStyle()!!,
                                    "",
                                    requireActivity()
                                )
                                binding?.apply {
                                    if (!edtSearch.getSearchContent().isNullOrEmpty()) {
                                        edtSearch.setSearchContent("")
                                    }
                                }
                            }
                        })

                    } else {
                        if (isOrderMore) {//取消加购
                            viewModel.removeOrderMore()
                            clearOrderMore()
                        } else {
                            ConfirmPendingDialog.showDialog(
                                activity?.supportFragmentManager ?: parentFragmentManager,
                                localDingingStyle, finalTotalPriceWithoutCoupon
                            ) { seriel, note ->
                                viewModel.addFoodToPending(serialNumber = seriel, note = note)
                            }

                        }
                    }
                }
            }

            btnCredit.setOnClickListener {
                if (hasUnProcess) {
                    showToast(getString(R.string.order_has_no_price_good))
                    return@setOnClickListener
                }

                val localDiningStyle = viewModel.getDingingStyle()
                var accountInfo: CreditDialog.AccountInfo? = null
                if (localDiningStyle != null) {
                    val shareRecord = ShoppingHelper.get(localDiningStyle)
                    val name = shareRecord?.name
                    val mobile = shareRecord?.mobile
                    val areaCode = shareRecord?.areaCode
                    accountInfo = CreditDialog.AccountInfo(
                        accountId = null,
                        nickName = name,
                        areaCode = areaCode,
                        telephone = mobile,
                    )
                }
                CreditDialog.showDialog(
                    parentFragmentManager,
                    finalTotalPrice,
                    accountInfo
                ) { aInfo ->
                    viewModel.payment(
                        PayTypeEnum.CREDIT,
                        accountId = aInfo.accountId,
                        nickName = aInfo.nickName,
                        telephone = "${aInfo.areaCode}${aInfo.telephone}",
                        reason = aInfo.reason
                    )
                }
            }
//            btnCoupon.setOnClickListener {
//                val localDingingStyle = viewModel.localDingingStyle!!
//                val shareRecord = ShoppingHelper.get(localDingingStyle)
//                val goodsBoList = viewModel.getGoodsBoList(shareRecord)
//                val couponInfo = shareRecord?.getCouponInfo()
//                if (couponInfo == null) {
//                    IdentifyCouponDialog.showDialog(
//                        activity?.supportFragmentManager ?: parentFragmentManager,
//                        diningStyle = localDingingStyle,
//                        goodsList = goodsBoList,
//                        isPreOrder = localDingingStyle == DiningStyleEnum.PRE_ORDER.id,
//                        tableUuid = shareRecord?.tableUuid
//                    ) {
//                        viewModel.updateCouponInfo(it)
//                    }
//                } else {
//                    //如果有选优惠券传进去展示
//                    binding?.apply {
//                        CouponListDialog.showDialog(
//                            activity?.supportFragmentManager ?: parentFragmentManager,
//                            diningStyle = localDingingStyle,
//                            goodsList = goodsBoList,
//                            isPreOrder = localDingingStyle == DiningStyleEnum.PRE_ORDER.id,
//                            tableUuid = shareRecord.tableUuid,
//                            currentCouponId = shareRecord.getCouponInfo()?.id,
//                            localCouponList = listOf(couponInfo),
//                            isShowVip = tvVipPrice.isVisible,
//                            isHasUnWeight = tvTotalPrice.text == getString(R.string.to_be_weighed)
//                        ) {
//                            viewModel.updateCouponInfo(it)
//                        }
//                    }
//                }
//            }

            layoutPrice.setOnClickListener {
                if (!btnTotalPriceDetail.isVisible) {
                    return@setOnClickListener
                }
                SingleClickUtils.isFastDoubleClick {
                    val list =
                        (ShoppingHelper.get(viewModel.getDingingStyle()!!)?.getGoodsVoList()?.map {
                            it.goods?.totalCount = it.num
                            it.goods?.feedStr = it.getGoodsTagStr()
                            it.goods?.totalServiceCharge = it.totalServiceChargePrice()
                            it.goods?.totalVipServiceCharge = it.totalVipServiceChargePrice()
                            it.goods?.totalDiscountServiceCharge =
                                it.totalDiscountServiceChargePrice()
//                            it.goods?.totalCommission = it.totalDiscountCommissionPrice()
                            it.goods?.goodHashCode = it.getHash()
                            it.goods?.orderMealSetGoodsDTOList = it.orderMealSetGoodList
                            commissionPair?.let { pair ->
                                pair.second.forEach { value ->
                                    if (value.key == it.goods?.goodHashCode) {
                                        it.goods?.totalCommission = value.value.toLong()
                                    }
                                }
                            }

                            it.goods
                        } ?: listOf()).toMutableList()
                    Timber.e("btnPackPriceCue list: ${list.size}")

                    prevoiusOrderedAdapter?.list?.forEach { value ->
                        val index = list.indexOfFirst {
                            it?.goodHashCode == value.hashKey
                        }
                        if (index == -1) {
                            list.add(ShoppingCartHelper.cartGoodsToGoods(value))
                        } else {
                            list[index]?.totalCount =
                                (list[index]?.totalCount ?: 0) + (value.num ?: 0)
                        }
                    }

                    if (list.isNullOrEmpty()) {
                        return@isFastDoubleClick
                    }

                    orderAmountDetail.goods = list

                    OrderAmountDetailDialog.showDialog(
                        parentFragmentManager, orderAmountDetail = orderAmountDetail
                    )
                }
            }

            btnTmpGood.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    TmpGoodDialog.showDialog(parentFragmentManager) {
                        viewModel.addTmpGoodToCart(it) {
                            TmpGoodDialog.dismissDialog(parentFragmentManager)
                        }
                    }
                }
            }

            btnPrint.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (hasUnProcess) {
                        showToast(getString(R.string.order_has_no_price_good))
                        return@isFastDoubleClick
                    }
                    lifecycleScope.launch {
                        val localDiningStyle = viewModel.getDingingStyle()!!
                        val shareRecord = ShoppingHelper.get(localDiningStyle)
                        val customerInfoVo = CustomerInfoVo(
                            name = shareRecord?.name,
                            mobile = shareRecord?.mobile,
                            diningNumber = shareRecord?.diningNumber,
                            diningTime = shareRecord?.diningTime,
                            areaCode = shareRecord?.areaCode
                        )
                        if (PreferenceHelper.getStoreInfo()?.isDiningNumber == true) {
                            //如果用餐人数没填
                            if ((customerInfoVo.diningNumber ?: 0) == 0) {
                                openCustomerDialog {
                                }
                                return@launch
                            }
                        }

                        if (localDiningStyle == DiningStyleEnum.PRE_ORDER.id) {
                            if (customerInfoVo.mobile.isNullOrEmpty()) {
                                paymentError(
                                    ApiResponse.Error(
                                        "Please enter customer mobile number",
                                        CUSTOMER_INFO_REQUIRE
                                    )
                                )
                                return@launch
                            }
                        }
                        viewModel.printPreSettlement()
                    }
                }
            }

            btnSetTimePrice.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (menuOrderFoodAdapter?.getSelectItem() != null) {
                        val selectItem = menuOrderFoodAdapter?.getSelectItem()
                        EditTimePriceDialog.showDialog(
                            parentFragmentManager,
                            cartGood = selectItem,
                            addToCart = false,
                            diningStyle = viewModel.getDingingStyle()!!,
                            updateCartCallBackListener = {
                                if (it != null) {
                                    viewModel.updateTimeGoodPrice(it)
                                }

                            }
                        )
                    }
                }
            }
        }
    }

    private fun onPay() {
        lifecycleScope.launch {
            binding?.apply {
                viewModel.getDingingStyle()?.let { localDiningStyle ->
                    val shareRecord = ShoppingHelper.get(localDiningStyle)
                    val customerInfoVo = CustomerInfoVo(
                        name = shareRecord?.name,
                        mobile = shareRecord?.mobile,
                        diningNumber = shareRecord?.diningNumber,
                        diningTime = shareRecord?.diningTime,
                        areaCode = shareRecord?.areaCode
                    )
                    /**
                     * 校验用餐人数是否必填
                     */

                    if (PreferenceHelper.getStoreInfo()?.isDiningNumber == true) {
                        //如果用餐人数没填
                        if ((customerInfoVo.diningNumber ?: 0) == 0) {
                            openCustomerDialog {
                                onPay()
                            }
                            return@launch
                        }
                    }

                    if (localDiningStyle == DiningStyleEnum.PRE_ORDER.id) {
                        if (customerInfoVo.mobile.isNullOrEmpty()) {
                            paymentError(
                                ApiResponse.Error(
                                    "Please enter customer mobile number",
                                    CUSTOMER_INFO_REQUIRE
                                )
                            )
                            return@launch
                        }
                    }

                    if (shareRecord?.tableUuid.isNullOrEmpty()) {
                        paymentError(
                            ApiResponse.Error(
                                "Please select a table number",
                                TABLE_INFO_REQUIRE
                            )
                        )
                        return@launch
                    }
                    if (isPayNow) {
                        //如果有单品折扣的东西就直接创建订单
                        val singleDiscountGoodsList =
                            (shareRecord?.getGoodsVoList()
                                ?.filter { it.isSetSingleItemDiscount() } ?: ArrayList())
                                .map {
                                    SingleDiscountGoods(
                                        name = it.goods?.name,
                                        num = it.num,
                                        totalPrice = it.totalDiscountPrice(),
                                        vipPrice = it.totalVipPrice(),
                                        isShowVipPrice = it.goods?.isShowVipPrice()
                                            ?: false,
                                        goodsId = it.goods?.id,
                                        goodsHashKey = it.getHash()
                                    )
                                }
                        if (singleDiscountGoodsList.isNotEmpty()) {
                            viewModel.payment(
                                PayTypeEnum.ONLINE_PAYMENT,
                            )
                        } else if (isTakeOut()) {
                            viewModel.payment(
                                PayTypeEnum.PAY_OTHER,
                            )
                        } else {
                            showPayDialog()
                        }

                    } else {
                        viewModel.payment(PayTypeEnum.PAY_AFTER)
                    }
                }
            }
        }
    }

    private fun btnRemarkEnable() {
        binding?.apply {
            if (menuOrderFoodAdapter?.getSelectItem() == null) {
                btnRemark.setEnableWithAlpha(true)
            } else {
                if (menuOrderFoodAdapter?.getSelectItem()?.goods?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                    btnRemark.setEnableWithAlpha(false)
                } else {
                    btnRemark.setEnableWithAlpha(true)
                }
            }
        }
    }

    private fun btnCreditShow(isCartEmpty: Boolean, shoppingRecord: ShoppingRecord?) {
        binding?.apply {
            //判断是否有挂账权限  先付款 的情况下才显示  预定和外卖不需要挂账
            val isCanCredit = (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                ?.contains(PermissionEnum.CREDIT.type) == true) && !isOrderMore && (MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == true || (shoppingRecord?.isUniversalQr() == true && shoppingRecord.getTakeOutPlatformModel() == null) || viewModel.getDingingStyle() == DiningStyleEnum.TAKE_AWAY.id) && viewModel.getDingingStyle() != DiningStyleEnum.PRE_ORDER.id
            llCredit.isVisible = isCanCredit && finalTotalPrice != 0L
            btnCredit.isEnabled = !isCartEmpty
        }
    }

    private fun showPayDialog() {
        val record = ShoppingHelper.get(viewModel.getDingingStyle()!!)
        PayDialog.showDialog(
            fragmentManager = parentFragmentManager,
            menuOrderScreen = menuOrderScreen,
            currentScene = SceneEnum.MENU.id,
            shoppingRecord = record,
            conversionRatio = FoundationHelper.conversionRatio,
            totalPrice = orderAmountDetail.totalPrice,
            totalVipPrice = if (orderAmountDetail.hasVipPrice == true) totalVipPrice else null,
            countryCode = record?.areaCode,
            phone = record?.mobile,
            paymentResponse = {
                when (it) {
                    is ApiResponse.Loading -> {
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        Timber.e("it.data.payType  ==>${it.data.payType}")
                        when (it.data.payType) {
//                            PayTypeEnum.CASH_PAYMENT.id -> {
//                                context?.let { context ->
//                                    viewModel.printTicketByOrderNo(context, it.data.orderNo ?: "")
//                                }
//                                PaymentSuccessDialog.showDialog(parentFragmentManager)
//                            }
                            PayTypeEnum.MIXED_PAYMENT.id,
                            PayTypeEnum.CASH_PAYMENT.id,
                            PayTypeEnum.USER_BALANCE.id -> {
                                context?.let { context ->
                                    viewModel.printTicketByOrderNo(context, it.data.orderNo ?: "")
                                }
                                PaymentSuccessDialog.showDialog(parentFragmentManager)
                                MixedPayDialog.dismissDialog(parentFragmentManager)
                                PayDialog.dismissDialog(parentFragmentManager)
                            }

                            else -> {}
                        }
                        dismissProgress()
                        binding?.btnPayNow?.setEnable(true)
                    }

                    is ApiResponse.Error -> {
                        binding?.btnPayNow?.setEnable(true)
                        dismissProgress()
                        Timber.e("it.errorCode  ${it.errorCode}")
                        paymentError(it)
                    }

                    else -> {
                        dismissProgress()
                    }
                }
            },
            onlineSuccessListener = { res ->
                PaymentSuccessDialog.showDialog(
                    parentFragmentManager,
                    orderedInfo = res,
                    menuOrderScreen
                ) {
                    initSecondary()
                    menuOrderScreen?.updateCategoryAdapter(adapterCategory?.categories)
                    menuOrderScreen?.updateMenuAdapter(menuAdapter?.list)
                    viewModel.updateShoppingRecord()
                }
                //在线支付完成去打印小票
                context?.let { context ->
                    viewModel.printTicket(context, res)
                }
            },
            onCloseListener = {
                try {
                    //try一下防止弹窗还没弹出时快速切换fragment 关闭弹窗这里闪退
                    val currentFragment =
                        parentFragmentManager.fragments.firstOrNull()
                    if (currentFragment is MainDashboardFragment) {
                        currentFragment.replaceFragmentFromOtherFragment(
                            FeatureMenuEnum.ORDER_MANAGEMENT.id,
                            OrderedFragment(),
                            bundle = Bundle().apply {
                                putString(
                                    OrderedFragment.LOCAL_ORDER_ID,
                                    it
                                )
                            }
                        )
                    }
                } catch (e: Exception) {

                }
            },
            onTopUpListener = {
                ConfirmDialog.showDialog(
                    parentFragmentManager,
                    content = getString(R.string.insufficient_balance),
                    positiveButtonTitle = getString(R.string.top_up),
                    negativeButtonTitle = getString(R.string.change_payment),
                ) {
                    MixedPayDialog.dismissDialog(parentFragmentManager)
                    PayDialog.dismissDialog(parentFragmentManager)
                    TopupBalanceDialog.showDialog(
                        parentFragmentManager,
                        menuOrderScreen = menuOrderScreen,
                        content = it.toJson()
                    ) { response ->
                        if (response) {
                            TopUpSuccessDialog.showDialog(
                                parentFragmentManager
                            ) {
                                menuOrderScreen?.showMenu()
                            }
                        } else {
                            menuOrderScreen?.showMenu()
                        }
//                        if (response.data != null) {
//                            TopupQrDialog.showDialog(
//                                fragmentManager = activity?.supportFragmentManager
//                                    ?: parentFragmentManager,
//                                paymentResponse = response.data,
//                                menuOrderScreen,
//                                onCloseListener = {
//                                    menuOrderScreen?.showMenu()
//                                },
//                                successListener = {
//                                    TopUpSuccessDialog.showDialog(
//                                        parentFragmentManager
//                                    ) {
//                                        menuOrderScreen?.showMenu()
//                                    }
//                                })
//                        } else {
//                            TopUpSuccessDialog.showDialog(
//                                parentFragmentManager
//                            )
//                        }
                    }
                }
            },
            onClearShoppingListener = {
                viewModel.clearLocalShoppingCart()
            }
        )
    }

    /**
     * 跳转到对应分类并且高亮
     *
     */
    private fun jumpToCategorizeAndHighLight(groupId: String?) {
        binding?.apply {
            removeRecyclerViewMenuListener()
            menuAdapter?.getHeaderPosition(groupId ?: "")
                ?.let { it1 ->
                    (recyclerViewMenu.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                        it1,
                        0
                    )
                }
            //update secondary as well
            menuOrderScreen?.run {
                updateMenuPosition(groupId ?: "")
            }
            recyclerViewMenu.postDelayed({ setRecyclerViewMenuListener() }, 500)
//
//            menuAdapter?.highLightGroupId = groupId
//            menuAdapter?.notifyDataSetChanged()
//            cancelCategorizeHighLight()
        }
    }

//    private fun cancelCategorizeHighLight() {
//        postUpdateMenuHighLight(2000)
//    }


    private var menuHoriCount = 3

    private fun initAdapter() {
        context?.let {
            binding?.apply {
                if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == false) {
                    menuHoriCount = 4
                }
                val gridLayoutManager = GridLayoutManager(context, menuHoriCount)
                val goodNameLine =
                    if (DisplayUtils.getAspectRatio(requireActivity()) < 1.7) 2 else 3
                Timber.e("getAspectRatio  ${DisplayUtils.getAspectRatio(requireActivity())}")

                menuAdapter = MenuAdapter(
                    arrayListOf(), it, goodNameLine,
                    onClickCallback = { good, _ ->
                        //有小料的时候弹出 弹窗
                        val content = updateGoodProgress(good)
                        content.goodsType = GoodTypeEnum.NORMAL.id
                        FoodDetailDialog.showDialog(
                            parentFragmentManager, content.toJson(),
                            menuOrderScreen = menuOrderScreen,
                            diningStyleEnum = viewModel.getDingingStyle() ?: 0,
                            orderButtonListener = { cartCount, list, goodsTagItemList, orderMealSetGoodList ->
//                                //如果是价格就按当前状态直接加入购物车
                                if (isOrderMore) {
                                    viewModel.plus(
                                        goods = content,
                                        feedList = list,
                                        cartCount = cartCount,
                                        goodsTagItemList = goodsTagItemList,
                                        orderMealSetGoodList = orderMealSetGoodList,
                                        singleDiscountGoods = null,
                                        note = ""
                                    )
                                    return@showDialog
                                }
                                // 判断时价菜购物车内是否设置了价格 如果设置了价格直接加到购物车，如果没设置价格就弹窗设置价格弹窗

                                if (content.isTimePriceGood() && !isHasThisGoodInCart(content)) {
                                    //如果是时价菜，且购物车还没这个菜品
                                    val goodsRequest = GoodsRequest(
                                        num = cartCount,
                                        feedInfoList = list,
                                        goods = content,
                                        goodsTagItems = goodsTagItemList,
                                        orderMealSetGoodList = orderMealSetGoodList,
                                        singleDiscountGoods = null,
                                        note = ""
                                    ).apply {
                                        finalSinglePrice = singleDiscountPrice()
                                    }

                                    EditTimePriceDialog.showDialog(
                                        parentFragmentManager,
                                        cartGood = goodsRequest,
                                        addToCart = true,
                                        diningStyle = viewModel.getDingingStyle()!!,
                                        updateCartCallBackListener = { goodsRequest ->
                                            viewModel.plus(
                                                goods = goodsRequest?.goods!!,
                                                feedList = list,
                                                cartCount = goodsRequest.num ?: 1,
                                                goodsTagItemList = goodsTagItemList,
                                                orderMealSetGoodList = orderMealSetGoodList,
                                                singleDiscountGoods = null,
                                                note = ""
                                            )
                                        }
                                    )
                                } else {
                                    viewModel.plus(
                                        goods = content,
                                        feedList = list,
                                        cartCount = cartCount,
                                        goodsTagItemList = goodsTagItemList,
                                        orderMealSetGoodList = orderMealSetGoodList,
                                        singleDiscountGoods = null,
                                        note = ""
                                    )
                                }

                            }, reserveButtonListener = {})
                    },
                    onPlusCallback = {
                        //没小料直接加
                        val content = updateGoodProgress(it)
                        content.goodsType = GoodTypeEnum.NORMAL.id
                        //如果是加购就按当前状态直接加入购物车
                        if (isOrderMore) {
                            viewModel.plus(goods = content, singleDiscountGoods = null, note = "")
                            return@MenuAdapter
                        }
                        if (content.isTimePriceGood() && !isHasThisGoodInCart(content)) {
                            //如果是时价菜，且还未设置过结果,
                            val goodsRequest = GoodsRequest(
                                num = 1,
                                feedInfoList = null,
                                goods = content,
                                goodsTagItems = null,
                                orderMealSetGoodList = null,
                                singleDiscountGoods = null,
                                note = ""
                            ).apply {
                                finalSinglePrice = singleDiscountPrice()
                            }

                            EditTimePriceDialog.showDialog(
                                parentFragmentManager,
                                cartGood = goodsRequest,
                                addToCart = true,
                                diningStyle = viewModel.getDingingStyle()!!,
                                updateCartCallBackListener = { goodsRequest ->
                                    viewModel.plus(
                                        goods = goodsRequest?.goods!!,
                                        singleDiscountGoods = null,
                                        note = "",
                                        cartCount = goodsRequest.num ?: 1,
                                    )
                                }
                            )

                        } else {
                            viewModel.plus(
                                goods = content,
                                singleDiscountGoods = null,
                                note = "",
                            )
                        }

//                        //Plus
//                        it.goodsType = GoodTypeEnum.NORMAL.id
//                        viewModel.plus(goods = it, singleDiscountGoods = null, note = "")
                    },
                    onSubCallback = {
                        //Subtraction
//                        if (it.withSpecifications == true || !it.feeds.isNullOrEmpty()) {
//                            SubDetailDialog.showDialog(
//                                parentFragmentManager,
//                                it.id, diningStyleEnum = viewModel.getDingingStyle() ?: 0,
//                                confirmButtonListener = { confirmList ->
//                                    //saved
////                            viewModel.updateGoods(it, cconfirmList)
//                                },
//                                addListener = { goodsRequest ->
//                                    goodsRequest.goods?.let { it1 ->
//                                        viewModel.plus(
//                                            goods = it1,
//                                            feedList = goodsRequest.feedInfoList,
//                                            goodsTagItemList = goodsRequest.goodsTagItems,
//                                            orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
//                                            singleDiscountGoods = goodsRequest.singleDiscountGoods,
//                                            note = goodsRequest.note
//                                        )
//                                    }
//                                },
//                                subListener = { goodsRequest ->
//                                    goodsRequest.goods?.let { it1 ->
//                                        viewModel.sub(
//                                            goods = it1,
//                                            feedList = goodsRequest.feedInfoList,
//                                            goodsTagItemList = goodsRequest.goodsTagItems,
//                                            orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
//                                            singleDiscountGoods = goodsRequest.singleDiscountGoods,
//                                            note = goodsRequest.note,
//
//                                        )
//                                    }
//                                })
//                        } else {
//                            viewModel.sub(goods = it, singleDiscountGoods = null, note = "")
//                        }
                    },
                    onMealSetCallback = {
                        val content = updateGoodProgress(it)
                        content.goodsType = GoodTypeEnum.NORMAL.id
                        FoodDetailDialog.showDialog(
                            parentFragmentManager, content.toJson(),
                            menuOrderScreen = menuOrderScreen,
                            diningStyleEnum = viewModel.getDingingStyle() ?: 0,
                            orderButtonListener = { cartCount, list, goodsTagItemList, orderMealSetGoodList ->
                                viewModel.plus(
                                    goods = content,
                                    feedList = list,
                                    cartCount = cartCount,
                                    goodsTagItemList = goodsTagItemList,
                                    orderMealSetGoodList = orderMealSetGoodList,
                                    singleDiscountGoods = null,
                                    note = "",
                                )
                            }, reserveButtonListener = {})
                    })

                adapterCategory = FoodCategoryAdapter(arrayListOf(), it) {
                    jumpToCategorizeAndHighLight(it.id)
//                    removeRecyclerViewMenuListener()
//                    menuAdapter?.getHeaderPosition(it.id ?: "")
//                        ?.let { it1 ->
//                            (recyclerViewMenu.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
//                                it1,
//                                0
//                            )
//                        }
//                    //update secondary as well
//                    menuOrderScreen?.run {
//                        updateMenuPosition(it.id ?: "")
//                        updateCategoryAdapter(it)
//                    }
//                    recyclerViewMenu.postDelayed({ setRecyclerViewMenuListener() }, 500)

                }

                gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return when (menuAdapter?.getItemViewType(position)) {
                            MenuAdapter.ViewType.Header.ordinal -> menuHoriCount
                            MenuAdapter.ViewType.Content.ordinal -> 1
                            else -> menuHoriCount
                        }
                    }
                }
                recyclerViewMenu.apply {
                    layoutManager = gridLayoutManager
                    adapter = menuAdapter
                    setItemAnimator(null)
                }
                recyclerViewCategories.apply {
                    layoutManager =
                        LinearLayoutManager(
                            requireContext(),
                            LinearLayoutManager.HORIZONTAL,
                            false
                        );
                    adapter = adapterCategory
                }
                //init secondary 初始化副屏
                initSecondary()
//                menuOrderScreen?.initAdapter(adapterCategory,menuAdapter)

                setRecyclerViewMenuListener()
            }

        }
    }

    /**
     * 查询购物车内有没有相同的时价菜已经设置过价格了
     *
     * @param good
     * @return
     */
    private fun updateGoodProgress(good: Goods): Goods {
        val content = Gson().fromJson<Goods>(good.toJson(), Goods::class.java)
        if (content.isTimePriceGood()) {
            //查看一下当前购物车有没有该时价菜
            val index1 =
                menuOrderFoodAdapter?.list?.indexOfFirst { it.goods?.id == good.id }
                    ?: -1
            if (content.isToBeWeighed()) {
                content.setWeighingCompletedFlag(false)
            } else {
                content.setWeighingCompletedFlag(true)
            }
            if (index1 != -1) {
                // 如果已经设置过价格了
                if (menuOrderFoodAdapter!!.list[index1].goods?.isHasCompletePricing() == true) {
                    content.sellPrice =
                        menuOrderFoodAdapter!!.list[index1].goods?.sellPrice
                    content.discountPrice = null
                    content.vipPrice =
                        menuOrderFoodAdapter!!.list[index1].goods?.vipPrice
                    content.setPriceCompletedFlag(true)
                } else {
                    content.setPriceCompletedFlag(false)
                }
            } else if (isOrderMore) {
                //查一下加购的时候有没有该时价菜
                if (prevoiusOrderedAdapter != null) {
                    //只查主单的对应的时价菜
                    val index2 =
                        prevoiusOrderedAdapter?.list?.indexOfFirst { it.id == content.id && it.orderId == viewModel.orderMoreID }
                            ?: -1
                    // 如果已经设置过价格了
                    if (index2 != -1) {
                        if (prevoiusOrderedAdapter!!.getData()[index2].isHasCompletePricing()) {
                            content.sellPrice =
                                prevoiusOrderedAdapter!!.getData()[index2].sellPrice
                            content.discountPrice = null
                            content.vipPrice =
                                prevoiusOrderedAdapter!!.getData()[index2].vipPrice
                            content.setPriceCompletedFlag(true)
                        } else {
                            content.setPriceCompletedFlag(false)
                        }
                    }
                } else {
                    content.setPriceCompletedFlag(false)
                }
            } else {
                content.setPriceCompletedFlag(false)
            }
        } else {
            content.setWeighingCompletedFlag(!content.isToBeWeighed())
        }
        return content
    }

    /**
     * 购物车内是否有该商品
     *
     * @param good
     * @return
     */
    private fun isHasThisGoodInCart(good: Goods): Boolean {
        val index1 =
            menuOrderFoodAdapter?.list?.indexOfFirst { it.goods?.id == good.id }
                ?: -1
        if (index1 != -1) {
            return true
        } else {
            val index2 =
                prevoiusOrderedAdapter?.list?.indexOfFirst { it.id == good.id && it.orderId == viewModel.orderMoreID }
                    ?: -1
            if (index2 != -1) {
                return true
            }
        }
        return false
    }

    private var lastPosition = 0

    private val menuScrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)

            //TODO 这边导致滑动卡顿
            // postUpdateClassification(800)
        }

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                postUpdateClassification(500)
            }
        }
    }

    private fun FragmentMenuOrderBinding.setRecyclerViewMenuListener() {
        recyclerViewMenu.removeOnScrollListener(menuScrollListener)
        recyclerViewMenu.addOnScrollListener(menuScrollListener)
    }

    private fun FragmentMenuOrderBinding.removeRecyclerViewMenuListener() {
        recyclerViewMenu.removeOnScrollListener(menuScrollListener)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMenuOrderBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    private fun getCheckedRadio(): DiningStyleEnum {
        var styleStr = getString(R.string.dine_in)
        binding?.apply {
            styleStr = tvDingStyle.text.toString()
        }
        var styleId = DiningStyleEnum.DINE_IN
        when (styleStr) {
            getString(R.string.dine_in) -> {
                styleId = DiningStyleEnum.DINE_IN
            }

            getString(R.string.take_away) -> {
                styleId = DiningStyleEnum.TAKE_AWAY
            }

            getString(R.string.pre_order) -> {
                styleId = DiningStyleEnum.PRE_ORDER
            }

            else -> {
                styleId = DiningStyleEnum.TAKE_OUT
            }
        }
        return styleId
    }


//    private fun showPopupWindowPaymentMethod(anchorView: View) {
//        //防止帕金森，这样就没意思了
//        if ((activity?.supportFragmentManager ?: parentFragmentManager).findFragmentByTag(
//                PayByBalanceDialog.PAY_BY_BALANCE
//            ) != null
//        ) {
//            Timber.e("余额弹窗已经点开")
//            return
//        }
//
//        activity?.hideKeyboard()
//        val popupView = PopupPaynowBinding.inflate(layoutInflater)
//        val popupWindow = PopupWindow(
//            popupView.root,
//            DisplayUtils.dp2px(requireContext(), 300f),
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            true
//        )
//        PopupWindowHelper.addPopupWindow(popupWindow)
//        popupWindow.animationStyle = R.style.PopupAnimation
//        popupWindow.setOnDismissListener {
//            PopupWindowHelper.deletePopupWindow(popupWindow)
//        }
//        val location = IntArray(2).apply {
//            anchorView.getLocationOnScreen(this)
//        }
//        popupWindow.contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
//
//        popupWindow.showAtLocation(
//            anchorView,
//            Gravity.BOTTOM or Gravity.START,
//            location[0] - popupWindow.width / 4,
//            100
//        )
//
//        popupView.tvOnlinePayment.setOnClickListener {
//            SingleClickUtils.isFastDoubleClick {
//                viewModel.payment(PayTypeEnum.ONLINE_PAYMENT)
//                popupWindow.dismiss()
//            }
//        }
//
//        popupView.tvPayByCash.setOnClickListener {
//            if (finalTotalPrice == 0L) {
//                //0圆够，线下 直接传现金支付
//                Printer.openCashierBox()
//
//                viewModel.payment(
//                    PayTypeEnum.CASH_PAYMENT,
//                    offlineChannelModel = PaymentMethodHelper.getCashPaymentModel(),
//                    cashConvertModel = CashConvertModel(0, 0, 0, 0.0)
//                )
//            } else {
//                OfflineChannelsDialog.showDialog(
//                    parentFragmentManager,
//                ) {
//                    if (it.id == OfflinePaymentChannelEnum.CASH.id) {
//                        Printer.openCashierBox()
//                        cashPopDialog?.dismiss()
//                        //如果现金支付则执行现金找零 If paying in cash, cash change will be issued
//                        cashPopDialog = CashConvertDialog(requireContext()).showDialog(
//                            finalTotalPrice,
//                            it,
//                            menuOrderScreen = menuOrderScreen, {
//                                cashPopDialog = null
//                            }
//                        ) { cashConvert ->
//                            viewModel.payment(
//                                PayTypeEnum.CASH_PAYMENT,
//                                offlineChannelModel = it,
//                                cashConvertModel = cashConvert
//                            )
//                        }
//                    } else {
//                        viewModel.payment(
//                            PayTypeEnum.CASH_PAYMENT,
//                            offlineChannelModel = it
//                        )
//                    }
//                }
//            }
//            popupWindow.dismiss()
//        }
//
//        popupView.tvBalance.setOnClickListener {
//            val record = ShoppingHelper.get(viewModel.getDingingStyle()!!)
//            PayByBalanceDialog.showDialog(
//                fragmentManager = activity?.supportFragmentManager
//                    ?: parentFragmentManager,
//                countryCode = record?.areaCode,
//                phone = record?.mobile,
//                content = totalVipPrice,
//                callBackClickListener = {
//                    ConfirmDialog.showDialog(
//                        parentFragmentManager,
//                        content = getString(R.string.insufficient_balance),
//                        positiveButtonTitle = getString(R.string.top_up),
//                        negativeButtonTitle = getString(R.string.change_payment),
//                    ) {
//                        TopupBalanceDialog.showDialog(
//                            parentFragmentManager,
//                            content = it.toJson()
//                        ) { response ->
//                            if (response.data != null) {
//                                TopupQrDialog.showDialog(
//                                    fragmentManager = activity?.supportFragmentManager
//                                        ?: parentFragmentManager,
//                                    paymentResponse = response.data,
//                                    menuOrderScreen,
//                                    onCloseListener = {
//                                        menuOrderScreen?.showMenu()
//                                    },
//                                    successListener = {
//                                        TopUpSuccessDialog.showDialog(
//                                            parentFragmentManager
//                                        ) {
//                                            menuOrderScreen?.showMenu()
//                                        }
//                                    })
//                            } else {
//                                TopUpSuccessDialog.showDialog(parentFragmentManager)
//                            }
//                        }
//                    }
//                },
//                toPayClick = {
//                    viewModel.payment(PayTypeEnum.USER_BALANCE, accountId = it)
//                })
//            popupWindow.dismiss()
//        }
//    }

    private var popupDingWindow: PopupWindow? = null

    private fun showPopupWindowDingStyle(anchorView: View) {
        if (popupDingWindow != null) {
            popupDingWindow?.dismiss()
        }
        binding?.apply {
            tvDingStyle.setBackgroundResource(R.drawable.background_white_border_black12_top_radius_20)
        }
        val popupView = PopupDingstyleBinding.inflate(layoutInflater)
        popupDingWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        if (popupDingWindow != null) {
            PopupWindowHelper.addPopupWindow(popupDingWindow!!)
            popupDingWindow?.animationStyle = R.style.PopupAnimation
            popupDingWindow?.setOnDismissListener {
                binding?.apply {
                    tvDingStyle.setBackgroundResource(R.drawable.background_white_border_black12_radius_20)
                }
                PopupWindowHelper.deletePopupWindow(popupDingWindow!!)
                popupDingWindow = null
            }
            val location = IntArray(2).apply {
                anchorView.getLocationOnScreen(this)
            }
            popupDingWindow?.contentView?.measure(
                View.MeasureSpec.UNSPECIFIED,
                View.MeasureSpec.UNSPECIFIED
            )

            popupDingWindow?.showAtLocation(
                anchorView,
                Gravity.TOP or Gravity.START,
                location[0],
                location[1] + anchorView.height
            )
            when (getCheckedRadio().id) {
                DiningStyleEnum.DINE_IN.id -> {
                    popupView.tvDinein.isSelected = true
                }

                DiningStyleEnum.TAKE_AWAY.id -> {
                    popupView.tvTakeAway.isSelected = true
                }

                DiningStyleEnum.PRE_ORDER.id -> {
                    popupView.tvPreOrder.isSelected = true
                }

                DiningStyleEnum.TAKE_OUT.id -> {
                    popupView.tvTakeOut.isSelected = true
                }
            }

            popupView.tvDinein.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(500) {
                    changeToDineIn()
                    popupDingWindow?.dismiss()
                }
            }

            popupView.tvTakeAway.setOnClickListener {
                updateDingStyleView(DiningStyleEnum.TAKE_AWAY.id)
                viewModel.changeDingStyleCustomerInfo(
                    viewModel.getDingingStyle()!!,
                    DiningStyleEnum.TAKE_AWAY.id
                )
                popupDingWindow?.dismiss()
                viewModel.getGoodsReserveList(DiningStyleEnum.TAKE_AWAY.id, "", requireActivity())
                binding?.apply {
                    if (!edtSearch.getSearchContent().isNullOrEmpty()) {
                        edtSearch.setSearchContent("")
                    }
                }
            }

            popupView.tvPreOrder.setOnClickListener {
                updateDingStyleView(DiningStyleEnum.PRE_ORDER.id)
                viewModel.changeDingStyleCustomerInfo(
                    viewModel.getDingingStyle()!!,
                    DiningStyleEnum.PRE_ORDER.id
                )
                popupDingWindow?.dismiss()
                viewModel.getGoodsReserveList(DiningStyleEnum.PRE_ORDER.id, "", requireActivity())
                binding?.apply {
                    if (!edtSearch.getSearchContent().isNullOrEmpty()) {
                        edtSearch.setSearchContent("")
                    }
                }
            }

            popupView.tvTakeOut.setOnClickListener {
                if (popupView.tvTakeOut.isSelected) {
                    popupDingWindow?.dismiss()
                    return@setOnClickListener
                }

                SingleClickUtils.isFastDoubleClick(500) {
                    TakeOutPlatformDialog.showDialog(parentFragmentManager) { takeOutPlatformModel, dinStyle, takeOutOrderId ->
                        changeToTakeOutPlatform(takeOutPlatformModel, dinStyle, takeOutOrderId)
                    }
                    popupDingWindow?.dismiss()
                }
            }

            lifecycleScope.launch {
                popupView.tvTakeOut.isVisible =
                    PreferenceHelper.getStoreInfo()?.isDeliveryEnable() == true
            }
        }
    }

    private fun changeToDineIn() {
        updateDingStyleView(DiningStyleEnum.DINE_IN.id)
        viewModel.changeDingStyleCustomerInfo(
            viewModel.getDingingStyle()!!,
            DiningStyleEnum.DINE_IN.id
        )

        viewModel.getGoodsReserveList(DiningStyleEnum.DINE_IN.id, "", requireActivity())
        binding?.apply {
            if (!edtSearch.getSearchContent().isNullOrEmpty()) {
                edtSearch.setSearchContent("")
            }
        }
    }

    /**
     *
     *
     * @param takeOutPlatformModel 目标平台
     * @param dinStyle 目标平台 本地的dinStyle
     * @param takeOutOrderId  订单id
     * @param oldShoppingRecord 旧购物车数据
     */
    private fun changeToTakeOutPlatform(
        takeOutPlatformModel: TakeOutPlatformModel,
        dinStyle: Int,
        takeOutOrderId: String,
        oldShoppingRecord: ShoppingRecord? = null
    ) {
        //如果是选平台 或者切的不是当前平台 清空菜单
        if (oldShoppingRecord == null || (oldShoppingRecord != null && oldShoppingRecord.getTakeOutPlatformModel()?.id != takeOutPlatformModel?.id)) {
            binding?.apply {
                pdMenu.isVisible = true
            }
            clearMenu()
        }
        updateDingStyleView(dinStyle)
        viewModel.changeTakeOutPlatform(
            takeOutPlatformModel,
            dinStyle,
            takeOutOrderId,
            oldShoppingRecord
        )
        viewModel.changeDingStyleCustomerInfo(
            viewModel.getDingingStyle()!!,
            dinStyle
        )
        viewModel.getGoodsReserveList(dinStyle, "", requireActivity())
        binding?.apply {
            if (!edtSearch.getSearchContent().isNullOrEmpty()) {
                edtSearch.setSearchContent("")
            }
        }
    }


    private fun showPopupWindowMoreAction(anchorView: View) {
        val popupView = PopupMenuMoreActionBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        PopupWindowHelper.addPopupWindow(popupWindow)
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.setOnDismissListener {
            binding?.apply {
                btnMore.isSelected = false
            }
            PopupWindowHelper.deletePopupWindow(popupWindow)
        }
        val location = IntArray(2).apply {
            anchorView.getLocationOnScreen(this)
        }
        popupWindow.contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)

        popupWindow.showAtLocation(
            anchorView,
            Gravity.TOP or Gravity.START,
            location[0] + anchorView.width,
            location[1]
        )

        val diningStyle = viewModel.getDingingStyle()
        val shoppingRecord = ShoppingHelper.get(diningStyle!!)
        val isWholeDiscountFirst =
            MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == true || shoppingRecord?.isUniversalQr() == true || (viewModel.getDingingStyle() == DiningStyleEnum.TAKE_AWAY.id && !hasUnProcess)
        var isCanWholeDiscount = isWholeDiscountFirst

        val selectItem = menuOrderFoodAdapter?.getSelectItem()
        //判断是否先付款  先付款的才
        val isSingleDiscountFirst =
            MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == true || shoppingRecord?.isUniversalQr() == true || (viewModel.getDingingStyle() == DiningStyleEnum.TAKE_AWAY.id)
        var isCanSingleDiscount =
            isSingleDiscountFirst && selectItem != null && selectItem.isProcessed() == true


        if ((MainDashboardFragment.CURRENT_USER?.getPermissionList()
                ?.contains(PermissionEnum.SELECT_DISCOUNT.type) != true && MainDashboardFragment.CURRENT_USER?.getPermissionList()
                ?.contains(PermissionEnum.CUSTOMIZE_DISCOUNT.type) != true) || viewModel.getDingingStyle() == DiningStyleEnum.PRE_ORDER.id
        ) {
            isCanWholeDiscount = false
            isCanSingleDiscount = false
        }

        /**
         * 外卖固定有整单 自定义折扣
         */
        if (viewModel.getDingingStyle()!! >= TakeOutPlatformToDiningHelper.BASE_INDEX) {
            isCanWholeDiscount = true
        }

        if (menuOrderFoodAdapter?.list.isNullOrEmpty()) {
            popupView.btnCoupon.isEnabled = false
            popupView.btnWholeDiscount.isEnabled = false
            popupView.btnSingleDiscount.isEnabled = false
        } else {
            val item = menuOrderFoodAdapter?.getSelectItem()
            Timber.e("updateActionBtnState: ${item?.goods?.id}")
//            val diningStyle = viewModel.getDingingStyle()

            popupView.btnCoupon.isEnabled = isOrderMore != true

            popupView.btnWholeDiscount.isEnabled =
                !hasUnProcess && isOrderMore != true && isCanWholeDiscount

            popupView.btnSingleDiscount.isEnabled =
                !hasUnProcess && isOrderMore != true && isCanSingleDiscount && item != null && item.goods?.goodsType == GoodTypeEnum.NORMAL.id

            /**
             * 如果订单里含有优惠活动商品，则不能单品改价
             */
            if (shoppingRecord?.getGoodsVoList()
                    ?.firstOrNull { !it.goods?.activityLabels.isNullOrEmpty() } != null
            ) {
                popupView.btnSingleDiscount.isEnabled = false
            }
        }

        if (isTakeOut()) {
            popupView.btnCoupon.isVisible = false
            popupView.btnSingleDiscount.isVisible = false
        }

        popupView.btnWholeDiscount.setOnClickListener {
            val shareRecord = ShoppingHelper.get(viewModel.getDingingStyle()!!)
            if (shareRecord?.tableUuid.isNullOrEmpty()) {
                paymentError(
                    ApiResponse.Error(
                        "Please select a table number",
                        TABLE_INFO_REQUIRE
                    )
                )
                return@setOnClickListener
            }
            val model = ReduceDiscountDetailModel()
            //美元转瑞尔汇率
            model.conversionRatio = null

            //	减免折扣率价格
            model.reduceAmount = null
            //	销售价：减免金额（美元）
            model.reduceDollar = null
            //销售价：减免金额（瑞尔）
            model.reduceKhr = null
            //销售价：减免百分比
            model.reduceRate = null
            //减免后实际金额
            model.reduceRealPrice = null
            //减免后实际金额(瑞尔)
            model.reduceRealPriceKhr = null

            //会员价：减免折扣率价格
            model.reduceVipAmount = null
            //会员价：减免金额（美元）
            model.reduceVipDollar = null
            //会员价：减免金额（瑞尔）
            model.reduceVipKhr = null
            //会员价：减免百分比
            model.reduceVipRate = null
            //会员价：减免后实际金额
            model.reduceVipRealPrice = null
            //会员价：减免后实际金额(瑞尔)
            model.reduceVipRealPriceKhr = null

            //销售价：订单实付总金额
            model.totalAmount = wholeDiscountPrice
            //销售价：订单实付总金额（瑞尔）
            model.totalAmountKhr = null
            //会员价：订单实付总金额，如果有值则需展示会员价tab
            model.totalVipAmount = totalVipPrice
            //会员价：订单实付总金额（瑞尔）
            model.totalVipAmountKhr = null


            model.totalVatPrice = totalVatPirce
            model.totalVipVatPrice = totalVipVatPirce


            binding?.apply {
                //是否有会员价tab
                model.vipTabFlag = tvVipPrice.isVisible
            }

            //当前设置的类型
            model.type = null

            Timber.e(model.toString())

            NewModifyDiscountDialog.showDialog(
                parentFragmentManager,
                null,
                model,
                shareRecord?.getGoodsVoList(),
//                couponCode = shareRecord?.getCouponInfo()?.couponCode,
                shoppingRecord?.getTakeOutPlatformModel(),
                { reduceDiscountDetailRequest ->
                    Timber.e("刷新订单")

                    viewModel.payment(
                        if (isTakeOut()) PayTypeEnum.PAY_OTHER else PayTypeEnum.ONLINE_PAYMENT,
                        reduceDiscountDetailRequest = reduceDiscountDetailRequest
                    )
                }, {})
            popupWindow.dismiss()
        }
        popupView.btnSingleDiscount.setOnClickListener {
//            SingleClickUtils.isFastDoubleClick {
            val shareRecord = ShoppingHelper.get(viewModel.getDingingStyle()!!)
            if (shareRecord?.tableUuid.isNullOrEmpty()) {
                paymentError(
                    ApiResponse.Error(
                        "Please select a table number",
                        TABLE_INFO_REQUIRE
                    )
                )
                return@setOnClickListener
            }

            val singleDiscountData =
                ShoppingHelper.get(viewModel.getDingingStyle() ?: 0)?.getGoodsVoList()
                    ?: emptyList()
            Timber.d("singleDiscountData: ${singleDiscountData.toJson()}")
            val singleDiscountGoods =
                singleDiscountData
                    .filter { it.goods?.goodsType != GoodTypeEnum.TEMPORARY.id }
                    .map {
                        SingleDiscountGoods(
                            name = it.goods?.name,
                            num = it.num,
                            type = it.singleDiscountGoods?.type,
                            reduceRatio = it.singleDiscountGoods?.reduceRatio,
                            vipReduce = it.singleDiscountGoods?.vipReduce,
                            saleReduce = it.singleDiscountGoods?.saleReduce,
                            totalPrice = it.totalDiscountPriceWithoutSingleDiscount(),
                            vipPrice = it.totalVipPriceWithoutSingleDiscount(),
                            isShowVipPrice = it.goods?.isShowVipPrice() ?: false,
                            goodsId = it.goods?.id,
                            goodsHashKey = it.getHash(),
                            singlePrice = it.singleDiscountPrice(),
                            singleVipPrice = it.calculateVipPrice()
                        )

                    }
            if (singleDiscountGoods.isEmpty()) {
                showToast(getString(R.string.no_good_can_set_single_discount))
                return@setOnClickListener
            }

            val selectItem = menuOrderFoodAdapter?.getSelectItem()
            if (selectItem != null) {
                Timber.e("当前选中的  singleDiscountGoods: ${selectItem.getHash()}   ${isSingleDiscountFirst}")
                EditSingleDiscountDialog.showDialog(
                    parentFragmentManager,
//                    isOnlyModifyPrice = !isSingleDiscountFirst,
                    isOnlyModifyPrice = false,
                    singleDiscountGoods = SingleDiscountGoods(
                        name = selectItem.goods?.name,
                        num = selectItem.num,
                        type = selectItem.singleDiscountGoods?.type,
                        reduceRatio = selectItem.singleDiscountGoods?.reduceRatio,
                        vipReduce = selectItem.singleDiscountGoods?.vipReduce,
                        saleReduce = selectItem.singleDiscountGoods?.saleReduce,
                        remark = selectItem.singleDiscountGoods?.remark,
                        adjustSalePrice = selectItem.singleDiscountGoods?.adjustSalePrice,
                        adjustVipPrice = selectItem.singleDiscountGoods?.adjustVipPrice,
                        totalPrice = selectItem.totalDiscountPriceWithoutSingleDiscount(),
                        vipPrice = selectItem.totalVipPriceWithoutSingleDiscount(),
                        isShowVipPrice = selectItem.goods?.isShowVipPrice() ?: false,
                        goodsId = selectItem.goods?.id,
                        goodsHashKey = selectItem.getHash(),
                        singlePrice = selectItem.singleDiscountPrice(),
                        singleVipPrice = selectItem.calculateVipPrice(),
                        discountReduceInfo = selectItem.singleDiscountGoods?.discountReduceInfo,
                        discountType = selectItem.singleDiscountGoods?.discountType,
                        discountReduceActivityId = selectItem.singleDiscountGoods?.discountReduceActivityId
                    ),
                    cartGoods = selectItem,
                    cartGoodsList = shareRecord?.getGoodsVoList(),
                    sourceHashKey = selectItem.getHashWithoutSingleDiscount(),
                    dingStyle = viewModel.getDingingStyle(),
                    modifyLocalListener =
                        { old, new ->
                            Timber.e("当前选择的111: ${selectItem.getHash()}")
                            Timber.e("当前选择的111  singleDiscountGoods: ${selectItem.singleDiscountGoods?.toJson()}")
                            viewModel.setSingleGoodDiscount(old, new!!, selectItem)
                            SingleDiscountDialog.dismissDialog(parentFragmentManager)
                        })
                popupWindow.dismiss()
            }
//            }
        }

        popupView.btnCoupon.setOnClickListener {
            val localDingingStyle = viewModel.getDingingStyle()!!
            val shareRecord = ShoppingHelper.get(localDingingStyle)
            val goodsBoList = ShoppingCartHelper.getGoodsBoList(shareRecord)
            val couponInfo = shareRecord?.getCouponInfo()
            if (couponInfo == null) {
                IdentifyCouponDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    diningStyle = localDingingStyle,
                    goodsList = goodsBoList,
                    isPreOrder = localDingingStyle == DiningStyleEnum.PRE_ORDER.id,
                    tableUuid = shareRecord?.tableUuid
                ) {
                    viewModel.updateCouponInfo(it)
                }
            } else {
                //如果有选优惠券传进去展示
                binding?.apply {
                    CouponListDialog.showDialog(
                        activity?.supportFragmentManager ?: parentFragmentManager,
                        diningStyle = localDingingStyle,
                        goodsList = goodsBoList,
                        isPreOrder = localDingingStyle == DiningStyleEnum.PRE_ORDER.id,
                        tableUuid = shareRecord.tableUuid,
                        currentCouponId = shareRecord.getCouponInfo()?.id,
                        localCouponList = listOf(couponInfo),
                        isShowVip = tvVipPrice.isVisible,
                        isHasUnWeight = tvTotalPrice.text == getString(R.string.to_be_confirmed)
                    ) {
                        viewModel.updateCouponInfo(it)
                    }
                }
            }
            popupWindow.dismiss()
        }


    }

    private var isFromOrderList = false

    private fun resumeOrder() {
        arguments?.let {
            if (it.containsKey(OrderedFragment.ORDERED_FRAGMENT_BUNDLE_DATA)) {
                isOrderMore = true
                isFromOrderList = true
                val diningStyleEnum =
                    it.getInt(OrderedFragment.ORDERED_FRAGMENT_BUNDLE_DATA)
                binding?.apply {
                    updateDingStyleView(diningStyleEnum)
                }
                it.clear()
            }
//            if (it.containsKey(PendingOrderFragmentDialog.PENDING_FRAGMENT_BUNDLE_DATA)) {
//                val diningStyleEnum =
//                    it.getInt(PendingOrderFragmentDialog.PENDING_FRAGMENT_BUNDLE_DATA)
//                binding?.apply {
//                    updateDingStyleView(diningStyleEnum)
//                }
//                it.clear()
//            }
        }
    }

    private fun modifyGoodNum(goodsRequest: GoodsRequest) {
        val diningStyle = viewModel.getDingingStyle()!!
        val record = GoodsHelper.get(goodsRequest.goods!!.id, viewModel.getDingingStyle()!!)
        var maxNum = GOOD_MAX_NUM
        if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
            //预定数量限制
            val restrictNum = (goodsRequest.goods?.restrictNum
                ?: 0)
            if (restrictNum > 0) {
                maxNum = restrictNum
            }

        }
        //剩余最多可加数量
        var remainingNum = maxNum - (record?.num ?: 0)
        val shoppingRecord = ShoppingHelper.get(diningStyle)
        shoppingRecord?.getGoodsVoList()?.forEach {
            if (it.getHash() == goodsRequest.getHash()
            ) {
                remainingNum += it.num ?: 0
            }
        }


        EditGoodNumDialog(requireContext()).showDialog(
            num = goodsRequest.num ?: 0,
            remainingNum = remainingNum,
            zeroEnable = true,
            isSoldOut = goodsRequest?.goods?.isSoldOut() == true,
            isPreOrder = viewModel.getDingingStyle() == DiningStyleEnum.PRE_ORDER.id
        ) { num ->
            viewModel.updateGoodsNum(goodsRequest, num)
        }
    }

    private fun updateAddGoodNumView(goodsRequest: GoodsRequest?) {
        binding?.apply {
            if (goodsRequest != null) {
                tvGoodNum.text = "${goodsRequest.num}"
            } else {
                tvGoodNum.text = ""
            }

            menuOrderFoodAdapter?.updateSelectItem(goodsRequest)
            updateActionBtnState(false)
        }
    }

    /**
     * 更新操作按钮状态
     *
     */
    private fun updateActionBtnState(isCartEmpty: Boolean) {
        binding?.apply {
            /**
             * 购物车为空的时候禁用按钮
             */
            val diningStyle = viewModel.getDingingStyle()
            val shoppingRecord = ShoppingHelper.get(diningStyle!!)
            Timber.e("${MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance}  ${(shoppingRecord?.isUniversalQr() == true && viewModel.getDingingStyle() != DiningStyleEnum.TAKE_OUT.id)}   ${(viewModel.getDingingStyle() == DiningStyleEnum.TAKE_AWAY.id || viewModel.getDingingStyle() == DiningStyleEnum.PRE_ORDER.id)}")
            //是否先付款类型  非加购
            val isPayFirstType =
                (MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == true || shoppingRecord?.isUniversalQr() == true || (viewModel.getDingingStyle() == DiningStyleEnum.TAKE_AWAY.id || viewModel.getDingingStyle() == DiningStyleEnum.PRE_ORDER.id)) && !isOrderMore && !isTakeOut()
            Timber.e("isPayFirstType :${isPayFirstType}")
            //先付款非加购显示 打印按钮
            llPrint.isVisible = isPayFirstType
            if (isCartEmpty) {
                btnWeight.isEnabled = false
                btnAdd.isEnabled = false
                btnReduce.isEnabled = false
                tvGoodNum.isEnabled = false
                tvGoodNum.text = ""
//                btnPrint.isEnabled = false
                btnRemark.isEnabled = true
                Timber.e("禁用了")
            } else {
//                btnPrint.isEnabled = !hasUnProcess
                val item = menuOrderFoodAdapter?.getSelectItem()
                Timber.e("updateActionBtnState: ${item?.goods?.id}")
//                val diningStyle = viewModel.getDingingStyle()
                if (item != null) {
                    btnAdd.isEnabled = false
                    btnReduce.isEnabled = false
                    tvGoodNum.isEnabled = false

                    val maxNum = item.getMaxBuyNum(diningStyle)
                    val record = item.goods?.id?.let { GoodsHelper.get(it, diningStyle) }
                    val canAddNum = maxNum - (record?.num ?: 0)

                    /**
                     * 如果商品已售罄
                     */
                    if (item.goods?.isSoldOut() == true) {
                        btnAdd.isEnabled = false
                        btnReduce.isEnabled = (item.num ?: 0) > 0
                        tvGoodNum.isEnabled = true
                    } else {
                        btnAdd.isEnabled = canAddNum > 0
                        btnReduce.isEnabled = (item.num ?: 0) > 0
                        tvGoodNum.isEnabled = true
                    }

                    if (item.isToBeWeighed()) {
                        tvGoodNum.isEnabled = false
                    }

                    tvGoodNum.text = "${(item.num ?: 0)}"

                    if (item.goods?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                        //临时菜不能修改备注
                        btnRemark.isEnabled = false
                    } else {
                        btnRemark.isEnabled = true
                    }

                    if (item.goods?.isTimePriceGood() == true) {
//                        if (item.goods?.isHasProcessed() == true) {
//                            btnSetTimePrice.text = getString(R.string.modify_time_price)
//                        } else {
//                            btnSetTimePrice.text = getString(R.string.set_time_price)
//                        }
                        btnSetTimePrice.isEnabled = true
                    } else {
                        btnSetTimePrice.text = getString(R.string.set_time_price)
                        btnSetTimePrice.isEnabled = false
                    }
                    Timber.d("isToBeWeighed:${item.goods?.toJson()}")
                    //套餐待称重可能需要修改
                    btnWeight.isEnabled = item.isToBeWeighed()
                } else {
                    btnWeight.isEnabled = false
                    btnAdd.isEnabled = false
                    btnReduce.isEnabled = false
                    tvGoodNum.isEnabled = false
                    tvGoodNum.text = ""
                    btnRemark.isEnabled = true
                    btnSetTimePrice.text = getString(R.string.set_time_price)
                    btnSetTimePrice.isEnabled = false
                }
//                btnCoupon.isVisible = isOrderMore != true
//
//                btnWholeDiscount.isVisible = isOrderMore != true && isCanDiscount
//                btnWholeDiscount.isEnabled = !hasUnWeighed
//
//                btnSingleDiscount.isVisible = isOrderMore != true && isCanDiscount
//                btnSingleDiscount.isEnabled = !hasUnWeighed
//
//                if (shoppingRecord?.getGoodsVoList()
//                        ?.firstOrNull { it.goods?.activityLabels.isNullOrEmpty() == false } != null
//                ) {
//                    btnSingleDiscount.isVisible = false
//                }
            }
        }
    }

    private fun showTableDialog() {
        AvailableTableListDialog.showDialog(fragmentManager = parentFragmentManager) {
            if (MainDashboardFragment.CURRENT_USER?.isTableService == true) {
                viewModel.switchTable(it?.uuid, it?.name, it?.type)
            } else {
                viewModel.updateSelectTable(it)
            }
        }
    }

    private fun isCanSwitchTable(enable: Boolean) {
        binding?.apply {
            btnChangeTable.isClickable = enable
            btnChangeTable.isEnabled = enable
            llChangeTable.isVisible = viewModel.getDingingStyle() != DiningStyleEnum.PRE_ORDER.id
        }
    }


    private fun clearOrderMore() {
        binding?.apply {
            Timber.e("清空clearOrderMore")
            prevoiusOrderedAdapter?.clearData()
            if (viewModel.getDingingStyle() != null) {
                val shoppingRecord = ShoppingHelper.get(viewModel.getDingingStyle()!!)
                val goodsList = shoppingRecord?.getGoodsVoList()
                btnPending.text =
                    getString(if (goodsList.isNullOrEmpty()) R.string.resume_order else R.string.pending)
            } else {
                btnPending.text = getString(R.string.resume_order)
            }

            layoutOrderMore.isVisible = false
            layoutNewOrderTitle.isVisible = false
            recyclerPreviousOrderedFood.isVisible = false
            flOrderedFood.isVisible = true
            llSetTimePrice.isVisible = true
        }
    }

    private fun openCustomerDialog(callBack: (() -> Unit)? = null) {
        lifecycleScope.launch {
            var isRequire = false
            val dingingStyle = viewModel.getDingingStyle()!!
            if (dingingStyle == DiningStyleEnum.PRE_ORDER.id) isRequire = true
            val record = ShoppingHelper.get(dingingStyle)
            withContext(Dispatchers.Main) {
                ReserveInputInfoDialog.showDialog(
                    parentFragmentManager, isRequire = isRequire,
                    shoppingRecord = record
                ) {
                    viewModel.updateCustomer(it)
                    callBack?.invoke()
                }
            }
//            }
        }
    }

    private fun isTakeOut(): Boolean {
        return takeOutPlatformModel != null
    }

    /**
     * EditText竖直方向是否可以滚动
     * @return true：可以滚动   false：不可以滚动
     */
//    private fun canVerticalScroll(editText: EditText): Boolean {
//        //滚动的距离
//        val scrollY = editText.scrollY
//        //控件内容的总高度
//        val scrollRange = editText.layout.height
//        //控件实际显示的高度
//        val scrollExtent =
//            editText.height - editText.compoundPaddingTop - editText.compoundPaddingBottom
//        //控件内容总高度与实际显示高度的差值
//        val mOffsetHeight = scrollRange - scrollExtent
//
//        return if (mOffsetHeight == 0) {
//            false
//        } else scrollY > 0 || scrollY < mOffsetHeight - 1
//    }

    //Socket
    override fun onResume() {
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        super.onResume()
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onStop() {
        super.onStop()
    }


    private fun wsHandel(event: WebSocket.Event) {
        when (event) {
//            is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
//            is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
//            is WebSocket.Event.OnConnectionClosing -> Timber.e(
//                "closing connection.."
//            )
//
//            is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
            is WebSocket.Event.OnMessageReceived -> {
                if (event.message is Message.Text) {
                    if ((event.message as Message.Text).value == "ping")
//                        viewModel.testingWebsocketSendMessage()
                    else {
                        try {
//                            TImber.e( (event.message as Message.Text).value)
                            val socketModel = JSON.parseObject(
                                (event.message as Message.Text).value,
                                SocketModel::class.java
                            )
                            socketModel.cmd?.let { cmd ->
                                //购物车变动
                                if (cmd == WsCommand.CART_CHANGE) {
                                    val socketGoods = JSON.parseObject(
                                        socketModel.data?.toJson(),
                                        SocketGoods::class.java
                                    )
                                    viewModel.updateCartFrom(socketGoods)
                                }

                                //减免折扣修改
                                if (cmd == WsCommand.WHOLE_DISCOUNT_MODIFY) {
                                    //加购的时候对应订单减免折扣修改则跳转到对应订单去
                                    socketModel.data?.let {
                                        val localDingingStyle = viewModel.getDingingStyle()
                                        if (localDingingStyle != null) {
                                            val shoppingRecord =
                                                ShoppingHelper.get(localDingingStyle)
                                            if (shoppingRecord?.isOrderMore == true && shoppingRecord.orderMoreID == it.toString()) {
                                                //如果减免折扣的是当前购物车的内容
                                                viewModel.removeOrderMore()

                                            }
                                        }
                                    }
                                }

                                if (cmd == WsCommand.CUSTOM_INFO_CHANGE) {
                                    socketModel.data?.let {
                                        val updateCustomerInfo = Gson().fromJson(
                                            it.toJson(),
                                            UpdateCustomerInfoRequest::class.java
                                        )
                                        viewModel.updateCustomerFromWs(updateCustomerInfo)
                                    }
                                }
                                if (cmd == WsCommand.TAKE_OUT_PLATFORM_CHANGE) {
                                    //外卖平台变动
                                    socketModel.data?.let {
                                        val wsTakeOutPlatformChangeModel = Gson().fromJson(
                                            it.toJson(),
                                            WsTakeOutPlatformChangeResponse::class.java
                                        )
                                        val shoppingRecord =
                                            ShoppingHelper.get(viewModel.getDingingStyle()!!)
                                        if (shoppingRecord?.takeOutPlatformModel?.isNotEmpty() == true) {
                                            val index =
                                                wsTakeOutPlatformChangeModel.deliveryPlatformList?.indexOfFirst {
                                                    it.id == shoppingRecord.getTakeOutPlatformModel()?.id
                                                } ?: -1
                                            if (index != -1) {
                                                val platformModel =
                                                    wsTakeOutPlatformChangeModel!!.deliveryPlatformList!![index]
                                                if (platformModel.isEnable()) {
                                                    //如果平台没关闭走一遍刷新
                                                    changeToTakeOutPlatform(
                                                        platformModel,
                                                        shoppingRecord.diningStyle,
                                                        shoppingRecord.takeOutOrderId ?: ""
                                                    )
                                                } else {
                                                    //如果平台已关闭 切到堂食
                                                    changeToDineIn()
                                                    requireActivity().runOnUiThread {
                                                        showToast(getString(R.string.take_out_platform_no_exist))
                                                    }
                                                }
                                            }
                                        }

                                    }
                                }
                                if (cmd == WsCommand.GOODS_SOLD_OUT_OR_DEL) {
                                    //如果有菜品变动，走一遍当前的逻辑
                                    viewModel.getGoodsReserveList(
                                        viewModel.getDingingStyle()!!,
                                        binding?.edtSearch?.getSearchContent() ?: "",
                                        requireActivity()
                                    )
                                }
                                if (cmd == WsCommand.ACTIVITY_COUPON_CHANGE) {
                                    if (viewModel.getDingingStyle()!! < TakeOutPlatformToDiningHelper.BASE_INDEX) {
                                        //如果有优惠活动变动 非外卖平台 更新菜单
                                        viewModel.getGoodsReserveList(
                                            viewModel.getDingingStyle()!!,
                                            binding?.edtSearch?.getSearchContent() ?: "",
                                            requireActivity()
                                        )
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }

            else -> {

            }
        }
    }

    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {
            SimpleEventType.UPDATE_STORE -> {
                requireActivity().runOnUiThread {
                    menuHoriCount =
                        if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == true) 3 else 4
                    val gridLayoutManager = GridLayoutManager(context, menuHoriCount)
                    gridLayoutManager.spanSizeLookup =
                        object : GridLayoutManager.SpanSizeLookup() {
                            override fun getSpanSize(position: Int): Int {
                                return when (menuAdapter?.getItemViewType(position)) {
                                    MenuAdapter.ViewType.Header.ordinal -> menuHoriCount
                                    MenuAdapter.ViewType.Content.ordinal -> 1
                                    else -> menuHoriCount
                                }
                            }
                        }
                    binding?.apply {
                        recyclerViewMenu.apply {
                            layoutManager = gridLayoutManager
                        }
                    }

                    menuAdapter?.notifyDataSetChanged()
                    menuOrderScreen?.updateMenuAdapter(true)
                    viewModel.updateShoppingRecord()
                    if (PayDialog.getDialog(parentFragmentManager)?.getOrderNo() == null) {
                        MixedPayDialog.dismissDialog(parentFragmentManager)
                        PayDialog.dismissDialog(parentFragmentManager)
                    }
                }
            }

            SimpleEventType.UPDATE_TMP_GOOD_INFO -> {
                Timber.e("更新购物车临时菜品")
                viewModel.updateCartGoodInfo(event.data as? List<Goods>)
            }


            else -> {

            }
        }
    }
}