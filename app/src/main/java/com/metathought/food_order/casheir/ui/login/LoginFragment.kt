package com.metathought.food_order.casheir.ui.login

import android.content.Context
import android.content.pm.PackageInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.gson.Gson
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper.Companion.getInstance
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.databinding.FragmentLoginBinding
import com.metathought.food_order.casheir.databinding.PopupLanguagesBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.navigateWithAnim
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setTextViewDrawableColor
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.helper.VersionHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.CheckVersionDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.litepal.LitePal
import org.litepal.LitePalDB
import timber.log.Timber
import java.util.Locale

@AndroidEntryPoint
class LoginFragment : BaseFragment() {
    companion object {
        fun newInstance() = LoginFragment()
    }

    private var _binding: FragmentLoginBinding? = null
    private val binding get() = _binding
    private val viewModel: LoginViewModel by viewModels()

    private var secondaryScreenUI: SecondaryScreenUI? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentLoginBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        MainDashboardFragment.CURRENT_USER = null
        dismissProgress()
        initSecondary()
        initView()
        initObserver()
        initListener()

        //版本更新
        viewModel.checkVersion(requireActivity())
    }

    override fun onLoad() {
        super.onLoad()
        showAccountAndPassword()
    }

    private fun initSecondary() {
        context?.let {
            secondaryScreenUI = MyApplication.myAppInstance.orderedScreen
        }
    }

    private fun setUserData(userName: String?) {
        userName?.let {
            val litePalDB = LitePalDB.fromDefault(userName)
            LitePal.use(litePalDB)
        }
    }

    private fun initObserver() {

        viewModel.versionResponse.observe(viewLifecycleOwner) { state ->
            context?.let { context ->
                lifecycleScope.launch {
                    if (state is ApiResponse.Success) {
                        if (state.data != null) {
                            var isUpdate = false
                            //判断一下可选更新判断是否不再提示
                            if (state.data.type == 2) {
                                isUpdate = true
                            } else if (state.data.type == 1) {
                                var versionName = ""
                                //判断一下是否不再提示
                                PreferenceDataStoreHelper.getInstance().apply {
                                    versionName = this.getFirstPreference(
                                        PreferenceDataStoreConstants.DATA_STORE_KEY_IGNORE_VERSION,
                                        ""
                                    )
                                }
                                Timber.e("versionName : $versionName")
                                if (versionName != state.data.name) {
                                    isUpdate = true
                                }
                            }

                            if (isUpdate) {
                                CheckVersionDialog.showDialog(parentFragmentManager, state.data)
                            }
                        }
                    }
                }
            }
        }

        viewModel.userLoginResponse.observe(viewLifecycleOwner) { response ->
            binding?.apply {
                context?.let { context ->
                    response?.let {
                        Glide.with(context).load(viewModel.userLoginResponse.value?.url)
                            .circleCrop()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo).into(imgLogo)
                        tvStoreName.text = viewModel.userLoginResponse.value?.getStoreNameByLan()
                        if (response.token?.isNotEmpty() == true) {
                            setUserData(response.userAccount)
                            Timber.e("几次2222")
                            findNavController().navigateWithAnim(
                                R.id.action_loginFragment_to_mainDashboardFragment,
                                popupToId = R.id.loginFragment
                            )
                        }
                    }

                }
            }
        }
        viewModel.uiState.observe(viewLifecycleOwner) {
            it.result?.let { res ->
                when (res) {
                    is ApiResponse.Loading -> {
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        context?.let { it1 ->
                            lifecycleScope.launch {
                                getInstance(it1).apply {
                                    this.putPreference(
                                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                        res.data.toJson()
                                    )
                                }

                                MainDashboardFragment.CURRENT_USER = res.data

                                activity.hideKeyboard()
                                binding?.apply {
                                    activity.hideKeyboard(edtPassword)
                                    activity.hideKeyboard(edtUserName)
                                }
//                                SecondaryManager.userInfo = res.data
//                                SecondaryManagerV2.userInfo = res.data
                                secondaryScreenUI?.showDefault(res.data)
//                                SecondaryManager.showOtherScreen(
//                                    it1,
//                                    user = res.data
//                                )
                                setUserData(res.data.userAccount)
                                Timber.e("几次1111")
                                findNavController().navigateWithAnim(
                                    R.id.action_loginFragment_to_mainDashboardFragment,
                                    bundle = Bundle().apply {
                                        putBoolean("is_login", true)
                                    },
                                    popupToId = R.id.loginFragment
                                )
                                dismissProgress()
                            }
                        }

                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
                        binding?.apply {
                            tvErrorLayout.text = if (res.errorCode == 3002) {
                                getString(R.string.username_or_password_incorrect)
                            } else {
                                res.message
                            }
                            layoutError.setVisibleInvisible(true)
                            viewModel.emitUiState(null)
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            edtUserName.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    edtUserName.setTextViewDrawableColor(R.color.black)
                } else {
                    if (edtUserName.text?.isEmpty() == true) {
                        edtUserName.setTextViewDrawableColor(R.color.bg_progress)
                    }
                }
            }
            edtPassword.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    edtPassword.setTextViewDrawableColor(R.color.black)
                } else {
                    if (edtPassword.text?.isEmpty() == true) {
                        edtPassword.setTextViewDrawableColor(R.color.bg_progress)
                    }
                }
            }
            edtPassword.addTextChangedListener {
                btnLogin.setEnable(it?.trim()?.length!! > 0 && edtUserName.text?.length!! > 0)
            }
            edtUserName.addTextChangedListener {
                btnLogin.setEnable(it?.trim()?.length!! > 0 && edtPassword.text?.length!! > 0)
            }
            edtPassword.setOnEditorActionListener { textView, actionId, keyEvent ->
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    if (btnLogin.isEnabled) {
                        viewModel.login(
                            edtUserName.text?.trim().toString(),
                            edtPassword.text?.trim().toString(),
                            checkboxRemember.isChecked
                        )
                    }
                }
                return@setOnEditorActionListener false
            }
            btnLogin.setOnClickListener {
                viewModel.login(
                    edtUserName.text?.trim().toString(),
                    edtPassword.text?.trim().toString(),
                    checkboxRemember.isChecked
                )
            }

            dropdownLanguage.setOnClickListener() {
                arrow.animate().rotation(180f).setDuration(200)
                dropdownLanguage.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupWindowLanguage(it)
            }
        }
    }

//    private fun initializeBlackBoard() {
//        context?.let { context ->
//            val mediaRouter = context.getSystemService(Context.MEDIA_ROUTER_SERVICE) as MediaRouter
//            val route = mediaRouter.getSelectedRoute(MediaRouter.ROUTE_TYPE_LIVE_VIDEO)
//            route?.let {
//                route.presentationDisplay?.let { presentationDisplay ->
//                    blackBoard = SecondaryScreenUI(context, presentationDisplay)
//                    blackBoard.show()
//                }
//            }
//            val displayManager = it.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager?
//            val displays =
//                displayManager!!.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
//            if (displays.isNotEmpty()) {
//                blackBoard = SecondaryScreenUI(it, displays[0])
//                blackBoard.show()
//            }
//        }
//    }
//    var otherScreenUI:OtherScreenUI?=null

    private fun initView() {
        Timber.e("切换语言会进来么")
        for (fragment in parentFragmentManager.fragments) {
            if (fragment !is LoginFragment)
                parentFragmentManager.beginTransaction().remove(fragment).commit()
        }
        Timber.e("切换语言会进来么")
        binding?.apply {
            context?.let {
                lifecycleScope.launch {
                    getInstance(it).apply {
                        val model = Gson().fromJson(
                            getFirstPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                ""
                            ), UserLoginResponse::class.java
                        )
                        Timber.e("本地获取出来的token  \n ${model?.token}")

                        viewModel.userLoginResponse.value = model


                        withContext(Dispatchers.Main) {
                            if (model == null || model.token.isNullOrEmpty()) {
//                            SecondaryManager.showOtherScreen(it, model)
                                secondaryScreenUI?.showDefault(model)
//                                SecondaryManagerV2.showOtherScreen(it, model)

                            } else {
//                            SecondaryManager.userInfo = model
//                                SecondaryManagerV2.userInfo = model
                                secondaryScreenUI?.showDefault(model)
                            }
                        }
                    }


                }

                btnLogin.setEnable(edtUserName.text?.trim()?.length!! > 0 && edtPassword.text?.length!! > 0)

//                val packageInfo =
//                    context?.packageManager!!.getPackageInfo(requireContext().packageName, 0)

                tvVersion.text =
                    "V${VersionHelper.getLocalVersionName(requireContext())} ${getString(R.string.version)}"
            }
            when (Locale.getDefault()) {
                Locale.CHINESE -> tvLanguages.text = getString(R.string.chinese_language)
                Locale.ENGLISH -> tvLanguages.text = getString(R.string.english_language)
                MyApplication.LOCALE_KHMER -> tvLanguages.text = getString(R.string.khmer_language)
                else -> {
                    tvLanguages.text = getString(R.string.english_language)
                }
            }
        }
    }

    private fun showAccountAndPassword() {
        binding?.apply {
            lifecycleScope.launch {
                getInstance().apply {
                    val account = getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_LOGIN_ACCOUNT, ""
                    )
                    edtUserName.setText(account)
                    edtUserName.setSelection(edtUserName.text?.length ?: 0)
                }

                getInstance().apply {
                    val password = getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_LOGIN_PASSWORD, ""
                    )
                    edtPassword.setText(password)
                    edtPassword.setSelection(edtPassword.text?.length ?: 0)
                }

                getInstance().apply {
                    val isCheck = getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_LOGIN_REMEMBER, false
                    )
                    checkboxRemember.isChecked = isCheck
                }
            }
        }
    }

    private fun showPopupWindowLanguage(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupLanguagesBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }
        popupView.tvChinese.setOnClickListener {
            setLocale(Locale.CHINESE)
            binding?.tvLanguages?.text = getString(R.string.chinese_language)
            popupWindow.dismiss()
            resetData()
        }
        popupView.tvEnglish.setOnClickListener {
            setLocale(Locale.ENGLISH)
            binding?.tvLanguages?.text = getString(R.string.english_language)
            popupWindow.dismiss()
            resetData()
        }
        popupView.tvKhmer.setOnClickListener {
            setLocale(MyApplication.LOCALE_KHMER)
            binding?.tvLanguages?.text = getString(R.string.khmer_language)
            popupWindow.dismiss()
            resetData()
        }
        context?.let {
            when (Locale.getDefault()) {
                Locale.CHINESE -> setSelectedLanguages(popupView.tvChinese, it)
                Locale.ENGLISH -> setSelectedLanguages(popupView.tvEnglish, it)
                MyApplication.LOCALE_KHMER -> setSelectedLanguages(popupView.tvKhmer, it)
                else -> {
                    setSelectedLanguages(popupView.tvEnglish, it)
                }
            }
        }
    }

    private fun resetData() {
        binding?.apply {
            edtPassword.setText("")
            edtUserName.setText("")
            layoutError.isVisible = false
        }
    }

    private fun setSelectedLanguages(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }

    private fun setLocale(locale: Locale) {
//        SecondaryManager.dismissScreen()
//        SecondaryManagerV2.dismissScreen()
        secondaryScreenUI?.dismiss()
        context?.let {
            LocaleHelper.setLocale(it, locale.language)
        }
        activity?.recreate()
    }


}