package com.metathought.food_order.casheir.utils


/**
 *<AUTHOR>
 *@time  2024/9/24
 *@desc
 **/

object TimeUtils {
    fun convertMillisecondsToTime(milliseconds: Long): String {
        val totalSeconds = milliseconds / 1000
        val seconds = totalSeconds % 60
        val totalMinutes = totalSeconds / 60
        val minutes = totalMinutes % 60
        val hours = totalMinutes / 60

        return String.format("%02d:%02d", minutes, seconds)
    }
}