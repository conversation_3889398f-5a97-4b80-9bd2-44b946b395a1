package com.metathought.food_order.casheir.data.model.base.response_model.report


/**
 *<AUTHOR>
 *@time  2024/12/4
 *@desc
 **/

data class ProductReportResponse(
    val storeName: String?,
    val startTime: String?,
    val endTime: String?,
    val printTime: String?,
    val data: ProductReportData?
)

data class ProductReportData(
    val startTime: String?,
    val endTime: String?,
    val exportTime: String?,
    val totalAmount: String?,
    val totalReceiveAmount: String?,
    val totalVat: String?,
    val totalItemNum: Int,
    val totalOrderNum: Long?,
    val salesItemOrdersDetailList: List<SalesItemOrdersDetail>?
)

data class SalesItemOrdersDetail(
    val index: Int,
    val goodId: Long?,
    val orderId: Long?,
    val itemNo: Int?,
    val itemGroupName: String?,
    val itemName: String?,
    val itemTotalPrice: String?,
    val packingFee: String?,
    val serviceCharge: String?,
    val vat: String?,
    val itemNum: Int?,
    val orderNum: Int?,
    val amountRatio: String?
)