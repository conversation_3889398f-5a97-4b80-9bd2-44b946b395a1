package com.metathought.food_order.casheir.ui.work_handover

import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.databinding.DialogShiftHandoverRecordsBinding
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.ui.adapter.ShiftHandoverRecordsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 交班记录
 */
@AndroidEntryPoint
class ShiftHandoverRecordsDialog : BaseDialogFragment() {
    private var binding: DialogShiftHandoverRecordsBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    private val viewModel: ShiftHandoverViewModel by viewModels()
    private val adapter = ShiftHandoverRecordsAdapter()


    private var startDateString: String? = null
    private var endDateString: String? = null
    private var keyword: String? = null
    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                keyword = edtSearch.getSearchContent()
                getCashRegisterHandoverLog()
            }
        } catch (e: Exception) {

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogShiftHandoverRecordsBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.cashRegisterHandoverLogState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        layoutProgressBar.isVisible = true
                }
                if (it.showEnd) {
                    layoutProgressBar.isVisible = false
                    if (it.isRefresh != false) {
                        layoutEmpty.root.setVisibleGone(true)
                        adapter.setList(arrayListOf())
                    }
                    refreshLayout.finishLoadMoreWithNoMoreData()
                }
                it.showError?.let { error ->
                    layoutProgressBar.isVisible = false
                    if (error.isNotEmpty()) Toast.makeText(context, error, Toast.LENGTH_SHORT)
                        .show()
                }

                it.showSuccess?.let { response ->
                    layoutProgressBar.isVisible = false
                    layoutEmpty.root.setVisibleGone(false)
                    if (it.isRefresh != false) {
                        response.records?.let { it1 ->
                            adapter.setList(it1)
                        }
                        refreshLayout.finishRefresh()

                    } else {
                        refreshLayout.finishLoadMore()
                        //delay for waiting hide Footer LoadSuccess
                        viewLifecycleOwner.lifecycleScope.launch {
                            delay(400)
                            response.records?.let { it1 ->
                                adapter.addList(it1)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun initData() {
        binding?.apply {
            rvShiftHandoverRecords.adapter = adapter
        }
        getCashRegisterHandoverLog()
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            tvCalendar.setOnClickListener {
                datePickerDialog()
            }
            edtSearch.setTextChangedListenerCallBack {
                postSearch(800)
            }
//            edtSearch.addTextChangedListener {
//                postSearch(800)
//            }
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getCashRegisterHandoverLog(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getCashRegisterHandoverLog(false)
                }

            })
            tvClearFilter.setOnClickListener {
                tvCalendar.text = ""
                tvCalendar.updateCalendarColor()
                edtSearch.setSearchContent("")
                edtSearch.removeFocus()
                startDateString = null
                endDateString = null
                keyword = null
                getCashRegisterHandoverLog()
            }
            adapter.onDetailClick = { log ->
                ShiftHandoverRecordsDetailDialog.showDialog(parentFragmentManager, log) {

                }
            }
//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    private fun getCashRegisterHandoverLog(isRefresh: Boolean? = null) {
        viewModel.pageCashRegisterHandoverLog(
            isRefresh = isRefresh,
            startDate = startDateString,
            endDate = endDateString,
            keyword = keyword,
        )
    }

    private fun postSearch(duration: Int) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        val constraintsBuilder = CalendarConstraints.Builder()
        // Set the minimum year
        constraintsBuilder.setStart(Calendar.getInstance().apply {
            set(Calendar.YEAR, 2024)
            set(Calendar.MONTH, Calendar.JANUARY)
        }.timeInMillis)
        val constraints = constraintsBuilder.build()
        builder.setCalendarConstraints(constraints)
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate)) + " 00:00:00"
            endDateString = sdf.format(Date(endDate)) + " 23:59:59"

            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"

            // Displaying the selected date range in the TextView
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
            //TODO 查询交班记录
            Timber.e("查询交班记录 $startDateString - $endDateString")
            getCashRegisterHandoverLog()
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    companion object {
        private const val SHIFT_HANDOVER_RECORDS_DIALOG = "SHIFT_HANDOVER_RECORDS_DIALOG"


        fun showDialog(
            fragmentManager: FragmentManager,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(SHIFT_HANDOVER_RECORDS_DIALOG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener)
            fragment.show(fragmentManager, SHIFT_HANDOVER_RECORDS_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(SHIFT_HANDOVER_RECORDS_DIALOG) as? ShiftHandoverRecordsDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
        ): ShiftHandoverRecordsDialog {
            val args = Bundle()
            val fragment = ShiftHandoverRecordsDialog()
            fragment.positiveButtonListener = iAgreeListener
            fragment.arguments = args
            return fragment
        }
    }

}
