package com.metathought.food_order.casheir.ui.order

import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.PrintTicketType
import com.metathought.food_order.casheir.constant.SingleDiscountType
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.AddOrderMoreGoodRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ConsumerPayRegisterInfo
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.PaymentRequest
import com.metathought.food_order.casheir.data.model.base.request_model.PendingRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.menu.AddTmpGoodsToCartRequest
import com.metathought.food_order.casheir.data.model.base.request_model.menu.AsyncChangeRequest
import com.metathought.food_order.casheir.data.model.base.request_model.menu.GoodsTemporaryInfo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CartConfirmPendingGoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PreSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.ReduceDiscountDetailRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateCustomerInfoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.BatchAddCart
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.SwitchTableRequest
import com.metathought.food_order.casheir.data.model.base.response_model.cart.CartInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.cart.OrderMoreDataResponse
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.Group
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.ReserveGoodDetail
import com.metathought.food_order.casheir.data.model.base.response_model.order.ReserveGoodListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.StoreIndexBaseVo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.PaymentChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.table.SwitchTableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.database.GoodsListRecord
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.database.dao.GoodsListHelper
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterTemplateHelper
import com.metathought.food_order.casheir.helper.ShoppingCartHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.CUSTOMER_INFO_REQUIRE
import com.metathought.food_order.casheir.network.ORDER_STATUS_ERROR
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.TABLE_INFO_REQUIRE
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketGoods
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import com.metathought.food_order.casheir.ui.widget.printer.LabelPrinter
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.supervisorScope
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.concurrent.ExecutionException
import javax.inject.Inject
import kotlin.coroutines.cancellation.CancellationException


@OptIn(FlowPreview::class)
@HiltViewModel
class MenuOrderViewModel @Inject
constructor(private val repository: Repository) : ViewModel() {
    private val gson: Gson by lazy { Gson() }
    val uiState get() = _uiState
    val listReserveGoods = MutableLiveData<UIGoodsListModel>()

    val listGroup = MutableLiveData<UIGroupListModel>()

    private val _uiState = MutableLiveData<UIModel>()
    private val _goodsReserve = MutableLiveData<ApiResponse<ReserveGoodDetail>>()
    val goodsReserve get() = _goodsReserve
    private var localDingingStyle: Int? = null

    private val _goodsRequestList = MutableLiveData<ArrayList<GoodsRequest>>()
    val goodsRequest get() = _goodsRequestList

    private val _availableTableList = MutableLiveData<ApiResponse<TableResponse>>()
    val availableTableList = _availableTableList

    var orderMoreID: String? = null

    val orderMoreDataResponse get() = _orderMoreDataResponse

    private val _orderMoreDataResponse = MutableLiveData<OrderMoreDataResponse>()

    private val _paymentChannel = MutableLiveData<List<PaymentChannelModel>>()
    val paymentChannel get() = _paymentChannel

    val uiLoading get() = _uiLoading
    private val _uiLoading = MutableLiveData<UILoading>()

//    private var dataVersion = 0L

    /**
     * 搜索查询流
     */
    private val _searchQueryFlow = MutableSharedFlow<String>()
//    val searchQueryFlow get() = _searchQueryFlow

    fun getDingingStyle(): Int? {
        return localDingingStyle
    }

    //前一次搜索内容  防止有内容清空时调用接口
    private var preSearchContent = ""

    init {
        // 监听搜索查询
        _searchQueryFlow
            .debounce(600)  // 防抖延迟 600 毫秒
            .distinctUntilChanged() // 过滤重复的查询
            .onEach { query ->
                try {
                    getDingingStyle()?.let { diningStyle ->
                        Timber.e("搜索 关键字  :${query}")
                        handleSearchRequest(diningStyle, query)
                    }
                } catch (e: Exception) {

                }

            }.launchIn(viewModelScope)
    }


    /**
     * 获取商品列表的主入口函数
     */
    fun getGoodsReserveList(diningStyle: Int, goodName: String, context: Context) {
        viewModelScope.launch(Dispatchers.IO + SupervisorJob()) {
            try {
                // 初始化设置
                initializeSettings(diningStyle)

                // 检查并更新通用桌信息
                checkAndUpdateUniversalTable()

                // 处理搜索或获取商品列表
                if (isSearchRequest(goodName)) {
                    handleSearchRequest(diningStyle, goodName)
                } else {
                    handleGoodsListRequest(context, diningStyle, goodName)
                }
            } catch (e: Exception) {
                Timber.e(e, "Error in getGoodsReserveList")
                emitUiState(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 初始化设置
     */
    private suspend fun initializeSettings(diningStyle: Int) {
        withContext(Dispatchers.IO) {
            PreferenceDataStoreHelper.getInstance().putPreference(
                PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_DINING_STYLE,
                diningStyle
            )
            localDingingStyle = diningStyle
//            preSearchContent = ""
        }
    }

    /**
     * 检查并更新通用桌信息
     */
    private suspend fun checkAndUpdateUniversalTable() {
        val defaultTable = PreferenceHelper.getUniversalTableInfo()
        if (defaultTable.isNullOrEmpty()) {
            supervisorScope {
                val tableResponse = async { repository.getTable("") }.await()
                if (tableResponse is ApiResponse.Success) {
                    tableResponse.data.firstOrNull { it.isUniversalQr() }?.let {
                        PreferenceHelper.setUniversalTableInfo(it.toJson())
                    }
                }
            }
        }
    }

    /**
     * 判断是否为搜索请求
     */
    private fun isSearchRequest(goodName: String): Boolean {
        return goodName.isNotEmpty() || preSearchContent.isNotEmpty()
    }

    /**
     * 处理搜索请求
     */
    private suspend fun handleSearchRequest(diningStyle: Int, goodName: String) {
        preSearchContent = goodName
        if (hasLocalGoods(diningStyle) && goodName.isNotEmpty()) {
            performLocalSearch(diningStyle, goodName)
        } else {
            loadLocalGoodsData(diningStyle, goodName)
        }
    }

    /**
     * 检查是否有本地商品数据
     */
    private fun hasLocalGoods(diningStyle: Int): Boolean {
        return GoodsHelper.getGoodsList(diningStyle).isNotEmpty()
    }

    /**
     * 显示本地数据
     */
    private suspend fun loadLocalGoodsData(
        diningStyle: Int,
        goodName: String,
    ) {
        val goodsListRecord = GoodsListHelper.get(diningStyle)
        if (GoodsHelper.getGoodsList(diningStyle).isNotEmpty() && GoodsHelper.getGroupsList(
                diningStyle
            ).isNotEmpty()
        ) {
            loadFromLocalCache(diningStyle)
        } else {
            getLoadGoodsList(goodsListRecord, diningStyle, goodName)
        }
    }

    /**
     * 执行本地搜索
     */
    private suspend fun performLocalSearch(diningStyle: Int, goodName: String) {
        withContext(Dispatchers.Default) {
            val searchResults = searchLocalGoods(diningStyle, goodName)
            updateUiWithSearchResults(searchResults, diningStyle)
        }
    }

    /**
     * 搜索本地商品
     */
    private suspend fun searchLocalGoods(
        diningStyle: Int,
        goodName: String
    ): Pair<List<BaseGoods>, List<Group>> {
        return withContext(Dispatchers.Default) {
            val allGoods = GoodsHelper.getGoodsList(diningStyle).filterIsInstance<Goods>()
            val matchedGoods = allGoods.filter {
                it.name?.uppercase()?.contains(goodName.uppercase()) == true
            }.distinctBy { it.id }

            val groupIds = matchedGoods.map { it.groupID }.distinct()
            val matchedGroups = GoodsHelper.getGroupsList(diningStyle).filter {
                groupIds.contains(it.id)
            }

            Pair(matchedGoods, matchedGroups)
        }
    }

    /**
     * 更新UI显示搜索结果
     */
    private suspend fun updateUiWithSearchResults(
        results: Pair<List<BaseGoods>, List<Group>>,
        diningStyle: Int
    ) {
        withContext(Dispatchers.Main) {
            val (goods, groups) = results
            listReserveGoods.value = UIGoodsListModel(ArrayList(goods), diningStyle)
            listGroup.value = UIGroupListModel(groups, diningStyle)

            val shoppingRecord = ShoppingHelper.getDefault(diningStyle).apply {
                ShoppingHelper.updateTotalPrice(this)
            }

            emitUiState(
                shoppingRecord = shoppingRecord,
                hideEmptyDataList = goods.isNotEmpty()
            )
        }
    }

    /**
     * 处理商品列表请求
     */
    private suspend fun handleGoodsListRequest(
        context: Context,
        diningStyle: Int,
        goodName: String
    ) {
        val goodsListRecord = GoodsListHelper.get(diningStyle)
        if (canUseLocalCache(diningStyle, goodsListRecord)) {
            Timber.e("先显示本地缓存")
            loadLocalGoodsData(diningStyle, goodName)
        } else {
            //本地没缓存才加loading
            emitUiState(ApiResponse.Loading)
        }
        fetchAndProcessNewData(diningStyle, goodName, goodsListRecord)
    }

    /**
     * 检查是否可以使用本地缓存
     */
    private fun canUseLocalCache(diningStyle: Int, goodsListRecord: GoodsListRecord?): Boolean {
        return goodsListRecord != null
    }

    /**
     * 从本地缓存加载数据
     */
    private suspend fun loadFromLocalCache(diningStyle: Int) {
        withContext(Dispatchers.Main) {
            val shoppingRecord = ShoppingHelper.getDefault(diningStyle).apply {
                ShoppingHelper.updateTotalPrice(this)
            }

            listReserveGoods.value = UIGoodsListModel(
                GoodsHelper.getGoodsList(diningStyle),
                diningStyle
            )
            listGroup.value = UIGroupListModel(
                GoodsHelper.getGroupsList(diningStyle),
                diningStyle
            )

            emitUiState(
                shoppingRecord = shoppingRecord,
                hideEmptyDataList = GoodsHelper.getGroupsList(diningStyle).isNotEmpty()
            )
        }
    }

    /**
     * 获取并处理新数据
     */
    private suspend fun fetchAndProcessNewData(
        diningStyle: Int,
        goodName: String,
        goodsListRecord: GoodsListRecord?
    ) {
        supervisorScope {
            val response = fetchGoodsList(diningStyle, goodName, goodsListRecord)
            processGoodsListResponse(response, diningStyle, goodsListRecord)
        }
    }

    /**
     * 获取商品列表数据
     */
    private suspend fun fetchGoodsList(
        diningStyle: Int,
        goodName: String,
        goodsListRecord: GoodsListRecord?
    ): ApiResponse<ReserveGoodListResponse> {
        return if (diningStyle >= TakeOutPlatformToDiningHelper.BASE_INDEX) {
            val shoppingRecord = ShoppingHelper.getDefault(diningStyle)
            repository.getTakeOutGoodsList(
                shoppingRecord.getTakeOutPlatformModel()?.id!!,
                goodName,
                goodsListRecord?.lastUpdateDate ?: ""
            )
        } else {
            repository.getGoodsListV2(
                diningStyle.toString(),
                goodName,
                goodsListRecord?.lastUpdateDate ?: ""
            )
        }
    }

    /**
     * 处理商品列表响应
     */
    private suspend fun processGoodsListResponse(
        response: ApiResponse<ReserveGoodListResponse>,
        diningStyle: Int,
        goodsListRecord: GoodsListRecord?
    ) {
        withContext(Dispatchers.Main) {
            val shoppingRecord = ShoppingHelper.getDefault(diningStyle).apply {
                ShoppingHelper.updateTotalPrice(this)
            }

            when (response) {
                is ApiResponse.Success -> {
                    Timber.e("response.data.menuChange: ${response.data.menuChange}")
                    if (goodsListRecord != null && !response.data.menuChange) {
                        emitUiState(
                            shoppingRecord = shoppingRecord,
                            localDingingStyle = diningStyle,
                            refreshEnd = true
                        )
                    } else {
                        emitUiState(
                            response,
                            shoppingRecord = shoppingRecord,
                            localDingingStyle = diningStyle
                        )
                    }
                }

                else -> {
                    emitUiState(
                        shoppingRecord = shoppingRecord,
                        localDingingStyle = diningStyle

                    )
                }
            }
        }
    }

    fun getLoadGoodsList(
        goodsListRecord: GoodsListRecord?,
        diningStyle: Int,
        goodName: String,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // 1. 解析商品信息
                val startTime = System.currentTimeMillis()
                val goodsInfo = parseGoodsInfo(goodsListRecord) ?: return@launch
                val endTime = System.currentTimeMillis()
                Timber.e("最终解析耗时 :${endTime - startTime} 毫秒")

                // 2. 更新服务费和增值税
                updateServiceCharges(goodsInfo)

                // 3. 更新用餐方式
                updateDiningStyle(diningStyle)

                // 4. 设置促销信息
                GoodsHelper.setOngoingGiftPromotionList(
                    diningStyle,
                    goodsInfo.ongoingGiftPromotionList ?: ArrayList()
                )

                // 5. 过滤和处理商品分类
                val newListCategory = processCategories(goodsInfo, goodName, diningStyle)

                // 6. 更新购物车
                val shoppingRecord = updateShoppingCart(diningStyle, newListCategory)

                // 7. 更新UI状态
                withContext(Dispatchers.Main) {
                    Timber.e("最终newListCategory分类数量 ${newListCategory?.size}")
                    listGroup.value = UIGroupListModel(newListCategory, diningStyle)
                    emitUiState(
                        shoppingRecord = shoppingRecord,
                        hideEmptyDataList = newListCategory?.isNotEmpty()
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "Error in getLoadGoodsList")
            }
        }
    }

    // 解析商品信息
    private fun parseGoodsInfo(
        goodsListRecord: GoodsListRecord?
    ): StoreIndexBaseVo? {
        val jsonObjectGoods = try {
            gson.fromJson<JsonObject>(
                goodsListRecord?.goodsInfo,
                JsonObject::class.java
            )
        } catch (e: Exception) {
            Timber.e(e, "Error parsing goods info")
            return null
        }
        Timber.e("解析商品信息")
        val goodsInfoStr =
            jsonObjectGoods.get(LocaleHelper.getLang(MyApplication.myAppInstance).uppercase())
        return try {
            val myType = object : TypeToken<StoreIndexBaseVo>() {}.type
            gson.fromJson<StoreIndexBaseVo>(goodsInfoStr, myType)
        } catch (e: Exception) {
            Timber.e(e, "Error parsing store info")
            null
        }
    }

    // 更新服务费和增值税
    private fun updateServiceCharges(goodsInfo: StoreIndexBaseVo) {
        MainDashboardFragment.CURRENT_USER?.let { user ->
            if (user.getCurrentVatPercentage() != goodsInfo.store.vatPercentage) {
                updateVatPercentage(goodsInfo.store.vatPercentage)
            }
            if (user.getCurrentServiceChargePercentage() != goodsInfo.store.serviceChargePercentage) {
                updateServicePercentage(goodsInfo.store.serviceChargePercentage)
            }
        }
    }

    // 更新用餐方式
    private suspend fun updateDiningStyle(diningStyle: Int) {
        PreferenceDataStoreHelper.getInstance().putPreference(
            PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_DINING_STYLE,
            diningStyle
        )
        localDingingStyle = diningStyle
    }

    // 处理商品分类
    private suspend fun processCategories(
        goodsInfo: StoreIndexBaseVo,
        goodName: String,
        diningStyle: Int
    ): MutableList<Group>? {
        val newListCategory = goodsInfo.groupList?.filter { it.status == 1 }?.toMutableList()
        val exitGoodsGroup = mutableListOf<Group>()
        val tempReserve = ArrayList<BaseGoods>()
        if (goodsInfo.goodsReserveList != null) {
            val jsonObjectGoods = gson.fromJson<JsonObject>(
                goodsInfo.goodsReserveList.toJson(),
                JsonObject::class.java
            )

            newListCategory?.forEach { group ->
                if (jsonObjectGoods.has(group.id)) {
                    val hasGoods = processGroupGoods(
                        jsonObjectGoods,
                        group,
                        goodName,
                        tempReserve,
                        diningStyle
                    )
                    if (hasGoods) {
                        Timber.e(" 存在分组")
                        exitGoodsGroup.add(group)
                    }
                }
            }

            // 如果是搜索,过滤重复商品
            if (goodName.isNotEmpty()) {
                val distinctGoods = tempReserve.distinctBy { if (it is Goods) it.id else "" }
                tempReserve.clear()
                tempReserve.addAll(distinctGoods)
            } else {
                // 只在非搜索状态下缓存菜品分类和列表
                GoodsHelper.setGoodsList(diningStyle, tempReserve)
                GoodsHelper.setGroupsList(diningStyle, exitGoodsGroup)
            }

            withContext(Dispatchers.Main) {
                listReserveGoods.value = UIGoodsListModel(tempReserve, diningStyle)
            }
        }
        return exitGoodsGroup
    }

    // 处理分组商品
    private fun processGroupGoods(
        jsonObject: JsonObject,
        group: Group,
        goodName: String,
        tempReserve: ArrayList<BaseGoods>,
        diningStyle: Int
    ): Boolean {
        val currentDynamicValue = jsonObject.getAsJsonArray(group.id)
        val myType = object : TypeToken<ArrayList<Goods>>() {}.type
        val list = gson.fromJson<ArrayList<Goods>>(currentDynamicValue, myType)

        val dishList = if (goodName.isEmpty()) {
            list.map { dish ->
                dish.apply {
                    groupID = group.id
                    groupName = group.name
                    header = false
                }
            }
        } else {
            list.filter { dish ->
                dish.name?.uppercase()?.contains(goodName.uppercase()) == true
            }.map { dish ->
                dish.apply {
                    groupID = group.id
                    groupName = group.name
                    header = false
                }
            }
        }

        if (dishList.isNotEmpty()) {
            Timber.e("goodName  :${goodName}")
            if (goodName.isEmpty()) {
                tempReserve.add(HeaderGoods(name = group.name, id = group.id).apply {
                    header = true
                })
            }
            tempReserve.addAll(dishList)
            return true
        }
        return false
    }

    // 更新购物车
    private fun updateShoppingCart(
        diningStyle: Int,
        newListCategory: List<Group>?
    ): ShoppingRecord {
        return ShoppingHelper.getDefault(diningStyle).apply {
            listReserveGoods.value?.let {
                ShoppingHelper.updateTotalPriceAndName(
                    this,
                    it.list?.filterIsInstance<Goods>() as ArrayList<Goods>
                )
            }
        }
    }

    //更新本地增值税，登录的时候获取一次，后面根据菜单接口返回 有变动的话就更新
    fun updateVatPercentage(vatPercentage: Int?) {
        viewModelScope.launch {
            PreferenceDataStoreHelper.getInstance().apply {
                val model = gson.fromJson(
                    getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        ""
                    ), UserLoginResponse::class.java
                )
                model.vatPercentage = vatPercentage ?: 0
                this.putPreference(
                    PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                    model.toJson()
                )
                MainDashboardFragment.CURRENT_USER = model
            }
        }
    }

    //更新本地增值税，登录的时候获取一次，后面根据菜单接口返回 有变动的话就更新
    fun updateServicePercentage(servicePercentage: Int?) {
        viewModelScope.launch {
            PreferenceDataStoreHelper.getInstance().apply {
                val model = gson.fromJson(
                    getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        ""
                    ), UserLoginResponse::class.java
                )
                model.serviceChargePercentage = servicePercentage ?: 0
                this.putPreference(
                    PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                    model.toJson()
                )
                MainDashboardFragment.CURRENT_USER = model
            }
        }
    }


    fun getGoodsReserveDetail(diningStyle: Int, goodsID: String) {
        Timber.e("diningStyle ${diningStyle}")
        viewModelScope.launch {
            _goodsReserve.value = ApiResponse.Loading
            try {
                when (diningStyle) {
                    DiningStyleEnum.DINE_IN.id,
                    DiningStyleEnum.TAKE_AWAY.id -> {
                        _goodsReserve.value = repository.getGoodsDetail(goodsID)
                    }

                    DiningStyleEnum.PRE_ORDER.id -> {
                        _goodsReserve.value = repository.getGoodsReserveDetail(goodsID)
                    }
                }

            } catch (e: Exception) {
                _goodsReserve.value = ApiResponse.Error(e.message)
            }
        }
    }

    fun getSubGoods(goodsId: String) {
        viewModelScope.launch {
            val localDingingStyle = PreferenceDataStoreHelper.getInstance().getFirstPreference(
                PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_DINING_STYLE,
                -1
            )
            val subListByGoods = ShoppingHelper.getSubListByGoods(goodsId, localDingingStyle)
            withContext(Dispatchers.Main) {
                _goodsRequestList.value = subListByGoods
            }
        }
    }


    /**
     * 是否请求共享接口
     *
     */
    private fun isRequestSharedInterface(dingingStyle: Int?): Boolean {
        val isShareStore = MainDashboardFragment.CURRENT_USER?.isTableService
        val record = ShoppingHelper.get(dingingStyle!!)
        /**
         * 共享门店 且 桌台id不为空 且 非通用桌 且 非外卖平台
         */
        return isShareStore == true && !record?.tableUuid.isNullOrEmpty() && record?.isUniversalQr() != true && record?.takeOutPlatformModel.isNullOrEmpty()
    }


    fun plus(
        goods: Goods,
        feedList: ArrayList<Feed>? = null,
        goodsTagItemList: ArrayList<GoodsTagItem>? = null,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        cartCount: Int = 1,
        singleDiscountGoods: SingleDiscountGoods?,
        note: String?,
        goodsReq: GoodsRequest? = null,
    ) {
        //Determine whether it is a shared store
        viewModelScope.launch(Dispatchers.IO) {
            goods.orderMealSetGoodsDTOList = orderMealSetGoodList
            Timber.d("FoodDetailDialog: ${orderMealSetGoodList?.toJson()}")
            val dingingStyle = getDingingStyle()!!
            val isToBeWeighed = if (!orderMealSetGoodList.isNullOrEmpty()) {
                orderMealSetGoodList.firstOrNull { it.isToBeWeighed() } != null
            } else {
                goods.isToBeWeighed()
            }
            Timber.d("isToBeWeighed:$isToBeWeighed isMealSet:${!orderMealSetGoodList.isNullOrEmpty()}")
//            val isToBeWeighed = goodsReq?.isToBeWeighed() ?: goods.isToBeWeighed()
            if (isToBeWeighed) {
                if (cartCount > 1) {
                    //称重菜一次添加多个需要才分开
                    for (i in 0 until cartCount) {
                        plus(
                            goods, feedList, goodsTagItemList, orderMealSetGoodList,
                            1, singleDiscountGoods, note
                        )
                    }
                    return@launch
                }
                //t通过+号增加的称重菜需要清除重量，套餐选中的重量在
                if (!orderMealSetGoodList.isNullOrEmpty()) {
                    orderMealSetGoodList.forEach {
                        Timber.d("uuid:${it.uuid}")
                        if (it.isToBeWeighed()) {
                            //
                            if (it.uuid != null) {
                                //点加号增加的套餐是从旧的创建的uuid不为null,要把旧的重量清除
                                it.weight = null
                                it.weighingCompleted = null
                            }
                            it.uuid = HashHelper.generateWeightGoodsUUID()
                        }
                    }
                } else {
                    goods.uuid = HashHelper.generateWeightGoodsUUID()
                    goods.weight = null
                    goods.weighingCompleted = null
                }
            }
            val startTime = System.currentTimeMillis()
            val shoppingRecord = ShoppingHelper.plusAndAdd(
                goods,
                localDingingStyle = dingingStyle,
                feedList = feedList,
                goodsTagItemList = goodsTagItemList,
                orderMealSetGoodList = orderMealSetGoodList,
                cartCount = cartCount,
                singleDiscountGoods = singleDiscountGoods,
                note = note
            )
//            Log.e("购物车耗时55555", " ${System.currentTimeMillis() - startTime} ms")
            //判断当前购物车内有参与活动的商品
            val hasActivityLabelList = shoppingRecord.getGoodsVoList()
                .filter { !it.goods?.activityLabels.isNullOrEmpty() }
            //有单品折扣的菜品列表
            val hasSingleDiscountList = shoppingRecord.getGoodsVoList()
                .filter { it.singleDiscountGoods != null }
            if (hasActivityLabelList.isNotEmpty() && hasSingleDiscountList.isNotEmpty()) {
                //如果购物车里有参与折扣的商品 ，要清空单品折扣
                hasSingleDiscountList.forEach {
                    setSingleGoodDiscount(
                        it.singleDiscountGoods,
                        null,
                        it,
                        isOnlyModify = false
                    )
                }
                val record = ShoppingHelper.get(dingingStyle)
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = record, goods = goods)
                }
            } else {
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shoppingRecord, goods = goods)
                }
            }
//            Log.e("购物车耗时66666", " ${System.currentTimeMillis() - startTime} ms")

            // 获取当前线程名称
            if (isRequestSharedInterface(dingingStyle)) {
//                val goodRequest = GoodsRequest(
//                    num = cartCount,
//                    feedInfoList = feedList,
//                    goodsTagItems = goodsTagItemList,
//                    orderMealSetGoodList = orderMealSetGoodList,
//                    goods = goods,
//                    note = note,
//                )
//                val requestObject =
//                    ShoppingCartHelper.getAddToCartRequest(goodRequest, dingingStyle)
//
//                val response = withContext(Dispatchers.IO) {
//                    repository.addToCart(requestObject)
//                }
//                if (response is ApiResponse.Success) {
//                    updateShoppingRecord(response.data, dingingStyle)
//                } else if (response is ApiResponse.Error) {
//                    emitUiState(errorResponse = response)
//                }
                asyncGoodList(goods)
            }
        }
    }

    /**
     * 批量添加临时菜品到购物车
     * @param goods
     */
    fun addTmpGoodToCart(goods: List<Goods>, onSuccess: (() -> Unit)? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val dingingStyle = getDingingStyle()!!
                goods.forEach {
                    ShoppingHelper.plusAndAdd(
                        it,
                        localDingingStyle = dingingStyle,
                        feedList = null,
                        goodsTagItemList = null,
                        cartCount = it.totalCount ?: 0,
                        singleDiscountGoods = null,
                        note = null
                    )
                }
                val record = ShoppingHelper.get(dingingStyle)
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = record)
                }
                onSuccess?.invoke()
                withContext(Dispatchers.IO) {
                    if (isRequestSharedInterface(dingingStyle)) {
                        val record = ShoppingHelper.get(dingingStyle)
                        val request = AddTmpGoodsToCartRequest()
                        request.tableUuid = record?.tableUuid
                        request.diningStyle = record?.diningStyle
                        request.goodsTemporaryInfoDTOList = goods.map {
                            GoodsTemporaryInfo(
                                goodsId = it.id.toLong(),
                                num = it.totalCount ?: 0
                            )
                        }
                        val response = repository.temporaryBatchAdd(request)
                        if (response is ApiResponse.Success) {
//                        getCartInfo()
//                        updateShoppingRecord(response.data, dingingStyle)
//                        onSuccess?.invoke()
                        } else if (response is ApiResponse.Error) {
                            withContext(Dispatchers.Main) {
                                emitUiState(errorResponse = response)
                            }
                        }
                    }
                }
            } catch (e: Exception) {

            }
        }
    }


    fun updateCustomer(reserveTableRequest: ReserveTableRequest) {
        viewModelScope.launch {
            getDingingStyle()?.let { localDingingStyle ->
                val record = ShoppingHelper.get(localDingingStyle)
                record?.let {
                    if (record.isOrderMore == true) {
                        //如果是加购 跟新订单信息
                        updateCustomerInfo(
                            reserveTableRequest,
                            record.orderMoreID
                        )
                    } else {
                        val shoppingRecord =
                            ShoppingHelper.updateCustomer(localDingingStyle, reserveTableRequest)
                        withContext(Dispatchers.Main) {
                            emitUiState(shoppingRecord = shoppingRecord)
                        }
                    }
                }
            }
        }
    }

    /**
     * 其他端更新订单顾客信息
     *
     * @param updateCustomerInfoRequest
     */
    fun updateCustomerFromWs(updateCustomerInfoRequest: UpdateCustomerInfoRequest) {
        viewModelScope.launch {
            getDingingStyle()?.let { localDingingStyle ->
                val record = ShoppingHelper.get(localDingingStyle)
                record?.let {
                    if (record.isOrderMore == true && record.orderMoreID == updateCustomerInfoRequest.orderId) {
                        val shoppingRecord =
                            ShoppingHelper.updateCustomer(
                                localDingingStyle,
                                ReserveTableRequest(
                                    diningNumber = updateCustomerInfoRequest.diningNumber,
                                    diningTime = updateCustomerInfoRequest.diningTime ?: "",
                                    name = updateCustomerInfoRequest.name ?: "",
                                    areaCode = updateCustomerInfoRequest.areaCode ?: "",
                                    mobile = updateCustomerInfoRequest.mobile ?: "",
                                    tableId = ""
                                )
                            )
                        withContext(Dispatchers.Main) {
                            emitUiState(shoppingRecord = shoppingRecord)
                        }
                    }
                }
            }

        }
    }


    fun updateSelectTable(it: TableResponseItem?) {
        viewModelScope.launch {
            getDingingStyle()?.let { localDingingStyle ->
                val shoppingRecord = ShoppingHelper.updateSelectTable(it, localDingingStyle)
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shoppingRecord)
                }
                getTableCustomerInfo(it?.uuid, shoppingRecord)
            }

        }
    }

    fun updateSelectTable(uuid: String?, tableName: String?, tableType: Int?, dingingStyle: Int) {
        viewModelScope.launch {
            val shoppingRecord =
                ShoppingHelper.updateSelectTable(uuid, tableName, tableType, dingingStyle)
            withContext(Dispatchers.Main) {
                emitUiState(shoppingRecord = shoppingRecord)
            }
        }
    }


    fun removeAllGoods() {
        viewModelScope.launch {
            getDingingStyle()?.let {
                val shareRecord = ShoppingHelper.get(it)
                if (shareRecord?.isOrderMore == false) {
                    //加购的时候清空购物车不清优惠券
                    ShoppingHelper.clearCoupon(it)
                }
                ShoppingHelper.clearNote(it)
                val shoppingRecord = ShoppingHelper.del(it)
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shoppingRecord, removeAll = true)
                }
                withContext(Dispatchers.IO) {
                    if (isRequestSharedInterface(it)) {
                        val tableUUID = shareRecord?.tableUuid ?: ""
                        if (tableUUID.isNotEmpty()) {
                            val response = repository.emptyFromCart(
                                it, tableUUID
                            )
                            if (response is ApiResponse.Success) {
                                Timber.e("removeAllGoods")
                                val shoppingRecord = ShoppingHelper.del(it)
                                withContext(Dispatchers.Main) {
                                    emitUiState(shoppingRecord = shoppingRecord, removeAll = true)
                                }
                            }
                        }
                    }
                }
            }

        }
    }


    fun sub(
        goods: Goods, feedList: ArrayList<Feed>? = null,
        goodsTagItemList: ArrayList<GoodsTagItem>? = null,
        orderMealSetGoodList: List<OrderMealSetGood>? = null,
        singleDiscountGoods: SingleDiscountGoods?,
        note: String?,
    ) {
        viewModelScope.launch {
            getDingingStyle()?.let {
                val shoppingRecord =
                    ShoppingHelper.subAndDel(
                        goods,
                        localDingingStyle = it,
                        feedList = feedList,
                        goodsTagItemList = goodsTagItemList,
                        orderMealSetGoodList = orderMealSetGoodList,
                        singleDiscountGoods = singleDiscountGoods,
                        note = note,
                    )
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shoppingRecord, goods = goods)
                }
                withContext(Dispatchers.IO) {
                    if (isRequestSharedInterface(it)) {
//                        if (goods.cartsId == null) {
                        asyncGoodList(goods)
//                        } else {
//                            val response = repository.deductFromCart(
//                                it,
//                                ShoppingHelper.get(it)?.tableUuid ?: "",
//                                goods.id, goods.cartsId
//                            )
//                            if (response is ApiResponse.Success) {
//                                updateShoppingRecord(response.data, it)
//                            }
//                        }
                    }
                }
            }
        }
    }

    // 用于跟踪每个商品ID的最后调用时间
    private val asyncGoodListTimers = mutableMapOf<String, Long>()
    private val asyncGoodListJobs = mutableMapOf<String, Job>()

    private fun asyncGoodList(goods: Goods?) {
        if (goods == null || goods.id.isEmpty()) return

        val goodId = goods.id
        val currentTime = System.currentTimeMillis()
        val lastCallTime = asyncGoodListTimers[goodId] ?: 0L

        // 取消该商品ID的待处理任务
        asyncGoodListJobs[goodId]?.cancel()
        Timber.e("时间差  ${currentTime - lastCallTime}")
        asyncGoodListJobs[goodId] = viewModelScope.launch {
            // 等待剩余时间
            delay(400)
            // 执行并更新时间戳
            executeAsyncGoodList(goods)
            // 执行后移除任务引用
            asyncGoodListJobs.remove(goodId)
        }
        // 只有当距离上次调用超过500毫秒才立即执行
//        if (currentTime - lastCallTime < 500) {
//            // 否则安排延迟调用
////            asyncGoodListJobs[goodId] = viewModelScope.launch {
////                delay(500 - (currentTime - lastCallTime))
////                executeAsyncGoodList(goods)
////                // 执行后移除任务引用
////                asyncGoodListJobs.remove(goodId)
////            }
//            asyncGoodListJobs[goodId] = viewModelScope.launch {
//                // 等待剩余时间
//                delay(500)
//                // 执行并更新时间戳
//                executeAsyncGoodList(goods)
//                // 执行后移除任务引用
//                asyncGoodListJobs.remove(goodId)
//            }
//        } else {
//            // 立即执行
//            viewModelScope.launch {
//                executeAsyncGoodList(goods)
//            }
//        }

        // 更新该商品ID的最后调用时间
//        asyncGoodListTimers[goodId] = currentTime
    }

    private fun executeAsyncGoodList(goods: Goods?) {
        viewModelScope.launch(Dispatchers.IO) {
            getDingingStyle()?.let {
                val shoppingRecord = ShoppingHelper.get(it)
                var dataVersion = PreferenceHelper.getCartDataVersion()
                dataVersion += 1
                PreferenceHelper.setCartDataVersion(dataVersion)
                val request = AsyncChangeRequest(
                    tableUuid = shoppingRecord?.tableUuid ?: "",
                    goodsId = goods?.id,
                    diningStyle = shoppingRecord?.diningStyle,
                    addGoodsToCartDTOList = (shoppingRecord?.getGoodsVoList()
                        ?: listOf()).filter { it.goods?.id == goods?.id }.map {
                        ShoppingCartHelper.getAddToCartRequest(
                            it,
                            shoppingRecord?.diningStyle
                        )
                    },
                    dataVersion = dataVersion
                )
                if (shoppingRecord?.takeOutPlatformModel == null) {
                    request.deliveryPlatformId =
                        shoppingRecord?.getTakeOutPlatformModel()?.id
                }
                val response = repository.asyncChange(
                    request
                )
                if (response is ApiResponse.Success) {
                    updateShoppingRecord(response.data, it)
                } else if (response is ApiResponse.Error) {
                    emitUiState(errorResponse = response)
                }
            }
        }
    }
//    private fun asyncGoodList(goods: Goods?) {
//        viewModelScope.launch {
//            getDingingStyle()?.let {
//                val shoppingRecord = ShoppingHelper.get(it)
//                var dataVersion = PreferenceHelper.getCartDataVersion()
//                dataVersion += 1
//                PreferenceHelper.setCartDataVersion(dataVersion)
//                val request = AsyncChangeRequest(
//                    tableUuid = shoppingRecord?.tableUuid ?: "",
//                    goodsId = goods?.id,
//                    diningStyle = shoppingRecord?.diningStyle,
//                    addGoodsToCartDTOList = (shoppingRecord?.getGoodsVoList()
//                        ?: listOf()).filter { it.goods?.id == goods?.id }.map {
//                        ShoppingCartHelper.getAddToCartRequest(
//                            it,
//                            shoppingRecord?.diningStyle
//                        )
//                    },
//                    dataVersion = dataVersion
//                )
//                if (shoppingRecord?.takeOutPlatformModel == null) {
//                    request.deliveryPlatformId =
//                        shoppingRecord?.getTakeOutPlatformModel()?.id
//                }
//                val response = repository.asyncChange(
//                    request
//                )
//                if (response is ApiResponse.Success) {
//                    updateShoppingRecord(response.data, it)
//                } else if (response is ApiResponse.Error) {
//                    emitUiState(errorResponse = response)
//                }
//            }
//        }
//    }

    fun parseListGood(
        jsonObject: JsonObject,
        listCategory: List<Group>?,
        goodName: String? = "",
        diningStyle: Int
    ): MutableList<Group> {
        //存在菜品的分组
        val exitGoodsGroup = mutableListOf<Group>()
        viewModelScope.launch {
            var tempReserve: ArrayList<BaseGoods> = arrayListOf()

            for (group in listCategory ?: listOf()) {
                val startTime = System.currentTimeMillis()
                if (jsonObject.has(group.id)) {

                    val currentDynamicValue = jsonObject.getAsJsonArray(group.id)


                    val head = HeaderGoods(name = group.name, id = group.id).apply {
                        header = true
                    }

                    val myType = object : TypeToken<ArrayList<Goods>>() {}.type
                    val dishList = mutableListOf<Goods>()
                    val startTime = System.currentTimeMillis()

                    /**
                     * 这段解析会耗时 需要想办法处理一下
                     */
                    val list = gson.fromJson<ArrayList<Goods>>(currentDynamicValue, myType)

                    val endTime = System.currentTimeMillis()
                    Timber.e("${group.id} 解析耗时: ${endTime - startTime}")
                    val startTime1 = System.currentTimeMillis()
                    for (dish in list) {
                        dish.groupID = group.id
                        dish.groupName = group.name
                        dish.header = false
                        if (goodName.isNullOrEmpty()) {
                            dishList.add(dish)
                        } else {
                            if (dish.name?.uppercase()?.contains(goodName.uppercase()) == true) {
                                dishList.add(dish)
                            }
                        }
                    }
                    val endTime2 = System.currentTimeMillis()
                    Timber.e("${group.id} 循环耗时: ${endTime2 - startTime1}")

                    if (goodName.isNullOrEmpty()) {
                        tempReserve.add(head)
                        tempReserve.addAll(dishList)
                        exitGoodsGroup.add(group)
                    } else {
                        if (dishList.isNotEmpty()) {
                            /**
                             * 如果是搜索就不菜单gridview不显示分组
                             */
//                        tempReserve.add(head)
                            tempReserve.addAll(dishList)
                            exitGoodsGroup.add(group)
                        }
                    }
                }
                val endTime = System.currentTimeMillis()
                Timber.e("菜单处理时长 ${group.id}: ${endTime - startTime}")
            }

            if (goodName.isNullOrEmpty()) {
                GoodsHelper.setGoodsList(diningStyle, tempReserve)
                GoodsHelper.setGroupsList(diningStyle, exitGoodsGroup)
            } else {
                //如果是搜索 就过滤一下相同菜品
                tempReserve = ArrayList(tempReserve.distinctBy { if (it is Goods) it.id else "" })
            }

            withContext(Dispatchers.Main) {
                listReserveGoods.value = UIGoodsListModel(tempReserve, diningStyle)
            }
        }
        return exitGoodsGroup
    }

    /**
     * 设置单品折扣减免
     *
     *  * 该方法用于处理商品的单品折扣设置，支持以下功能：
     *  * 1. 比较新旧折扣信息，相同则不做处理
     *  * 2. 根据折扣数量拆分商品（带折扣部分和不带折扣部分）
     *  * 3. 智能合并相同商品，避免购物车中出现重复项
     *  * 4. 处理固定减免类型的折扣值合并
     *
     * @param oldSingleDiscount  旧的折扣信息
     * @param singleDiscount  新的折扣信息
     * @param goodsRequest 当前选中的菜品
     * @param isOnlyModify 是否只做修改不做视图更新
     */
    fun setSingleGoodDiscount(
        oldSingleDiscount: SingleDiscountGoods?,
        singleDiscount: SingleDiscountGoods?,
        goodsRequest: GoodsRequest,
        isOnlyModify: Boolean? = false,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            // 判断新旧折扣信息是否相同
            if (isSameDiscount(oldSingleDiscount, singleDiscount)) {
                Timber.e("新旧折扣信息相同，无需修改")
                return@launch
            }

            // 判断新折扣是否有效，如果无效则按清空处理
            val effectiveSingleDiscount =
                if (singleDiscount?.isSetSingleItemDiscount() == true) singleDiscount else null

            Timber.e("修改前的hashkey ${oldSingleDiscount?.goodsHashKey}")
            Timber.e("要修改的 singleDiscount json串${effectiveSingleDiscount?.toJson()}")

            // 获取当前购物车
            var shareRecord = ShoppingHelper.get(getDingingStyle()!!)
            if (shareRecord == null) {
                return@launch
            }

            // 计算新的折扣hashkey
            val newDiscountHashKey = if (effectiveSingleDiscount != null) {
                HashHelper.getHash(
                    goodsRequest.feedInfoList,
                    goodsRequest.goodsTagItems,
                    goodsRequest.orderMealSetGoodList,
                    goodsRequest.goods!!.id,
                    effectiveSingleDiscount,
                    goodsPriceKey = goodsRequest.getGoodPriceKey(),
                    note = goodsRequest.note,
                    uuid = goodsRequest.goods?.uuid ?: ""
                )
            } else null

            // 计算新的无折扣hashkey
            val newNoDiscountHashKey = HashHelper.getHash(
                goodsRequest.feedInfoList,
                goodsRequest.goodsTagItems,
                goodsRequest.orderMealSetGoodList,
                goodsRequest.goods!!.id,
                null,
                goodsPriceKey = goodsRequest.getGoodPriceKey(),
                note = goodsRequest.note,
                uuid = goodsRequest.goods?.uuid ?: ""
            )

            Timber.e("新的折扣hashkey: $newDiscountHashKey")
            Timber.e("新的无折扣hashkey: $newNoDiscountHashKey")

            // 查找购物车中是否已存在相同hashkey的商品
            val existDiscountItem = if (newDiscountHashKey != null) {
                shareRecord.getGoodsVoList().firstOrNull {
                    it.getHash() == newDiscountHashKey && it.getHash() != goodsRequest.getHash()
                }
            } else null

            val existNoDiscountItem = shareRecord.getGoodsVoList().firstOrNull {
                it.getHash() == newNoDiscountHashKey && it.getHash() != goodsRequest.getHash()
            }

            // 尝试直接修改原商品的折扣信息
            val originalItemIndex = shareRecord.getGoodsVoList().indexOfFirst {
                it.getHash() == goodsRequest.getHash()
            }
            Timber.e("originalItemIndex: $originalItemIndex")
            if (originalItemIndex != -1) {
                // 找到原商品，准备修改折扣信息
                val originalItem = shareRecord.getGoodsVoList()[originalItemIndex]

                // 计算新的数量分配
                val totalNum = originalItem.num ?: 0
                val discountNum = effectiveSingleDiscount?.num ?: 0
                val remainingNum = totalNum - discountNum

                if (discountNum > 0 && remainingNum >= 0) {
                    // 如果有折扣部分且剩余数量合法
                    if (remainingNum > 0) {
                        // 需要拆分为带折扣和不带折扣两部分

                        // 查找后续相同商品并叠加数量
                        if (existDiscountItem != null) {
                            // 如果存在相同的折扣商品，合并到已存在的折扣商品
                            Timber.e("存在相同的折扣商品，合并到已存在的折扣商品")

                            // 如果是固定减免类型，需要合并减免值
                            val mergedSingleDiscountGoods =
                                if (effectiveSingleDiscount?.type == SingleDiscountType.FIXED_AMOUNT.id &&
                                    existDiscountItem.singleDiscountGoods?.type == SingleDiscountType.FIXED_AMOUNT.id
                                ) {

                                    // 创建新的折扣对象，合并减免值
                                    effectiveSingleDiscount.clone().apply {
                                        // 合并销售价减免
                                        if (this.saleReduce != null && existDiscountItem.singleDiscountGoods?.saleReduce != null) {
                                            this.saleReduce =
                                                this.saleReduce!! + existDiscountItem.singleDiscountGoods?.saleReduce!!
                                        }

                                        // 合并会员价减免
                                        if (this.vipReduce != null && existDiscountItem.singleDiscountGoods?.vipReduce != null) {
                                            this.vipReduce =
                                                this.vipReduce!! + existDiscountItem.singleDiscountGoods?.vipReduce!!
                                        }

                                        // 更新数量
                                        this.num = (this.num
                                            ?: 0) + (existDiscountItem.singleDiscountGoods?.num
                                            ?: 0)
                                    }
                                } else {
                                    // 非固定减免类型，使用现有折扣
                                    existDiscountItem.singleDiscountGoods
                                }

                            // 更新已存在的折扣商品数量
                            shareRecord = ShoppingHelper.updateGoodsNum(
                                existDiscountItem.goods!!,
                                localDingingStyle = shareRecord.diningStyle!!,
                                goodsTagItemList = existDiscountItem.goodsTagItems,
                                feedList = existDiscountItem.feedInfoList,
                                orderMealSetGoodList = existDiscountItem.orderMealSetGoodList,
                                singleDiscountGoods = mergedSingleDiscountGoods,
                                note = existDiscountItem.note,
                                cartCount = (existDiscountItem.num ?: 0) + discountNum
                            )

                            // 从原商品中扣除相应数量
                            if (remainingNum > 0) {
                                // 更新原商品为不带折扣部分
                                shareRecord = ShoppingHelper.updateGoodsSingleDiscount(
                                    shareRecord.diningStyle!!,
                                    goodsRequest.copy(num = remainingNum),
                                    remainingNum,
                                    null
                                )
                            } else {
                                // 原商品数量为0，删除
                                shareRecord = ShoppingHelper.subAndDel(
                                    goodsRequest.goods!!,
                                    localDingingStyle = shareRecord.diningStyle!!,
                                    feedList = goodsRequest.feedInfoList,
                                    goodsTagItemList = goodsRequest.goodsTagItems,
                                    orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                                    cartCount = totalNum, // 传入原商品总数量，表示全部减少
                                    singleDiscountGoods = goodsRequest.singleDiscountGoods,
                                    note = goodsRequest.note
                                )
                            }
                        } else if (existNoDiscountItem != null) {
                            // 如果存在相同的无折扣商品，合并到已存在的无折扣商品
                            Timber.e("存在相同的无折扣商品，合并到已存在的无折扣商品")

                            // 先更新原商品为带折扣部分
                            shareRecord = ShoppingHelper.updateGoodsSingleDiscount(
                                shareRecord.diningStyle!!,
                                goodsRequest.copy(num = discountNum),
                                discountNum,
                                effectiveSingleDiscount
                            )

                            // 更新已存在的无折扣商品数量
                            shareRecord = ShoppingHelper.updateGoodsNum(
                                existNoDiscountItem.goods!!,
                                localDingingStyle = shareRecord?.diningStyle!!,
                                goodsTagItemList = existNoDiscountItem.goodsTagItems,
                                feedList = existNoDiscountItem.feedInfoList,
                                orderMealSetGoodList = existNoDiscountItem.orderMealSetGoodList,
                                singleDiscountGoods = null,
                                note = existNoDiscountItem.note,
                                cartCount = (existNoDiscountItem.num ?: 0) + remainingNum
                            )
                        } else {
                            // 不存在相同的商品，直接更新原商品
                            Timber.e("不存在相同的商品，直接更新原商品")

                            // 先更新原商品为带折扣部分
                            shareRecord = ShoppingHelper.updateGoodsSingleDiscount(
                                shareRecord.diningStyle!!,
                                goodsRequest.copy(num = discountNum),
                                discountNum,
                                effectiveSingleDiscount
                            )

                            // 再添加不带折扣部分
                            shareRecord = ShoppingHelper.plusAndAdd(
                                goodsRequest.goods!!,
                                localDingingStyle = shareRecord?.diningStyle!!,
                                goodsTagItemList = goodsRequest.goodsTagItems,
                                feedList = goodsRequest.feedInfoList,
                                orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                                singleDiscountGoods = null,
                                note = goodsRequest.note,
                                cartCount = remainingNum
                            )
                        }
                    } else {
                        // 全部是折扣部分
                        if (existDiscountItem != null) {
                            // 如果存在相同的折扣商品，合并到已存在的折扣商品
                            Timber.e("全部是折扣部分，存在相同的折扣商品，合并到已存在的折扣商品")

                            // 如果是固定减免类型，需要合并减免值
                            val mergedSingleDiscountGoods =
                                if (effectiveSingleDiscount?.type == SingleDiscountType.FIXED_AMOUNT.id &&
                                    existDiscountItem.singleDiscountGoods?.type == SingleDiscountType.FIXED_AMOUNT.id
                                ) {

                                    // 创建新的折扣对象，合并减免值
                                    effectiveSingleDiscount.clone().apply {
                                        // 合并销售价减免
                                        if (this.saleReduce != null && existDiscountItem.singleDiscountGoods?.saleReduce != null) {
                                            this.saleReduce =
                                                this.saleReduce!! + existDiscountItem.singleDiscountGoods?.saleReduce!!
                                        }

                                        // 合并会员价减免
                                        if (this.vipReduce != null && existDiscountItem.singleDiscountGoods?.vipReduce != null) {
                                            this.vipReduce =
                                                this.vipReduce!! + existDiscountItem.singleDiscountGoods?.vipReduce!!
                                        }

                                        // 更新数量
                                        this.num = (this.num
                                            ?: 0) + (existDiscountItem.singleDiscountGoods?.num
                                            ?: 0)
                                    }
                                } else {
                                    // 非固定减免类型，使用现有折扣
                                    existDiscountItem.singleDiscountGoods
                                }

                            // 更新已存在的折扣商品数量
                            shareRecord = ShoppingHelper.updateGoodsNum(
                                existDiscountItem.goods!!,
                                localDingingStyle = shareRecord.diningStyle!!,
                                goodsTagItemList = existDiscountItem.goodsTagItems,
                                feedList = existDiscountItem.feedInfoList,
                                orderMealSetGoodList = existDiscountItem.orderMealSetGoodList,
                                singleDiscountGoods = mergedSingleDiscountGoods,
                                note = existDiscountItem.note,
                                cartCount = (existDiscountItem.num ?: 0) + totalNum
                            )

                            // 原商品数量为0，删除
                            shareRecord = ShoppingHelper.subAndDel(
                                goodsRequest.goods!!,
                                localDingingStyle = shareRecord.diningStyle!!,
                                feedList = goodsRequest.feedInfoList,
                                goodsTagItemList = goodsRequest.goodsTagItems,
                                orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                                cartCount = totalNum, // 传入原商品总数量，表示全部减少
                                singleDiscountGoods = goodsRequest.singleDiscountGoods,
                                note = goodsRequest.note
                            )
                        } else {
                            // 不存在相同的折扣商品，直接修改原商品
                            Timber.e("全部是折扣部分，不存在相同的折扣商品，直接修改原商品")
                            shareRecord = ShoppingHelper.updateGoodsSingleDiscount(
                                shareRecord.diningStyle!!,
                                goodsRequest,
                                discountNum,
                                effectiveSingleDiscount
                            )
                        }
                    }
                } else if (discountNum <= 0) {
                    // 没有折扣部分
                    if (existNoDiscountItem != null) {
                        // 如果存在相同的无折扣商品，合并到已存在的无折扣商品
                        Timber.e("没有折扣部分，存在相同的无折扣商品，合并到已存在的无折扣商品")

                        // 更新已存在的无折扣商品数量
                        shareRecord = ShoppingHelper.updateGoodsNum(
                            existNoDiscountItem.goods!!,
                            localDingingStyle = shareRecord.diningStyle!!,
                            goodsTagItemList = existNoDiscountItem.goodsTagItems,
                            feedList = existNoDiscountItem.feedInfoList,
                            orderMealSetGoodList = existNoDiscountItem.orderMealSetGoodList,
                            singleDiscountGoods = null,
                            note = existNoDiscountItem.note,
                            cartCount = (existNoDiscountItem.num ?: 0) + totalNum
                        )

                        // 原商品数量为0，删除
                        shareRecord = ShoppingHelper.subAndDel(
                            goodsRequest.goods!!,
                            localDingingStyle = shareRecord.diningStyle!!,
                            feedList = goodsRequest.feedInfoList,
                            goodsTagItemList = goodsRequest.goodsTagItems,
                            orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                            cartCount = totalNum, // 传入原商品总数量，表示全部减少
                            singleDiscountGoods = goodsRequest.singleDiscountGoods,
                            note = goodsRequest.note
                        )
                    } else {
                        // 不存在相同的无折扣商品，直接修改原商品
                        Timber.e("没有折扣部分，不存在相同的无折扣商品，直接修改原商品")
                        shareRecord = ShoppingHelper.updateGoodsSingleDiscount(
                            shareRecord.diningStyle!!,
                            goodsRequest,
                            totalNum,
                            null
                        )
                    }
                } else {
                    // 折扣数量大于总数量，错误情况，不处理
                    Timber.e("折扣数量大于总数量，错误情况")
                }
            } else {
                // 找不到原商品，可能是hashkey已经变了，使用传统的删除再添加方式
                Timber.e("找不到原商品，使用传统的删除再添加方式")

                // 计算剩余数量（不带折扣的部分）
                val remainingNum = (goodsRequest.num ?: 0) - (effectiveSingleDiscount?.num ?: 0)

                // 处理带折扣的部分
                if (effectiveSingleDiscount != null && (effectiveSingleDiscount.num ?: 0) > 0) {
                    if (existDiscountItem != null) {
                        // 如果存在相同的折扣商品，合并到已存在的折扣商品
                        Timber.e("找到相同hashkey的折扣商品，合并数量")

                        // 如果是固定减免类型，需要合并减免值
                        val mergedSingleDiscountGoods =
                            if (effectiveSingleDiscount.type == SingleDiscountType.FIXED_AMOUNT.id &&
                                existDiscountItem.singleDiscountGoods?.type == SingleDiscountType.FIXED_AMOUNT.id
                            ) {

                                // 创建新的折扣对象，合并减免值
                                effectiveSingleDiscount.clone().apply {
                                    // 合并销售价减免
                                    if (this.saleReduce != null && existDiscountItem.singleDiscountGoods?.saleReduce != null) {
                                        this.saleReduce =
                                            this.saleReduce!! + existDiscountItem.singleDiscountGoods?.saleReduce!!
                                    }

                                    // 合并会员价减免
                                    if (this.vipReduce != null && existDiscountItem.singleDiscountGoods?.vipReduce != null) {
                                        this.vipReduce =
                                            this.vipReduce!! + existDiscountItem.singleDiscountGoods?.vipReduce!!
                                    }

                                    // 更新数量
                                    this.num =
                                        (this.num
                                            ?: 0) + (existDiscountItem.singleDiscountGoods?.num
                                            ?: 0)
                                }
                            } else {
                                // 非固定减免类型，使用现有折扣
                                existDiscountItem.singleDiscountGoods
                            }

                        // 更新已存在的折扣商品数量
                        shareRecord = ShoppingHelper.updateGoodsNum(
                            existDiscountItem.goods!!,
                            localDingingStyle = shareRecord.diningStyle!!,
                            goodsTagItemList = existDiscountItem.goodsTagItems,
                            feedList = existDiscountItem.feedInfoList,
                            orderMealSetGoodList = existDiscountItem.orderMealSetGoodList,
                            singleDiscountGoods = mergedSingleDiscountGoods,
                            note = existDiscountItem.note,
                            cartCount = (existDiscountItem.num ?: 0) + (effectiveSingleDiscount.num
                                ?: 0)
                        )
                    } else {
                        // 添加新的折扣商品
                        Timber.e("未找到相同hashkey的折扣商品，添加新商品")
                        shareRecord = ShoppingHelper.plusAndAdd(
                            goodsRequest.goods!!,
                            localDingingStyle = shareRecord.diningStyle!!,
                            goodsTagItemList = goodsRequest.goodsTagItems,
                            feedList = goodsRequest.feedInfoList,
                            orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                            singleDiscountGoods = effectiveSingleDiscount,
                            note = goodsRequest.note,
                            cartCount = effectiveSingleDiscount.num ?: 0
                        )
                    }
                }

                // 处理不带折扣的部分
                if (remainingNum > 0) {
                    if (existNoDiscountItem != null) {
                        // 如果存在相同的无折扣商品，合并到已存在的无折扣商品
                        Timber.e("找到相同hashkey的不带折扣商品，合并数量")

                        // 更新已存在的无折扣商品数量
                        shareRecord = ShoppingHelper.updateGoodsNum(
                            existNoDiscountItem.goods!!,
                            localDingingStyle = shareRecord.diningStyle!!,
                            goodsTagItemList = existNoDiscountItem.goodsTagItems,
                            feedList = existNoDiscountItem.feedInfoList,
                            orderMealSetGoodList = existNoDiscountItem.orderMealSetGoodList,
                            singleDiscountGoods = null,
                            note = existNoDiscountItem.note,
                            cartCount = (existNoDiscountItem.num ?: 0) + remainingNum
                        )
                    } else {
                        // 添加新的不带折扣商品
                        Timber.e("未找到相同hashkey的不带折扣商品，添加新商品")
                        shareRecord = ShoppingHelper.plusAndAdd(
                            goodsRequest.goods!!,
                            localDingingStyle = shareRecord.diningStyle!!,
                            goodsTagItemList = goodsRequest.goodsTagItems,
                            feedList = goodsRequest.feedInfoList,
                            orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                            singleDiscountGoods = null,
                            note = goodsRequest.note,
                            cartCount = remainingNum
                        )
                    }
                }

                // 从购物车中删除原商品（如果还存在）
                val originalItemAfterChanges = shareRecord.getGoodsVoList().firstOrNull {
                    it.getHash() == goodsRequest.getHash()
                }
                if (originalItemAfterChanges != null) {
                    // 原商品数量为0，删除
                    shareRecord = ShoppingHelper.subAndDel(
                        goodsRequest.goods!!,
                        localDingingStyle = shareRecord.diningStyle!!,
                        feedList = goodsRequest.feedInfoList,
                        goodsTagItemList = goodsRequest.goodsTagItems,
                        orderMealSetGoodList = goodsRequest.orderMealSetGoodList,
                        cartCount = originalItemAfterChanges.num ?: 0, // 传入原商品当前数量，表示全部减少
                        singleDiscountGoods = goodsRequest.singleDiscountGoods,
                        note = goodsRequest.note
                    )
                }
            }

            // 更新UI
            if (isOnlyModify != true) {
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shareRecord)
                }
            }
        }
    }

    /**
     * 判断两个折扣信息是否相同
     * @param oldDiscount 旧的折扣信息
     * @param newDiscount 新的折扣信息
     * @return 是否相同
     */
    private fun isSameDiscount(
        oldDiscount: SingleDiscountGoods?,
        newDiscount: SingleDiscountGoods?
    ): Boolean {
        // 如果都为null，则相同
        if (oldDiscount == null && newDiscount == null) {
            return true
        }

        // 如果一个为null，一个不为null，则不相同
        if (oldDiscount == null || newDiscount == null) {
            return false
        }

        // 比较关键字段
        return oldDiscount.type == newDiscount.type &&
                oldDiscount.reduceRatio == newDiscount.reduceRatio &&
                oldDiscount.saleReduce == newDiscount.saleReduce &&
                oldDiscount.vipReduce == newDiscount.vipReduce &&
                oldDiscount.adjustSalePrice == newDiscount.adjustSalePrice &&
                oldDiscount.adjustVipPrice == newDiscount.adjustVipPrice &&
                oldDiscount.discountReduceActivityId == newDiscount.discountReduceActivityId &&
                oldDiscount.num == newDiscount.num &&
                oldDiscount.remark == newDiscount.remark &&
                oldDiscount.discountType == newDiscount.discountType
    }

    /**
     * 判断折扣信息是否有效
     * @param discount 折扣信息
     * @return 是否有效
     */
//    private fun isValidDiscount(discount: SingleDiscountGoods?): Boolean {
//        if (discount == null) return false
//        if (discount.num == null || discount.num!! <= 0) return false
//
//        // 根据折扣类型判断必要字段是否有效
//        when (discount.type) {
//            SingleDiscountType.PERCENTAGE.id -> {
//                // 百分比折扣必须有折扣率
//                return discount.reduceRatio != null && discount.reduceRatio!! > 0
//            }
//
//            SingleDiscountType.FIXED_AMOUNT.id -> {
//                // 固定金额折扣必须有减免金额
//                return (discount.saleReduce != null && discount.saleReduce!! > 0) ||
//                        (discount.vipReduce != null && discount.vipReduce!! > 0)
//            }
//
//            SingleDiscountType.MODIFY_PRICE.id -> {
//                // 固定价格折扣必须有调整价格
//                return (discount.adjustSalePrice != null && discount.adjustSalePrice!! > 0) ||
//                        (discount.adjustVipPrice != null && discount.adjustVipPrice!! > 0)
//            }
//
//            else -> return false
//        }
//    }


    fun payment(
        payType: PayTypeEnum,
        accountId: String? = null,
        offlineChannelModel: OfflineChannelModel? = null,
        cashConvertModel: CashConvertModel? = null,
        reduceDiscountDetailRequest: ReduceDiscountDetailRequest? = null,
        nickName: String? = null,
        telephone: String? = null,
        reason: String? = null,
    ) {
        val connectUSD = Printer.isPosPrinterConnectUSB()
        connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                getDingingStyle()?.let { localDingingStyle ->
                    viewModelScope.launch {
                        val shareRecord = ShoppingHelper.get(localDingingStyle)
                        var customerInfoVo: CustomerInfoVo? = CustomerInfoVo(
                            name = shareRecord?.name,
                            mobile = shareRecord?.mobile,
                            diningNumber = shareRecord?.diningNumber,
                            diningTime = shareRecord?.diningTime,
                            areaCode = shareRecord?.areaCode
                        )
                        if (shareRecord?.name.isNullOrEmpty() && shareRecord?.mobile.isNullOrEmpty() && (shareRecord?.diningNumber == null || shareRecord?.diningNumber == 0) && shareRecord?.areaCode.isNullOrEmpty() && shareRecord?.diningTime.isNullOrEmpty()) {
                            customerInfoVo = null
                        }


                        if (localDingingStyle == DiningStyleEnum.PRE_ORDER.id) {
                            if (customerInfoVo?.mobile.isNullOrEmpty()) {
                                emitUiState(
                                    paymentResponse = ApiResponse.Error(
                                        "Please enter customer mobile number",
                                        CUSTOMER_INFO_REQUIRE
                                    )
                                )
                                return@launch
                            }
                        }
                        if (shareRecord?.tableUuid.isNullOrEmpty()) {
                            emitUiState(
                                paymentResponse = ApiResponse.Error(
                                    "Please select a table number",
                                    TABLE_INFO_REQUIRE
                                )
                            )
                            return@launch
                        }
                        emitUiState(paymentResponse = ApiResponse.Loading)
                        val goodsBoList = ShoppingCartHelper.getGoodsBoList(shareRecord)

                        try {
                            val paymentRequest =
                                if (localDingingStyle == DiningStyleEnum.PRE_ORDER.id) {
                                    //Reservation order
                                    PaymentRequest(
                                        accountId = accountId,
                                        diningStyle = localDingingStyle,
                                        tableUuid = shareRecord?.tableUuid,
                                        payType = payType.id,
                                        customerInfoVo = customerInfoVo,
                                        goodsList = goodsBoList,
                                        isPreOrder = true,
                                        peopleDate = customerInfoVo?.diningTime?.replace("/", "-"),
                                        peopleNum = customerInfoVo?.diningNumber,
                                        note = shareRecord?.note ?: ""
                                    )
                                } else if (payType == PayTypeEnum.PAY_OTHER) {
                                    //外卖
                                    PaymentRequest(
                                        accountId = accountId,
                                        diningStyle = DiningStyleEnum.TAKE_OUT.id,
                                        tableUuid = shareRecord?.tableUuid,
                                        payType = payType.id,
                                        customerInfoVo = customerInfoVo,
                                        goodsList = goodsBoList,
                                        isPreOrder = false,
                                        reduceVipDollar = reduceDiscountDetailRequest?.reduceVipDollar,
                                        reduceDollar = reduceDiscountDetailRequest?.reduceDollar,
                                        reduceKhr = reduceDiscountDetailRequest?.reduceKhr,
                                        reduceVipKhr = reduceDiscountDetailRequest?.reduceVipKhr,
                                        reduceType = reduceDiscountDetailRequest?.reduceType,
                                        reduceRate = reduceDiscountDetailRequest?.reduceRate,
                                        reduceReason = reduceDiscountDetailRequest?.reduceReason,
                                        discountType = reduceDiscountDetailRequest?.discountType,
                                        discountReduceActivityId = reduceDiscountDetailRequest?.discountReduceActivityId,
                                        note = shareRecord?.note ?: "",
                                        deliveryOrderNo = shareRecord?.takeOutOrderId,
                                        deliveryPlatformId = shareRecord?.getTakeOutPlatformModel()?.id,
                                    )
                                } else if (payType == PayTypeEnum.CREDIT) {
                                    //挂账
                                    PaymentRequest(
                                        accountId = accountId,
                                        diningStyle = localDingingStyle,
                                        tableUuid = shareRecord?.tableUuid,
                                        payType = payType.id,
                                        customerInfoVo = customerInfoVo,
                                        goodsList = goodsBoList,
                                        isPreOrder = false,
                                        reduceVipDollar = reduceDiscountDetailRequest?.reduceVipDollar,
                                        reduceDollar = reduceDiscountDetailRequest?.reduceDollar,
                                        reduceKhr = reduceDiscountDetailRequest?.reduceKhr,
                                        reduceVipKhr = reduceDiscountDetailRequest?.reduceVipKhr,
                                        reduceType = reduceDiscountDetailRequest?.reduceType,
                                        reduceRate = reduceDiscountDetailRequest?.reduceRate,
                                        reduceReason = reduceDiscountDetailRequest?.reduceReason,
                                        discountType = reduceDiscountDetailRequest?.discountType,
                                        discountReduceActivityId = reduceDiscountDetailRequest?.discountReduceActivityId,
                                        note = shareRecord?.note ?: "",
                                        isCredit = true,
                                        consumerPayRegisterInfo = ConsumerPayRegisterInfo(
                                            telephone = telephone,
                                            nickName = nickName,
                                        ),
                                        creditReason = reason
                                    )
                                } else if (payType != PayTypeEnum.PAY_AFTER) {
                                    //先付款模式：Advance payment mode
                                    PaymentRequest(
                                        accountId = accountId,
                                        diningStyle = localDingingStyle,
                                        tableUuid = shareRecord?.tableUuid,
                                        payType = payType.id,
                                        customerInfoVo = customerInfoVo,
                                        goodsList = goodsBoList,
                                        isPreOrder = false,
                                        reduceVipDollar = reduceDiscountDetailRequest?.reduceVipDollar,
                                        reduceDollar = reduceDiscountDetailRequest?.reduceDollar,
                                        reduceKhr = reduceDiscountDetailRequest?.reduceKhr,
                                        reduceVipKhr = reduceDiscountDetailRequest?.reduceVipKhr,
                                        reduceType = reduceDiscountDetailRequest?.reduceType,
                                        reduceRate = reduceDiscountDetailRequest?.reduceRate,
                                        reduceReason = reduceDiscountDetailRequest?.reduceReason,
                                        discountType = reduceDiscountDetailRequest?.discountType,
                                        discountReduceActivityId = reduceDiscountDetailRequest?.discountReduceActivityId,
                                        note = shareRecord?.note ?: ""
                                    )
                                } else {
                                    //后付款模式：Post-payment mode
                                    //不需要传递payType和accountId
                                    //No need payType and accountId
                                    PaymentRequest(
                                        diningStyle = localDingingStyle,
                                        tableUuid = shareRecord?.tableUuid,
                                        customerInfoVo = customerInfoVo,
                                        goodsList = goodsBoList,
                                        isPreOrder = false,
                                        note = shareRecord?.note ?: ""
                                    )
                                }
//                        singleReduceReason?.let {
//                            paymentRequest.singleReduceReason = it
//                        }

//                        singleItemDiscountList?.let {
//                            paymentRequest.singleItemDiscountList = it
//                        }

                            offlineChannelModel?.let {
                                paymentRequest.apply {
                                    offlinePayChannelsId = it.id
                                    offlinePayChannelsName = it.channelsName
                                }
                            }
                            cashConvertModel?.let {
                                paymentRequest.apply {
                                    collectCash = it.collectCash
                                    changeAmount = it.changeAmount
                                    collectCashDollar = it.collectCashDollar
                                    changeAmountDollar = it.changeAmountDollar
                                }
                            }
                            shareRecord?.getCouponInfo()?.let {
                                paymentRequest.apply {
                                    couponCode = it.couponCode
                                }
                            }

                            val singleDiscountItemList =
                                goodsBoList.filter { it.singleItemDiscount != null }.map {
                                    it.singleItemDiscount!!
                                }

                            //如果存在本地打印机
                            paymentRequest.isPosPrint = result

                            val response = repository.cartCreateOrder(
                                paymentRequest, payType.id
                            )
                            var shoppingRecord: ShoppingRecord? = null

                            if (response is ApiResponse.Success) {
                                ShoppingHelper.clearNote(localDingingStyle)
                                ShoppingHelper.clearCoupon(localDingingStyle)
                                shoppingRecord =
                                    ShoppingHelper.delAndCustomerAndTable(localDingingStyle)
                            }

                            Timber.e("paymentResponse:${response}")

                            emitUiState(
                                paymentResponse = response,
                                shoppingRecord = shoppingRecord,
                                reduceDiscountDetailRequest = reduceDiscountDetailRequest,
                                singleItemDiscountList = singleDiscountItemList,
                                removeAll = true
                            )

                        } catch (e: Exception) {
                            emitUiState(paymentResponse = ApiResponse.Error(e.message))
                        }
                    }
                }

            }

            override fun onFailure(e: ExecutionException) {
                Timber.e("e:->${e.message}")
            }

        })

    }

    fun updateShoppingRecord() {
        viewModelScope.launch {
            getDingingStyle()?.let { localDingingStyle ->
                ShoppingHelper.get(localDingingStyle)?.let {
                    emitUiState(shoppingRecord = it)
                }
            }

        }
    }

    //跟新本地hashcode 兜底用，防止其他tab 删除时 本地hashcode 被删了
    fun updateLocalHashCode(list: List<GoodsRequest>? = null) {
//        viewModelScope.launch {
//            list?.forEach {
//                if (it.goods != null) {
//                    HashHelper.save(
//                        it.feedInfoList,
//                        it.goodsTagItems,
//                        it.orderMealSetGoodList,
//                        it.goods!!.id,
//                        it.singleDiscountGoods,
//                        goodsPriceKey = it.getGoodPriceKey(),
//                        note = it.note,
//                        uuid = it.goods?.uuid,
//                    )
//                }
//            }
//        }
    }

    //更新订单优惠券
    fun updateCouponInfo(couponModel: CouponModel?) {
        viewModelScope.launch {
            try {
                getDingingStyle()?.let { localDingingStyle ->
                    val shoppingRecord =
                        ShoppingHelper.updateCoupon(localDingingStyle, couponModel)
                    emitUiState(shoppingRecord = shoppingRecord)
                }

            } catch (e: Exception) {

            }
        }
    }

    fun orderMore(context: Context?, note: String?) {
        getDingingStyle()?.let { localDingingStyle ->
            Timber.e("走加菜逻辑")
            val connectUSD = Printer.isPosPrinterConnectUSB()
            connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
                override fun onSuccess(result: Boolean) {
                    viewModelScope.launch {
                        val shareRecord = ShoppingHelper.get(localDingingStyle!!)
                        emitUiState(orderMoreResponse = ApiResponse.Loading)

                        val goodsVoList = ShoppingCartHelper.getGoodsBoList(shareRecord)

                        val orderMoreGoodRequest = AddOrderMoreGoodRequest(
                            orderNo = orderMoreID,
                            note = shareRecord?.note ?: "",
                            isPosPrint = result,
                            goodsList = goodsVoList,
                            isValid = true
                        )
                        try {
                            val response = repository.addMoreGoods(orderMoreGoodRequest)
                            var shoppingRecord = ShoppingHelper.get(localDingingStyle)

                            if (response is ApiResponse.Success) {
                                ShoppingHelper.clearCoupon(localDingingStyle)
                                ShoppingHelper.clearNote(localDingingStyle)
                                shoppingRecord =
                                    ShoppingHelper.delAndCustomerAndTable(localDingingStyle)
                                val list = arrayListOf<GoodsBo>()
                                //过滤掉待称重的菜
                                goodsVoList.filter { it.isProcessed == true }
                                    .forEach { list.add(it) }
                                val orderedInfo = response.data
                                orderedInfo.note = note
                                orderedInfo.currentOrderMore = list
                                orderedInfo.goods = orderedInfo.getGoodsVo().goodsList?.let {
                                    ArrayList(
                                        it
                                    )
                                }
                                //确认单加购  或者 后付款堂食待支付加购 且非待确认
                                Timber.e("加购是否后付款 orderedInfo.isPaymentAdvance :${orderedInfo.isPaymentAdvance}")
                                if (orderedInfo.isPaymentAdvance == false && orderedInfo.payStatus != OrderedStatusEnum.TO_BE_CONFIRM.id) {
//                            if (orderedInfo.payStatus == OrderedStatusEnum.BE_CONFIRM.id || (orderedInfo.payStatus == OrderedStatusEnum.UNPAID.id && orderedInfo?.diningStyle == DiningStyleEnum.DINE_IN.id && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance != true)) {
                                    getOrderedInfoFromAddOrder(
                                        response,
                                        orderedInfo,
                                        context,
                                        shoppingRecord
                                    )
                                } else {
                                    emitUiState(
                                        orderMoreResponse = response,
                                        shoppingRecord = shoppingRecord,
                                        removeAll = true
                                    )
                                }
                            } else {
                                emitUiState(
                                    orderMoreResponse = response,
                                    shoppingRecord = shoppingRecord,
                                    removeAll = true
                                )
                            }
                        } catch (e: Exception) {
                            emitUiState(orderMoreResponse = ApiResponse.Error(e.message))
                        }
                    }
                }

                override fun onFailure(e: ExecutionException) {
                }

            })
        }
    }

    fun electFreelist(dingingStyle: Int, shoppingRecord: ShoppingRecord?) {
        viewModelScope.launch {
            if (dingingStyle != -1) {
                _availableTableList.value = ApiResponse.Loading
                try {
                    Timber.e("MainDashboardFragment.CURRENT_USER?.isDisplayTable ${MainDashboardFragment.CURRENT_USER?.isDisplayTable}")
                    val response =
                        if (MainDashboardFragment.CURRENT_USER?.isTableService == false && MainDashboardFragment.CURRENT_USER?.isDisplayTable == false) {
                            // 如果是隐藏桌台 用这个接口 返回值过滤预定
                            repository.getTable("")
                        } else {
                            repository.electFreelist(dingingStyle, shoppingRecord)
                        }
                    if (response is ApiResponse.Success) {
                        if (MainDashboardFragment.CURRENT_USER?.isTableService == false && MainDashboardFragment.CURRENT_USER?.isDisplayTable == false) {
                            //如果隐藏桌台 只显示通用码
                            response.data.removeIf { it.type != 2 }
                        }
                    }
                    _availableTableList.value = response
                } catch (e: Exception) {
                    _availableTableList.value = ApiResponse.Error(e.message)
                }
            }
        }
    }


    fun updateShopRecordByMenu(
        list: List<GoodsRequest>? = null,
        menu: List<BaseGoods>? = null
    ): List<GoodsRequest> {
        val goodsList = list?.map {
            menu?.forEach { menuItem ->
                if (menuItem is Goods && menuItem.id == it.goods?.id) {
                    it.goods?.restrictNum = menuItem.restrictNum
                }
            }

            it

        } ?: listOf()

        return goodsList
    }


    fun emitUiState(
        result: ApiResponse<ReserveGoodListResponse>? = null,
        paymentResponse: ApiResponse<PaymentResponse>? = null,
        orderMoreResponse: ApiResponse<OrderedInfoResponse>? = null,
        shoppingRecord: ShoppingRecord? = null,
        removeAll: Boolean? = null,
        goods: Goods? = null,
        localDingingStyle: Int? = null,
//        localGoodsLoading: List<Group>? = null,
        pendingResponse: ApiResponse<BaseBooleanResponse>? = null,
        hideEmptyDataList: Boolean? = null,
        goBackOrdered: String? = null,
        errorResponse: ApiResponse<BaseBooleanResponse>? = null,
        customerInfoVo: CustomerInfoVo? = null,
        reduceDiscountDetailRequest: ReduceDiscountDetailRequest? = null,
        singleItemDiscountList: List<SingleDiscountRequest>? = null,
        refreshEnd: Boolean? = null
    ) {
//        Timber.e("shoppingRecord.goodsList==> ${shoppingRecord?.getGoodsVoList()?.size}")
        val uiModel =
            UIModel(
                result,
                paymentResponse,
                shoppingRecord,
                removeAll,
                goods,
                localDingingStyle,
//                localGoodsLoading,
                pendingResponse,
                orderMoreResponse,
                hideEmptyDataList,
                goBackOrdered, errorResponse,
                customerInfoVo,
                reduceDiscountDetailRequest,
                singleItemDiscountList,
                refreshEnd
            )
        _uiState.postValue(uiModel)
    }


    data class UIModel(
        val result: ApiResponse<ReserveGoodListResponse>?,
        var paymentResponse: ApiResponse<PaymentResponse>? = null,
        var shoppingRecord: ShoppingRecord?,
        var removeAll: Boolean? = null,
        var goods: Goods?,
        var localDingingStyle: Int?,
//        var localGoodsLoading: List<Group>? = null,
        var pendingResponse: ApiResponse<BaseBooleanResponse>? = null,
        var orderMoreResponse: ApiResponse<OrderedInfoResponse>? = null,
        var hideEmptyDataList: Boolean? = null,
        var goBackOrdered: String? = null,
        val errorResponse: ApiResponse<BaseBooleanResponse>? = null,
        val customerInfoVo: CustomerInfoVo? = null,
        val reduceDiscountDetailRequest: ReduceDiscountDetailRequest? = null,
        val singleItemDiscountList: List<SingleDiscountRequest>? = null,
        val refreshEnd: Boolean? = null
    )

    data class UIGroupListModel(
        val groupList: List<Group>? = null,
        var localDingingStyle: Int?,
    )

    data class UIGoodsListModel(
        val list: ArrayList<BaseGoods>?,
        var localDingingStyle: Int?,
    )

    data class UILoading(
        val isLoading: Boolean,
    )


    fun updateGoodsList(
        dingingStyle: Int,
        goodsInfo: String,
        lastUpdateDate: String
    ) {
        Timber.e("最后更新时间   $lastUpdateDate")
        GoodsListHelper.update(dingingStyle, goodsInfo, lastUpdateDate)
    }

    /**
     * 挂单
     *
     * @param serialNumber
     * @param note
     */
    fun addFoodToPending(serialNumber: String? = null, note: String? = "") {
        viewModelScope.launch {
            getDingingStyle()?.let { localDingingStyle ->
                val shareRecord = ShoppingHelper.get(localDingingStyle)
                val customerInfoVo = CustomerInfoVo(
                    name = shareRecord?.name,
                    mobile = shareRecord?.mobile,
                    diningNumber = shareRecord?.diningNumber,
                    diningTime = shareRecord?.diningTime,
                    areaCode = shareRecord?.areaCode
                )
                emitUiState(pendingResponse = ApiResponse.Loading)

                //挂单的时候清空单品折扣
                val shareGoodsList = shareRecord?.getGoodsVoList()
                shareGoodsList?.forEach {
                    it.singleDiscountGoods = null
                }
                //根据hashkey分组
                val groupedByHashKey = shareGoodsList?.groupBy { it.getHash() }
                val goodsBoList = ArrayList<GoodsRequest>()
                groupedByHashKey?.forEach {
                    var num = 0
                    it.value.forEach { num += (it.num ?: 0) }
                    it.value.first().num = num
                    goodsBoList.add(it.value.first())
                }
                val goodsVoList = goodsBoList.map {
                    ShoppingCartHelper.goodsRequestToGoodsBo(it)
                }

                try {
                    val pendingRequest = PendingRequest(
                        diningStyle = localDingingStyle,
                        customerInfoVo = customerInfoVo,
                        goodsList = goodsVoList,
                        isPreOrder = localDingingStyle == DiningStyleEnum.PRE_ORDER.id,
                        serialNumber = serialNumber,
                        note = note,
                        orderNote = shareRecord?.note,
                        tableUuid = shareRecord?.tableUuid
                    )

                    val response = repository.addPendingOrder(pendingRequest)

                    var shoppingRecord: ShoppingRecord? = null
                    if (response is ApiResponse.Success) {
                        ShoppingHelper.clearNote(localDingingStyle)
                        ShoppingHelper.clearCoupon(localDingingStyle)
                        shoppingRecord =
                            ShoppingHelper.delAndCustomerAndTable(localDingingStyle)
                        removeAllGoods()
                    }
                    Timber.e("挂单测试77777")
                    emitUiState(
                        pendingResponse = response,
                        shoppingRecord = shoppingRecord,
                        removeAll = true
                    )

                } catch (e: Exception) {
                    Timber.e("挂单测试222222")
                    emitUiState(pendingResponse = ApiResponse.Error(e.message))
                }

            }
        }
    }

    /**
     * 获取购物车信息
     *
     */
    fun getCartInfo() {
        viewModelScope.launch {
            getDingingStyle()?.apply {
                if (ShoppingHelper.get(diningStyle = this)?.tableUuid.isNullOrEmpty()) {
                    return@launch
                }
                val dingingStyle = this
                val response = repository.getCartInfo(
                    diningStyle = dingingStyle,
                    tableUuid = ShoppingHelper.get(diningStyle = dingingStyle)?.tableUuid
                )
                if (response is ApiResponse.Success) {
                    if (dingingStyle == getDingingStyle()) {
                        Timber.e("回来的时候更新购物车  $dingingStyle")
                        updateShoppingRecord(response.data, dingingStyle)
                    } else {
                        Timber.e("购物车已经变更")
                    }
                }
            }
        }
    }

    //清除当前购物车
    fun clearLocalShoppingCart() {
        viewModelScope.launch {
            getDingingStyle()?.apply {
                ShoppingHelper.clearCoupon(this)
                ShoppingHelper.clearNote(this)
                val shoppingRecord =
                    ShoppingHelper.delAndCustomerAndTable(this)
                emitUiState(shoppingRecord = shoppingRecord, removeAll = true)
            }

        }
    }
//
//    fun getLocalCart() {
//        val shoppingRecord = ShoppingHelper.get(diningStyle = localDingingStyle!!).apply {
//            /**
//             * 取完单以后 要再去匹配一下本地的数据 更新菜品已售罄状态
//             */
//            if (this != null) {
//                listReserveGoods.value?.let {
//                    ShoppingHelper.updateTotalPriceAndName(
//                        this,
//                        it.list?.filter { it is Goods } as ArrayList<Goods>)
//                }
//            }
//        }
//
//        emitUiState(shoppingRecord = shoppingRecord)
//    }

    /**
     * 修改菜品数量
     *
     * @param goodRequest
     * @param numStr
     */
    fun updateGoodsNum(goodRequest: GoodsRequest, numStr: String) {
        viewModelScope.launch {
            val num = numStr.toInt()
            getDingingStyle()?.let { dingStyle ->
                if (num == 0) {
                    //如果数量为0的时候 在本地购物车清掉改商品
                    val goods = goodRequest.goods!!
                    val shoppingRecord = ShoppingHelper.subAndDel(
                        goods,
                        localDingingStyle = dingStyle,
                        feedList = goodRequest.feedInfoList,
                        goodsTagItemList = goodRequest.goodsTagItems,
                        orderMealSetGoodList = goodRequest.orderMealSetGoodList,
                        cartCount = goodRequest.num!!,
                        singleDiscountGoods = goodRequest.singleDiscountGoods,
                        note = goodRequest.note
                    )

                    withContext(Dispatchers.Main) {
                        emitUiState(shoppingRecord = shoppingRecord, goods = goods)
                    }
                } else {
                    val goods = goodRequest.goods!!
                    val shoppingRecord = ShoppingHelper.updateGoodsNum(
                        goods,
                        localDingingStyle = dingStyle,
                        feedList = goodRequest.feedInfoList,
                        goodsTagItemList = goodRequest.goodsTagItems,
                        orderMealSetGoodList = goodRequest.orderMealSetGoodList,
                        cartCount = num,
                        singleDiscountGoods = goodRequest.singleDiscountGoods,
                        note = goodRequest.note
                    )
                    withContext(Dispatchers.Main) {
                        emitUiState(shoppingRecord = shoppingRecord, goods = goods)
                    }
                }
                withContext(Dispatchers.IO) {
                    if (isRequestSharedInterface(dingStyle)) {
                        if (goodRequest.goods?.cartsId == null) {
                            asyncGoodList(goodRequest.goods)
                        } else {
                            goodRequest.num = num
                            val requestObject =
                                ShoppingCartHelper.getAddToCartRequest(goodRequest, dingStyle)

                            val response = repository.changeNum(requestObject)
                            if (response is ApiResponse.Success) {
                                updateShoppingRecord(response.data, dingStyle)
                            } else if (response is ApiResponse.Error) {
                                emitUiState(errorResponse = response)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 修改菜品备注
     *
     * @param goodRequest
     * @param note
     */
    fun updateGoodsRemark(goodRequest: GoodsRequest, note: String) {
        viewModelScope.launch {

            getDingingStyle()?.let { dingStyle ->

                val record = ShoppingHelper.get(dingStyle)
                //查询一下修改后的hashkey 购物车里面是否已经存在了
                val newGoodRequest = goodRequest.deepCopy()
                newGoodRequest.note = note
                val index = record?.getGoodsVoList()
                    ?.indexOfFirst { it.getHash() == newGoodRequest.getHash() } ?: -1
                var shareRecord: ShoppingRecord? = null
                if (index == -1) {
                    //如果修改后hashkey也不存在  那就直接修改原值的备注
                    shareRecord =
                        ShoppingHelper.updateGoodsRemark(dingStyle, goodRequest, note)
                    withContext(Dispatchers.Main) {
                        emitUiState(shoppingRecord = shareRecord, goods = goodRequest.goods)
                    }
                } else {
                    //如果已经有了那就删掉 重新加入 进行合并
                    ShoppingHelper.subAndDel(
                        goodRequest.goods!!,
                        localDingingStyle = dingStyle,
                        feedList = goodRequest.feedInfoList,
                        goodsTagItemList = goodRequest.goodsTagItems,
                        orderMealSetGoodList = goodRequest.orderMealSetGoodList,
                        cartCount = goodRequest?.num ?: 0,
                        singleDiscountGoods = goodRequest.singleDiscountGoods,
                        note = goodRequest.note
                    )

                    shareRecord = ShoppingHelper.plusAndAdd(
                        goodRequest.goods!!,
                        localDingingStyle = dingStyle,
                        feedList = goodRequest.feedInfoList,
                        goodsTagItemList = goodRequest.goodsTagItems,
                        orderMealSetGoodList = goodRequest.orderMealSetGoodList,
                        cartCount = goodRequest?.num ?: 0,
                        singleDiscountGoods = goodRequest.singleDiscountGoods,
                        note = note
                    )
                }

                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shareRecord, goods = goodRequest.goods)
                }

                withContext(Dispatchers.IO) {
                    if (isRequestSharedInterface(dingStyle)) {
                        Timber.e("cartId :${goodRequest.goods?.cartsId}")
                        if (goodRequest.goods?.cartsId == null) {
                            asyncGoodList(goodRequest.goods)
                        } else {
                            val response = repository.cartGoodEdit(
                                ShoppingCartHelper.createCartGoodEditRequest(
                                    goodRequest,
                                    dingStyle,
                                    note
                                )
                            )
                            if (response is ApiResponse.Success) {
                                updateShoppingRecord(response.data, dingStyle)
                            } else if (response is ApiResponse.Error) {
                                emitUiState(errorResponse = response)
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 修改时价菜价格
     *
     * @param goodRequest
     * @param note
     */
    fun updateTimeGoodPrice(goodRequest: GoodsRequest) {
        viewModelScope.launch {
            getDingingStyle()?.let {
                //把所有时价菜同个id 的 都修改一遍
                //找出所有该时价菜的合集（包括不同备注  不同单品折扣的）

                val shareRecord = ShoppingHelper.updateTimeGoodsPrice(goodRequest, it)

                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = shareRecord, goods = goodRequest.goods)
                }

                withContext(Dispatchers.IO) {
                    if (isRequestSharedInterface(it)) {
//                        Timber.e("cartId :${goodRequest.goods?.cartsId}")
                        if (goodRequest.goods?.cartsId == null) {
                            asyncGoodList(goodRequest.goods)
                        } else {
                            val response =
                                repository.cartConfirmPendingGoods(
                                    CartConfirmPendingGoodsRequest(
                                        cartsId = goodRequest.goods?.cartsId,
                                        sellPrice = goodRequest.goods?.sellPrice,
                                        vipPrice = goodRequest.goods?.vipPrice,
                                        mealSetWeighingGoodsKey = null
                                    )
                                )
                            if (response is ApiResponse.Success) {
                                updateShoppingRecord(response.data, it)
                            } else if (response is ApiResponse.Error) {
                                emitUiState(errorResponse = response)
                            }
                        }
                    }
                }
            }
        }
    }


    //返回分类不满足条件的提示语
    fun checkClassificationNoEnablePosition(context: Context): Pair<String, String>? {
        var pair: Pair<String, String>? = null
        getDingingStyle()?.let { localDingingStyle ->
            val record = ShoppingHelper.get(localDingingStyle)
            val goodsList = GoodsHelper.getGoodsList(localDingingStyle).filter { it is Goods }
            val groupList = GoodsHelper.getGroupsList(localDingingStyle)

            for (it in groupList) {
                if (it.minGoodsNum == null && it.maxGoodsNum == null) {
                    //如果没限制直接跳过
                    continue
                }
                //查询购物车里面这个分组下有多少菜品
                var goodsNum = 0;
                //这个分组下的所有菜品
                val filterGoodByGroupId =
                    goodsList.filter { good -> (good as Goods).groupID == it.id }

                record?.getGoodsVoList()?.forEach {
                    //匹配购物车的菜品 分组下有没有对应菜品
                    val index =
                        filterGoodByGroupId.indexOfFirst { good -> (good as Goods).id == it.goods?.id }
                    if (index != -1) {
                        goodsNum += (it.num ?: 0)
                    }
                }
                var errorStr = ""
                //如果不满足条件 直接break   直接返回当次不满足条件的分类
                if (it.minGoodsNum != null) {

                    if (goodsNum < (it.minGoodsNum ?: 0)) {
                        errorStr = context.getString(
                            R.string.classification_min_select,
                            "${it.name}",
                            "${it.minGoodsNum ?: 0}"
                        )
                        pair = Pair(it.id!!, errorStr)
                        break
                    }
                }
                if (it.maxGoodsNum != null) {
                    if (goodsNum > (it.maxGoodsNum ?: 0)) {
//                    Timber.e("${it}最多可选购${it.maxGoodsNum ?: 0}")
                        errorStr = context.getString(
                            R.string.classification_max_select,
                            "${it.name}",
                            "${it.maxGoodsNum ?: 0}"
                        )
                        pair = Pair(it.id!!, errorStr)
                        break
                    }
                }
            }
        }
        return pair
    }


    fun getOrderMoreProduct(context: Context) {
        viewModelScope.launch {
            getDingingStyle()?.apply {
                val orderId = ShoppingHelper.get(diningStyle = this)?.orderMoreID
                val response = repository.getOrderMoreProduct(
                    diningStyle = this,
                    tableUuid = ShoppingHelper.get(diningStyle = this)?.tableUuid,
                    orderId = orderId
                )
                if (response is ApiResponse.Success) {
                    if (response.data != null) {
                        orderMoreID = response.data.orderNo
                        orderMoreDataResponse.value = response.data
                        updateShoppingRecord(response.data, this)
                    } else {
                        //如果请求回来data 是空的就移除加购状态
                        removeOrderMore()
                    }
                } else if (response is ApiResponse.Error) {
                    if (response.errorCode == ORDER_STATUS_ERROR) {
                        Toast.makeText(
                            context,
                            response.message,
                            Toast.LENGTH_SHORT
                        )
                            .show()
                    }
                    orderMoreID = orderId
                    removeOrderMore()
                }
            }

        }
    }

    private fun updateShoppingRecord(
        orderMoreResponse: OrderMoreDataResponse,
        diningStyleEnum: Int
    ) {
        viewModelScope.launch(Dispatchers.IO + SupervisorJob()) {
            Timber.e("updateShoppingRecord  orderMoreResponse: OrderMoreDataResponse    diningStyleEnum: $diningStyleEnum")
            var shoppingRecord = ShoppingHelper.del(diningStyleEnum)

            ShoppingHelper.updateCoupon(diningStyleEnum, orderMoreResponse.coupon)
            shoppingRecord =
                ShoppingHelper.updateOrderMore(true, orderMoreResponse.orderNo, diningStyleEnum)

            val phonePair = orderMoreResponse.getConsumePhoneNumber()
            val reserveTableRequest = ReserveTableRequest(
                diningTime = "",
                diningNumber = 0,
                areaCode = phonePair?.first ?: "",
                mobile = phonePair?.second ?: "",
                name = orderMoreResponse.getLastCustomerName(),
                tableId = ""
            )

            orderMoreResponse.customerInfoVo?.let {
                reserveTableRequest.diningTime = it.diningTime ?: ""
                reserveTableRequest.diningNumber = it.diningNumber ?: 0
            }

            ShoppingHelper.updateCustomer(diningStyleEnum, reserveTableRequest)

            //购物车
            orderMoreResponse.addGoodsList?.forEach { orderedGood ->
                val good = ShoppingCartHelper.cartGoodsToGoods(orderedGood)
                var arrayFeed = ArrayList<Feed>()
                var arrayTag = ArrayList<GoodsTagItem>()
                orderedGood.tagItems?.let {
                    arrayTag = ArrayList(orderedGood.tagItems)
                }
                orderedGood.feeds?.let {
                    arrayFeed = ArrayList(orderedGood.feeds)
                }
                /**
                 * 如果是时价菜 从已点的里面主订单找出对应时价菜 赋值价格
                 */
                if (good.isTimePriceGood()) {
                    val index =
                        orderMoreResponse.goodsJsonList?.goodsList?.indexOfFirst { it.orderId == orderMoreResponse.orderNo && it.id == good.id }
                            ?: -1
                    if (index != -1) {
                        val data = orderMoreResponse.goodsJsonList?.goodsList?.get(index)
                        if (data != null) {
                            good.sellPrice = data.sellPrice
                            good.vipPrice = data.vipPrice
                            good.discountPrice = null
                            good.setWeighingCompletedFlag(data.isHasCompleteWeight())
                            good.setPriceCompletedFlag(data.isHasCompletePricing())
                        } else {
                            good.sellPrice = 0
                            good.vipPrice = null
                            good.discountPrice = null
                            good.isProcessed = false
                            good.setWeighingCompletedFlag(false)
                            good.setPriceCompletedFlag(false)
                        }
                    } else {
                        good.sellPrice = 0
                        good.vipPrice = null
                        good.discountPrice = null
                        good.isProcessed = false
                        good.setWeighingCompletedFlag(false)
                        good.setPriceCompletedFlag(false)
                    }
                }

                shoppingRecord = ShoppingHelper.plusAndAdd(
                    good,
                    localDingingStyle = diningStyleEnum,
                    feedList = arrayFeed,
                    goodsTagItemList = arrayTag,
                    orderMealSetGoodList = orderedGood.orderMealSetGoodsDTOList,
                    cartCount = orderedGood.num ?: 0,
                    singleDiscountGoods = orderedGood.singleItemDiscount?.toCartSingleDiscountGood(),
                    note = orderedGood.note
                )
            }

            withContext(Dispatchers.Main) {
                emitUiState(shoppingRecord = shoppingRecord, removeAll = true)
            }
        }
    }

    fun removeOrderMore() {
        viewModelScope.launch {
            getDingingStyle()?.apply {
                var shoppingRecord = ShoppingHelper.get(this)
                if (isRequestSharedInterface(this)) {
                    val response = repository.emptyFromCart(
                        this, shoppingRecord?.tableUuid ?: ""
                    )
                    if (response is ApiResponse.Success) {
                        ShoppingHelper.clearCoupon(this)
                        ShoppingHelper.clearNote(this)
                        shoppingRecord = ShoppingHelper.delAndCustomerAndTable(this)
                        emitUiState(
                            shoppingRecord = shoppingRecord,
                            removeAll = true,
                            goBackOrdered = orderMoreID
                        )
                    }
                } else {
                    ShoppingHelper.clearCoupon(this)
                    shoppingRecord = ShoppingHelper.delAndCustomerAndTable(this)
                    emitUiState(
                        shoppingRecord = shoppingRecord,
                        removeAll = true,
                        goBackOrdered = orderMoreID
                    )
                }
            }

        }
    }

    /**
     * 清掉本地购物车优惠券
     *
     * @param diningStyleEnum
     */
    fun clearCoupon(diningStyleEnum: Int) {
        viewModelScope.launch {
            Timber.e("clearCoupon")
            val shoppingRecord = ShoppingHelper.updateCoupon(diningStyleEnum, null)
            emitUiState(shoppingRecord = shoppingRecord)
        }
    }

    // 用于跟踪和取消updateShoppingRecord的协程
    private var updateShoppingRecordJob: Job? = null

    /**
     * 更新购物车记录
     *
     * 该方法用于从服务端获取的购物车数据更新本地购物车，包含以下优化：
     * 1. 使用IO调度器确保在后台线程执行耗时操作
     * 2. 版本比较避免旧数据覆盖新数据
     * 3. 批量处理商品列表减少GC压力
     * 4. 性能日志记录关键步骤耗时
     * 5. 取消之前的更新任务，避免竞态条件
     *
     * @param cartInfoResponse 服务端返回的购物车信息
     * @param diningStyleEnum 用餐方式
     */
    private fun updateShoppingRecord(cartInfoResponse: CartInfoResponse, diningStyleEnum: Int) {
        // 取消之前的更新任务
        updateShoppingRecordJob?.cancel()

        // 创建并保存新的协程引用
        updateShoppingRecordJob = viewModelScope.launch(Dispatchers.IO + SupervisorJob()) {
            try {
                Timber.e("updateShoppingRecord cartInfoResponse: CartInfoResponse, diningStyleEnum: Int")

                // 检查协程是否已取消
                if (!isActive) {
                    Timber.e("更新购物车任务已取消")
                    return@launch
                }

                // 1. 检查是否是自己的token，避免自我更新
                if (cartInfoResponse.isSelfToken()) {
                    Timber.e("如果是自己的token 就不处理")
                    return@launch
                }

                // 2. 版本检查，避免旧数据覆盖新数据
                val shoppingRecord = ShoppingHelper.get(diningStyleEnum)
                Timber.e("本地dataVersion =>${shoppingRecord?.dataVersion}   服务端dataVersion => ${cartInfoResponse.dataVersion}")

                if ((cartInfoResponse.dataVersion ?: 0) < (shoppingRecord?.dataVersion ?: 0)) {
                    val oldTimestamp = shoppingRecord?.timestamp?.toLongOrNull()
                    val newTimestamp = cartInfoResponse.timestamp?.toLongOrNull()

                    if (oldTimestamp != null && newTimestamp != null && newTimestamp < oldTimestamp) {
                        Timber.e("本地version 更新就不更新")
                        return@launch
                    }
                }

                // 3. 性能计时开始
                val start = System.nanoTime()

                // 再次检查协程是否已取消
                if (!isActive) {
                    Timber.e("更新购物车任务已取消")
                    return@launch
                }

                // 4. 清除原来购物车内容（在IO线程执行）
                val updatedShoppingRecord = ShoppingHelper.del(diningStyleEnum)
//                Log.e("购物车耗时 删除耗时", "Time: ${(System.nanoTime() - start) / 1_000_000} ms")

                // 5. 预处理商品列表（批量处理减少GC压力）
                val goodsRequestList = prepareGoodsRequestList(cartInfoResponse)
//                Log.e(
//                    "购物车耗时 创建列表耗时",
//                    "Time: ${(System.nanoTime() - start) / 1_000_000} ms"
//                )

                // 再次检查协程是否已取消
                if (!isActive) {
                    Timber.e("更新购物车任务已取消")
                    return@launch
                }

                // 6. 批量更新购物车
                val finalShoppingRecord = ShoppingHelper.updateGoods(
                    cartInfoResponse,
                    diningStyleEnum,
                    ArrayList(goodsRequestList)
                )
//                Log.e(
//                    "购物车耗时 更新购物车",
//                    "Time: ${(System.nanoTime() - start) / 1_000_000} ms"
//                )

                // 最后检查协程是否已取消
                if (!isActive) {
                    Timber.e("更新购物车任务已取消")
                    return@launch
                }

                // 7. 切换到主线程更新UI
                withContext(Dispatchers.Main) {
                    emitUiState(shoppingRecord = finalShoppingRecord, removeAll = true)
                }

                // 8. 记录总耗时
//                Log.e("购物车耗时 方法总", "Time: ${(System.nanoTime() - start) / 1_000_000} ms")
            } catch (e: Exception) {
                if (e is CancellationException) {
                    Timber.e("更新购物车任务被取消")
                } else {
                    Timber.e(e, "更新购物车异常")
                }
                // 异常不会导致应用崩溃，但会记录日志
            } finally {
                // 如果这是最新的任务，清除引用
                if (updateShoppingRecordJob?.isActive == false) {
                    updateShoppingRecordJob = null
                }
            }
        }
    }

    /**
     * 预处理商品列表
     */
    private fun prepareGoodsRequestList(cartInfoResponse: CartInfoResponse): List<GoodsRequest> {
        // 预分配容量，避免动态扩容
        val capacity = cartInfoResponse.goodsList?.size ?: 10
        val list = ArrayList<GoodsRequest>(capacity)

        // 批量处理商品
        cartInfoResponse.goodsList?.forEach { orderedGood ->
            val good = ShoppingCartHelper.cartGoodsToGoods(orderedGood)

            // 使用apply构建器模式简化代码
            val arrayFeed = orderedGood?.feeds?.let { ArrayList(it) } ?: ArrayList()
            val arrayTag = orderedGood?.tagItems?.let { ArrayList(it) } ?: ArrayList()

            list.add(
                GoodsRequest(
                    num = orderedGood?.num ?: 0,
                    feedInfoList = arrayFeed,
                    goodsTagItems = arrayTag,
                    orderMealSetGoodList = orderedGood?.orderMealSetGoodsDTOList,
                    goods = good,
                    note = orderedGood?.note
                ).apply {
                    finalSinglePrice = calculateSinglePrice()
                }
            )
        }

        return list
    }

    fun switchTable(uuid: String?, tableName: String?, tableType: Int?) {
        viewModelScope.launch {
            getDingingStyle()?.apply {
                val shareRecord = ShoppingHelper.get(this)
                val goodsVoList = shareRecord?.getGoodsVoList()
                val goodsBoList = ArrayList<BatchAddCart>()
                goodsVoList?.forEach {
                    goodsBoList.add(
                        ShoppingCartHelper.toBatchAddCartModel(it)
                    )
                }
                if (tableType == 2) {
                    //如果是通用桌
                    updateSelectTable(
                        uuid,
                        tableName,
                        tableType,
                        dingingStyle = this
                    )
                    return@launch
                }

                /**
                 * batchAddCartList  通用桌换桌的时候才传
                 * **/
                val isSendGoods =
                    shareRecord?.isUniversalQr() == true || shareRecord?.tableUuid.isNullOrEmpty()
                //如果没选旧餐桌的时候才把 菜品给服务端 ，有旧餐桌服务端自己去查

                val response = repository.switchTable(
                    switchTableRequest = SwitchTableRequest(
                        batchAddCartList = if (isSendGoods) goodsBoList else null,
                        diningStyle = this,
                        newTableUuid = uuid,
                        oldTableUuid = if (shareRecord?.tableUuid != uuid && !shareRecord?.tableUuid.isNullOrEmpty()) shareRecord?.tableUuid else null
                    )
                )
                if (response is ApiResponse.Success) {
                    updateSelectTable(
                        uuid,
                        tableName,
                        tableType,
                        dingingStyle = this
                    )
                    if (response.data.confirmOrderExists == true) {
                        val orderListResponse = repository.getAbNormalOrderV2(
                            uuid,
                            null,
                            this,
                            listOf(
                                OrderedStatusEnum.BE_CONFIRM.id,
                                OrderedStatusEnum.TO_BE_CONFIRM.id
                            )
                        )
                        var toBeConfirmOrderId = ""
                        if (orderListResponse is ApiResponse.Success) {
                            if (orderListResponse.data != null) {
                                val orderInfo = orderListResponse.data.firstOrNull()
                                //判断一下当前是否待确认单
                                if (orderInfo?.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || orderInfo?.payStatus == OrderedStatusEnum.BE_CONFIRM.id) {
                                    //如果异常单是待确认单
                                    toBeConfirmOrderId = orderInfo.id ?: ""
                                    ShoppingHelper.updatePayStatus(
                                        orderInfo?.payStatus,
                                        this
                                    )
                                }
                            }
                        }

                        val shoppingRecord =
                            ShoppingHelper.updateOrderMore(
                                true,
                                toBeConfirmOrderId,
                                this
                            )
                        Timber.e("localDingingStyle $this")
                        emitUiState(
                            localDingingStyle = this,
                            shoppingRecord = shoppingRecord
                        )
                        getTableCustomerInfo(uuid, shoppingRecord)
                    } else {
                        val shoppingRecord =
                            ShoppingHelper.updateOrderMore(false, "", this)
//                        emitUiState(
////                            localDingingStyle = this,
//                            shoppingRecord = shoppingRecord
//                        )
                        updateShoppingRecord(response.data, this)
                        getTableCustomerInfo(uuid, shoppingRecord)
                    }
                }
            }
        }
    }

    private fun getTableCustomerInfo(uuid: String?, shareRecord: ShoppingRecord?) {
        viewModelScope.launch {
            val response = repository.getCustomerInfo(
                uuid,
                if (!shareRecord?.name.isNullOrEmpty() || !shareRecord?.mobile.isNullOrEmpty() || (shareRecord?.diningNumber
                        ?: 0) > 0 || !shareRecord?.diningTime.isNullOrEmpty()
                )
                    CustomerInfoVo(
                        shareRecord?.name,
                        shareRecord?.areaCode,
                        shareRecord?.mobile,
                        shareRecord?.diningNumber,
                        shareRecord?.diningTime
                    ) else null,
//                if (!shareRecord?.mobile.isNullOrEmpty())
//                    CustomerInfoVo(
//                        shareRecord?.name,
//                        shareRecord?.areaCode,
//                        shareRecord?.mobile,
//                        shareRecord?.diningNumber,
//                        shareRecord?.diningTime
//                    ) else null,
                shareRecord?.diningStyle
            )
            if (response is ApiResponse.Success) {

                var currentShareRecord = ShoppingHelper.get(shareRecord?.diningStyle!!)
                //防止展示的不是当前桌的用户信息
                if (currentShareRecord?.tableUuid == uuid) {
                    val reserveTableRequest = ReserveTableRequest(
                        diningTime = response.data?.diningTime ?: "",
                        diningNumber = response.data?.diningNumber ?: 0,
                        areaCode = response.data?.areaCode ?: "",
                        mobile = response.data?.mobile ?: "",
                        name = response.data?.name ?: "",
                        tableId = uuid ?: ""
                    )
                    ShoppingHelper.updateCustomer(shareRecord?.diningStyle, reserveTableRequest)
                    currentShareRecord = ShoppingHelper.get(shareRecord?.diningStyle!!)
                    emitUiState(
                        shoppingRecord = currentShareRecord
                    )
                }


            }
        }
    }

    //更新购物车记录
    private fun updateShoppingRecord(
        cartInfoResponse: SwitchTableResponse,
        diningStyleEnum: Int
    ) {
        viewModelScope.launch {
            Timber.e("updateShoppingRecord cartInfoResponse: SwitchTableResponse, diningStyleEnum: Int")
            var shoppingRecord = ShoppingHelper.del(diningStyleEnum)
            cartInfoResponse.goodsList?.forEach { orderedGood ->
                val good = ShoppingCartHelper.cartGoodsToGoods(orderedGood)
                var arrayFeed = ArrayList<Feed>()
                var arrayTag = ArrayList<GoodsTagItem>()
                orderedGood?.tagItems?.let {
                    arrayTag = ArrayList(orderedGood.tagItems)
                }
                orderedGood?.feeds?.let {
                    arrayFeed = ArrayList(orderedGood.feeds)
                }
                if (good.isTimePriceGood() && !good.isHasCompletePricing()) {
                    good.sellPrice = null
                    good.vipPrice = null
                }
                shoppingRecord = ShoppingHelper.plusAndAdd(
                    good,
                    localDingingStyle = diningStyleEnum,
                    feedList = arrayFeed,
                    goodsTagItemList = arrayTag,
                    orderMealSetGoodList = orderedGood?.orderMealSetGoodsDTOList,
                    cartCount = orderedGood?.num ?: 0,
                    singleDiscountGoods = orderedGood?.singleItemDiscount?.toCartSingleDiscountGood(),
                    note = orderedGood?.note
                )
            }
            withContext(Dispatchers.Main) {
                emitUiState(shoppingRecord = shoppingRecord, removeAll = true)
            }
        }
    }

    //共享餐桌 更新购物车
    fun updateCartFrom(goods: SocketGoods) {
        viewModelScope.launch(Dispatchers.IO) {
            getDingingStyle()?.let { localDingingStyle ->
                if (localDingingStyle == goods.diningStyle) {
                    if (goods.tableUuid == ShoppingHelper.get(localDingingStyle)?.tableUuid) {
                        goods.cartInfoVo?.let {
                            /**
                             * 处理翻译 其他端不同语言提交的时候 与当前收银端语言不一样
                             */
                            val goodsInOrderList =
                                orderMoreDataResponse.value?.goodsJsonList?.goodsList ?: listOf()
                            it.goodsList?.forEach { good ->

                                val baseGoods =
                                    GoodsHelper.getGoodsMap(localDingingStyle, good?.id!!)
                                if (baseGoods != null) {
                                    val localGood = baseGoods as Goods
                                    good.name = localGood.name

                                    good.feeds?.forEach { feed ->
                                        val localFeed =
                                            localGood.feeds?.firstOrNull { it.id == feed.id }
                                        if (localFeed != null) {
                                            feed.name = localFeed.name
                                        }
                                    }

                                    good.tagItems?.forEach { tagItem ->
                                        localGood.tags?.forEach { tag ->
                                            val localTagItem =
                                                tag.goodsTagItems?.firstOrNull { it.id == tagItem.id }
                                            if (localTagItem != null) {
                                                tagItem.name = localTagItem.name
                                            }
                                        }
                                    }

                                    good.activityLabels = baseGoods.activityLabels

                                    good.orderMealSetGoodsDTOList?.forEach { goodsDto ->
                                        localGood.mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
                                            mealSetGroup.mealSetGoodsList?.forEach { mealSetGoods ->
                                                //找到本地菜单里面套餐里面的这个菜品
                                                if (goodsDto.mealSetGoodsId == mealSetGoods.goodsId) {
                                                    //修改套餐内对应菜品名字翻译
                                                    goodsDto.mealSetGoodsName = mealSetGoods.name

                                                    goodsDto.mealSetTagItemList?.forEach { tag ->
                                                        mealSetGoods.tags?.forEach { localTag ->
                                                            val index =
                                                                localTag.goodsTagItems?.indexOfFirst { it.id == tag.id }
                                                                    ?: -1
                                                            if (index != -1) {
                                                                tag.name =
                                                                    localTag.goodsTagItems!![index].name
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                try {
                                    /**
                                     * 修改购物车内 主单内存在的时价菜的价格
                                     */
                                    if (orderMoreID != null && good.isTimePriceGood()) {
                                        //只查主单的对应的时价菜
                                        val index =
                                            goodsInOrderList?.indexOfFirst { it.id == good.id && it.orderId == orderMoreID }
                                                ?: -1
                                        // 如果已经设置过价格了
                                        if (index != -1) {
                                            if (goodsInOrderList[index].isHasCompletePricing() == true) {
                                                good.sellPrice =
                                                    goodsInOrderList[index].sellPrice
                                                good.discountPrice = null
                                                good.vipPrice =
                                                    goodsInOrderList[index].vipPrice
                                                good.setPriceCompletedFlag(true)
                                            } else {
                                                good.setPriceCompletedFlag(false)
                                            }
                                        }
                                    }
                                } catch (e: Exception) {

                                }
                                Timber.e("goods ===> ${it.goodsList.toJson()}")
                            }

                            updateShoppingRecord(it, localDingingStyle)
                            withContext(Dispatchers.Main) {
                                EventBus.getDefault()
                                    .post(
                                        SimpleEvent(
                                            SimpleEventType.TIME_PRICE_GOOD_CHANGE_PRICE,
                                            goods.cartInfoVo
                                        )
                                    )
                            }
                        }
                    }
                }
            }
        }
    }

    fun printTicketByOrderNo(
        context: Context,
        orderNo: String,
        callBack: (() -> Unit)? = null
    ) {
        viewModelScope.launch {
            //防止ws 收到消息重复打印
            PrinterDeviceHelper.setOrderPrinter(orderNo)
            val orderedInfo = repository.orderedInfo(orderNo)
            if (orderedInfo is ApiResponse.Success) {
                printTicket(context, orderedInfo.data, callBack)
            }
        }
    }

    fun printTicket(
        context: Context,
        res: OrderedInfoResponse,
        callBack: (() -> Unit)? = null
    ) {
        //防止ws 收到消息重复打印
        PrinterDeviceHelper.setOrderPrinter(res.orderNo)
        //支付完成直接打印
        getPrinterTemplate(context, res, callBack)
    }

    private fun getPrinterTemplate(
        context: Context,
        res: OrderedInfoResponse, callBack: (() -> Unit)? = null
    ) {
        viewModelScope.launch {
            if (res.isPreOrder()) {
                //这边判断一下如果预定时间没到不能打印
                if (res.getDingTime().isNotEmpty()) {
                    //是否有预定模板
                    val preOrderTemplate = PrinterTemplateHelper.getPerOrderTemplate()

                    if (!PrinterTemplateHelper.isTimeToPrinterPerOrderTicket(
                            preOrderTemplate,
                            res
                        )
                    ) {
                        return@launch
                    }
                }
            }


            val printInfoList = PrinterDeviceHelper.getPrinterList()

            for (printerConfigInfo in printInfoList) {
                //获取菜品的多语言
                repository.orderTranslate(res)
                val printTemplateList =
                    printerConfigInfo.printerTemplateList ?: listOf()
                if (printTemplateList.isNotEmpty()) {

                    var isPrint = false
                    val kitchenPrintTemplate =
                        printTemplateList.filter { it.type == PrintTemplateTypeEnum.KITCHEN.id }

                    val printType =
                        res.getPrintTypeFromDiningStyle()
                    val printTemplate =
                        printTemplateList.firstOrNull { it.type == printType.id }

                    val labelTickerTemplate =
                        printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.LABEL.id }

                    //是否自动打印结账单
                    val isAutoPrintCheckTicket =
                        printTemplate?.type in listOf(
                            PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id,
                            PrintTemplateTypeEnum.PRE_ORDER.id
                        ) && MainDashboardFragment.STORE_INFO?.autoCheckoutTicket() == true

                    if (kitchenPrintTemplate.isNotEmpty()) {
                        kitchenPrintTemplate.forEach {
                            Printer.printTicket(
                                context,
                                it, res,
                                printerConfigInfo = printerConfigInfo,
                            )
                        }
                        isPrint = true
                    }

                    if (printTemplate != null) {
                        if (res.isOrderSuccess()) {
                            if (isAutoPrintCheckTicket) {
                                Printer.printTicket(
                                    context,
                                    printTemplate,
                                    res, printerConfigInfo = printerConfigInfo,
                                )
                            }
                        } else {
                            val printTicketType = Printer.getPrintTicketType()
                            if (printTicketType == PrintTicketType.NORMAL.id) {
                                Printer.printTicket(
                                    context,
                                    printTemplate,
                                    res, printerConfigInfo = printerConfigInfo,
                                )
                            }
                        }
                        isPrint = true
                    }

                    if (labelTickerTemplate != null) {
                        LabelPrinter.printLabelTicket(
                            context,
                            labelTickerTemplate,
                            res,
                            printerConfigInfo
                        )
                        isPrint = true
                    }

                    Timber.e("isPrint  $isPrint")
                    if (isPrint) {
                        repository.updatePrintLog(res.orderNo)
                    }
                }
            }
            callBack?.invoke()
        }
    }

    /**
     * 购物车先付款打印预结
     *
     */
    fun printPreSettlement() {
        viewModelScope.launch {
            uiLoading.postValue(UILoading(true))
            val shareRecord = ShoppingHelper.get(getDingingStyle()!!)
            val goodsBoList = ShoppingCartHelper.getGoodsBoList(shareRecord)
            val response = repository.preSettlement(
                PreSettlementRequest(
                    diningStyle = getDingingStyle()!!,
                    goodsList = goodsBoList,
                    note = shareRecord?.note,
                    tableUuid = shareRecord?.tableUuid,
                    couponCode = shareRecord?.getCouponInfo()?.couponCode,
                    peopleDate = shareRecord?.diningTime?.replace("/", "-")
                )
            )
            if (response is ApiResponse.Success) {
                val orderInfo = response.data
                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.PRINT_CART_PRE_STELLEMENT, orderInfo))
            }
            uiLoading.postValue(UILoading(false))

        }
    }

    //加购打印走这里
    fun getOrderedInfoFromAddOrder(
        orderMoreResponse: ApiResponse<OrderedInfoResponse>? = null,
        orderedInfo: OrderedInfoResponse? = null,
        context: Context? = null,
        shoppingRecord: ShoppingRecord? = null
    ) {
        viewModelScope.launch {
            Timber.e("加购打印  $orderedInfo")
            try {
                if (orderedInfo == null) {
                    return@launch
                }

                orderedInfo.currentOrderMore?.let {
                    val list = ArrayList<OrderedGoods>()
                    //返回的菜单里面找出一样的菜品
                    orderedInfo.currentOrderMore?.forEach { bo ->
                        //可能会有同一到待称重的菜，一个没称重，一个带称重
                        val first = orderedInfo.goods?.firstOrNull { orderGood ->
                            val hash1 = HashHelper.getHashByTagStr(
                                ArrayList(bo.feeds ?: listOf()),
                                bo.tagItemId?.replace(",", "") ?: "",
                                bo.mealSetGoodsDTOList,
                                bo.id!!,
                                singleItemDiscount = bo.singleDiscountGoods,
                                goodsPriceKey = "",
                                note = bo.note,
                                uuid = bo.uuid
                            )
                            Timber.e("hash1:${hash1}")

                            val hash2 = HashHelper.getHash(
                                ArrayList(orderGood.feeds ?: listOf()),
                                ArrayList(orderGood.tagItems ?: listOf()),
                                orderGood.orderMealSetGoodsDTOList,
                                orderGood.id!!,
                                singleItemDiscount = orderGood.singleItemDiscount?.toCartSingleDiscountGood(),
                                goodsPriceKey = "",
                                note = orderGood.note,
                                uuid = orderGood.uuid
                            )
                            Timber.e("")
                            Timber.e("hash2:${hash2}")
                            hash1 == hash2
                        }?.copy()
                        first?.let {
                            list.add(it.apply {
//                                weight = null
//                                weighingCompleted = false
                                num = bo.num
                            })
                        }
                    }
                    //可能出现已经被过滤掉待称重的菜，所以此时只有currentOrderMore有值，list为空
                    orderedInfo.currentOrderMoreList = list
                }
//                orderedInfo.currentOrderMoreList = OrderHelper.getLastAddGoods(orderedInfo)

                if (orderedInfo.currentOrderMoreList?.isNotEmpty() == true) {
                    repository.orderTranslate(orderedInfo)

                    val printInfoList = PrinterDeviceHelper.getPrinterList()

                    var isPrinter = false
                    context?.let {
                        for (printerConfigInfo in printInfoList) {
                            val printTemplateList =
                                printerConfigInfo.printerTemplateList ?: listOf()
                            if (printTemplateList.isNotEmpty()) {
                                //获取菜品的多语言
                                val kitchenPrintTemplate =
                                    printTemplateList.filter { it.type == PrintTemplateTypeEnum.KITCHEN.id }
                                val printType =
                                    orderedInfo.getPrintTypeFromDiningStyle()

                                val printTemplate =
                                    printTemplateList.firstOrNull { it.type == printType?.id }

                                val labelTickerTemplate =
                                    printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.LABEL.id }

                                Timber.e("打印模板 id  ${printTemplate?.type}   ${printType?.id}")

                                if (kitchenPrintTemplate.isNotEmpty()) {
                                    kitchenPrintTemplate.forEach {
                                        Printer.printTicket(
                                            context,
                                            it,
                                            orderedInfo,
                                            false,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                    }
                                    isPrinter = true
                                }
                                if (labelTickerTemplate != null) {
                                    LabelPrinter.printLabelTicket(
                                        context,
                                        labelTickerTemplate,
                                        orderedInfo,
                                        printerConfigInfo
                                    )
                                    isPrinter = true
                                }

                                if (printTemplate != null) {
                                    Timber.e(" 打印")
//                                    val ticketType = Printer.getPrintTicketType()
                                    Printer.printTicket(
                                        context,
                                        printTemplate,
                                        orderedInfo,
                                        false,
                                        printerConfigInfo = printerConfigInfo,
                                    )
                                    isPrinter = true
                                }
                            }
                            //如果wifi打印机状态不正常直接逃过
                            if (printerConfigInfo.type == PrinterTypeEnum.WIFI.type) {
                                val printerState =
                                    PrinterDeviceHelper.getWifiConnectState(printerConfigInfo)
                                if (!printerState && isPrinter) {
                                    isPrinter = false
                                }
                            }
                        }
                    }
                    if (isPrinter) {
                        val printResponse = repository.updatePrintLog(orderedInfo?.orderNo)
                        if (printResponse is ApiResponse.Success) {
                            EventBus.getDefault().post(
                                SimpleEvent(
                                    SimpleEventType.UPDATE_UNPRINT_EVENT,
                                    orderedInfo?.orderNo
                                )
                            )
                            EventBus.getDefault().post(
                                SimpleEvent(
                                    SimpleEventType.GET_UNREAD_EVENT,
                                    null
                                )
                            )
                        }
                    }
                }

                emitUiState(
                    orderMoreResponse = orderMoreResponse,
                    shoppingRecord = shoppingRecord,
                    removeAll = true
                )
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {

            }

        }
    }

    /**
     * 更新临时菜品名字
     *
     * @param goods
     */
    fun updateCartGoodInfo(goods: List<Goods>?) {
        if (!goods.isNullOrEmpty()) {
            getDingingStyle()?.let { localDingingStyle ->
                val shoppingRecord = ShoppingHelper.getDefault(localDingingStyle).apply {
                    goods.let {
                        ShoppingHelper.updateTmpGoodTotalPriceAndName(this, ArrayList(it))
                    }
                }
                emitUiState(
                    shoppingRecord = shoppingRecord,
                )
            }

        }
    }

    /**
     * 加购的时候更新订单内的顾客信息
     *
     * @param reserveTableRequest
     * @param orderId
     */
    private fun updateCustomerInfo(
        reserveTableRequest: ReserveTableRequest,
        orderId: String? = null
    ) {
        viewModelScope.launch {
            try {
                val response =
                    repository.updateCustomerInfo(
                        UpdateCustomerInfoRequest(
                            reserveTableRequest.name,
                            reserveTableRequest.areaCode,
                            reserveTableRequest.mobile,
                            reserveTableRequest.diningNumber,
                            reserveTableRequest.diningTime,
                            orderId
                        )
                    )
                if (response is ApiResponse.Success) {
                    val shoppingRecord =
                        ShoppingHelper.updateCustomer(getDingingStyle(), reserveTableRequest)
                    withContext(Dispatchers.Main) {
                        emitUiState(shoppingRecord = shoppingRecord)
                    }
                }
            } catch (e: Exception) {

            }
        }
    }

    /**
     * 切换类型的时候 把用户信息带到对应类型
     *
     */
    fun changeDingStyleCustomerInfo(oldDingStyle: Int, newDingStyle: Int) {
        if (oldDingStyle != newDingStyle) {
            //旧tab
            val oldShareRecord = ShoppingHelper.getDefault(oldDingStyle)
            val newShareRecord = ShoppingHelper.get(newDingStyle)
            //判断新旧是否同一个桌台
            if (oldShareRecord?.tableUuid == newShareRecord?.tableUuid || newShareRecord == null) {
                //切换过去的过购物费非加购状态
                if (newShareRecord?.isOrderMore == false) {
                    if ((newShareRecord?.diningNumber == 0 || newShareRecord?.diningNumber == null) && newShareRecord?.diningTime.isNullOrEmpty() && newShareRecord?.mobile.isNullOrEmpty() && newShareRecord?.name.isNullOrEmpty()) {
                        //如果要切换过去的状态 没设置 就把 切换前的换过去
                        ShoppingHelper.updateCustomer(
                            newDingStyle,
                            ReserveTableRequest(
                                diningNumber = oldShareRecord?.diningNumber,
                                diningTime = oldShareRecord?.diningTime ?: "",
                                name = oldShareRecord?.name ?: "",
                                mobile = oldShareRecord?.mobile ?: "",
                                areaCode = oldShareRecord?.areaCode ?: "",
                                tableId = ""
                            )
                        )
                    }
                }
            }
        }
    }


    /**
     *  切换外带平台
     *
     */
    fun changeTakeOutPlatform(
        takeOutPlatformModel: TakeOutPlatformModel,
        dinStyle: Int,
        takeOutOrderId: String,
        oldShoppingRecord: ShoppingRecord? = null
    ) {
        viewModelScope.launch {
            val universalTableInfo = PreferenceHelper.getUniversalTableInfo()
            if (universalTableInfo.isNotEmpty()) {
                val tableInfo = gson.fromJson(
                    universalTableInfo,
                    TableResponseItem::class.java
                )
                updateSelectTable(
                    tableInfo.uuid,
                    tableInfo.name,
                    tableInfo.type,
                    dinStyle
                )
            }

            var shoppingRecord =
                ShoppingHelper.updateTakeOutPlatform(takeOutPlatformModel, dinStyle, takeOutOrderId)

            if (oldShoppingRecord != null) {
                /**
                 * 切平台 菜品 信息不带过去
                 */
                shoppingRecord.storeId = oldShoppingRecord.storeId
//                shoppingRecord.takeOutOrderId = oldShoppingRecord.takeOutOrderId
                shoppingRecord.peopleNum = oldShoppingRecord.peopleNum
                shoppingRecord.peopleDate = oldShoppingRecord.peopleDate
//                shoppingRecord.totalPrice = oldShoppingRecord.totalPrice
//                shoppingRecord.vatCharge = oldShoppingRecord.vatCharge
//                shoppingRecord.serviceFeeCharge = oldShoppingRecord.serviceFeeCharge
                shoppingRecord.tableUuid = oldShoppingRecord.tableUuid
                shoppingRecord.tableLabel = oldShoppingRecord.tableLabel
                shoppingRecord.tableType = oldShoppingRecord.tableType
                shoppingRecord.diningNumber = oldShoppingRecord.diningNumber
                shoppingRecord.diningTime = oldShoppingRecord.diningTime
                shoppingRecord.areaCode = oldShoppingRecord.areaCode
                shoppingRecord.mobile = oldShoppingRecord.mobile
                shoppingRecord.name = oldShoppingRecord.name
//                shoppingRecord.goodsVoStr = oldShoppingRecord.goodsVoStr
//                shoppingRecord.isOrderMore = oldShoppingRecord.isOrderMore
//                shoppingRecord.orderMoreID = oldShoppingRecord.orderMoreID
//                shoppingRecord.packPrice = oldShoppingRecord.packPrice
//                shoppingRecord.payStatus = oldShoppingRecord.payStatus
//                shoppingRecord.note = oldShoppingRecord.note
//                shoppingRecord.couponModel = oldShoppingRecord.couponModel
                shoppingRecord.takeOutPlatformModel = takeOutPlatformModel.toJson()
                shoppingRecord.takeOutOrderId = takeOutOrderId
                shoppingRecord.save()


                shoppingRecord = ShoppingHelper.updateTakeOutPlatform(
                    takeOutPlatformModel,
                    dinStyle,
                    takeOutOrderId
                )
            }

            emitUiState(shoppingRecord = shoppingRecord, removeAll = true)
        }
    }

    fun confirmGoodsWeight(
        requireContext: Context,
        goodsRequest: GoodsRequest,
        weight: String? = null,
        orderGoods: OrderMealSetGood? = null
    ) {
        viewModelScope.launch {
            val dingingStyle = getDingingStyle()!!
            val shoppingRecord =
                ShoppingHelper.updateGoodsWeight(dingingStyle, goodsRequest, orderGoods, weight)
            withContext(Dispatchers.Main) {
                emitUiState(shoppingRecord = shoppingRecord)
            }
            if (isRequestSharedInterface(dingingStyle)) {
                asyncGoodList(goodsRequest.goods)
//                val request = if (orderGoods == null) {
//                    //单品称重商品确认
//                    CartConfirmPendingGoodsRequest(
//                        type = 2,
//                        cartsId = goodsRequest.goods?.cartsId,
//                        weight = weight?.toDoubleOrNull(),
//                        mealSetWeighingGoodsKey = null,
//                    )
//                } else {
//                    //套餐内称重商品确认
//                    CartConfirmPendingGoodsRequest(
//                        type = 2,
//                        cartsId = goodsRequest.goods?.cartsId,
//                        weight = orderGoods.weight,
//                        mealSetWeighingGoodsKey = orderGoods.hashKey,
//                    )
//                }
//                val response =
//                    repository.cartConfirmPendingGoods(request)
//                if (response is ApiResponse.Success) {
//                    updateShoppingRecord(response.data, dingingStyle)
//                } else if (response is ApiResponse.Error) {
//                    emitUiState(errorResponse = response)
//                }
            }
        }
    }
}

