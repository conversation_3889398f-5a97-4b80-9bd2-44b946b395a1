package com.metathought.food_order.casheir.ui.dialog

import android.annotation.SuppressLint
import android.graphics.PointF
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import com.lxj.xpopup.XPopup
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.DialogOrderAmountDetailBinding
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.ordered.coupon.GiftProductsListDialog
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2024/10/30
 *@desc 订单金额详情
 **/
@AndroidEntryPoint
class OrderAmountDetailDialog : BaseDialogFragment() {

    private var orderAmountDetail: OrderAmountDetail? = null

    companion object {
        private const val TAG = "OrderAmountDetailDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            orderAmountDetail: OrderAmountDetail? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    orderAmountDetail = orderAmountDetail
                )

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            orderAmountDetail: OrderAmountDetail? = null
        ): OrderAmountDetailDialog {
            val fragment = OrderAmountDetailDialog()
            fragment.orderAmountDetail = orderAmountDetail
            return fragment
        }

        fun getCurrentOrderAmountDetailDialog(fragmentManager: FragmentManager): OrderAmountDetailDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? OrderAmountDetailDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? OrderAmountDetailDialog
            fragment?.dismissAllowingStateLoss()
        }
    }

    private var binding: DialogOrderAmountDetailBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogOrderAmountDetailBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()

        initListener()
    }


    private fun initView() {
        binding?.apply {
            if (orderAmountDetail?.orderedInfo != null) {
                showInfoFromOrder()
            } else {
                showInfoFromCart()
            }
        }
    }

    fun getCurrentOrderNo(): String? {
        if (orderAmountDetail?.orderedInfo != null) {
            return orderAmountDetail?.orderedInfo?.orderNo
        }
        return null
    }

    var isShowVip = false

    @SuppressLint("SetTextI18n")
    private fun showInfoFromOrder() {
        binding?.apply {
            orderAmountDetail?.orderedInfo?.let {
                //非赠品券
                if (!it.isAfterPayStatus()) {
                    //支付前 总计要自己再计算一遍优惠券的金额
                    it.getTotalVipPriceBySelf()
                    it.getTotalPriceBySelf()
                }
                val isTakeOut = it.isTakeOut()
                val subTotalPrice = it.getSubTotal()
                val subVipTotalPrice = it.getVipSubTotal()

                val vatPrice = it.getRealVatPrice()
                val vipVatPrice = it.getVipVat()

                val serviceFeePrice = it.getRealServiceFeePrice()
                val vipServiceFeePrice = it.getVipServiceFeePrice()

                val commissionPrice = it.getCommissionPriceStr()

                val totalPrice = it.getRealPayPrice()
                val totalVipPrice = it.getFinalTotalVipPrice()

                Timber.e("totalVipPrice1111  $totalVipPrice")
                tvPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    (it.totalPackingFee ?: 0).toLong(),
                    it.isKhr()
                )
                tvVipPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    (it.totalPackingFee ?: 0).toLong(),
                    it.isKhr()
                )
//                tvPackingAmount.text = "${it.totalPackingFee?.priceFormatTwoDigitZero2()}"
//                tvVipPackingAmount.text = "${it.totalPackingFee?.priceFormatTwoDigitZero2()}"
                llPackPrice.isVisible = (it.totalPackingFee ?: 0) > 0

                //小计
                tvSubtotal.text = subTotalPrice.priceFormatTwoDigitZero2()
                tvVipSubTotalPrice.text = subVipTotalPrice.priceFormatTwoDigitZero2()

                //增值税
                tvVat.text = vatPrice.priceFormatTwoDigitZero2()
                tvVipVat.text = vipVatPrice.priceFormatTwoDigitZero2()

                //服务费
                tvServiceFee.text = serviceFeePrice.priceFormatTwoDigitZero2()
                tvVipServiceFee.text = vipServiceFeePrice.priceFormatTwoDigitZero2()

                tvCommissionPrice.text = "-${commissionPrice}"

                //总计
                tvTotalPrice.text = totalPrice.priceFormatTwoDigitZero2()
                tvTotalKhrPrice.text =
                    "៛${
                        FoundationHelper.usdConverToKhr(
                            it.conversionRatio ?: FoundationHelper.conversionRatio,
                            totalPrice
                        ).decimalFormatZeroDigit()
                    }"
                //会员价
                tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()
                isShowVip = it.isShowVipPrice()

                tvVipPrice.isVisible = isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()

                tvVipSubTotalPrice.isVisible =
                    isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()
                tvVipVat.isVisible = isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()
                tvVipServiceFee.isVisible =
                    isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()
                tvVipPackingAmount.isVisible =
                    isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()
                tvVipDiscount.isVisible =
                    isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()
                tvVipDiscountAmount.isVisible =
                    isShowVip && !it.isAfterPayStatus() && !it.isOrderExpire()

                //=======优惠活动金额================
                if (it.couponActivityList.isNullOrEmpty()) {
                    llDiscountActivity.isVisible = false
                } else {
                    btnDiscountActivityPriceCue.isVisible = true
                    if (it.couponActivityList?.size == 1) {
                        tvDiscountActivityTitle.text =
                            it.couponActivityList?.firstOrNull()?.activityLabelName
                    } else {
                        tvDiscountActivityTitle.text = getString(R.string.discount_activity)
                    }
                    val discountActivityUnWeightList =
                        it.couponActivityList?.filter { it.weightMark == true } ?: listOf()
                    if (discountActivityUnWeightList.isNotEmpty()) {
                        tvDiscountActivityAmount.text = getString(R.string.to_be_confirmed)
                    } else {
                        //没有待称重商品
                        tvDiscountActivityAmount.text = "-${
                            it.price?.getTotalCouponActivityAmountToLong()
                                ?.priceFormatTwoDigitZero2()
                        }"

                        tvVipDiscountActivityAmount.text =
                            "-${it.getTotalVipCouponActivityAmount().priceFormatTwoDigitZero2()}"
                        val isVipMark = it.couponActivityList?.filter { it.vipMark == true }
                        tvVipDiscountActivityAmount.isVisible =
                            isShowVip && !isVipMark.isNullOrEmpty()

                    }

                    llDiscountActivity.isVisible = true
                }

                //==============================

                //===================优惠券相关=================

                llCoupon.isVisible = false
                val isHasNeedProcess = it.isHasNeedProcess()
                tvCoupon.text = it.getCouponDesc(
                    requireContext(),
                    false, isHasNeedProcess, true
                )
                tvViewCouponGiftGood.isVisible = false

                if (it.getCurrentCoupon() != null) {
                    llCoupon.isVisible = true
                    if (it.getZsCouponValid()) {
                        tvViewCouponGiftGood.isVisible = true
                    }
                }

                //======================优惠券================================

                titleVat.text = "${getString(R.string.vat)}(${it.price?.getVatPercentage()}%)"
                if (isShowVip) {
                    llServiceFee.isVisible =
                        serviceFeePrice > 0 || vipServiceFeePrice > BigDecimal.ZERO
                    llVat.isVisible = vatPrice > 0 || vipVatPrice > BigDecimal.ZERO

                    if (it.isAfterPayStatus()) {
                        //如果是支付后的状态
                        llServiceFee.isVisible = serviceFeePrice > 0
                        llVat.isVisible = vatPrice > 0
                    }
                } else {
                    tvVipPrice.isVisible = false

                    llServiceFee.isVisible = serviceFeePrice > 0

                    llVat.isVisible = vatPrice > 0
                }
                if (it.isSetWholeDiscount()) {
                    if (it.discountReduceActivity != null) {
                        if (it.discountReduceActivity?.isPercentDiscount() == true) {
                            llDiscount.isVisible = true
                            tvDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getWholePercentDiscountAmount()
                                        .times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvVipDiscount.text =
                                "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        it.conversionRatio ?: FoundationHelper.conversionRatio,
                                        it.getVipWholePercentDiscountAmount()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        it.isKhr()
                                    )
                                }"
                            tvVipDiscount.isVisible = isShowVip
                            tvDiscountTitle.text =
                                "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                        } else if (it.discountReduceActivity?.isFixedAmount() == true) {
                            //设置的是整单减免
                            llDiscountAmount.isVisible = true
                            val title = getString(R.string.discounts2)
                            tvDiscountAmount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getNormalWholeItemReduceDollar()
                                        .times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvVipDiscountAmount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getNormalWholeItemReduceVipDollar()
                                        .times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvVipDiscountAmount.isVisible = isShowVip
                            tvDiscountAmountTitle.text = title
                        }
                    } else {
                        if (it.getWholePercentDiscount() > BigDecimal.ZERO) {
                            //设置的是整单百分比折扣
                            llDiscount.isVisible = true
                            tvDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getWholePercentDiscountAmount().times(BigDecimal(100))
                                        .toLong(),
                                    it.wholeDiscountReduce?.isCustomizeKhr() == true
                                )
                            }"
                            tvVipDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getVipWholePercentDiscountAmount().times(BigDecimal(100))
                                        .toLong(),
                                    it.wholeDiscountReduce?.isCustomizeKhr() == true
                                )
                            }"
                            tvVipDiscount.isVisible = isShowVip

                            tvDiscountTitle.text =
                                "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                        }

                        if (it.isSetCustomizeReduce()) {
                            //设置的是整单减免
                            llDiscountAmount.isVisible = true
//                            var content = "-${
//                                FoundationHelper.getPriceStrByUnit(
//                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
//                                    it.getNormalWholeReduceDollar().times(BigDecimal(100))
//                                        .toLong(),
//                                    it.isKhr()
//                                )
//                            }"
//                            if (it.getNormalWholeReduceKhr() > BigDecimal.ZERO || it.getVipWholeReduceKhr() > BigDecimal.ZERO) {
//                                content =
//                                    "-៛${it.getNormalWholeReduceKhr().decimalFormatZeroDigit()}"
//                            }
                            if (it.wholeDiscountReduce?.isCustomizeKhr() == true) {
                                tvDiscountAmount.text =
                                    "-៛${it.getNormalWholeItemReduceKhr().decimalFormatZeroDigit()}"
                                tvVipDiscountAmount.text =
                                    "-៛${
                                        it.getNormalWholeItemReduceVipKhr().decimalFormatZeroDigit()
                                    }"
                                tvVipDiscountAmount.isVisible =
                                    isShowVip && it.getVipWholeReduceKhr() > BigDecimal.ZERO
                            } else {
                                tvDiscountAmount.text =
                                    "-${
                                        it.getNormalWholeItemReduceDollar()
                                            .priceFormatTwoDigitZero2()
                                    }"
                                tvVipDiscountAmount.text =
                                    "-${
                                        it.getNormalWholeItemReduceVipDollar()
                                            .priceFormatTwoDigitZero2()
                                    }"
                                tvVipDiscountAmount.isVisible =
                                    isShowVip && it.getVipWholeReduceDollar() > BigDecimal.ZERO
                            }

                            if (it.isAfterPayStatus()) {
                                tvVipDiscountAmount.isVisible = false
                                //如果支付完了以后 有设置值才显示
                                if (it.getNormalWholeReduceDollar() == BigDecimal.ZERO && it.getNormalWholeReduceKhr() == BigDecimal.ZERO) {
                                    llDiscountAmount.isVisible = false
                                }
                            }
                            tvDiscountAmountTitle.text = getString(R.string.discounts2)
                        }
                    }
                    btnDiscountCue.isVisible = it.getWholeDiscountReason().isNotEmpty()
                    btnDiscountAmountCue.isVisible = it.getWholeDiscountReason().isNotEmpty()
                }


                if (isTakeOut) {
                    llCommission.isVisible = true
                }

                showTobeProcess(it)
            }
        }
    }

    private fun showTobeProcess(ordered: OrderedInfoResponse?): Boolean {
        //是否有待定价商品
        val isHasNeedProcess = ordered?.isHasNeedProcess() ?: false
        ordered?.let {
            binding?.apply {
                var unProcessNum = 0
                var unProcessServiceWhiteGoods = 0
                if (it.needShowUnProcessState()) {
                    it.goods?.forEach { good ->
                        if (!good.isHasProcessed()) {
                            unProcessNum += 1
                            if (good.serviceChargeWhitelisting == true) {
                                unProcessServiceWhiteGoods += 1
                            }
                        }
                    }
                }
                Timber.d("showTobeProcess isHasNeedProcess:$isHasNeedProcess $unProcessNum>${unProcessServiceWhiteGoods}")
                if (isHasNeedProcess) {
                    tvSubtotal.text = getString(R.string.to_be_confirmed)
                    tvTotalPrice.text = getString(R.string.to_be_confirmed)
                    tvTotalKhrPrice.isVisible = false
                    tvVipPrice.isVisible = false
                    tvVipVat.isVisible = false
                    tvVipSubTotalPrice.isVisible = false
                    tvVipServiceFee.isVisible = false
                    tvVat.text = getString(R.string.to_be_confirmed)
                    llVat.isVisible =
                        (ordered.price?.vatPercentage ?: BigDecimal(0)) > BigDecimal(0)

                    if (!ordered.isTakeAway()) {
                        if (unProcessNum > unProcessServiceWhiteGoods && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)) {
                            tvServiceFee.text = getString(R.string.to_be_confirmed)
                            llServiceFee.isVisible = true
                        }
                    }

                }
            }
        }
        return isHasNeedProcess
    }


    @SuppressLint("SetTextI18n")
    private fun showInfoFromCart() {
        binding?.apply {
            isShowVip = orderAmountDetail?.hasVipPrice ?: false
            //是否外卖
            var isTakeOut = orderAmountDetail?.takeOutModel != null
//                (orderAmountDetail?.diningStyle ?: 0) >= TakeOutPlatformToDiningHelper.BASE_INDEX
            var conversionRatio =
                orderAmountDetail?.conversionRatio ?: FoundationHelper.useConversionRatio

            var isNeedProcess = orderAmountDetail?.isNeedProcess ?: false

            var packingFee = orderAmountDetail?.packagePrice ?: 0L

            var subTotalPrice = orderAmountDetail?.subtotalPrice ?: 0L
            var subVipTotalPrice = orderAmountDetail?.vipSubtotalPrice ?: 0L

            var vatPrice = orderAmountDetail?.vatPrice ?: 0L
            var vipVatPrice = orderAmountDetail?.vipVatPrice ?: 0L

            var serviceFeePrice = orderAmountDetail?.servicePrice ?: 0L
            var vipServiceFeePrice = orderAmountDetail?.vipServicePrice ?: 0L

            var commissionPrice = orderAmountDetail?.totalCommissionPrice ?: 0L

            var totalPrice = orderAmountDetail?.totalPrice ?: 0L
            var totalVipPrice = orderAmountDetail?.totalVipPrice ?: 0L

            val discountActivityList = orderAmountDetail?.couponActivityModel ?: listOf()
            if (orderAmountDetail?.couponActivityModel.isNullOrEmpty()) {
                llDiscountActivity.isVisible = false
            } else {
                btnDiscountActivityPriceCue.isVisible = true
                if (discountActivityList.size == 1) {
                    tvDiscountActivityTitle.text =
                        discountActivityList.first().activityLabel?.name
                } else {
                    tvDiscountActivityTitle.text = getString(R.string.discount_activity)
                }
                val discountActivityUnWeightList =
                    discountActivityList.filter { it.weightMark == true }
                if (discountActivityUnWeightList.isNotEmpty()) {
                    tvDiscountActivityAmount.text = getString(R.string.to_be_confirmed)
                } else {
                    val discountActivityAmount = discountActivityList.fold(0L) { acc, element ->
                        acc + (element.activityCouponAmount ?: 0)
                    }
                    val discountActivityVipAmount =
                        discountActivityList.fold(0L) { acc, element ->
                            acc + (element.activityVipCouponAmount ?: 0)
                        }
                    tvDiscountActivityAmount.text = "-${
                        discountActivityAmount.priceFormatTwoDigitZero2()
                    }"
                    tvVipDiscountActivityAmount.text = "-${
                        discountActivityVipAmount.priceFormatTwoDigitZero2()
                    }"
                    tvVipDiscountActivityAmount.isVisible = isShowVip
                    val isVipMarkList = discountActivityList.filter { it.vipMark == true }
                    tvVipDiscountActivityAmount.isVisible =
                        isShowVip && isVipMarkList.isNotEmpty()
                }
                llDiscountActivity.isVisible = true
            }

            Timber.e("totalVipPrice1111  $totalVipPrice")
            tvPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio,
                packingFee,
                orderAmountDetail?.takeOutModel?.isKhr() == true
            )

            tvVipPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio,
                packingFee,
                orderAmountDetail?.takeOutModel?.isKhr() == true
            )
//            tvPackingAmount.text = packingFee.priceFormatTwoDigitZero2()
            llPackPrice.isVisible = packingFee > 0

            //小计
            tvSubtotal.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio,
                subTotalPrice,
                orderAmountDetail?.takeOutModel?.isKhr() == true
            )

            tvVipSubTotalPrice.text = subVipTotalPrice.priceFormatTwoDigitZero2()

            titleVat.text =
                "${getString(R.string.vat)}(${MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage()}%)"
            //增值税
            tvVat.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio,
                vatPrice,
                orderAmountDetail?.takeOutModel?.isKhr() == true
            )
            tvVipVat.text = vipVatPrice.priceFormatTwoDigitZero2()

            //服务费
            tvServiceFee.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio,
                serviceFeePrice,
                orderAmountDetail?.takeOutModel?.isKhr() == true
            )
            tvVipServiceFee.text = vipServiceFeePrice.priceFormatTwoDigitZero2()

            tvCommissionPrice.text = "-${
                FoundationHelper.getPriceStrByUnit(
                    conversionRatio,
                    commissionPrice,
                    orderAmountDetail?.takeOutModel?.isKhr() == true
                )
            }"

            //总计
            tvTotalPrice.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio,
                totalPrice,
                orderAmountDetail?.takeOutModel?.isKhr() == true
            )
            llCommission.isVisible = isTakeOut
            tvTotalKhrPrice.isVisible = orderAmountDetail?.takeOutModel?.isKhr() != true
            tvTotalKhrPrice.text =
                "៛${
                    FoundationHelper.usdConverToKhr(
                        conversionRatio,
                        totalPrice
                    ).decimalFormatZeroDigit()
                }"

            //会员价
            tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()
//            val isShowVip = it.isShowVipPrice()
            tvVipPrice.isVisible = isShowVip
            tvVipPackingAmount.isVisible = isShowVip
            tvVipSubTotalPrice.isVisible = isShowVip
            tvVipVat.isVisible = isShowVip
            tvVipServiceFee.isVisible = isShowVip

            //===================优惠券相关=================

            llCoupon.isVisible = false

            tvCoupon.text = orderAmountDetail?.couponDesc ?: ""
            tvViewCouponGiftGood.isVisible = false

            if (orderAmountDetail?.coupon != null) {
                llCoupon.isVisible = true
                if (orderAmountDetail?.coupon?.isZsCoupon() == true) {
                    tvViewCouponGiftGood.isVisible = true
                }
            }

            //======================优惠券================================

            if (isShowVip) {
                llServiceFee.isVisible = serviceFeePrice > 0 || vipServiceFeePrice > 0
                llVat.isVisible = vatPrice > 0 || vipVatPrice > 0
            } else {
                tvVipPrice.isVisible = false
                llServiceFee.isVisible = serviceFeePrice > 0
                llVat.isVisible = vatPrice > 0
            }


            /**
             * 点单打开的详情没折扣相关内容
             */
            llDiscount.isVisible = false
            llDiscountAmount.isVisible = false
            var unProcessNum = 0
            var unProcessServiceWhiteGoodsNum = 0
            orderAmountDetail?.goods?.forEach {
                Timber.d("${it?.toJson()}")
                if (it?.isHasProcessed() == false) {
                    //未定价
                    unProcessNum += 1
                    if (it.serviceChargeWhitelisting == true) {
                        unProcessServiceWhiteGoodsNum += 1
                    }
                }
            }

            Timber.d("showTobeProcess isNeedProcess:$isNeedProcess  $unProcessNum>${unProcessServiceWhiteGoodsNum}")
            if (isNeedProcess) {
                tvSubtotal.text = getString(R.string.to_be_confirmed)
                tvTotalPrice.text = getString(R.string.to_be_confirmed)
                tvTotalKhrPrice.isVisible = false
                tvVipPrice.isVisible = false
                tvVipVat.isVisible = false
                tvVipSubTotalPrice.isVisible = false
                tvVipServiceFee.isVisible = false

//                if (unWeightNum > unWeightVatWhiteGoodsNum && (MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage() != 0)) {
                llVat.isVisible =
                    (MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage() != 0)
                tvVat.text = getString(R.string.to_be_confirmed)
//                }

                if (orderAmountDetail?.diningStyle != DiningStyleEnum.TAKE_AWAY.id) {
                    //如果未称重的商品数量 大于 未称重商品白名单数量  说明 有非白名单未称重商品 要显示 未称重字样
                    if (unProcessNum > unProcessServiceWhiteGoodsNum && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)
                    ) {
                        tvServiceFee.text = getString(R.string.to_be_confirmed)
                        llServiceFee.isVisible = true
                    }
                }
            }
        }
    }


    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener { dismissAllowingStateLoss() }

            tvViewCouponGiftGood.setOnClickListener {
                var list: List<UsageGoods>? = null
                if (orderAmountDetail?.orderedInfo != null) {
                    list = orderAmountDetail?.orderedInfo?.getGiftGoodsList()
                } else {
                    list = orderAmountDetail?.coupon?.getGiftGoodsList()
                }
                GiftProductsListDialog.showDialog(
                    fragmentManager = parentFragmentManager,
                    list
                )
            }


            llPackPrice.setOnClickListener {
                if (!btnPackPriceCue.isVisible) {
                    return@setOnClickListener
                }
//                PackPriceDetailDialog.showDialog(
//                    parentFragmentManager,
//                    goods = orderAmountDetail?.goods,
//                )
                SingleClickUtils.isFastDoubleClick {
                    if (orderAmountDetail?.orderedInfo == null) {
                        PackPriceDetailDialog.showDialog(
                            parentFragmentManager, goods = orderAmountDetail?.goods,
                            takeOutPlatformModel = orderAmountDetail?.takeOutModel,
                            conversionRatio = orderAmountDetail?.conversionRatio
                        )
                    } else {
                        PackPriceDetailDialog.showDialog(
                            parentFragmentManager, goods = orderAmountDetail?.goods,
                            takeOutPlatformModel = TakeOutPlatformModel(
                                id = orderAmountDetail?.orderedInfo?.deliveryPlatformId,
                                name = orderAmountDetail?.orderedInfo?.deliveryPlatformName,
                                type = orderAmountDetail?.orderedInfo?.deliveryPlatformCurrencyType
                            ),
                            conversionRatio = orderAmountDetail?.orderedInfo?.conversionRatio
                        )
                    }
                }
            }

            llServiceFee.setOnClickListener {
                if (!btnServiceFeeCue.isVisible) {
                    return@setOnClickListener
                }
                ServiceFeeDetailDialog.showDialog(
                    parentFragmentManager,
                    goods = orderAmountDetail?.goods,
                    orderedInfo = orderAmountDetail?.orderedInfo
                )
            }

            llDiscountActivity.setOnClickListener {
                if (!btnDiscountActivityPriceCue.isVisible) {
                    return@setOnClickListener
                }
                DiscountActivityDialog.showDialog(
                    parentFragmentManager,
                    if (orderAmountDetail?.orderedInfo != null) {
                        (orderAmountDetail?.orderedInfo?.couponActivityList) ?: listOf()
                    } else {
                        orderAmountDetail?.couponActivityModel ?: listOf()
                    },
                    isShowVip
                )
            }

            llCommission.setOnClickListener {
                if (!btnCommissionCue.isVisible) {
                    return@setOnClickListener
                }
                SingleClickUtils.isFastDoubleClick {
                    if (orderAmountDetail?.orderedInfo == null) {
                        CommissionDetailDialog.showDialog(
                            parentFragmentManager, goods = orderAmountDetail?.goods,
                            takeOutPlatformModel = orderAmountDetail?.takeOutModel,
                            conversionRatio = orderAmountDetail?.conversionRatio
                        )
                    } else {
                        CommissionDetailDialog.showDialog(
                            parentFragmentManager, goods = orderAmountDetail?.goods,
                            takeOutPlatformModel = TakeOutPlatformModel(
                                id = orderAmountDetail?.orderedInfo?.deliveryPlatformId,
                                name = orderAmountDetail?.orderedInfo?.deliveryPlatformName,
                                type = orderAmountDetail?.orderedInfo?.deliveryPlatformCurrencyType
                            ),
                            conversionRatio = orderAmountDetail?.orderedInfo?.conversionRatio
                        )
                    }
                }
            }

            llDiscount.setOnClickListener {
                if (!btnDiscountCue.isVisible) {
                    return@setOnClickListener
                }
                if (orderAmountDetail?.orderedInfo?.getWholeDiscountReason().isNullOrEmpty()) {
                    return@setOnClickListener
                }
                val location = IntArray(2)
                btnDiscountCue.getLocationOnScreen(location)
                val x = location[0] + btnDiscountCue.width / 2
                val y = location[1]
                XPopup.Builder(requireContext())
                    .hasShadowBg(false)
                    .isTouchThrough(true)
                    .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                    .atPoint(PointF(x.toFloat(), y.toFloat()))
                    .isCenterHorizontal(true)
                    .hasShadowBg(false) // 去掉半透明背景
                    .asCustom(
                        CustomBubbleAttachPopup(
                            requireContext(),
                            orderAmountDetail?.orderedInfo?.getWholeDiscountReason()
                        )
                    )
                    .show()
            }

            llDiscountAmount.setOnClickListener {
                if (!btnDiscountAmountCue.isVisible) {
                    return@setOnClickListener
                }
                if (orderAmountDetail?.orderedInfo?.getWholeDiscountReason().isNullOrEmpty()) {
                    return@setOnClickListener
                }
                val location = IntArray(2)
                btnDiscountAmountCue.getLocationOnScreen(location)
                val x = location[0] + btnDiscountAmountCue.width / 2
                val y = location[1]
                XPopup.Builder(requireContext())
                    .hasShadowBg(false)
                    .isTouchThrough(true)
                    .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                    .atPoint(PointF(x.toFloat(), y.toFloat()))
                    .isCenterHorizontal(true)
                    .hasShadowBg(false) // 去掉半透明背景
                    .asCustom(
                        CustomBubbleAttachPopup(
                            requireContext(),
                            orderAmountDetail?.orderedInfo?.getWholeDiscountReason()
                        )
                    )
                    .show()
            }

        }
    }
//
//    override fun onResume() {
//        super.onResume()
//
//    }
}


data class OrderAmountDetail(
    var subtotalPrice: Long? = 0,
    var vipSubtotalPrice: Long? = 0,

    var packagePrice: Long? = 0,

    var vatPrice: Long? = 0,
    var vipVatPrice: Long? = 0,

    var servicePrice: Long? = 0,
    var vipServicePrice: Long? = 0,

    var coupon: CouponModel? = null,
    var couponDesc: SpannableStringBuilder? = null,

    var totalCommissionPrice: Long? = null,
    var takeOutModel: TakeOutPlatformModel? = null,
//    var deliveryPlatformName: String? = null,

    var reduceType: Int? = null,
    var reduceRate: Double? = null,
    var reduceAmount: Long? = null,
    var reduceDollar: Long? = null,
    var reduceKhr: Long? = 0,

    var totalPrice: Long? = 0,
    var totalVipPrice: Long? = 0,

    var hasVipPrice: Boolean? = false,
    var isNeedProcess: Boolean? = false,

    var conversionRatio: Long? = null,

    var couponActivityModel: List<CouponActivityModel>? = null,


    var goods: List<Goods?>? = null,
    var orderedInfo: OrderedInfoResponse? = null,

    var diningStyle: Int? = null,
)