package com.metathought.food_order.casheir.ui.widget

import android.content.Context

import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible

import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.ViewTitleContentTextBinding
import com.metathought.food_order.casheir.utils.DisplayUtils


class TitleContentView(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {
    private var _binding: ViewTitleContentTextBinding? = null

    init {
        // 取得属性值数组
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.TitleContentView)

        // 获取尺寸属性
        val title = typedArray.getString(
            R.styleable.TitleContentView_title
        )

        // 最后需要回收数组
        typedArray.recycle()
        initView()

        _binding?.apply {
            tvTitle.text = title
        }
    }

    private fun initView() {

        _binding = ViewTitleContentTextBinding.inflate(LayoutInflater.from(context), this, true)

    }

    fun setTitle(title: String?) {
        _binding?.apply {
            tvTitle.text = title
        }
    }

    fun setContent(content: String?) {
        _binding?.apply {
            tvContent.text = content
        }
    }

    fun setContentVisible(isVisible: Boolean) {
        _binding?.apply {
            tvContent.isVisible = isVisible
        }
    }

    fun setContentStartDrawable(res: Int) {
        _binding?.apply {
            val drawable = ContextCompat.getDrawable(
                context,
                res
            )
            tvContent.compoundDrawablePadding = DisplayUtils.dp2px(context, 5f)
            tvContent.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null)
        }
    }

}