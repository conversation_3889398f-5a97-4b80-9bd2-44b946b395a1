package com.metathought.food_order.casheir.helper

import android.hardware.usb.UsbDevice
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.UsbPrinterDevice
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.listener.SettableFuture
import net.posprinter.POSConnect
import net.posprinter.POSPrinter
import net.posprinter.TSPLPrinter
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.concurrent.ExecutionException


/**
 *<AUTHOR>
 *@time  2024/7/22
 *@desc
 **/


object PrinterUsbDeviceHelper {

//    /**
//     * USB 打印机列表
//     */
//    var usbPrinterInfoList = mutableListOf<PrinterConfigInfo>()

    /**
     *USB Connection
     */
    var usbDeviceMap: MutableMap<String, UsbPrinterDevice> = mutableMapOf()

    /**
     * vendorId_productId 作为key
     */
    private var localPrinterInfoMap = mutableMapOf(
        "1155_22339" to LocalPrinterEnum.TICKET_PRINTER.id,
        "8137_8214" to LocalPrinterEnum.LABEL_PRINTER.id,
    )

    /**
     * 用于登录后提前初次连接打印机
     * Used to connect to the printer for the first time in advance after logging in
     */
//    fun initConnectPrinter() {
//        val connectUSB = isConnectUSB()
//        connectUSB.addListener(object : ListenableFuture.Listener<Boolean> {
//            override fun onSuccess(result: Boolean) {
//                if (!result) {
//                    connectUSB { code, _, _ ->
//                        when (code) {
//                            POSConnect.CONNECT_SUCCESS -> {
//                                Timber.e("initConnectPrinter  CONNECT_SUCCESS")
//                                PrinterDeviceHelper.printer =
//                                    POSPrinter(PrinterDeviceHelper.curConnect)
//                                EventBus.getDefault().post(
//                                    SimpleEvent(
//                                        SimpleEventType.UPDATE_PRINTER_MANAGER_LIST,
//                                        null
//                                    )
//                                )
//                            }
//
//                            POSConnect.CONNECT_FAIL -> {
//                                Timber.e("initConnectPrinter  CONNECT_FAIL")
//                            }
//                        }
//                    }
//                }
//            }
//
//            override fun onFailure(e: ExecutionException) {
//            }
//        }
//        )
//    }

    /**
     * 获取打印机列表 连接打印机 Connect the printer
     */
    fun initUsbPrintAndConnectUSB() {
        try {
            val usbDevice = POSConnect.getUsbDevice(MyApplication.myAppInstance)
            releaseAllUsbConnect()
            EventBus.getDefault().post(
                SimpleEvent(
                    SimpleEventType.UPDATE_USB_PRINT_INFO,
                    null
                )
            )
            usbDevice.forEach {
//                Timber.e("usbDevice>>> vendorId: ${it.vendorId}  || productId: ${it.productId}  || deviceName: ${it.deviceName}  || deviceClass: ${it.deviceClass} || productName: ${it.productName} ||  serialNumber: ${it.serialNumber}")
                Timber.e("usbDevice  ${it.toJson()}")
                usbDeviceMap[it.deviceName] = UsbPrinterDevice().apply {
                    this.usbDevice = it
                    this.iDeviceConnection = POSConnect.createDevice(POSConnect.DEVICE_TYPE_USB)
                    this.iDeviceConnection?.connect(it.deviceName) { code, _, _ ->
                        when (code) {
                            POSConnect.CONNECT_SUCCESS -> {
                                Timber.e("${it.deviceName}  连接成功")
                                PrinterDeviceHelper.dealUsbPrinterList()
                                EventBus.getDefault().post(
                                    SimpleEvent(
                                        SimpleEventType.UPDATE_USB_PRINT_INFO,
                                        null
                                    )
                                )
                            }

                            POSConnect.CONNECT_FAIL -> {

                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 创建 USB 小票打印
     *
     * @param deviceName
     */
    fun createPOSPrinter(deviceName: String?): UsbPrinterDevice? {
        if (usbDeviceMap.contains(deviceName)) {
            usbDeviceMap[deviceName]?.tSPLPrinter = null
            if (usbDeviceMap[deviceName]?.iDeviceConnection != null) {
                Timber.e("创建小票打印实例")
                usbDeviceMap[deviceName]?.pOSPrinter =
                    POSPrinter(usbDeviceMap[deviceName]?.iDeviceConnection!!)
            }
            return usbDeviceMap[deviceName]
        }
        return null
    }

    /**
     * 创建 USB 标签打印
     *
     * @param deviceName
     */
    fun createTSPLPrinter(deviceName: String?): UsbPrinterDevice? {
        if (usbDeviceMap.contains(deviceName)) {
            usbDeviceMap[deviceName]?.pOSPrinter = null
            if (usbDeviceMap[deviceName]?.iDeviceConnection != null) {
                Timber.e("创建标签打印实例")
                usbDeviceMap[deviceName]?.tSPLPrinter =
                    TSPLPrinter(usbDeviceMap[deviceName]?.iDeviceConnection!!)
            }
            return usbDeviceMap[deviceName]
        }
        return null
    }

    /**
     * 释放掉所有Usb打印资源
     */
    fun releaseAllUsbConnect() {
        usbDeviceMap.forEach { s, usbPrinterDevice ->
            usbPrinterDevice.iDeviceConnection?.close()
            usbPrinterDevice.pOSPrinter = null
            usbPrinterDevice.tSPLPrinter = null
        }
        usbDeviceMap.clear()
        PrinterDeviceHelper.clearUsbPrintInPrintList()

    }


    /**
     * XPrinter是否连接成功
     *Is XPrinter connected successfully
     */
    fun isConnectUSB(deviceName: String?): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
        if (usbDeviceMap.contains(deviceName)) {
            val device = usbDeviceMap[deviceName]
            if (device?.pOSPrinter != null) {
                /**
                 * 校验小票打印机
                 */
                device?.pOSPrinter?.isConnect { status -> //1: connected 0: disconnect
                    future.set(status == 1)
                }
            } else if (device?.tSPLPrinter != null) {
                /**
                 * 校验标签打印机
                 */
                device?.tSPLPrinter?.isConnect { status -> //1: connected 0: disconnect
                    future.set(status == 1)
                }
            } else if (device?.iDeviceConnection != null) {
                device?.iDeviceConnection?.isConnect(byteArrayOf(0)) { status -> //1: connected 0: disconnect
                    future.set(status == 1)
                }
            } else {
                future.set(false)
            }
        } else {
            future.set(false)
        }
        return future
    }

    /**
     * XPrinter 本地小票打印机是否有链接
     *Is XPrinter connected successfully
     */
    fun isPosPrinterConnectUSB(): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
//        future.set(false)
        usbDeviceMap.forEach { s, usbPrinterDevice ->
            if (usbPrinterDevice.pOSPrinter != null) {
                usbPrinterDevice.pOSPrinter?.isConnect { status -> //1: connected 0: disconnect
                    //如果有一个链接了那就连接了
                    if (status == 1 && !future.isDone) {
                        future.set(true)
                    }
                }
            }
        }
        if (!future.isDone) {
            future.set(false)
        }
        Timber.e("====== ${future.get()}")
        return future
    }

    /**
     *  是否 标签打印
     *
     */
    fun isLabelPrinter(vendorId: Int? = null, productId: Int? = null): Boolean {
        if (vendorId == null || productId == null) {
            return false
        }
        val key = "${vendorId}_${productId}"
        if (localPrinterInfoMap.containsKey(key)) {
            return localPrinterInfoMap[key] == LocalPrinterEnum.LABEL_PRINTER.id
        }
        return false
    }


    /**
     * 获取USB POSPrinter打印机实例
     * Get XPrinter instance
     */
    fun getPosPrinter(usbDevice: UsbDevice?): SettableFuture<POSPrinter?> {
        val future = SettableFuture<POSPrinter?>()
        if (usbDevice == null) {
            future.setException(Exception("USB设备为空"))
            return future
        }

        val deviceName = usbDevice.deviceName
        val connectUSB = isConnectUSB(deviceName)

        connectUSB.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                Timber.d("USB连接状态检查结果: $result, 设备: $deviceName")

                if (result) {
                    // 设备已连接，检查是否有现有的打印机实例
                    if (usbDeviceMap[deviceName]?.pOSPrinter != null) {
                        Timber.d("使用现有POSPrinter实例: $deviceName")
                        future.set(usbDeviceMap[deviceName]?.pOSPrinter)
                    } else {
                        // 创建新的打印机实例前，确保释放标签打印机实例
                        Timber.d("创建新的POSPrinter实例: $deviceName")
                        createPOSPrinter(deviceName)
                        future.set(usbDeviceMap[deviceName]?.pOSPrinter)
                    }
                } else {
                    // 设备未连接，尝试连接
                    Timber.d("设备未连接，尝试连接: $deviceName")

                    // 如果设备已存在，先释放资源
                    if (usbDeviceMap.containsKey(deviceName)) {
                        releaseUsbDevice(deviceName)
                    }

                    // 创建新的设备连接
                    usbDeviceMap[deviceName] = UsbPrinterDevice().apply {
                        this.usbDevice = usbDevice
                        this.iDeviceConnection = POSConnect.createDevice(POSConnect.DEVICE_TYPE_USB)
                        this.iDeviceConnection?.connect(deviceName) { code, _, _ ->
                            when (code) {
                                POSConnect.CONNECT_SUCCESS -> {
                                    Timber.d("设备连接成功: $deviceName")
                                    createPOSPrinter(deviceName)
                                    future.set(usbDeviceMap[deviceName]?.pOSPrinter)
                                }
                                POSConnect.CONNECT_FAIL -> {
                                    Timber.e("设备连接失败: $deviceName")
                                    future.setException(Exception("Connect fail"))
                                }
                            }
                        }
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {
                Timber.e(e, "USB连接状态检查失败: $deviceName")
                future.setException(e)
            }
        })

        return future
    }

    /**
     * 获取USB TSPLPrinter打印机实例
     * Get Label Printer instance
     */
    fun getLabelPrinter(usbDevice: UsbDevice?): SettableFuture<TSPLPrinter?> {
        val future = SettableFuture<TSPLPrinter?>()
        if (usbDevice == null) {
            future.setException(Exception("USB设备为空"))
            return future
        }

        val deviceName = usbDevice.deviceName
        val connectUSB = isConnectUSB(deviceName)

        connectUSB.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                Timber.d("USB连接状态检查结果: $result, 设备: $deviceName")

                if (result) {
                    // 设备已连接，检查是否有现有的打印机实例
                    if (usbDeviceMap[deviceName]?.tSPLPrinter != null) {
                        Timber.d("使用现有TSPLPrinter实例: $deviceName")
                        future.set(usbDeviceMap[deviceName]?.tSPLPrinter)
                    } else {
                        // 创建新的打印机实例前，确保释放小票打印机实例
                        Timber.d("创建新的TSPLPrinter实例: $deviceName")
                        createTSPLPrinter(deviceName)
                        future.set(usbDeviceMap[deviceName]?.tSPLPrinter)
                    }
                } else {
                    // 设备未连接，尝试连接
                    Timber.d("设备未连接，尝试连接: $deviceName")

                    // 如果设备已存在，先释放资源
                    if (usbDeviceMap.containsKey(deviceName)) {
                        releaseUsbDevice(deviceName)
                    }

                    // 创建新的设备连接
                    usbDeviceMap[deviceName] = UsbPrinterDevice().apply {
                        this.usbDevice = usbDevice
                        this.iDeviceConnection = POSConnect.createDevice(POSConnect.DEVICE_TYPE_USB)
                        this.iDeviceConnection?.connect(deviceName) { code, _, _ ->
                            when (code) {
                                POSConnect.CONNECT_SUCCESS -> {
                                    Timber.d("设备连接成功: $deviceName")
                                    createTSPLPrinter(deviceName)
                                    future.set(usbDeviceMap[deviceName]?.tSPLPrinter)
                                }
                                POSConnect.CONNECT_FAIL -> {
                                    Timber.e("设备连接失败: $deviceName")
                                    future.setException(Exception("Connect fail"))
                                }
                            }
                        }
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {
                Timber.e(e, "USB连接状态检查失败: $deviceName")
                future.setException(e)
            }
        })

        return future
    }

    /**
     * 清空所有打印设备 对应的实例
     *
     */
    fun cleatALlPrinterObject() {
        usbDeviceMap.forEach {
            it.value.pOSPrinter = null
            it.value.tSPLPrinter = null
        }
    }

    /**
     * 释放单个USB打印机资源
     * @param deviceName 设备名称
     * @return 是否成功释放
     */
    fun releaseUsbDevice(deviceName: String?): Boolean {
        if (deviceName.isNullOrEmpty() || !usbDeviceMap.containsKey(deviceName)) {
            Timber.w("无法释放USB打印机资源: 设备名称为空或设备不存在")
            return false
        }

        try {
            Timber.d("开始释放USB打印机资源: $deviceName")
            val usbPrinterDevice = usbDeviceMap[deviceName] ?: return false

            // 先关闭打印机实例
            if (usbPrinterDevice.pOSPrinter != null) {
                Timber.d("关闭POSPrinter实例: $deviceName")
//                usbPrinterDevice.pOSPrinter?.close()
                usbPrinterDevice.pOSPrinter = null
            }

            if (usbPrinterDevice.tSPLPrinter != null) {
                Timber.d("关闭TSPLPrinter实例: $deviceName")
//                usbPrinterDevice.tSPLPrinter?.close()
                usbPrinterDevice.tSPLPrinter = null
            }

            // 最后关闭连接
            if (usbPrinterDevice.iDeviceConnection != null) {
                Timber.d("关闭设备连接: $deviceName")
                usbPrinterDevice.iDeviceConnection?.close()
                usbPrinterDevice.iDeviceConnection = null
            }

            // 从映射中移除
            usbDeviceMap.remove(deviceName)

            Timber.d("USB打印机资源释放完成: $deviceName")
            return true
        } catch (e: Exception) {
            Timber.e(e, "释放USB打印机资源失败: $deviceName")
            return false
        }
    }

}