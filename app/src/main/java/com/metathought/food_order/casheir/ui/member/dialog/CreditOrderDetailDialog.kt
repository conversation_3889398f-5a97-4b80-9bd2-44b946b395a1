package com.metathought.food_order.casheir.ui.member.dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecordVo
import com.metathought.food_order.casheir.databinding.DialogCreditDetailBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.member.credit.MemberCreditViewModel
import dagger.hilt.android.AndroidEntryPoint


/**
 * 挂账订单详情弹窗
 */
@AndroidEntryPoint
class CreditOrderDetailDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "CreditOrderDetailDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            creditRecordVo: CreditRecordVo
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(creditRecordVo)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CreditOrderDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            creditRecordVo: CreditRecordVo
        ): CreditOrderDetailDialog {
            val fragment = CreditOrderDetailDialog()
            fragment.creditRecordVo = creditRecordVo
            return fragment
        }
    }


    private val viewModel: MemberCreditViewModel by viewModels()

    private var creditRecordVo: CreditRecordVo? = null

    private var binding: DialogCreditDetailBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCreditDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        onTouchOutSide(binding?.mainLayout)
        initData()
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiOrderDetailsState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        state.data.let {
                            layoutDetail.setTitle(it.orderNo?:"")
                            layoutDetail.setOrderInfo(it)
                            layoutDetail.replaceorderedInfo(it)
                        }
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                    }

                }

                else -> {

                }
            }

        }
    }

    private fun initListener() {
        binding?.apply {
            layoutDetail.onCloseListener = {
                dismissAllowingStateLoss()
            }
            layoutDetail.onBackListener = {
                dismissAllowingStateLoss()
            }
        }
    }

    private fun initData() {
        binding?.apply {
            viewModel.getOrderDetails(creditRecordVo?.orderId)
        }
    }

    private fun initView() {
        binding?.apply {
            layoutDetail.setFragmentManager(parentFragmentManager)
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * PERCENT_85).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }
}