package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.google.gson.Gson
import com.metathought.food_order.casheir.MainActivity
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.databinding.DialogSessionExpiredBinding
import com.metathought.food_order.casheir.extension.navigateWithAnim
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.UnReadAndUnPrintHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import kotlinx.coroutines.launch


class SessionExpiredDialog : BaseDialogFragment() {
    private var binding: DialogSessionExpiredBinding? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSessionExpiredBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {
        binding?.apply {
            val title = arguments?.getString(SESSION_EXPIRE_MSG)
                ?: getString(R.string.login_information_expired_please_login_again)
            tvContent.text = title
        }

    }

    private fun initListener() {
        binding?.apply {
            MainDashboardFragment.CURRENT_USER = null
            btnYes.setOnClickListener {
                lifecycleScope.launch {
                    context?.let { it1 ->
                        PreferenceDataStoreHelper.getInstance(it1).apply {
                            val userLoginResponse = Gson().fromJson(
                                getFirstPreference(
                                    PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                    ""
                                ), UserLoginResponse::class.java
                            )
                            userLoginResponse.token = ""
                            this.putPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                userLoginResponse.toJson()
                            )

                            UnReadAndUnPrintHelper.clear()
                            val currentFragment =
                                parentFragmentManager.fragments.firstOrNull()
                            val childFragment =
                                currentFragment?.childFragmentManager?.fragments?.firstOrNull()
                            if (childFragment is MainDashboardFragment) {
                                findNavController().navigateWithAnim(
                                    R.id.action_mainDashboardFragment_to_loginFragment,
                                    popupToId = R.id.mainDashboardFragment
                                )
                            }
                            MainActivity.mainActivityInstance?.dissmissAllFragmentDialog()
                            dismissAllowingStateLoss()
                        }
                    }
                }
            }
        }
    }

    companion object {
        private const val SESSION_EXPIRE_DIALOG = "SESSION_EXPIRE_DIALOG"
        private const val SESSION_EXPIRE_MSG = "SESSION_EXPIRE_MSG"
        private const val PERCENT_85 = 0.85
        fun showDialog(
            fragmentManager: FragmentManager,
            msg: String? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(SESSION_EXPIRE_DIALOG)
            if (fragment != null) return
            fragment = newInstance(msg)
            fragment.show(fragmentManager, SESSION_EXPIRE_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(SESSION_EXPIRE_DIALOG) as? SessionExpiredDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(msg: String? = null): SessionExpiredDialog {
            val args = Bundle()
            args.putString(SESSION_EXPIRE_MSG, msg)
            val fragment = SessionExpiredDialog()
            fragment.arguments = args
            return fragment
        }
    }

}
