package com.metathought.food_order.casheir.data.model.base.response_model.ordered


import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.AcceptOrderedStatusEnum
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import timber.log.Timber
import java.util.Objects

data class OrderedRecord(

    @SerializedName("tableName")
    var tableName: String? = null,
    @SerializedName("tableTmpid")
    val tableTmpid: String? = null,
    @SerializedName("tableType")
    var tableType: Int? = null,
    @SerializedName("tableUuid")
    var tableUuid: String? = null,
    @SerializedName("sourcePlatform")
    val sourcePlatform: Int? = null,
    @SerializedName("orderNo")
    var orderNo: String? = null,
    @SerializedName("createTime")
    val createTime: String? = null,
    @SerializedName("payStatus")
    var payStatus: Int? = null,
    @SerializedName("realPrice")
    var realPrice: Long? = null,
    @SerializedName("isPreOrder")
    val isPreOrder: Boolean? = null,
    @SerializedName("diningStyle")
    val diningStyle: Int? = null,
    //===================订单列表V2 版本会返回这部分内容
    //商品总数
    @SerializedName("goodsTotalNum")
    var goodsTotalNum: Int? = null,
    //是否已读
    @SerializedName("isRead")
    var isRead: Boolean? = null,
    //是否打印
    @SerializedName("isPrinted")
    var isPrinted: Boolean? = null,

    //是否有待定价的  true 有商品未定价
    @SerializedName("isOrdersWeightMark")
    var isOrdersWeightMark: Boolean? = null,

    //===================订单列表V2 版本会返回这部分内容


    //以下自动订单列表V2版本优化后不返回
    @SerializedName("applyStatus")
    val applyStatus: Int? = null,
    @SerializedName("cashRefundPrice")
    val cashRefundPrice: Int? = null,
    @SerializedName("consumerAddressId")
    val consumerAddressId: Any? = null,
    @SerializedName("consumerId")
    val consumerId: String? = null,
    @SerializedName("consumerName")
    val consumerName: String? = null,
    @SerializedName("couponPrice")
    val couponPrice: Int? = null,
    @SerializedName("couponTicketId")
    val couponTicketId: Any? = null,

    @SerializedName("createdUserId")
    val createdUserId: Any? = null,
    @SerializedName("customerJson")
    val customerJson: String? = null,

    @SerializedName("finalPrice")
    val finalPrice: Any? = null,
    @SerializedName("getOrderNumber")
    val getOrderNumber: Any? = null,
    @SerializedName("goodsJson")
    var goodsJson: String? = null,
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("kioskId")
    val kioskId: Any? = null,
    @SerializedName("mainStoreId")
    val mainStoreId: Any? = null,
    @SerializedName("mealCost")
    val mealCost: Int? = null,
    @SerializedName("note")
    val note: String? = null,
    @SerializedName("onlineRefundPrice")
    val onlineRefundPrice: Int? = null,
    @SerializedName("openid")
    val openid: Any? = null,

    @SerializedName("outOrderNo")
    val outOrderNo: Any? = null,
    @SerializedName("outOrderNo2")
    val outOrderNo2: Any? = null,
    @SerializedName("over")
    val over: Boolean? = null,
    @SerializedName("payOrderNo")
    val payOrderNo: Any? = null,

    @SerializedName("payTime")
    val payTime: String? = null,
    @SerializedName("payType")
    val payType: Int? = null,
    @SerializedName("payTypeStr")
    val payTypeStr: String? = null,
    @SerializedName("paymentAdvance")
    val paymentAdvance: Boolean? = null,
    @SerializedName("peopleNum")
    val peopleNum: Any? = null,
    @SerializedName("pickupCode")
    val pickupCode: String? = null,
//    @SerializedName("pickupNo")
//    val pickupNo: Any? = null,
    @SerializedName("plan")
    val plan: Boolean? = null,
    @SerializedName("planStatus")
    val planStatus: Int? = null,
    @SerializedName("profitsharingOrderNo")
    val profitsharingOrderNo: Any? = null,

    @SerializedName("refundDateTime")
    val refundDateTime: Any? = null,
    @SerializedName("refundNote")
    val refundNote: Any? = null,
    @SerializedName("refundPrice")
    val refundPrice: Int? = null,
    @SerializedName("reminderNumber")
    val reminderNumber: Int? = null,
    @SerializedName("saasStoreId")
    val saasStoreId: String? = null,
    @SerializedName("settlementId")
    val settlementId: Any? = null,
    @SerializedName("settlementStatus")
    val settlementStatus: Int? = null,
    @SerializedName("settlementTime")
    val settlementTime: Any? = null,
    @SerializedName("shareCart")
    val shareCart: Boolean? = null,

    @SerializedName("startTime")
    val startTime: String? = null,
    @SerializedName("status")
    var status: Int? = null,
    @SerializedName("statusStr")
    val statusStr: Any? = null,
    @SerializedName("store")
    val store: Any? = null,
    @SerializedName("storeId")
    val storeId: String? = null,
    @SerializedName("tempTableCodeId")
    val tempTableCodeId: String? = null,
    @SerializedName("totalPrice")
    val totalPrice: Int? = null,
    @SerializedName("type")
    val type: Int? = null,
    @SerializedName("updateTime")
    val updateTime: String? = null,

    /**
     * 订单过期时间 接单信息接口会返回
     */
    @SerializedName("expireTime")
    var expireTime: String? = null,

    /**
     * 接单状态 0待接单 1已接单
     */
    @SerializedName("acceptStatus")
    var acceptStatus: Int? = null,

    /**
     * 接单剩余倒计时时间（毫秒）接单信息接口会返回
     */
    @SerializedName("expireCountDown")
    var expireCountDown: Long? = null,

    /**
     * 是否打印了预结单
     */
    @SerializedName("hasPrintPreSettlement")
    var hasPrintPreSettlement: Boolean? = false,

    /**
     * 订单汇率
     */
    @SerializedName("conversionRatio")
    var conversionRatio: Long? = null,

    /**
     * 外卖平台币种  1-usd  2-khr
     */
    @SerializedName("deliveryPlatformCurrencyType")
    var deliveryPlatformCurrencyType: Int? = null,

    ) {
    @Transient
    var select = false

//    private var orderedGoodJson: OrderedGoodJson? = null
//
//    fun clearOrderedGoodJson() {
//        orderedGoodJson = null
//    }
//
//    fun getOrderedGoodJson(): OrderedGoodJson? {
//        if (orderedGoodJson == null && !goodsJson.isNullOrEmpty()) {
//            orderedGoodJson =
//                MyApplication.globalGson.fromJson(goodsJson, OrderedGoodJson::class.java)
//        }
//        return orderedGoodJson
//    }

    fun isKhr(): Boolean {
        return deliveryPlatformCurrencyType == 2
    }

    fun getShowPrice(context: Context): String {
        if (acceptStatus == AcceptOrderedStatusEnum.WAIT_ACCEPT.id) {
            //待接单
            if (isOrdersWeightMark == true) {
                return "${context.getString(R.string.to_be_confirmed)}"
            }
            return FoundationHelper.getPriceStrByUnit(
                conversionRatio ?: FoundationHelper.conversionRatio, realPrice, isKhr()
            )
        }

        if (payStatus == OrderedStatusEnum.BE_CONFIRM.id || payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || payStatus == OrderedStatusEnum.UNPAID.id || payStatus == OrderedStatusEnum.CANCEL_ORDER.id) {
            Timber.e("orderNo: $orderNo  ==> hasWeight:${isOrdersWeightMark}")
            if (isOrdersWeightMark == true) {
                return "${context.getString(R.string.to_be_confirmed)}"
            }
            return FoundationHelper.getPriceStrByUnit(
                conversionRatio ?: FoundationHelper.conversionRatio, realPrice, isKhr()
            )
        } else {
            return FoundationHelper.getPriceStrByUnit(
                conversionRatio ?: FoundationHelper.conversionRatio, realPrice, isKhr()
            )
        }
    }


    fun getDiningStyleStr(context: Context): String {
        return if (isPreOrder == true) {
            context.getString(R.string.pre_order)
        } else {
            if (diningStyle == DiningStyleEnum.DINE_IN.id) {
                context.getString(R.string.dine_in)
            } else if (diningStyle == DiningStyleEnum.TAKE_OUT.id) {
                context.getString(R.string.take_out)
            } else {
                context.getString(R.string.take_away)
            }
        }
    }

    fun getDiningStyleIcon(): Int {
        return if (isPreOrder == true) {
            R.drawable.icon_pre_order
        } else {
            if (diningStyle == DiningStyleEnum.DINE_IN.id) {
                R.drawable.icon_dine_in
            } else if (diningStyle == DiningStyleEnum.TAKE_OUT.id) {
                R.drawable.icon_take_out
            } else {
                R.drawable.icon_take_away
            }
        }
    }

    /**
     * 待接订单是否可接单
     *
     */
    fun isCanReceiveOrder(): Boolean {
        if (expireCountDown == null || (expireCountDown
                ?: 0) <= 0
        ) {
            return false
        }
        return true
    }


    override fun hashCode(): Int {
        return Objects.hash(orderNo)
    }

    override fun equals(other: Any?): Boolean {
        if (other === this) return true
        if (other !is OrderedRecord) return false
        return other.orderNo == this.orderNo
    }


}