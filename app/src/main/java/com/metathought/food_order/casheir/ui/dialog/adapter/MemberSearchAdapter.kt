package com.metathought.food_order.casheir.ui.dialog.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.ItemMemberSearchBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils

class MemberSearchAdapter(
    private val onMemberClick: (CustomerMemberResponse) -> Unit
) : RecyclerView.Adapter<MemberSearchAdapter.MemberViewHolder>() {

    // 内部数据列表
    private val memberList = mutableListOf<CustomerMemberResponse>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberViewHolder {
        val binding = ItemMemberSearchBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MemberViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MemberViewHolder, position: Int) {
        holder.bind(memberList[position])
    }

    override fun getItemCount(): Int = memberList.size

    inner class MemberViewHolder(
        private val binding: ItemMemberSearchBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(member: CustomerMemberResponse) {
            binding.apply {
                // 设置会员昵称
                tvMemberName.text = member.nickName ?: ""

                // 设置手机号
                tvMemberPhone.text = member.telephone ?: ""

                // 设置余额
                val balance = member.balance ?: 0L
                tvMemberBalance.text = balance.priceFormatTwoDigitZero2()

                // 设置点击事件
                root.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        onMemberClick(member)
                    }
                }
            }
        }
    }



    /**
     * 添加数据到现有列表（用于分页加载）
     */
    fun addData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d("MemberAdapter", "addData: 当前${memberList.size}条, 新增${newData.size}条")

        val startPosition = memberList.size
        memberList.addAll(newData)

        // 通知适配器有新数据插入
        notifyItemRangeInserted(startPosition, newData.size)

        android.util.Log.d("MemberAdapter", "addData完成: 适配器数量=${itemCount}")
    }

    /**
     * 替换所有数据（用于新搜索）
     */
    fun replaceData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d("MemberAdapter", "replaceData: 新数据${newData.size}条, 当前列表${memberList.size}条")

        memberList.clear()
        memberList.addAll(newData)

        // 通知适配器数据已完全改变
        notifyDataSetChanged()

        android.util.Log.d("MemberAdapter", "replaceData完成: 适配器数量=${itemCount}")
    }

    /**
     * 清空数据
     */
    fun clearData() {
        android.util.Log.d("MemberAdapter", "clearData: 清空数据")

        val oldSize = memberList.size
        memberList.clear()

        // 通知适配器数据已移除
        if (oldSize > 0) {
            notifyItemRangeRemoved(0, oldSize)
        }

        android.util.Log.d("MemberAdapter", "clearData完成: 适配器数量=${itemCount}")
    }

    /**
     * 获取指定位置的会员数据
     */
    fun getMemberAt(position: Int): CustomerMemberResponse? {
        return if (position in 0 until memberList.size) {
            memberList[position]
        } else {
            null
        }
    }

    /**
     * 根据ID查找会员
     */
    fun findMemberById(memberId: String): CustomerMemberResponse? {
        return memberList.find { it.accountId == memberId }
    }

    /**
     * 高亮搜索关键词（如果需要）
     */
    private fun highlightSearchKeyword(text: String, keyword: String?): String {
        if (keyword.isNullOrEmpty() || !text.contains(keyword, ignoreCase = true)) {
            return text
        }
        // 这里可以添加高亮逻辑，比如使用 SpannableString
        return text
    }
}
