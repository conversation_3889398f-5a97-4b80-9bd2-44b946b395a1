package com.metathought.food_order.casheir.ui.member.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogCreditRecordsBinding
import com.metathought.food_order.casheir.ui.adapter.CreditRecordsAdapter
import com.metathought.food_order.casheir.ui.adapter.PaymentRecordsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.member.credit.MemberCreditViewModel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint


/**
 * 挂账记录弹窗
 */
@AndroidEntryPoint
class CreditRecordsDialog : BaseDialogFragment() {

    enum class SCENE {
        CREDIT_RECORDS, //挂账记录
        PAYMENT_RECORDS //还款记录
    }

    companion object {
        private const val TAG = "CreditRecordsDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        private const val KEY_SCENE = "scene"
        private const val KEY_CONSUMERID = "consumerId"

        fun showDialog(
            fragmentManager: FragmentManager, consumerId: Long?, scene: SCENE
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(consumerId, scene)
            fragment.show(fragmentManager, TAG)
        }

        fun getDialog(fragmentManager: FragmentManager): CreditRecordsDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CreditRecordsDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CreditRecordsDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            consumerId: Long?,
            scene: SCENE
        ): CreditRecordsDialog {
            val fragment = CreditRecordsDialog()
            fragment.arguments = Bundle().apply {
                putString(KEY_SCENE, scene.toString())
                putLong(KEY_CONSUMERID, consumerId ?: 0)
            }
            return fragment
        }
    }

    private var scene: SCENE? = null

    private val viewModel: MemberCreditViewModel by viewModels()

    private var creditRecordsAdapter: CreditRecordsAdapter? = null
    private var paymentRecordsAdapter: PaymentRecordsAdapter? = null

    private var binding: DialogCreditRecordsBinding? = null

    fun getConsumerId(): Long {
        return consumerId
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCreditRecordsBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        onTouchOutSide(binding?.mainLayout)
        initData()
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiRecordsState.observe(viewLifecycleOwner) {
            binding?.apply {
                if (it.showEnd) {
                    if (it.isRefresh != false) {
                        paymentRecordsAdapter?.replaceData(arrayListOf())
                        creditRecordsAdapter?.replaceData(arrayListOf())
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    if (error.isNotEmpty())
                        Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
                }
                it.showPaymentRecordsSuccess?.let { response ->
                    if (it.isRefresh != false) {
                        paymentRecordsAdapter?.replaceData(response)
                        refreshLayout.finishRefresh()

                    } else {
                        paymentRecordsAdapter?.addData(response)
                        refreshLayout.finishLoadMore()
                    }
                }

                it.showCreditRecordsSuccess?.let { response ->
                    if (it.isRefresh != false) {
                        creditRecordsAdapter?.replaceData(response)
                        refreshLayout.finishRefresh()

                    } else {
                        creditRecordsAdapter?.addData(response)
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            btnPrint.setOnClickListener {
                viewModel.printAllCreditUnpaidOrders(
                    requireContext(),
                    consumerId,
                    isRepayment = false
                )
            }
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    if (scene == SCENE.CREDIT_RECORDS) {
                        viewModel.loadCreditRecords(consumerId, true)
                    } else {
                        viewModel.loadPaymentRecords(consumerId, true)
                    }
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    if (scene == SCENE.CREDIT_RECORDS) {
                        viewModel.loadCreditRecords(consumerId, false)
                    } else {
                        viewModel.loadPaymentRecords(consumerId, false)
                    }
                }

            })
            creditRecordsAdapter?.onDetailClick = { item, _ ->
                CreditOrderDetailDialog.showDialog(parentFragmentManager, item)
            }
        }
    }

    private var consumerId = 0L
    private fun initData() {
        binding?.apply {
            val e = arguments?.getString(KEY_SCENE) ?: SCENE.CREDIT_RECORDS.toString()
            consumerId = arguments?.getLong(KEY_CONSUMERID) ?: 0L
            scene = SCENE.valueOf(e)

            if (scene == SCENE.CREDIT_RECORDS) {
                tvDialogName.text = getString(R.string.credit_records)
                viewModel.loadCreditRecords(consumerId, true)
            } else {
                tvDialogName.text = getString(R.string.payment_records)
                viewModel.loadPaymentRecords(consumerId, true)
            }
        }
    }

    private fun initView() {
        binding?.apply {
            if (scene == SCENE.CREDIT_RECORDS) {
                btnPrint.isVisible = true
                titleCreditRecords.isVisible = true
                titlePaymentRecords.isVisible = false
                creditRecordsAdapter = CreditRecordsAdapter()
                rvRecords.adapter = creditRecordsAdapter
            } else {
                btnPrint.isVisible = false
                titleCreditRecords.isVisible = false
                titlePaymentRecords.isVisible = true
                paymentRecordsAdapter = PaymentRecordsAdapter()
                rvRecords.adapter = paymentRecordsAdapter
            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * PERCENT_85).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }
}