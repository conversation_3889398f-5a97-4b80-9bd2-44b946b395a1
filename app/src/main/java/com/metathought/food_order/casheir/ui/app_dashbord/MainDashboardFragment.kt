package com.metathought.food_order.casheir.ui.app_dashbord

import android.annotation.SuppressLint
import android.database.CursorWindow
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import cn.bingoogolapple.badgeview.BGABadgeViewHelper
import com.alibaba.fastjson.JSON
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.gson.Gson
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.constant.KitchenCheckTicketType
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.WsReserveOrderCancel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsPrintOrderedInfo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsRepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.WsPrinterActionEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.WsPrinterActionResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.FragmentMainDashboardBinding
import com.metathought.food_order.casheir.databinding.PopupLogoutBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterTemplateHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.helper.UnReadAndUnPrintHelper
import com.metathought.food_order.casheir.helper.UsbBroadcastReceiver
import com.metathought.food_order.casheir.helper.VersionHelper
import com.metathought.food_order.casheir.helper.WeightScaleManager
import com.metathought.food_order.casheir.helper.XpopHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.ui.adapter.FeatureMenuAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.CheckVersionDialog
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.dialog.CustomerServiceDialog
import com.metathought.food_order.casheir.ui.dialog.OpenCashBoxDialog
import com.metathought.food_order.casheir.ui.dialog.ResetCashBoxDialog
import com.metathought.food_order.casheir.ui.dialog.SetCashBoxDialog
import com.metathought.food_order.casheir.ui.dialog.SetLanguageDialog
import com.metathought.food_order.casheir.ui.dialog.notice.NoticeDetailDialog
import com.metathought.food_order.casheir.ui.dialog.notice.NoticeListDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.ui.dialog.store_manager.EditStoreConfigurationDetailDialog
import com.metathought.food_order.casheir.ui.dialog.store_manager.StoreDetailDialog
import com.metathought.food_order.casheir.ui.dialog.store_report.StoreReportDialog
import com.metathought.food_order.casheir.ui.logout.LogoutDialog
import com.metathought.food_order.casheir.ui.member.MemberMainFragment
import com.metathought.food_order.casheir.ui.order.MenuOrderFragment
import com.metathought.food_order.casheir.ui.ordered.OrderedFragment
import com.metathought.food_order.casheir.ui.printer.PrinterManagerFragment
import com.metathought.food_order.casheir.ui.receiving_order.ReceivingOrderedFragment
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.store_dashbord.DashboardFragment
import com.metathought.food_order.casheir.ui.table.TableFragment
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.metathought.food_order.casheir.ui.work_handover.CloseClassesDialog
import com.metathought.food_order.casheir.ui.work_handover.ShiftHandoverRecordsDialog
import com.metathought.food_order.casheir.ui.work_handover.StartClassesDialog
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.SoundPlayUtils
import com.metathought.food_order.casheir.ui.dialog.pay_center.PaymentSlideFragment
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.data.model.base.response_model.credit.CreditRecord
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.payment.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.payment.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.recharge.RechargeQRStatusResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import java.util.Locale


@AndroidEntryPoint
class MainDashboardFragment : BaseFragment() {

    companion object {
        //        fun newInstance() = MainDashboardFragment()
        var CURRENT_USER: UserLoginResponse? = null
        var STORE_INFO: StoreInfoResponse? = null
    }

    private val viewModel: MainDashboardViewModel by viewModels()

    //    private val storeManagerViewModel: StoreManagerViewModel by viewModels()
    private var _binding: FragmentMainDashboardBinding? = null
    private val binding get() = _binding
    private var featureMenuAdapter: FeatureMenuAdapter? = null
    private var secondaryScreenUI: SecondaryScreenUI? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMainDashboardBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.e("onViewCreated  ${viewModel.listview.value?.size}")

        initEventBus()
        initSecondary()
        initView()
        initListener()
        initObserver()
        initObserverSocket()

//        PrinterDeviceHelper.initConnectPrinter()
        //init Sunmi printer
        context?.let { viewModel.initPrinter(it) }
        //init XPrinter
        PrinterUsbDeviceHelper.initUsbPrintAndConnectUSB()
        try {
            // 增加 CursorWindow 的大小，但这会增加内存占用，可能导致性能问题
            val field = CursorWindow::class.java.getDeclaredField("sCursorWindowSize")
            field.isAccessible = true
            field.set(null, 6 * 1024 * 1024) // 设置为 6MB
        } catch (e: Exception) {
            e.printStackTrace()
        }

        WeightScaleManager.getInstance().initialize(requireActivity())
    }

    override fun onLoad() {
        super.onLoad()
        //版本更新
        viewModel.checkVersion(requireActivity())

    }


    private fun initSecondary() {
        context?.let {
            secondaryScreenUI = MyApplication.myAppInstance.orderedScreen
        }
    }

    private fun initObserver() {

        viewModel.noticeUnReadResponse.observe(viewLifecycleOwner) { data ->
            if (data != null) {
                lifecycleScope.launch {
                    binding?.apply {
                        UnReadAndUnPrintHelper.setNoticeUnReadNum(data ?: 0)

                        featureMenuAdapter?.updateNoticeItem()
                    }
                }
            }
        }

        viewModel.noticeResponse.observe(viewLifecycleOwner) { data ->
            if (data != null) {
                lifecycleScope.launch {
                    binding?.apply {
                        val localNoticeId = PreferenceHelper.getLocalNoticeId(requireContext())
                        /**
                         * 如果是已读或者是手动关掉的则不显示
                         */
                        if (localNoticeId != data.id.toString()) {
                            llNotice.isVisible = data.isRead == false
                            tvNotice.setMarqueeText(data.title ?: "")
                        }
                    }
                }
            }
        }

        viewModel.unReadAndPrintResponse.observe(viewLifecycleOwner) { state ->
            context?.let { _ ->
                if (state is ApiResponse.Success) {

                    UnReadAndUnPrintHelper.setUnReadNum(state.data.unreadNum ?: 0)
                    UnReadAndUnPrintHelper.setUnPrintNum(state.data.unPrintNum ?: 0)
                    featureMenuAdapter?.updateOrderItem()

                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.UPDATE_UNREAD_EVENT, state.data))

                }

            }
        }

        viewModel.unAcceptOrderReadAndPrintResponse.observe(viewLifecycleOwner) { state ->
            context?.let { _ ->
                if (state is ApiResponse.Success) {

                    UnReadAndUnPrintHelper.setUnReceiveOrderNum(state.data.unreadNum ?: 0)

                    featureMenuAdapter?.updateAcceptOrderItem()

                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.UPDATE_UNREAD_EVENT, state.data))

                }

            }
        }

        viewModel.versionResponse.observe(viewLifecycleOwner) { state ->
            context?.let { _ ->
                lifecycleScope.launch {
                    val versionCheckResponse = state.versionCheckResponse
                    if (versionCheckResponse is ApiResponse.Success) {
                        if (versionCheckResponse.data != null) {
                            binding?.apply {
                                imgLogo.showCirclePointBadge()
                            }
                            var isUpdate = false
                            Timber.e("state.isManualTriggering ${state.isManualTriggering}")
                            if (state.isManualTriggering == true) {
                                //手动触发是否有新包
                                isUpdate = true
                            } else {
                                //判断一下可选更新判断是否不再提示
                                if (versionCheckResponse.data.type == 2) {
                                    isUpdate = true
                                } else if (versionCheckResponse.data.type == 1) {
                                    var versionName = ""
                                    //判断一下是否不再提示
                                    PreferenceDataStoreHelper.getInstance().apply {
                                        versionName = this.getFirstPreference(
                                            PreferenceDataStoreConstants.DATA_STORE_KEY_IGNORE_VERSION,
                                            ""
                                        )
                                    }
                                    Timber.e("versionName : $versionName")
                                    if (versionName != versionCheckResponse.data.name) {
                                        isUpdate = true
                                    }
                                }
                            }

                            if (isUpdate) {
                                Timber.e("显示弹窗？？")
                                CheckVersionDialog.showDialog(
                                    parentFragmentManager,
                                    versionCheckResponse.data
                                )
                                viewModel.clearVersionModel()
                            }
                        }

                    }
                }
            }
        }

        viewModel.listview.observe(viewLifecycleOwner) { array ->
            context?.let { context ->
                featureMenuAdapter = FeatureMenuAdapter(array, context) {
                    when (it.id) {
                        FeatureMenuEnum.NOTICE.id -> {
                            SingleClickUtils.isFastDoubleClick {
                                NoticeListDialog.showDialog(parentFragmentManager)
                            }

                        }

                        FeatureMenuEnum.OPEN_CASH_BOX.id -> {
                            lifecycleScope.launch {
                                if (PreferenceHelper.getCashBoxPwd(requireActivity()).isEmpty()) {
                                    Timber.e("无密码打开钱箱")
                                    Printer.openCashierBox()
                                } else {
                                    OpenCashBoxDialog.showDialog(parentFragmentManager) {
                                        Timber.e("有密码打开钱箱")
                                        Printer.openCashierBox()
                                    }
                                }
                            }
                        }

//                        FeatureMenuEnum.PRINTER.id -> {
////                            SecondaryManager.showDefault(context)
////                            SecondaryManagerV2.showDefault(context)
//                            secondaryScreenUI?.showDefault()
//                            replaceFragment(PrinterManagerFragment())
//                        }

                        FeatureMenuEnum.STORE_DASHBOARD.id -> {
//                            SecondaryManager.showDefault(context)
//                            SecondaryManagerV2.showDefault(context)
                            secondaryScreenUI?.showDefault()
                            replaceFragment(DashboardFragment())
                        }

                        FeatureMenuEnum.TABLE.id -> {
//                            SecondaryManager.showDefault(context)
//                            SecondaryManagerV2.showDefault(context)
                            secondaryScreenUI?.showDefault()
                            replaceFragment(TableFragment())
                        }

                        FeatureMenuEnum.ORDER.id -> {
                            replaceFragment(MenuOrderFragment())
                        }

//                        FeatureMenuEnum.PENDING_ORDER.id -> {
//                            secondaryScreenUI?.showDefault()
//                            replaceFragment(PendingOrderFragment())
//                        }


                        FeatureMenuEnum.RECEIVING_ORDER.id -> {
                            secondaryScreenUI?.showDefault()
                            replaceFragment(ReceivingOrderedFragment())
                        }

                        FeatureMenuEnum.ORDER_MANAGEMENT.id -> {
                            replaceFragment(OrderedFragment())
                        }

                        FeatureMenuEnum.MEMBER_MANAGEMENT.id -> {
//                            SecondaryManager.showDefault(context)
//                            SecondaryManagerV2.showDefault(context)
                            secondaryScreenUI?.showDefault()
                            replaceFragment(MemberMainFragment())
                        }
                    }
                }
                binding?.navigationMenu?.layoutManager =
                    LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false);
                binding?.navigationMenu?.adapter = featureMenuAdapter

            }
        }


//
    }

    private fun initView() {

        binding?.apply {
            context?.let {
                lifecycleScope.launch {
                    PreferenceDataStoreHelper.getInstance(it).apply {
                        viewModel.userLoginResponse.value = Gson().fromJson(
                            getFirstPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                ""
                            ), UserLoginResponse::class.java
                        )
                        CURRENT_USER = viewModel.userLoginResponse.value
                        CURRENT_USER?.getPermissionList()
                        viewModel.getPermissions(CURRENT_USER?.userId)
                        viewModel.connectWebsocket()
                    }
                }

                Glide.with(it).asBitmap()
                    .load(viewModel.userLoginResponse.value?.url)
                    .circleCrop()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .error(R.drawable.ic_logo)
                    .placeholder(R.drawable.ic_logo).into(imgLogo)

                tvStoreName.text = viewModel.userLoginResponse.value?.getStoreNameByLan()

                viewModel.initailizeListMenu(it, viewModel.userLoginResponse.value?.isTableService)
                val tab = viewModel.listview.value?.firstOrNull { it.isSelected == true }
                secondaryScreenUI?.showDefault()
                if (tab?.id == FeatureMenuEnum.TABLE.id) {
                    replaceFragment(TableFragment())
                } else if (tab?.id == FeatureMenuEnum.ORDER.id) {
                    replaceFragment(MenuOrderFragment())
                    secondaryScreenUI?.showMenu()
//                } else if (tab?.id == FeatureMenuEnum.PENDING_ORDER.id) {
//                    replaceFragment(PendingOrderFragment())
                } else if (tab?.id == FeatureMenuEnum.ORDER_MANAGEMENT.id) {
                    replaceFragment(OrderedFragment())
                    secondaryScreenUI?.showOrderedInfo()
                } else if (tab?.id == FeatureMenuEnum.MEMBER_MANAGEMENT.id) {
                    replaceFragment(MemberMainFragment())
                } else if (tab?.id == FeatureMenuEnum.STORE_DASHBOARD.id) {
                    replaceFragment(DashboardFragment())
                } else if (tab?.id == FeatureMenuEnum.RECEIVING_ORDER.id) {
                    replaceFragment(ReceivingOrderedFragment())
                } else {
                    if (viewModel.listview.value?.first()?.id == FeatureMenuEnum.TABLE.id)
                        replaceFragment(TableFragment()) else replaceFragment(MenuOrderFragment())
                }

            }

            Timber.e("Locale.getDefault()  ${Locale.getDefault()}")

            //初始化配置
            viewModel.initConfiguration(requireContext())


            imgLogo.badgeViewHelper.setBadgeGravity(BGABadgeViewHelper.BadgeGravity.RightTop)
            imgLogo.badgeViewHelper.setBadgeVerticalMarginDp(
                DisplayUtils.dp2px(
                    requireActivity(),
                    0f
                )
            )
            imgLogo.badgeViewHelper.setBadgeHorizontalMarginDp(
                DisplayUtils.dp2px(
                    requireActivity(),
                    0f
                )
            )

        }

        //只有第一次登录才显示
        val isLogin = arguments?.getBoolean("is_login") ?: false
        Timber.e("isLogin $isLogin")
        if (isLogin) {
            val isPerm =
                CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.WORK_HANDOVER.type) == true
            //是否需要开班
            val isNeedStartShift = CURRENT_USER?.isNeedStartShift ?: false
            if (isPerm && isNeedStartShift) {
                activity?.supportFragmentManager?.let { it1 ->
                    StartClassesDialog.showDialog(
                        it1,
                        type = StartClassesDialog.Type.CLASSES_AND_IMPREST
                    ) {

                    }
                }
            }
        }
    }

    fun getPrinterInfoList() {
        viewModel.getPrinterInfoList()
    }

    private fun initListener() {
        binding?.apply {
//            tvVersion.setOnClickListener {
//                viewModel.checkVersion(requireActivity(), true)
//            }

//            dropdownLanguage.setOnClickListener {
////                arrow.animate().rotation(180f).setDuration(200)
//                dropdownLanguage.setBackgroundResource(R.drawable.background_spinner_top)
//                showPopupWindowLanguage(it)
//            }

            dropdownUser.setOnClickListener {
//                SingleClickUtils.isFastDoubleClick {
                arrowUser.animate().rotation(180f).setDuration(200)
                dropdownUser.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupWindowLogout(it)
//                }
            }

//            llOpenCashBox.setOnClickListener {
//                lifecycleScope.launch {
//                    if (PreferenceHelper.getCashBoxPwd(requireActivity()).isEmpty()) {
//                        Timber.e("无密码打开钱箱")
//                        Printer.openCashierBox()
//                    } else {
//                        OpenCashBoxDialog.showDialog(parentFragmentManager) {
//                            Timber.e("有密码打开钱箱")
//                            Printer.openCashierBox()
//                        }
//                    }
//                }
//            }

            tvContactUs.setOnClickListener {
//                SoundPlayUtils.play()
//                CustomerServiceDialog.showDialog(parentFragmentManager)
//                showPopupWindowCustomerService(tvContactUs)
            }


            ivCloseNotice.setOnClickListener {
                lifecycleScope.launch {
                    if (viewModel.noticeResponse.value?.id != null) {
                        PreferenceHelper.setLocalNoticeId(
                            requireContext(),
                            viewModel.noticeResponse.value?.id!!.toString()
                        )
                    }
                    llNotice.isVisible = false
                }
            }

            tvNotice.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (viewModel.noticeResponse.value != null) {
                        NoticeDetailDialog.showDialog(
                            parentFragmentManager,
                            viewModel.noticeResponse.value
                        )
                        llNotice.isVisible = false
                    }
                }
            }
        }
    }

//    private fun showPopupWindowLanguage(anchorView: View) {
//        activity?.hideKeyboard()
//        val popupView = PopupLanguagesBinding.inflate(layoutInflater)
//        val popupWindow = PopupWindow(
//            popupView.root,
//            anchorView.width,
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            true
//        )
//
//        popupWindow.animationStyle = R.style.PopupAnimation
//        popupWindow.showAsDropDown(anchorView)
//        popupWindow.setOnDismissListener {
////            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
//            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
//        }
//        popupView.tvChinese.setOnClickListener {
//            if (binding?.tvLanguages?.text != getString(R.string.chinese_language)) {
//                setLocale(Locale.CHINESE)
//                binding?.tvLanguages?.text = getString(R.string.chinese_language)
//            }
//            popupWindow.dismiss()
//
//        }
//        popupView.tvEnglish.setOnClickListener {
//            if (binding?.tvLanguages?.text != getString(R.string.english_language)) {
//                setLocale(Locale.ENGLISH)
//                binding?.tvLanguages?.text = getString(R.string.english_language)
//            }
//            popupWindow.dismiss()
//
//        }
//        popupView.tvKhmer.setOnClickListener {
//            if (binding?.tvLanguages?.text != getString(R.string.khmer_language)) {
//                setLocale(MyApplication.LOCALE_KHMER)
//                binding?.tvLanguages?.text = getString(R.string.khmer_language)
//            }
//            popupWindow.dismiss()
//        }
//        context?.let {
//            when (Locale.getDefault()) {
//                Locale.CHINESE -> setSelectedLanguages(popupView.tvChinese, it)
//                Locale.ENGLISH -> setSelectedLanguages(popupView.tvEnglish, it)
//                MyApplication.LOCALE_KHMER -> setSelectedLanguages(popupView.tvKhmer, it)
//                else -> {
//                    setSelectedLanguages(popupView.tvEnglish, it)
//                }
//            }
//        }
//    }

//    private fun setSelectedLanguages(textView: TextView, context: Context) {
//        textView.setBackgroundResource(R.drawable.background_language_selected)
//        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
//    }

    private fun setLocale(locale: Locale) {
//        SecondaryManager.dismissScreen()
//        SecondaryManagerV2.dismissScreen()
        secondaryScreenUI?.dismiss()
        context?.let {
            LocaleHelper.setLocale(it, locale.language)
        }
        activity?.recreate()
    }

    @SuppressLint("SetTextI18n")
    private fun showPopupWindowLogout(anchorView: View) {
        activity?.hideKeyboard()

        val popupView = PopupLogoutBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            DisplayUtils.dp2px(requireContext(), 260f),
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        PopupWindowHelper.addPopupWindow(popupWindow)

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrowUser?.animate()?.rotation(0f)?.setDuration(200)
            PopupWindowHelper.deletePopupWindow(popupWindow)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

        popupView.tvUserName.text = viewModel.userLoginResponse.value?.name ?: ""

        CURRENT_USER?.let { user ->
            if (user.sex == 2) {
                popupView.imgUserProfile.setImageResource(R.drawable.ic_profile)
            } else {
                popupView.imgUserProfile.setImageResource(R.drawable.ic_man_profile)
            }
        }

        popupView.tvLogout.setOnClickListener {
            popupWindow.dismiss()
            activity?.supportFragmentManager?.let { it1 ->
                val isShiftEmployee = CURRENT_USER?.isShiftEmployee ?: false
                if(isShiftEmployee){
                    ConfirmDialog.showDialog(
                        parentFragmentManager,
                        title = getString(R.string.log_out),
                        content = getString(R.string.not_handover_logout_tips),
                        positiveButtonTitle = getString(R.string.go_to_handover),
                        negativeButtonTitle = getString(R.string.log_out2),
                        isShowCancel = true,
                        negativeButtonListener = {
                            LogoutDialog.showDialog(it1) {

                            }
                        }
                    ) {
                        CloseClassesDialog.showDialog(it1) {

                        }
                    }
                }else{
                    LogoutDialog.showDialog(it1) {

                    }
                }
            }
        }

        val isLogPerm =
            CURRENT_USER?.getPermissionList()
                ?.contains(PermissionEnum.WORK_HANDOVER_LOG.type) == true
        popupView.tvShiftHandoverRecords.isVisible = isLogPerm

        //是否有开班权限
        val isPerm =
            CURRENT_USER?.getPermissionList()?.contains(PermissionEnum.WORK_HANDOVER.type) == true
        //是否是开班人员
        val isShiftEmployee = CURRENT_USER?.isShiftEmployee ?: false
        //是否需要开班
        val isNeedStartShift = CURRENT_USER?.isNeedStartShift ?: false

        popupView.tvClassesStart.isVisible = if (isNeedStartShift) {
            //还没有开班 有开班权限的都可以看到
            isPerm
        } else {
            //开班后只有开班人员可以看到
            isShiftEmployee
        }

        popupView.tvShiftHandoverRecords.setOnClickListener {
            //交接班记录
            popupWindow.dismiss()
            activity?.supportFragmentManager?.let { it1 ->
                ShiftHandoverRecordsDialog.showDialog(it1) {

                }
            }
        }

        popupView.tvClassesStart.setOnClickListener {
            //开班与备用金
            popupWindow.dismiss()
            activity?.supportFragmentManager?.let { it1 ->
                val type = if (CURRENT_USER?.isNeedStartShift == true) {
                    StartClassesDialog.Type.START_CLASSES
                } else {
                    StartClassesDialog.Type.SAVE_IMPREST
                }
                StartClassesDialog.showDialog(it1, type = type) {

                }
            }
        }

        //有门店管理权限才展示
        popupView.tvStoreManager.isVisible = CURRENT_USER?.getPermissionList()
            ?.contains(PermissionEnum.STORE_MANAGE.type) == true
        popupView.tvStoreManager.setOnClickListener {
            popupWindow.dismiss()
            SingleClickUtils.isFastDoubleClick {
                StoreDetailDialog.showDialog(parentFragmentManager)
            }
        }

        popupView.tvStoreConfiguration.isVisible = CURRENT_USER?.getPermissionList()
            ?.contains(PermissionEnum.STORE_MANAGE.type) == true
        popupView.tvStoreConfiguration.setOnClickListener {
            popupWindow.dismiss()
            activity?.supportFragmentManager?.let { it1 ->
                EditStoreConfigurationDetailDialog.showDialog(it1)
            }
        }


        popupView.tvSetBox.setOnClickListener {
            popupWindow.dismiss()
            activity?.supportFragmentManager?.let { it1 ->
                SetCashBoxDialog.showDialog(it1) {
                    ResetCashBoxDialog.showDialog(parentFragmentManager)
                }
            }
        }

        if (viewModel.userLoginResponse.value?.getPermissionList()
                ?.contains(PermissionEnum.OFFLINE_PAY.type) == true
        ) {
            popupView.tvSetBox.isVisible = true
        }


        popupView.tvContactUs.setOnClickListener {
            popupWindow.dismiss()
            CustomerServiceDialog.showDialog(parentFragmentManager)
//            showPopupWindowCustomerService(anchorView, popupView.tvContactUs)
        }


        popupView.tvPrinterManager.setOnClickListener {
            popupWindow.dismiss()
            featureMenuAdapter?.clearSelect()
            secondaryScreenUI?.showDefault()
            replaceFragment(PrinterManagerFragment())
        }

        popupView.tvPrinterReport.isVisible = viewModel.userLoginResponse.value?.getPermissionList()
            ?.contains(PermissionEnum.VIEW_DATA.type) == true
        popupView.tvPrinterReport.setOnClickListener {
            popupWindow.dismiss()
            //PrintReportDialog.showDialog(parentFragmentManager)
            StoreReportDialog.showDialog(parentFragmentManager)
        }

        when (Locale.getDefault()) {
            Locale.CHINESE -> {
                popupView.tvLanguages.text = getString(R.string.chinese_language)
//                popupView.radioGroupPaymentMethod.check(R.id.radioZh)
            }

            Locale.ENGLISH -> {
                popupView.tvLanguages.text = getString(R.string.english_language)
//                popupView.radioGroupPaymentMethod.check(R.id.radioEn)
            }

            MyApplication.LOCALE_KHMER -> {
                popupView.tvLanguages.text =
                    getString(R.string.khmer_language)
//                popupView.radioGroupPaymentMethod.check(R.id.radioKm)
            }

            else -> {
                popupView.tvLanguages.text = getString(R.string.chinese_language)
//                popupView.radioGroupPaymentMethod.check(R.id.radioZh)
            }
        }

//        popupView.radioGroupPaymentMethod.setOnCheckedChangeListener { radioGroup, checkedId ->
//            Timber.e("radioGroup  $checkedId")
//            when (checkedId) {
//                R.id.radioZh -> {
//                    Timber.e(" 切换中文  $checkedId")
//                    setLocalCurrentTabId()
//                    setLocale(Locale.CHINESE)
//                }
//
//                R.id.radioEn -> {
//                    Timber.e(" 切换英文  $checkedId")
//                    setLocalCurrentTabId()
//                    setLocale(Locale.ENGLISH)
//                }
//
//                R.id.radioKm -> {
//                    Timber.e(" 切换柬文  $checkedId")
//                    setLocalCurrentTabId()
//                    setLocale(MyApplication.LOCALE_KHMER)
//                }
//            }
//            popupWindow.dismiss()
//        }


        popupView.llChangeLanguage.setOnClickListener {
            SingleClickUtils.isFastDoubleClick(1000) {
                SetLanguageDialog.showDialog(parentFragmentManager) {
                    setLocalCurrentTabId()
                    setLocale(it)
                }
                popupWindow.dismiss()
//                popupView.radioGroupPaymentMethod.isVisible =
//                    !popupView.radioGroupPaymentMethod.isVisible
//                if (popupView.radioGroupPaymentMethod.isVisible) {
//                    popupView.tvLanguages.setTextColor(
//                        ContextCompat.getColor(
//                            requireContext(),
//                            R.color.primaryColor
//                        )
//                    )
//                    popupView.arrow.setImageResource(R.drawable.ic_dropdown_primary)
//                    popupView.arrow.animate()?.rotation(180f)?.setDuration(200)
//                } else {
//                    popupView.tvLanguages.setTextColor(
//                        ContextCompat.getColor(
//                            requireContext(),
//                            R.color.black
//                        )
//                    )
//                    popupView.arrow.setImageResource(R.drawable.ic_dropdown)
//                    popupView.arrow.animate()?.rotation(0f)?.setDuration(200)
//                }
            }
        }


        binding?.apply {
            if (imgLogo.badgeViewHelper.isShowBadge) {
                popupView.tvVersion.showCirclePointBadge()
            }
        }
        popupView.tvVersion.text =
            "V${VersionHelper.getLocalVersionName(requireActivity())} ${getString(R.string.version)}"
        popupView.tvVersion.badgeViewHelper.setBadgeGravity(BGABadgeViewHelper.BadgeGravity.RightTop)
        popupView.tvVersion.badgeViewHelper.setBadgeVerticalMarginDp(
            DisplayUtils.dp2px(
                requireActivity(),
                12f
            )
        )
        popupView.tvVersion.badgeViewHelper.setBadgeHorizontalMarginDp(
            DisplayUtils.dp2px(
                requireActivity(),
                6f
            )
        )
        popupView.tvVersion.setOnClickListener {
            viewModel.checkVersion(requireActivity(), true)
        }

    }

//    private fun showPopupWindowCustomerService(anchorView: View, textView: TextView) {
////        activity?.hideKeyboard()
//
//        val popupView = DialogCustomerServiceBinding.inflate(layoutInflater)
//        val popupWindow = PopupWindow(
//            popupView.root,
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            true
//        )
//        PopupWindowHelper.addPopupWindow(popupWindow)
//        popupWindow.animationStyle = R.style.PopupAnimation
//        popupWindow.setOnDismissListener {
//            PopupWindowHelper.deletePopupWindow(popupWindow)
//            textView.setTextColor(ContextCompat.getColor(requireContext(), R.color.black))
//        }
//        popupWindow.contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
//
//        textView.setTextColor(ContextCompat.getColor(requireContext(), R.color.primaryColor))
//
//        popupWindow.showAsDropDown(
//            anchorView,
//            DisplayUtils.dp2px(requireContext(), 80f),
//            DisplayUtils.dp2px(requireContext(), 130f),
//        )
//
//    }

    private fun setLocalCurrentTabId() {
        val tab = featureMenuAdapter?.list?.firstOrNull { it.isSelected == true }
        if (tab != null) {
            lifecycleScope.launch {
                PreferenceHelper.setCurrentTabId(tab.id)
            }
        }
    }

    override fun onResume() {
        viewModel.startLifeCycle()
        super.onResume()
    }

    override fun onPause() {
        Timber.e("onPause")
//
//        SecondaryManager.dismissScreen()
        super.onPause()
    }

    override fun onStop() {

        Timber.e("onStop")
        super.onStop()
    }

    override fun onDestroyView() {
        Timber.e("onDestroyView")
        super.onDestroyView()
    }

    override fun onDestroy() {
        Timber.e("onDestroy")
        viewModel.destroylifeCycle()
        NetworkHelper.setWsMessageListener(null)
        PopupWindowHelper.closeAllPopupWindows()
        XpopHelper.removeAllXpop()
        GoodsHelper.clean()
//        viewModel.liveDataRespose.removeObservers(viewLifecycleOwner)
        PrinterDeviceHelper.clear()
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }


    private fun replaceFragment(fragment: BaseFragment, bundle: Bundle? = null) {
        fragment.arguments = bundle
        parentFragmentManager.beginTransaction()
            .replace(R.id.dashboard_fragment, fragment).commit()
    }

    fun replaceFragmentFromOtherFragment(
        featureMenuEnum: Int,
        fragment: BaseFragment,
        bundle: Bundle? = null
    ) {
        featureMenuAdapter?.updateSelectedState(featureMenuEnum)
        fragment.arguments = bundle
        parentFragmentManager.beginTransaction()
            .replace(R.id.dashboard_fragment, fragment).commit()
    }


    private fun printTickerFromWs(
        orderedInfoResponse: OrderedInfoResponse,
        isPreSettlement: Boolean? = false
    ) {
        viewModel.printTickerFromWs(requireContext(), orderedInfoResponse, isPreSettlement)
    }

    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    private fun updateStoreInfo() {
        binding?.apply {
            context?.let {
                requireActivity().runOnUiThread {
                    Glide.with(it).asBitmap()
                        .load(viewModel.userLoginResponse.value?.url)
                        .circleCrop()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .error(R.drawable.ic_logo)
                        .placeholder(R.drawable.ic_logo).into(imgLogo)

                    tvStoreName.text = viewModel.userLoginResponse.value?.getStoreNameByLan()

                    viewModel.initailizeListMenu(
                        it,
                        viewModel.userLoginResponse.value?.isTableService,
                        isInit = false
                    )

                    //如果操作隐藏桌台  且当前在桌台界面 则跳到菜单界面
                    val currentFragment = parentFragmentManager.fragments.firstOrNull()
                    if ((currentFragment is TableFragment) && (viewModel.userLoginResponse.value?.isDisplayTable == false)) {
                        Timber.e("如果操作隐藏桌台  且当前在桌台界面 则跳到菜单界面")
                        replaceFragmentFromOtherFragment(
                            FeatureMenuEnum.ORDER.id,
                            MenuOrderFragment(),
                        )
                    }
                }

            }
        }
    }

    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
//        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {
            SimpleEventType.UPDATE_STORE -> {
                updateStoreInfo()
            }

            SimpleEventType.GET_UNREAD_EVENT -> {
                viewModel.updateUnRead()
            }

            SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT -> {
                viewModel.updateAcceptOrderUnRead()
            }

            SimpleEventType.UPDATE_USB_PRINT_INFO -> {
                viewModel.getPrinterInfoList()
            }

            SimpleEventType.GET_NOTICE_UNREAD_EVENT -> {
                val data = event.data as? NoticeResponse
                requireActivity()?.runOnUiThread {
                    if (viewModel.noticeResponse.value?.id == data?.id) {
                        binding?.apply {
                            llNotice.isVisible = false
                        }
                    }
                    viewModel.updateNoticeUnRead()
                }
            }

            SimpleEventType.GET_LAST_NOTICE_EVENT -> {
                viewModel.getLastNotice()
            }

            SimpleEventType.PRINT_CART_PRE_STELLEMENT -> {
                val data = event.data as? OrderedInfoResponse
                data?.apply {
                    printTickerFromWs(
                        this, isPreSettlement = true
                    )
                }
            }

            else -> {

            }

        }
    }

    private var isConnect = false

    private fun initObserverSocket() {
        viewModel.liveDataRespose.observe(viewLifecycleOwner) {
            when (it) {
                is WebSocket.Event.OnConnectionOpened<*> -> {
                    Timber.e("connection opened")
                    isConnect = true
                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_ORDER_LIST, null))
                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_NOTICE_UNREAD_EVENT, null))
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_LAST_NOTICE_EVENT, null))
                }

                is WebSocket.Event.OnConnectionClosed -> {
                    Timber.e("connection closed")
//                    CrashReport.postCatchedException(NullPointerException("connection closed.."))
                    isConnect = false
                }

                is WebSocket.Event.OnConnectionClosing -> {
                    Timber.e(
                        "closing connection.."
                    )
//                    CrashReport.postCatchedException(NullPointerException("closing connection.."))
                    isConnect = false
                }

                is WebSocket.Event.OnConnectionFailed -> {
                    Timber.e("connection failed  $it")
//                    CrashReport.postCatchedException(NullPointerException("connection failed.."))
                    isConnect = false
                }

                is WebSocket.Event.OnMessageReceived -> {
                    if (it.message is Message.Text) {
                        if ((it.message as Message.Text).value == "ping") {
                            viewModel.testingWebsocketSendMessage()
                        } else if ((it.message as Message.Text).value == "pong")
                            Timber.e("收到服务端的 pong")
                        // viewModel.testingWebsocketSendMessage()
                        else {
                            if (!isConnect) {
                                return@observe
                            }
                            try {
                                Timber.e((it.message as Message.Text).value)
                                val socketModel = JSON.parseObject(
                                    (it.message as Message.Text).value,
                                    SocketModel::class.java
                                )
                                socketModel.cmd?.let { cmd ->
                                    NetworkHelper.getMOnWsMessageListener()?.onMessage(it)

                                    if (cmd == WsCommand.CONVENTION_ORDER_CANCEL) {
                                        EventBus.getDefault().post(
                                            SimpleEvent(
                                                SimpleEventType.GET_UNREAD_EVENT,
                                                null
                                            )
                                        )

                                    }
                                    if (cmd == WsCommand.MODIFY_PRINTER_CONFIG) {
                                        socketModel.data?.let { data ->
                                            val wsPrinterActionResponse = JSON.parseObject(
                                                data.toJson(),
                                                WsPrinterActionResponse::class.java
                                            )
                                            Timber.e("wsPrinterActionResponse.type  ${wsPrinterActionResponse.type}")
                                            if (wsPrinterActionResponse.type == WsPrinterActionEnum.CREATE.type || wsPrinterActionResponse.type == WsPrinterActionEnum.UPDATE_TEMPLATE.type) {
                                                //获取Usb 打印配置
                                                val currentFragment =
                                                    parentFragmentManager.fragments.firstOrNull()
                                                if (currentFragment != null) {
                                                    (currentFragment as MainDashboardFragment).getPrinterInfoList()
                                                }
                                            } else if (wsPrinterActionResponse.type == WsPrinterActionEnum.DEL.type) {
                                                PrinterDeviceHelper.delPrinter(
                                                    wsPrinterActionResponse.data
                                                )
                                            } else if (wsPrinterActionResponse.type == WsPrinterActionEnum.UPDATE.type) {
                                                PrinterDeviceHelper.updatePrinter(
                                                    wsPrinterActionResponse.data
                                                )
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.CREATE_ORDER) {
                                        //员工 用户端下单
                                        socketModel.data?.let { data ->
                                            val orderResponse = Gson().fromJson(
                                                data.toJson(),
                                                OrderedInfoResponse::class.java
                                            )

                                            EventBus.getDefault()
                                                .post(
                                                    SimpleEvent(
                                                        SimpleEventType.GET_UNREAD_EVENT,
                                                        null
                                                    )
                                                )
                                            if (PrinterDeviceHelper.isAcceptOrderPrinter(
                                                    orderResponse.orderNo
                                                )
                                            ) {
                                                //把接单 已打印记录清楚， 防止其他情况下打印不了
                                                PrinterDeviceHelper.removeAcceptOrder(orderResponse.orderNo)
                                                return@observe
                                            }

                                            if (orderResponse.payStatus == OrderedStatusEnum.UNPAID.id) {
                                                //如果是待支付订单就不打单子
                                                return@observe
                                            }

                                            //打印订单
                                            orderResponse?.apply {
                                                printTickerFromWs(
                                                    this
                                                )
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.PAY_ORDER) {
                                        //用户付款
                                        socketModel.data?.let { data ->
                                            val orderResponse = Gson().fromJson(
                                                data.toJson(),
                                                OrderedInfoResponse::class.java
                                            )
                                            EventBus.getDefault()
                                                .post(
                                                    SimpleEvent(
                                                        SimpleEventType.GET_UNREAD_EVENT,
                                                        null
                                                    )
                                                )

                                            if (orderResponse.isPreOrder()) {
                                                //这边判断一下如果预定时间没到不能打印
                                                if (orderResponse.getDingTime().isNotEmpty()) {
                                                    //是否有预定模板
                                                    val preOrderTemplate =
                                                        PrinterTemplateHelper.getPerOrderTemplate()

                                                    if (!PrinterTemplateHelper.isTimeToPrinterPerOrderTicket(
                                                            preOrderTemplate,
                                                            orderResponse
                                                        )
                                                    ) {
                                                        return@observe
                                                    }
                                                }
                                            }

                                            //判断一下是不是扫码弹窗开着
                                            val fragment =
                                                PayDialog.getDialog(parentFragmentManager)
                                            Timber.e("$fragment   fragment.getOrderNo()  ${fragment?.getOrderNo()}   ${orderResponse.orderNo}")
                                            if (fragment != null && fragment.getOrderNo() == orderResponse.orderNo) {
                                                Timber.e("对应订单的二维码弹窗开着， 所以这里不打，那边轮询打")
                                            } else {
                                                Timber.e(
                                                    "二维码弹窗关闭打印 : 订单是已打印过${
                                                        PrinterDeviceHelper.isOrderPrinter(
                                                            orderNo = orderResponse.orderNo
                                                        )
                                                    }"
                                                )
                                                if (!PrinterDeviceHelper.isOrderPrinter(orderNo = orderResponse.orderNo)) {
                                                    Timber.e("打印结账单")
                                                    //打印订单
                                                    orderResponse?.apply {
                                                        printTickerFromWs(
                                                            this
                                                        )
                                                    }
                                                }
                                            }

                                        }
                                    }
                                    if (cmd == WsCommand.REPAYMENT) {
                                        socketModel.data?.let { data ->
                                            val response = Gson().fromJson(
                                                data.toJson(),
                                                WsRepaymentResponse::class.java
                                            )
                                            val repaymentResponse = response.repaymentResult
                                            if (repaymentResponse != null) {
                                                viewModel.printCreditRecordFromWs(
                                                    requireContext(), repaymentResponse
                                                )
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.USER_ADD_GOODS) {
                                        //用户加购
                                        socketModel.data?.let { data ->

                                            val orderResponse = Gson().fromJson(
                                                data.toJson(),
                                                OrderedInfoResponse::class.java
                                            )
                                            Timber.e("加购  ${orderResponse.isPaymentAdvance}")
                                            if (orderResponse.isPaymentAdvance == false && orderResponse.payStatus != OrderedStatusEnum.TO_BE_CONFIRM.id) {
                                                orderResponse.currentOrderMoreList =
                                                    orderResponse.goods
                                                //打印订单
                                                orderResponse?.apply {
                                                    printTickerFromWs(
                                                        this
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.MODIFY_WEIGHT) {
                                        // 确认订单  /修改重量
                                        socketModel.data?.let { data ->
//                                            val orderResponse = JSON.parseObject(
//                                                data.toJson(),
//                                                OrderedInfoResponse::class.java
//                                            )
                                            val orderResponse = Gson().fromJson(
                                                data.toJson(),
                                                OrderedInfoResponse::class.java
                                            )

                                            orderResponse.currentOrderMoreList = orderResponse.goods
                                            //打印订单
                                            orderResponse?.apply {
                                                printTickerFromWs(
                                                    this
                                                )
                                            }

                                        }
                                    }
                                    if (cmd == WsCommand.PLAY_ORDER_SOUND) {
                                        //播放新订单语音
                                        socketModel.data?.let {
                                            SoundPlayUtils.play()
                                        }
                                    }

                                    if (cmd == WsCommand.ORDER_HAS_READ) {
                                        //消息已读
                                        EventBus.getDefault()
                                            .post(
                                                SimpleEvent(
                                                    SimpleEventType.GET_UNREAD_EVENT,
                                                    null
                                                )
                                            )
                                    }

                                    if (cmd == WsCommand.PRINT_PRE_SETTLEMENT_TICKET) {
                                        //打印预结算小票
                                        socketModel.data?.let { data ->
//                                            val orderResponse = JSON.parseObject(
//                                                data.toJson(),
//                                                OrderedInfoResponse::class.java
//                                            )
                                            val orderResponse = Gson().fromJson(
                                                data.toJson(),
                                                OrderedInfoResponse::class.java
                                            )
                                            //打印订单
                                            orderResponse?.apply {
                                                printTickerFromWs(
                                                    this, isPreSettlement = true
                                                )
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.STAFF_PRINT_TICKET) {
                                        //员工端点打小票
                                        socketModel.data?.let { data ->
//                                            val wsPrintOrderedInfo = JSON.parseObject(
//                                                data.toJson(),
//                                                WsPrintOrderedInfo::class.java
//                                            )
                                            val wsPrintOrderedInfo = Gson().fromJson(
                                                data.toJson(),
                                                WsPrintOrderedInfo::class.java
                                            )

                                            //打印订单
                                            wsPrintOrderedInfo?.apply {
                                                //打结算单
                                                if (orderInfo != null) {
                                                    viewModel.printTickerFromWs(
                                                        requireContext(),
                                                        orderInfo,
                                                        isPrinterAgain = wsPrintOrderedInfo.isReprint
                                                            ?: true,
                                                        isCashierCheck = ticketTypes?.contains(
                                                            PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id
                                                        ) == true || ticketTypes?.contains(
                                                            PrintTemplateTypeEnum.PRE_ORDER.id
                                                        ) == true || ticketTypes?.contains(
                                                            PrintTemplateTypeEnum.DINE_IN.id
                                                        ) == true || ticketTypes?.contains(
                                                            PrintTemplateTypeEnum.TAKE_AWAY.id
                                                        ) == true,
                                                        kitchenCheckType =
                                                        if (ticketTypes?.contains(
                                                                PrintTemplateTypeEnum.KITCHEN.id
                                                            ) == true
                                                        ) {
                                                            if (wsPrintOrderedInfo.kitchenReceiptType == KitchenCheckTicketType.PART.id) {
                                                                KitchenCheckTicketType.PART.id
                                                            } else {
                                                                KitchenCheckTicketType.ALL.id
                                                            }
                                                        } else {
                                                            KitchenCheckTicketType.NONE.id
                                                        },
                                                        isLabel = ticketTypes?.contains(
                                                            PrintTemplateTypeEnum.LABEL.id
                                                        ) == true,
                                                        isPreSettlement = ticketTypes?.contains(
                                                            PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                                                        ) == true
                                                    )
                                                }
                                            }
                                        }
                                    }


                                    if (cmd == WsCommand.CANCEL_ACCEPT_ORDER || cmd == WsCommand.ACCEPT_ORDER || cmd == WsCommand.NEW_ACCEPT_ORDER) {
                                        EventBus.getDefault().post(
                                            SimpleEvent(
                                                SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT,
                                                null
                                            )
                                        )
                                    }
                                    if (cmd == WsCommand.NOTICE_CHANGE) {
                                        viewModel.requestNotice()
                                    }
                                    if (cmd == WsCommand.CANCEL_CREDIT) {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val orderNo = it.toString()
                                                PrinterDeviceHelper.clearOrderPrinter(
                                                    orderNo = orderNo
                                                )
                                            }
                                        }
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = JSON.parseObject(
                                                    it.toJson(),
                                                    WsReserveOrderCancel::class.java
                                                )
                                                Timber.e(" data.orderNo-> ${data.orderNo}")
                                                if (data.orderNo != null) {
                                                    /**
                                                     * 反结账  清掉本地 已支付已打印标记
                                                     */
                                                    PrinterDeviceHelper.clearOrderPrinter(
                                                        orderNo = data.orderNo
                                                    )
                                                }
                                            }
                                        }
                                    }
                                    if (cmd == WsCommand.ANTI_SETTLEMENT) {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = JSON.parseObject(
                                                    it.toJson(),
                                                    WsReserveOrderCancel::class.java
                                                )
                                                Timber.e(" data.orderNo-> ${data.orderNo}")
                                                if (data.orderNo != null) {
                                                    /**
                                                     * 反结账  清掉本地 已支付已打印标记
                                                     */
                                                    PrinterDeviceHelper.clearOrderPrinter(
                                                        orderNo = data.orderNo
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.SHIFT_HANDOVER_START) {
                                        //开班通知
                                        val isPerm =
                                            CURRENT_USER?.getPermissionList()
                                                ?.contains(PermissionEnum.WORK_HANDOVER.type) == true
                                        if (!isPerm) {
                                            return@observe
                                        }
                                        socketModel.data?.let { data ->
                                            Timber.e("data-> ${data.toString()}  userId-> ${CURRENT_USER?.userId}")
                                            if (CURRENT_USER?.userId == data.toString()) {
                                                viewModel.updateShiftHandoverStatus(
                                                    isNeedStartShift = false,
                                                    isShiftEmployee = true
                                                )
                                            } else {
                                                viewModel.updateShiftHandoverStatus(
                                                    isNeedStartShift = false,
                                                    isShiftEmployee = false
                                                )
                                            }
                                        }
                                    }
                                    if (cmd == WsCommand.SHIFT_HANDOVER_EXIT) {
                                        //交班通知
                                        val isPerm =
                                            CURRENT_USER?.getPermissionList()
                                                ?.contains(PermissionEnum.WORK_HANDOVER.type) == true
                                        if (!isPerm) {
                                            return@observe
                                        }
                                        socketModel.data?.let { data ->
                                            viewModel.updateShiftHandoverStatus(
                                                isNeedStartShift = true,
                                                isShiftEmployee = false
                                            )
                                            if (CURRENT_USER?.userId == data.toString()) {
                                                return@observe
                                            }

                                            ConfirmDialog.showDialog(
                                                parentFragmentManager,
                                                content = getString(R.string.handover_tips),
                                                positiveButtonTitle = getString(R.string.start_classes),
                                                negativeButtonTitle = getString(R.string.cancel),
                                                isShowCancel = true
                                            ) {
                                                activity?.supportFragmentManager?.let { it1 ->
                                                    StartClassesDialog.showDialog(
                                                        it1,
                                                        type = StartClassesDialog.Type.CLASSES_AND_IMPREST
                                                    ) {

                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (cmd == WsCommand.STORE_CONFIG_CHANGE) {
                                        viewModel.getStoreInfo(requireContext())
                                    }
                                }

                            } catch (e: Exception) {
                                e.printStackTrace()

                            }
                        }
                        //Toast.makeText(context, it.message.toString(), Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }

//    /**
//     * 影藏PopupWindow页面弹出时的虚拟按键
//     */
//    fun hideBottomUIMenuForPopupWindow(popupWindow: PopupWindow?) {
//        if (popupWindow != null && popupWindow.contentView != null) {
//            popupWindow.contentView.setOnSystemUiVisibilityChangeListener { //        //保持布局状态
//                var uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or  //布局位于状态栏下方
//                        View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or  //全屏
//                        View.SYSTEM_UI_FLAG_FULLSCREEN or  //隐藏导航栏
//                        View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
//                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                uiOptions = if (Build.VERSION.SDK_INT >= 19) {
//                    uiOptions or 0x00001000
//                } else {
//                    uiOptions or View.SYSTEM_UI_FLAG_LOW_PROFILE
//                }
//                popupWindow.contentView.systemUiVisibility = uiOptions
//            }
//        }
//    }

    /**
     * 显示从右边滑出的支付Fragment
     */
    fun showPaymentSlideFragment(
        menuOrderScreen: SecondaryScreenUI? = null,
        currentScene: Int? = null,
        orderInfo: OrderedInfoResponse? = null,
        shoppingRecord: ShoppingRecord? = null,
        conversionRatio: Long? = null,
        totalPrice: Long? = null,
        totalVipPrice: Long? = null,
        countryCode: String? = null,
        phone: String? = null,
        rechargeData: PayDialog.RechargeData? = null,
        creditRecord: CreditRecord? = null,
        paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
        repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
        onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null,
        onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
        rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null,
        onCloseListener: ((String?) -> Unit)? = null,
        onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
        onClearShoppingListener: (() -> Unit)? = null,
    ) {
        val paymentContainer = binding?.paymentFragmentContainer
        val grayOverlay = binding?.grayOverlay

        // 显示灰色遮罩
        grayOverlay?.visibility = View.VISIBLE
        grayOverlay?.alpha = 0f
        grayOverlay?.animate()?.alpha(1f)?.setDuration(200)?.start()

        // 点击遮罩关闭支付Fragment
        grayOverlay?.setOnClickListener {
            hidePaymentSlideFragment()
        }

        paymentContainer?.visibility = View.VISIBLE

        val fragment = PaymentSlideFragment.newInstance(
            menuOrderScreen = menuOrderScreen,
            currentScene = currentScene,
            orderInfo = orderInfo,
            shoppingRecord = shoppingRecord,
            conversionRatio = conversionRatio,
            totalPrice = totalPrice,
            totalVipPrice = totalVipPrice,
            countryCode = countryCode,
            phone = phone,
            rechargeData = rechargeData,
            creditRecord = creditRecord,
            paymentResponse = paymentResponse,
            repaymentResponse = repaymentResponse,
            onlineRepaymentSuccessListener = onlineRepaymentSuccessListener,
            onlineSuccessListener = onlineSuccessListener,
            rechargeSuccessListener = rechargeSuccessListener,
            onCloseListener = onCloseListener,
            onTopUpListener = onTopUpListener,
            onClearShoppingListener = onClearShoppingListener
        )

        childFragmentManager.beginTransaction()
            .setCustomAnimations(
                R.anim.slide_in_right_fast, 0, 0, R.anim.slide_out_right_fast
            )
            .replace(R.id.paymentFragmentContainer, fragment)
            .commit()
    }

    /**
     * 隐藏支付Fragment
     */
    fun hidePaymentSlideFragment() {
        val paymentContainer = binding?.paymentFragmentContainer
        val grayOverlay = binding?.grayOverlay
        val fragment = childFragmentManager.findFragmentById(R.id.paymentFragmentContainer)

        // 隐藏灰色遮罩
        grayOverlay?.animate()?.alpha(0f)?.setDuration(200)?.withEndAction {
            grayOverlay.visibility = View.GONE
        }?.start()

        if (fragment != null) {
            childFragmentManager.beginTransaction()
                .setCustomAnimations(
                    0, R.anim.slide_out_right_fast, 0, 0
                )
                .remove(fragment)
                .commit()
        }

        // 延迟隐藏容器，等待动画完成
        paymentContainer?.postDelayed({
            paymentContainer.visibility = View.GONE
        }, 200)
    }
}