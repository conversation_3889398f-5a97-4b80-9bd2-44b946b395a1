package com.metathought.food_order.casheir.helper

import android.content.Context
import com.metathought.food_order.casheir.constant.LocalPrinterEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.WifiPrinterDevice
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.doubleStrToIntStr
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.widget.printer.WifiPrintQueueManager
import net.posprinter.IDeviceConnection
import net.posprinter.POSConnect
import net.posprinter.POSPrinter
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit


/**
 *<AUTHOR>
 *@time  2024/7/22
 *@desc
 **/


object PrinterDeviceHelper {


    private var timer: Timer? = null

    //挂账记录打印
    private var creditRecordPrinterMap = mutableMapOf<String, String>()

    //存二维码已付款打印过的订单
    private var orderPrinterMap = mutableMapOf<String, String>()

    private var acceptOrderPrinterMap = mutableMapOf<String, String>()

    //当前USB打印配置
//    private var currentUsbPrinterInfo: PrinterConfigInfo? = null

    /**
     *  网络请求回来的打印列表
     */
    private var printerInfoList = mutableListOf<PrinterConfigInfo>()

//    private var curConnect: IDeviceConnection? = null
//    private var printer: POSPrinter? = null
//

//    var noPrintList = mutableListOf<NoPrintModel>()


    //wifi 打印机
    private var wifiPrinterDevice: MutableMap<String, WifiPrinterDevice> = mutableMapOf()

    //    private var wifiConnectionsMap: MutableMap<String, IDeviceConnection> = mutableMapOf()
//    private var wifiConnectionsStateMap: MutableMap<String, Boolean> = mutableMapOf()
//    var wifiPOSPrinter: POSPrinter? = null

    private var logoBase64: String? = null

    fun getLogo(): String? {
        return logoBase64
    }

    fun initPrinter(context: Context) {
        // 1. 计算线程池大小（I/O密集型）
        val cpuCores = Runtime.getRuntime().availableProcessors()
        val ioIntensiveThreads = cpuCores * 3  // I/O密集型建议为CPU核心数的2-3倍（4核→12线程）
            // 2. 自定义线程工厂（带命名和异常处理）
        val threadFactory = ThreadFactory { runnable ->
            Thread(runnable, "Printer-BgThread-${System.currentTimeMillis()}").apply {
                isDaemon = true  // 守护线程避免阻塞应用退出
                priority = Thread.NORM_PRIORITY  // 普通优先级
                uncaughtExceptionHandler = Thread.UncaughtExceptionHandler { _, e ->
                    Timber.e("打印机后台线程异常: ${e.message}", e)
                }
            }
        }
        // 3. 配置线程池（关键优化）
        val threadPool = ThreadPoolExecutor(
            ioIntensiveThreads,       // 核心线程数（长期保留）
             ioIntensiveThreads,    // 最大线程数（与核心数相同，固定大小）
            30,                      // 空闲线程存活时间（30秒）
            TimeUnit.SECONDS,
            LinkedBlockingQueue(),                   // 无界队列（I/O任务等待时间长，避免任务丢弃）
            threadFactory,
            ThreadPoolExecutor.CallerRunsPolicy()    // 拒绝策略：主线程执行（避免任务丢失）
        )
        // 4. 替换SDK的后台线程池（在init前）
        POSConnect.backgroundThreadExecutor = threadPool
        POSConnect.init(context)
    }

//    fun resetPrinter() {
//        curConnect?.close()
//        curConnect = null
//        printer = null
//        EventBus.getDefault().post(
//            SimpleEvent(
//                SimpleEventType.UPDATE_PRINTER_MANAGER_LIST,
//                null
//            )
//        )
//    }


    fun connectWifiPrinter(ipAddress: String?) {
        if (ipAddress == null) {
            return
        }

        if (wifiPrinterDevice.contains(ipAddress)) {
            Timber.e("connectWifiPrinter11111")
            //如果是连过 ，判断一下是否正在连接
            wifiPrinterDevice[ipAddress]?.iDeviceConnection?.isConnect(byteArrayOf(0)) { status ->
                Timber.e("${ipAddress} 存在 然后 status ${status}")
                if (status != 1) {
                    closeWifiConnection(ipAddress)
                    connectWifiPrinterByIp(ipAddress)
                }
            }
        } else {
            //如果没连过
            Timber.e("connectWifiPrinter22222222")
            connectWifiPrinterByIp(ipAddress)
        }
    }

    fun connectWifiPrinterByIp(ipAddress: String?) {
        Timber.e("connectWifiPrinterByIp")
        if (ipAddress != null) {
            wifiPrinterDevice[ipAddress] = WifiPrinterDevice().apply {
                iDeviceConnection =
                    POSConnect.createDevice(POSConnect.DEVICE_TYPE_ETHERNET)
                iDeviceConnection?.connect(ipAddress) { code: Int, msg: String ->
                    Timber.e("code111:${code}   msg ->${msg}  ip->${ipAddress}")
                    Timber.e("wifi hashcode ${wifiPrinterDevice[ipAddress]?.iDeviceConnection?.hashCode()}")
                    connectListener(ipAddress, code)
                }
            }
        }
    }


    private fun connectListener(ipAddress: String?, code: Int) {
        when (code) {
            POSConnect.CONNECT_SUCCESS -> {
                Timber.e("链接上了")
                if (wifiPrinterDevice[ipAddress]?.iDeviceConnection != null) {
                    wifiPrinterDevice[ipAddress]?.pOSPrinter =
                        POSPrinter(wifiPrinterDevice[ipAddress]?.iDeviceConnection)
                }
                setWifiConnectState(ipAddress, true)
                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))

//                autoPrintTicket()
            }

            POSConnect.CONNECT_INTERRUPT -> {
                Timber.e("链接中断")
                /**
                 * 这里不做处理了 轮询查状态失败那边做重连
                 */
                setWifiConnectState(ipAddress, false)
//                closeWifiConnection(ipAddress)
//                connectWifiPrinter(ipAddress)
            }

            POSConnect.CONNECT_FAIL -> {
                Timber.e("链接失败")
                /**
                 * 这里不做处理了 轮询查状态失败那边做重连
                 */
//                closeWifiConnection(ipAddress)
                setWifiConnectState(ipAddress, false)

            }

        }
        EventBus.getDefault()
            .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
    }


    fun setWifiConnectState(ipAddress: String?, isConnect: Boolean) {
        Timber.e("setWifiConnectState  ${isConnect}")
        if (ipAddress != null) {
//            wifiConnectionsStateMap[ipAddress] = isConnect
            wifiPrinterDevice[ipAddress]?.isConnect = isConnect

        }
        EventBus.getDefault()
            .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
    }

    fun getWifiConnectState(printerConfigInfo: PrinterConfigInfo?): Boolean {
        if (printerConfigInfo?.ipAddress != null) {
            if (wifiPrinterDevice.contains(printerConfigInfo.ipAddress)) {
                return wifiPrinterDevice[printerConfigInfo.ipAddress]?.isConnect ?: false
            }
            return false
        }
        return false
    }


    fun getPrinterList(): List<PrinterConfigInfo> {
        return printerInfoList
    }

    //删除打印机
    fun delPrinter(printerConfigInfo: PrinterConfigInfo?) {
        val index = printerInfoList.indexOfFirst {
            Timber.e("it.id ${it.id}   ${printerConfigInfo?.id?.doubleStrToIntStr()}")
            it.id == printerConfigInfo?.id?.doubleStrToIntStr()
        }
        Timber.e("del index-> $index")
        if (index != -1) {
            printerInfoList.removeAt(index)
            if (printerConfigInfo?.type == PrinterTypeEnum.WIFI.type) {
                /**
                 * 如果是wifi 要断开对应的链接
                 * 判断一下wifi打印机列表里是否还有相同ip地址
                 */
                val list = printerInfoList.filter {
                    it.ipAddress == printerConfigInfo.ipAddress
                }
                if (list.isNullOrEmpty()) {
                    closeWifiConnection(printerConfigInfo?.ipAddress)
                } else {
                    Timber.e("还存在 相同ipAddress")
                }
            } else if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                dealUsbPrinterList()
            }
            EventBus.getDefault()
                .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
        }
    }

    //更新打印机配置
    fun updatePrinter(printerConfigInfo: PrinterConfigInfo?) {
        val index = printerInfoList.indexOfFirst {
            Timber.e("it.id ${it.id}   ${printerConfigInfo?.id?.doubleStrToIntStr()}")
            it.id == printerConfigInfo?.id?.doubleStrToIntStr()
        }
        Timber.e("update index-> $index")
        if (index != -1) {
            //新设置的ip 和原来的不一样
            val isDiffIp = printerInfoList[index].ipAddress != printerConfigInfo!!.ipAddress
            if (printerConfigInfo.type == PrinterTypeEnum.WIFI.type && isDiffIp) {
                //如果是wifi 要断开对应的链接   不一样才去断开重连
                //判断一下wifi打印机列表里是否还有其他和要断开连接的相同ip地址
                val list = printerInfoList.filter {
                    it.ipAddress == printerInfoList[index].ipAddress && it.id != printerInfoList[index].id
                }
                if (list.isNullOrEmpty()) {
                    Timber.e("不存在 相同ipAddress")
                    closeWifiConnection(printerInfoList[index].ipAddress)
                } else {
                    Timber.e("还存在 相同ipAddress")
                }
                connectWifiPrinter(printerConfigInfo.ipAddress)
            }

            printerInfoList[index] = printerConfigInfo
            printerInfoList[index].id = printerConfigInfo.id?.doubleStrToIntStr()
            if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                dealUsbPrinterList()
            }
            EventBus.getDefault()
                .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
        }

    }

    fun getWifiConnection(ipAddress: String?): IDeviceConnection? {
        if (wifiPrinterDevice.contains(ipAddress)) {
            return wifiPrinterDevice[ipAddress]?.iDeviceConnection
        }
        Timber.e("iDeviceConnection 不存在")
        return null
    }

    fun getWifiPosPrinter(ipAddress: String?): POSPrinter? {
        if (wifiPrinterDevice.contains(ipAddress)) {
            return wifiPrinterDevice[ipAddress]?.pOSPrinter
        }
        Timber.e("pOSPrinter 不存在")
        return null
    }

    fun closeWifiConnection(ipAddress: String?, isReConnection: Boolean? = false) {
        try {
            val connect = getWifiConnection(ipAddress)
            Timber.e("关闭连接wifi连接 ${connect.hashCode()} $ipAddress")
            connect?.close()
            if (isReConnection == false) {
                //做重连的时候删除
                wifiPrinterDevice.remove(ipAddress)
            }
            setWifiConnectState(ipAddress, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    fun getUsbPrinterInfo(): PrinterConfigInfo? {
//        if (currentUsbPrinterInfo != null) {
//            return currentUsbPrinterInfo
//        }
        val printerInfo = printerInfoList.firstOrNull { it.type == PrinterTypeEnum.USB.type }
        return printerInfo
    }

    //获取USB和WIFI打印配置
    suspend fun getPrinterInfoListFromNet(repository: Repository) {
        try {
            val response = repository.getPrinterInfoList()
            if (response is ApiResponse.Success) {
                printerInfoList.clear()
                printerInfoList.addAll(response.data)

                dealWifiPrinterList()
                dealUsbPrinterList()

                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.UPDATE_PRINTER_MANAGER_LIST, null))
            }

            val logoResponse = repository.getLogoBase64()
            if (logoResponse is ApiResponse.Success) {
                logoBase64 = logoResponse.data ?: ""
            }

        } catch (e: Exception) {

        }
    }

    /**
     *  处理Wifi打印机列表
     *
     */
    private fun dealWifiPrinterList() {
        var isHasWifiPrinter = false
        printerInfoList.forEach {
            if (it.type == PrinterTypeEnum.WIFI.type) {
                connectWifiPrinter(it.ipAddress)
                isHasWifiPrinter = true
            }
        }
        if (isHasWifiPrinter) {
            startCheckWifiPrinterStatus()
//            startSendWifiPrinter()
        }
    }

    /**
     *  处理USb打印机列表
     *
     */
    fun dealUsbPrinterList() {
        PrinterUsbDeviceHelper.cleatALlPrinterObject()
        printerInfoList.forEachIndexed { index, printerConfigInfo ->
            if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                /**
                 * 先处理后台设置usb 打印 sn 不为空的情况 ，把usb打印设备对应后台sn 码的 一一对应的了
                 */
                if (!printerConfigInfo.sn.isNullOrEmpty()) {
                    //如果Sn不为空,查找指定USB打印机
                    var map =
                        PrinterUsbDeviceHelper.usbDeviceMap.filterValues { usbDevice ->
                            Timber.e("usbDevice.usbDevice?.serialNumber==>${usbDevice.usbDevice?.serialNumber}")
                            usbDevice.usbDevice?.serialNumber == printerConfigInfo.sn
                        }
                    map.forEach { s, usbPrinterDevice ->
                        if (printerConfigInfo.machineType == LocalPrinterEnum.LABEL_PRINTER.id) {
                            PrinterUsbDeviceHelper.createTSPLPrinter(usbPrinterDevice.usbDevice?.deviceName)
                        } else {
                            PrinterUsbDeviceHelper.createPOSPrinter(usbPrinterDevice.usbDevice?.deviceName)
                        }
                    }
                    map =
                        PrinterUsbDeviceHelper.usbDeviceMap.filterValues { usbDevice -> usbDevice.usbDevice?.serialNumber == printerConfigInfo.sn }
                    val first = map.toList().firstOrNull()
                    printerConfigInfo.usbDevice = first?.second?.usbDevice

                }
            }
        }

        Timber.e("开始处理后台没有设置sn码的，从为被配置的")
        printerInfoList.forEachIndexed { index, printerConfigInfo ->
            if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                /**
                 * 处理后台设置usb 小票 打印 sn 为空的情况  //如果后台没设Sn 那默认连的都是同一个打印设备
                 */
                Timber.e("index:${index}")
                if (printerConfigInfo.sn.isNullOrEmpty() && printerConfigInfo.machineType == LocalPrinterEnum.TICKET_PRINTER.id) {
                    //筛选出usb打印设备里面 里面还有没对应实例化的 打印设备 ，并且 不是标签打印
                    var usbPrinterDeviceMap =
                        PrinterUsbDeviceHelper.usbDeviceMap.filterValues { usbDevice ->
                            Timber.e("vendorId:${usbDevice.usbDevice?.vendorId}  productId:${usbDevice.usbDevice?.productId}")
                            usbDevice.tSPLPrinter == null && usbDevice.pOSPrinter == null && !PrinterUsbDeviceHelper.isLabelPrinter(
                                usbDevice.usbDevice?.vendorId,
                                usbDevice.usbDevice?.productId
                            )
                        }
                    Timber.e("usbPrinterDeviceMap :${usbPrinterDeviceMap.size}")
                    //如果列表不为空 从里面取出第一个用来示例
                    if (usbPrinterDeviceMap.isNotEmpty()) {
                        var usbPrinterDevice = usbPrinterDeviceMap.entries.firstOrNull()?.value
                        usbPrinterDevice =
                            if (printerConfigInfo.machineType == LocalPrinterEnum.LABEL_PRINTER.id) {
                                //这个判断是不会进来了，因为上面判断了小票类型
                                PrinterUsbDeviceHelper.createTSPLPrinter(usbPrinterDevice?.usbDevice?.deviceName)
                            } else {
                                PrinterUsbDeviceHelper.createPOSPrinter(usbPrinterDevice?.usbDevice?.deviceName)
                            }
                        printerConfigInfo.usbDevice = usbPrinterDevice?.usbDevice
                    }
                }
            }
        }
    }

    /**
     *  清理打印机列表 里面 Usb相关硬件数据
     *
     */
    fun clearUsbPrintInPrintList() {
        printerInfoList.forEach {
            it.usbDevice = null
        }
    }


    //是否打印过该已付款订单
    fun isOrderPrinter(orderNo: String?): Boolean {
        return orderPrinterMap.contains(orderNo)
    }

    fun setOrderPrinter(orderNo: String?) {
        if (!orderPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            orderPrinterMap[orderNo] = orderNo
        }
    }

    fun clearOrderPrinter(orderNo: String?) {
        if (orderPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            orderPrinterMap.remove(orderNo)
        }
    }


    fun isCreditRecordPrinter(orderNo: String?): Boolean {
        return creditRecordPrinterMap.contains(orderNo)
    }

    fun setCreditRecordPrinter(orderNo: String?) {
        if (!creditRecordPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            creditRecordPrinterMap[orderNo] = orderNo
        }
    }

    /**
     * 收银端 接的单时候已打印 防止ws 下发重复打印
     *
     * @param orderNo
     * @return
     */
    fun isAcceptOrderPrinter(orderNo: String?): Boolean {
        return acceptOrderPrinterMap.contains(orderNo)
    }

    fun setAcceptOrderPrinter(orderNo: String?) {
        if (!acceptOrderPrinterMap.contains(orderNo) && !orderNo.isNullOrEmpty()) {
            acceptOrderPrinterMap[orderNo] = orderNo
        }
    }

    fun removeAcceptOrder(orderNo: String?) {
        if (acceptOrderPrinterMap.contains(orderNo)) {
            acceptOrderPrinterMap.remove(orderNo)
        }
    }


    //开始检测wifi打印机状态
    private fun startCheckWifiPrinterStatus() {
        Timber.e("开始计时1111")
        cancelCheckWifiPrinterStatus()
        val task = object : TimerTask() {
            override fun run() {
                Timber.e("定时器任务执行，查询wifi打印状态..")

                Timber.e("wifiPrinterDevice ${wifiPrinterDevice.size}")
                try {
                    wifiPrinterDevice.forEach { (s, wifiPrinterDevice) ->
                        Timber.e("循环 查询 printerStatus  ${wifiPrinterDevice.pOSPrinter} 发送ping  ${s}")
//                        if (wifiPrinterDevice.pOSPrinter == null) {
//                            Timber.e("pOSPrinter 没有  重连  ${s}")
//                            closeWifiConnection(s, true)
//                            connectWifiPrinterByIp(s)
//                        } else {
//                            wifiPrinterDevice.pOSPrinter?.printerStatus {
//                                Timber.e("printerStatus11  :${it}")
//                                if (it != POSConst.STS_NORMAL) {
//                                    Timber.e("重连  ${s}")
//                                    closeWifiConnection(s, true)
//                                    connectWifiPrinterByIp(s)
//                                } else {
//                                    Timber.e("还连着  ${s}")
//                                }
//                            }
//                        }
                        wifiPrinterDevice.iDeviceConnection?.isConnect(byteArrayOf(0)) { status ->
                            if (status == 0) {
                                Timber.e("重连  ${s}")
                                closeWifiConnection(s, true)
                                connectWifiPrinterByIp(s)
                            } else {
                                Timber.e("还连着 ->> ${s}")
                            }
                        }
//
                    }

                } catch (e: Exception) {
                    //   Timber.e("s->${s}  校验wifi 异常")
                }
            }
        }
        timer = Timer()
        timer?.schedule(task, 12000, 10000)
    }

    //停止检测wifi打印机状态
    private fun cancelCheckWifiPrinterStatus() {
        timer?.cancel()
        timer = null
    }

    fun clear() {
//        noPrintList.clear()
        printerInfoList.clear()
        wifiPrinterDevice.forEach { s, wifiPrinterDevice ->
            wifiPrinterDevice.iDeviceConnection?.close()
        }
        wifiPrinterDevice.clear()
//      wifiConnectionsStateMap.clear()

//      waitPrinterList.clear()
        cancelCheckWifiPrinterStatus()

        WifiPrintQueueManager.clearTask()

//      cancelSendWifiPrinter()
    }


//    private var printerSendTimer: Timer? = null

//    private var waitPrinterList = mutableListOf<NoPrintModel>()
//    private var lock = Object()

//    fun insetToWaitPrinterList(noPrintModel: NoPrintModel, isLock: Boolean = true) {
//        try {
//            val currentTimeStamp = Date().time
//            if (currentTimeStamp - noPrintModel.timeStamp < 30 * 60 * 1000) {
//                Timber.e("插入最后的时间")
//                /**
//                 * 只保留30分钟内的
//                 */
////                if (isLock) {
////                    synchronized(lock) {
////                        waitPrinterList.add(noPrintModel)
////                    }
////                } else
//                waitPrinterList.add(noPrintModel)
//                Timber.e("插入后剩余 ${waitPrinterList.size}")
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//
//    private fun startSendWifiPrinter() {
//        Timber.e("开始计时2222")
//        cancelSendWifiPrinter()
//
//        val task = object : TimerTask() {
//            override fun run() {
//                Timber.e("定时向wifi打印机发送")
//                try {
////                    synchronized(lock) {
//                    if (!waitPrinterList.isNullOrEmpty()) {
//                        Timber.e("剩余未打印wifi列表1111  ${waitPrinterList.size}")
//                        val noPrintModel = waitPrinterList.removeFirst()
//                        Timber.e("剩余未打印wifi列表22222  ${waitPrinterList.size}")
//                        if (wifiPrinterDevice.containsKey(noPrintModel.printerConfigInfo?.ipAddress)) {
//                            val time = Date().time
////                            Timber.e("向 ip 为 ${noPrintModel.printerConfigInfo?.ipAddress} 发送  ==>${time}    :${wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect}")
//                            Timber.e("wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect  ${wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect}")
//                            if (wifiPrinterDevice[noPrintModel.printerConfigInfo?.ipAddress]?.isConnect == true) {
//                                Printer.printWifiTicket(
//                                    MyApplication.myAppInstance,
//                                    noPrintModel,
//                                    time
//                                )
//                            } else {
//                                Timber.e("ip 为 ${noPrintModel.printerConfigInfo?.ipAddress} 状态不对 ，加回待打印队列")
//                                insetToWaitPrinterList(noPrintModel, false)
//                            }
//                        }
//                    } else {
//                        Timber.e("打印列表为空")
//                    }
////                    }
//
//                } catch (e: Exception) {
//                    //   Timber.e("s->${s}  校验wifi 异常")
//                }
//            }
//        }
//
//        printerSendTimer = Timer()
//        printerSendTimer?.schedule(task, 1000, 3000)
//
//    }
//
//    private fun cancelSendWifiPrinter() {
//        printerSendTimer?.cancel()
//        printerSendTimer = null
//    }
}