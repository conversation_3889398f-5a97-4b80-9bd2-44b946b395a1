package com.metathought.food_order.casheir.ui.widget;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.Build;
import android.os.Handler;
import android.text.Layout;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AlignmentSpan;
import android.text.style.ClickableSpan;
import android.text.style.StyleSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Transformation;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;


import com.metathought.food_order.casheir.R;
import com.metathought.food_order.casheir.utils.DisplayUtils;
import com.wecloud.im.common.context.AppContextWrapper;

import java.lang.reflect.Field;

import timber.log.Timber;

//不建议集成MediumScaleTextView 普通字体的时候没问题，大字体会有高度计算差错问题
public class ExpandTextView extends AppCompatTextView {
    private static final String ELLIPSIS_STRING = new String(new char[]{'\u2026'});
    private static final int DEFAULT_MAX_LINE = 1;
    private static final String DEFAULT_OPEN_SUFFIX ="review";// AppContextWrapper.Companion.getApplicationContext().getString(R.string.review);
    private static final String DEFAULT_CLOSE_SUFFIX ="contract";// AppContextWrapper.Companion.getApplicationContext().getString(R.string.contract);
    volatile boolean animating = false;
    private boolean isClosed = false;
    private int mMaxLines = DEFAULT_MAX_LINE;
    /**
     * TextView可展示宽度，包含paddingLeft和paddingRight
     */
    private int initWidth = 0;
    /**
     * 原始文本
     */
    private CharSequence originalText;

    private String needFirst = "";
    private String clientId = "0";
//    private OnExpandListener onExpandViewListener;

    private SpannableStringBuilder mOpenSpannableStr, mCloseSpannableStr;

    private boolean hasAnimation = false;
    private ExpandCollapseAnimation mOpenAnim, mCloseAnim;
    private int mItemHeight;
    private int mOpenHeight, mCLoseHeight;
    private int mCLoseWidth;
    private boolean mExpandable;
    private boolean mCloseInNewLine;
    @Nullable
    private SpannableString mOpenSuffixSpan, mCloseSuffixSpan;
    //    @Nullable
//    private SpannableString mFirstClickSpan;
    private String mOpenSuffixStr =getContext().getString(R.string.review);// DEFAULT_OPEN_SUFFIX;
    private String mCloseSuffixStr =getContext().getString(R.string.contract);// DEFAULT_CLOSE_SUFFIX;
    private int mOpenSuffixColor, mCloseSuffixColor;

    private boolean mClickHandled = false;
    //前面文字监听
    private SpannableString mOpenSpan, mCloseSpan;

    private OnClickListener mOnClickListener;

    //是否禁用点击
    private Boolean isClickEnable = true;

    private int screenHeight = 0;
    private CharSequenceToSpannableHandler mCharSequenceToSpannableHandler;

    public ExpandTextView(Context context) {
        this(context, null);
    }

    public ExpandTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExpandTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initialize();
    }

    /**
     * 初始化
     */
    public void initialize() {
        screenHeight = DisplayUtils.getScreenHeight(getContext());
        mOpenSuffixColor = mCloseSuffixColor = Color.parseColor("#b22c2c2c");
//        setMovementMethod(OverLinkMovementMethod.getInstance());
        setIncludeFontPadding(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            setFallbackLineSpacing(false);
        }
        updateOpenSuffixSpan();
        updateCloseSuffixSpan();
    }

    @Override
    public boolean hasOverlappingRendering() {
        return false;
    }

    public void setNeedFirstText(String needFirst, String toClientId) {
        this.needFirst = needFirst;
        this.clientId = toClientId;
    }

    public void setClickEnable(Boolean isClickEnable) {
        this.isClickEnable = isClickEnable;
    }


//    public void setExpandViewListener(OnExpandListener onExpandViewListener) {
//        this.onExpandViewListener = onExpandViewListener;
//    }

    public CharSequence getOriginalText() {
        return originalText;
    }

    public void setOriginalText(CharSequence originalText) {
        if (originalText == null || originalText.length() == 0) {
            setNeedFirstText("", "0");
//            return;
        }
        this.originalText = originalText;
        mExpandable = false;
        mCloseSpannableStr = new SpannableStringBuilder();
        final int maxLines = mMaxLines;
        SpannableStringBuilder tempText = charSequenceToSpannable(originalText);
        mOpenSpannableStr = charSequenceToSpannable(originalText);

        //添加前面文字的监听方法
        myOpenSuffixSpan(originalText);

        if (maxLines != -1) {
            Layout layout = createStaticLayout(tempText);
            mExpandable = layout.getLineCount() > maxLines;
            if (mExpandable) {
                //拼接展开内容
                if (mCloseInNewLine) {
                    mOpenSpannableStr.append("\n");
                }
                if (mCloseSuffixSpan != null) {
                    mOpenSpannableStr.append(mCloseSuffixSpan);
                }
                //计算原文截取位置
                int endPos = layout.getLineEnd(maxLines - 1);
                Timber.e("originalText.length():" + originalText.length() + "   endPos  :" + endPos);
                if (originalText.length() <= endPos) {
                    mCloseSpannableStr = charSequenceToSpannable(originalText);
                } else {
                    mCloseSpannableStr = charSequenceToSpannable(originalText.subSequence(0, endPos));
                }

                SpannableStringBuilder tempText2 = charSequenceToSpannable(mCloseSpannableStr).append(ELLIPSIS_STRING);
                if (mOpenSuffixSpan != null) {
                    tempText2.append(mOpenSuffixSpan);
                }
                Timber.e("mCloseSpannableStr  :" + tempText2);
                //循环判断，收起内容添加展开后缀后的内容
                Layout tempLayout = createStaticLayout(tempText2);
                while (tempLayout.getLineCount() > maxLines) {
                    int lastSpace = mCloseSpannableStr.length() - 1;
                    if (lastSpace == -1) {
                        break;
                    }
                    if (originalText.length() <= lastSpace) {
                        mCloseSpannableStr = charSequenceToSpannable(originalText);
                    } else {
                        mCloseSpannableStr = charSequenceToSpannable(originalText.subSequence(0, lastSpace));
                    }

                    tempText2 = charSequenceToSpannable(mCloseSpannableStr).append(ELLIPSIS_STRING);
                    Timber.e("修正 mCloseSpannableStr :" + tempText2);
                    if (mOpenSuffixSpan != null) {
                        tempText2.append(mOpenSuffixSpan);
                    }
                    tempLayout = createStaticLayout(tempText2);
                }
                int lastSpace = mCloseSpannableStr.length() - mOpenSuffixSpan.length();
                Timber.e("lastSpace :" + lastSpace);
//                if (lastSpace >= 0 && originalText.length() > lastSpace) {
//                    CharSequence redundantChar = originalText.subSequence(lastSpace, lastSpace + mOpenSuffixSpan.length());
//                    int offset = hasEnCharCount(redundantChar) - hasEnCharCount(mOpenSuffixSpan) + 1;
//                    lastSpace = offset <= 0 ? lastSpace : lastSpace - offset;
//                    mCloseSpannableStr = charSequenceToSpannable(originalText.subSequence(0, lastSpace));
//                    Timber.e("修正22 mCloseSpannableStr :" + mCloseSpannableStr);
//                }

                //计算收起的文本高度
                mCLoseWidth = tempLayout.getWidth() + getPaddingLeft() + getPaddingRight();
                mCLoseHeight = tempLayout.getHeight() + getPaddingTop() + getPaddingBottom();

                mCloseSpannableStr.append(ELLIPSIS_STRING);

                //添加前面文字的监听方法
                myCloseSuffixSpan(mCloseSpannableStr.toString());

                if (mOpenSuffixSpan != null) {
                    mCloseSpannableStr.append(mOpenSuffixSpan);
                }
                //需要从新更新下最大行数，原文最大行刚好是换行的话计算后会少一行。会导致展开时行数计算错误
                tempLayout = createStaticLayout(mCloseSpannableStr);
                mMaxLines = tempLayout.getLineCount();
            }
        }
        isClosed = mExpandable;
        if (mExpandable) {
            setCloseFirstColor();
            setText(mCloseSpannableStr);
        } else {
            getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            setOpenFirstColor();
            Log.e("ChannelChildCommonAdapter", "mOpenSpannableStr::" + mOpenSpannableStr);
            setText(mOpenSpannableStr);
        }
    }


    //维持原来状态
    public void originalState(boolean isClosed) {
        Log.e("originalState", "" + isClosed);
        this.isClosed = isClosed;
        if (isClosed) {
            setText(mCloseSpannableStr);
            Layout tempLayout = createStaticLayout(mCloseSpannableStr);
            Log.e("`mCloseSpannableStr", "   " + tempLayout.getHeight());
            int lines = getLineCount();  //set后才能获取行数， layout 这个行数 在scale 为 2.625上计算正确，在别的机型上计算有问题  差的有点多
            mCLoseHeight = tempLayout.getHeight();
            getLayoutParams().height = mCLoseHeight;
        } else {
            Layout tempLayout = createStaticLayout(mOpenSpannableStr);
            int lines = getLineCount();
            mOpenHeight = tempLayout.getHeight();
            getLayoutParams().height = mOpenHeight;
            Log.e("展开后高度", "height" + getLayoutParams().height);
            ExpandTextView.super.setMaxLines(Integer.MAX_VALUE);
            setOpenFirstColor();
            requestLayout();
            setText(mOpenSpannableStr);
        }

        //这边高度重新计算一下
//        getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
//        requestLayout();
    }


//
//    @Override
//    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
//        super.onSizeChanged(w, h, oldw, oldh);
//        initWidth = w;
//        Timber.d("onSizeChanged(" + w + "," + h + "," + oldw + "," + oldh + ")  initWidth:"+initWidth);
//        //宽度没变不做处理
//        if (oldw == 0 || w==oldw)
//            return;
//        relayout(originalText);
//    }
//
//
//    private void relayout(CharSequence originalText) {
//        if (originalText == null || originalText.length() == 0) {
//            setNeedFirstText("", "0");
//        }
//        mCloseSpannableStr = new SpannableStringBuilder();
//        final int maxLines = mMaxLines;
//        SpannableStringBuilder tempText = charSequenceToSpannable(originalText);
//        mOpenSpannableStr = charSequenceToSpannable(originalText);
//
//        if (maxLines != -1) {
//            Layout layout = createStaticLayout(tempText);
//            mExpandable = layout.getLineCount() > maxLines;
//            if (mExpandable) {
//                //拼接展开内容
//                if (mCloseInNewLine) {
//                    mOpenSpannableStr.append("\n");
//                }
//                if (mCloseSuffixSpan != null) {
//                    mOpenSpannableStr.append(mCloseSuffixSpan);
//                }
//                //计算原文截取位置
//                int endPos = layout.getLineEnd(maxLines - 1);
//                if (originalText.length() <= endPos) {
//                    mCloseSpannableStr = charSequenceToSpannable(originalText);
//                } else {
//                    mCloseSpannableStr = charSequenceToSpannable(originalText.subSequence(0, endPos));
//                }
//                SpannableStringBuilder tempText2 = charSequenceToSpannable(mCloseSpannableStr).append(ELLIPSIS_STRING);
//                if (mOpenSuffixSpan != null) {
//                    tempText2.append(mOpenSuffixSpan);
//                }
//                //循环判断，收起内容添加展开后缀后的内容
//                Layout tempLayout = createStaticLayout(tempText2);
//                while (tempLayout.getLineCount() > maxLines) {
//                    int lastSpace = mCloseSpannableStr.length() - 1;
//                    if (lastSpace == -1) {
//                        break;
//                    }
//                    if (originalText.length() <= lastSpace) {
//                        mCloseSpannableStr = charSequenceToSpannable(originalText);
//                    } else {
//                        mCloseSpannableStr = charSequenceToSpannable(originalText.subSequence(0, lastSpace));
//                    }
//                    tempText2 = charSequenceToSpannable(mCloseSpannableStr).append(ELLIPSIS_STRING);
//                    if (mOpenSuffixSpan != null) {
//                        tempText2.append(mOpenSuffixSpan);
//                    }
//                    tempLayout = createStaticLayout(tempText2);
//                }
//                int lastSpace = mCloseSpannableStr.length() - mOpenSuffixSpan.length();
//                if (lastSpace >= 0 && originalText.length() > lastSpace) {
//                    CharSequence redundantChar = originalText.subSequence(lastSpace, lastSpace + mOpenSuffixSpan.length());
//                    int offset = hasEnCharCount(redundantChar) - hasEnCharCount(mOpenSuffixSpan) + 1;
//                    lastSpace = offset <= 0 ? lastSpace : lastSpace - offset;
//                    mCloseSpannableStr = charSequenceToSpannable(originalText.subSequence(0, lastSpace));
//                }
//
//                //计算收起的文本高度
//                mCLoseWidth = tempLayout.getWidth() + getPaddingLeft() + getPaddingRight();
//                mCLoseHeight = tempLayout.getHeight() + getPaddingTop() + getPaddingBottom();
//
//                mCloseSpannableStr.append(ELLIPSIS_STRING);
//
//                //添加前面文字的监听方法
//                myCloseSuffixSpan(mCloseSpannableStr.toString());
//
//                if (mOpenSuffixSpan != null) {
//                    mCloseSpannableStr.append(mOpenSuffixSpan);
//                }
//                //需要从新更新下最大行数，原文最大行刚好是换行的话计算后会少一行。会导致展开时行数计算错误
//                tempLayout = createStaticLayout(mCloseSpannableStr);
//                mMaxLines = tempLayout.getLineCount();
//            }
//        }
////        isClosed = mExpandable;
//        if (isClosed) {
//            setCloseFirstColor();
//            setText(mCloseSpannableStr);
//        } else {
//            getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
//            setOpenFirstColor();
//            Log.e("ChannelChildCommonAdapter", "mOpenSpannableStr::" + mOpenSpannableStr);
//            setText(mOpenSpannableStr);
//        }
//    }

    private int hasEnCharCount(CharSequence str) {
        int count = 0;
        if (!TextUtils.isEmpty(str)) {
            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                if (c >= ' ' && c <= '~') {
                    count++;
                }
            }
        }
        return count;
    }

    private void switchOpenClose() {
        if (!isClickEnable) {
            if (mOpenCloseCallback != null) {
                mOpenCloseCallback.clickWhenDisEnable();
            }
            return;
        }
        if (mExpandable) {
            isClosed = !isClosed;
            if (isClosed) {
                close();
            } else {
                open();
            }
        }
    }

    //文字测量工具
    private Paint.FontMetricsInt textFm;

    /**
     * 设置是否有动画
     *
     * @param hasAnimation
     */
    public void setHasAnimation(boolean hasAnimation) {
        this.hasAnimation = hasAnimation;
    }

    /**
     * 展开
     */
    public void open() {

        if (hasAnimation) {
            //计算展开的文本高度
            setText(mOpenSpannableStr);
            Layout tempLayout = createStaticLayout(mOpenSpannableStr);
            Log.e("`tempLayout", "   " + tempLayout.getHeight());
            int lines = getLineCount();  //set后才能获取行数， layout 这个行数 在scale 为 2.625上计算正确，在别的机型上计算有问题  差的有点多
            Timber.d("lineCount：" + lines);
            mOpenHeight = tempLayout.getHeight();

//            int closeH = getHeight();//关闭时候显示的view高度
//            if (mItemHeight == 0) {
//                mItemHeight = closeH / mMaxLines;//计算出每个item的高度
//                mCLoseHeight = closeH;
//            }

//            float strokeW = getStrokeWidth();
//            if (strokeW > 0.1f) {
//                float strokeItemPadding = strokeW / 0.1f * 0.4f;//根据字体宽度计算 对应行间距差 0.1f 正常字体， 字体宽高容错
//                height = height + (int) strokeItemPadding * lines; //3*lines
//            }

//            mOpenHeight = height + getPaddingTop() + getPaddingBottom();//DisplayUtils.px2dp(getContext(),height); //+ getPaddingTop() + getPaddingBottom() + padding;
//            Timber.d("open() mOpenHeight: " + mOpenHeight + "   getPaddingTop():" + getPaddingTop() + "  getPaddingBottom():" + getPaddingBottom() );
            executeOpenAnim();
        } else {
            getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            ExpandTextView.super.setMaxLines(Integer.MAX_VALUE);
            setOpenFirstColor();
            requestLayout();
            setText(mOpenSpannableStr);
            if (mOpenCloseCallback != null) {
                mOpenCloseCallback.onOpen();
            }
            postDelayed(() -> Log.e(TAG, "" + getHeight()), 1000);

        }
    }

    private void setOpenFirstColor() {
        if (needFirst.length() > 0 && mOpenSpannableStr.length() > needFirst.length()) {

            mOpenSpannableStr.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    mClickHandled = true; // 标记为已处理
//                    if (onExpandViewListener != null) {
//                        onExpandViewListener.onReplyMemberClick(clientId);
//                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    //设置文件颜色
//                    ds.setColor(ContextCompat.getColor(getContext(), R.color.color_067eff));
//                // 去掉下划线
                    ds.setUnderlineText(false);
                }
            }, 0, needFirst.length(), 0);

        }
    }

    private void setCloseFirstColor() {
        if (needFirst.length() > 0 && mCloseSpannableStr.length() > needFirst.length()) {
            mCloseSpannableStr.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    mClickHandled = true; // 标记为已处理
//                    if (onExpandViewListener != null) {
//                        onExpandViewListener.onReplyMemberClick(clientId);
//                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    //设置文件颜色
                    //  ds.setColor(ContextCompat.getColor(getContext(), R.color.color_067eff));
//                // 去掉下划线
                    ds.setUnderlineText(false);
                }
            }, 0, needFirst.length(), 0);

        }
    }

    /**
     * 收起
     */
    private void close() {
        if (mOpenCloseCallback != null) {
            mOpenCloseCallback.closeAnimationEnd(mCLoseHeight);
        }
        if (hasAnimation) {
            executeCloseAnim();
        } else {

            new Handler().postDelayed(() -> {

                ExpandTextView.super.setMaxLines(mMaxLines);
                setCloseFirstColor();
                setText(mCloseSpannableStr);
                if (mOpenCloseCallback != null) {
                    mOpenCloseCallback.onClose();
                }
            }, 300);
        }
    }

    /**
     * 执行展开动画
     */
    private void executeOpenAnim() {
        Log.e("executeOpenAnim", "executeOpenAnim");
        //创建展开动画
        if (mOpenAnim == null) {
            mOpenAnim = new ExpandCollapseAnimation(this, mCLoseHeight, mOpenHeight);
            mOpenAnim.setFillAfter(true);

            mOpenAnim.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                    ExpandTextView.super.setMaxLines(Integer.MAX_VALUE);
                    setOpenFirstColor();
//                    setText(mOpenSpannableStr);//点击那边设置了，这边就不需要设置了
                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    //  动画结束后textview设置展开的状态
                    Timber.d("getHeight():" + getHeight() + "mOpenHeight:" + mOpenHeight);
                    getLayoutParams().height = mOpenHeight;
                    Timber.d("ViewHeight：" + getHeight());
                    requestLayout();
                    mOpenAnim.setAnimationEnded(true);
                    animating = false;
                    mOpenAnim = null;
                    if (mOpenCloseCallback != null) {
                        mOpenCloseCallback.onOpen();
                    }
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        }

        if (animating) {
            return;
        }
        animating = true;
        clearAnimation();
        //  执行动画
        startAnimation(mOpenAnim);
    }

    /**
     * 执行收起动画
     */
    private void executeCloseAnim() {
        //创建收起动画

        if (mCloseAnim == null) {
            Log.e(TAG, "mOpenHeight: " + mOpenHeight + "   mCLoseHeight: " + mCLoseHeight);
            mCloseAnim = new ExpandCollapseAnimation(this, mOpenHeight, mCLoseHeight);
            mCloseAnim.setFillAfter(true);
            mCloseAnim.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
//                    Log.e(TAG, "收起动画结束");
//                    ExpandTextView.super.setMaxLines(mMaxLines);
//                    setCloseFirstColor();
//                    setText(mCloseSpannableStr);
//                    getLayoutParams().height = mCLoseHeight;
//                    requestLayout();
//                    mCloseAnim = null;
//                    animating = false;
//                    Timber.d("mCLoseHeight:" + mCLoseHeight);
                    if (mOpenCloseCallback != null) {
                        mOpenCloseCallback.onClose();
                    }
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        }

        if (animating) {
            return;
        }
        animating = true;
        clearAnimation();
        //  执行动画
        startAnimation(mCloseAnim);
        //这样写是为了防止文本过长，导致onAnimationEnd 可能没有触发
        new Handler().postDelayed(() -> {
            Log.e(TAG, "收起动画结束");
            clearAnimation();
            mCloseAnim.cancel();
            ExpandTextView.super.setMaxLines(mMaxLines);
            setCloseFirstColor();
            setText(mCloseSpannableStr);
            getLayoutParams().height = mCLoseHeight;
            requestLayout();
            mCloseAnim = null;
            animating = false;
            // mOpenCloseCallback.closeAnimationEnd(mCLoseHeight);
            Timber.e("mCLoseHeight:" + mCLoseHeight);
        }, mCloseAnim.getDuration());
    }

    /**
     * @param spannable
     * @return
     */
    private Layout createStaticLayout(SpannableStringBuilder spannable) {
        int contentWidth = initWidth - getPaddingLeft() - getPaddingRight();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {

            StaticLayout.Builder builder = StaticLayout.Builder.obtain(spannable, 0, spannable.length(), getPaint(), contentWidth);
            builder.setAlignment(Layout.Alignment.ALIGN_NORMAL);
            builder.setIncludePad(getIncludeFontPadding());
            builder.setLineSpacing(getLineSpacingExtra(), getLineSpacingMultiplier());
            builder.setBreakStrategy(getBreakStrategy())
                    .setHyphenationFrequency(getHyphenationFrequency());

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder.setJustificationMode(getJustificationMode());
            }

            if (getEllipsize() != null && getKeyListener() == null) {
                builder.setEllipsize(getEllipsize())
                        .setEllipsizedWidth(contentWidth);

            }
            return builder.build();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            return new StaticLayout(spannable, getPaint(), contentWidth, Layout.Alignment.ALIGN_NORMAL,
                    getLineSpacingMultiplier(), getLineSpacingExtra(), getIncludeFontPadding());
        } else {
            return new StaticLayout(spannable, getPaint(), contentWidth, Layout.Alignment.ALIGN_NORMAL,
                    getFloatField("mSpacingMult", 1f), getFloatField("mSpacingAdd", 0f), getIncludeFontPadding());
        }
    }

    private float getFloatField(String fieldName, float defaultValue) {
        float value = defaultValue;
        if (TextUtils.isEmpty(fieldName)) {
            return value;
        }
        try {
            // 获取该类的所有属性值域
            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (TextUtils.equals(fieldName, field.getName())) {
                    value = field.getFloat(this);
                    break;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return value;
    }


    /**
     * @param charSequence
     * @return
     */
    private SpannableStringBuilder charSequenceToSpannable(@NonNull CharSequence charSequence) {
        SpannableStringBuilder spannableStringBuilder = null;
        if (mCharSequenceToSpannableHandler != null) {
            spannableStringBuilder = mCharSequenceToSpannableHandler.charSequenceToSpannable(charSequence);
        }
        if (spannableStringBuilder == null) {
            spannableStringBuilder = new SpannableStringBuilder(charSequence);
        }

        return spannableStringBuilder;
    }

    public void setStrokeWidth(float stroke) {

    }

    /**
     * 初始化TextView的可展示宽度
     *
     * @param width
     */
    public void initWidth(int width) {
        initWidth = width;
    }

    @Override
    public void setMaxLines(int maxLines) {
        this.mMaxLines = maxLines;
        super.setMaxLines(maxLines);
    }

    /**
     * 设置展开后缀text
     *
     * @param openSuffix
     */
    public void setOpenSuffix(String openSuffix) {
        mOpenSuffixStr = openSuffix;
        updateOpenSuffixSpan();
    }

    /**
     * 设置展开后缀文本颜色
     *
     * @param openSuffixColor
     */
    public void setOpenSuffixColor(@ColorInt int openSuffixColor) {
        mOpenSuffixColor = openSuffixColor;

        updateOpenSuffixSpan();
    }

    /**
     * 设置收起后缀text
     *
     * @param closeSuffix
     */
    public void setCloseSuffix(String closeSuffix) {
        mCloseSuffixStr = closeSuffix;
        updateCloseSuffixSpan();
    }

    /**
     * 设置收起后缀文本颜色
     *
     * @param closeSuffixColor
     */
    public void setCloseSuffixColor(@ColorInt int closeSuffixColor) {
        mCloseSuffixColor = closeSuffixColor;
        updateCloseSuffixSpan();
    }

    /**
     * 收起后缀是否另起一行
     *
     * @param closeInNewLine
     */
    public void setCloseInNewLine(boolean closeInNewLine) {
        mCloseInNewLine = closeInNewLine;
        updateCloseSuffixSpan();
    }

    /**
     * 更新展开后缀Spannable
     */
    private void updateOpenSuffixSpan() {
        if (TextUtils.isEmpty(mOpenSuffixStr)) {
            mOpenSuffixSpan = null;
            return;
        }
        mOpenSuffixSpan = new SpannableString(mOpenSuffixStr);
        mOpenSuffixSpan.setSpan(new StyleSpan(android.graphics.Typeface.NORMAL), 0, mOpenSuffixStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mOpenSuffixSpan.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                mClickHandled = true; // 标记为已处理
                switchOpenClose();
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(mOpenSuffixColor);
                ds.setUnderlineText(false);
            }
        }, 0, mOpenSuffixStr.length(), Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
    }

    /**
     * 更新收起后缀Spannable
     */
    private void updateCloseSuffixSpan() {
        if (TextUtils.isEmpty(mCloseSuffixStr)) {
            mCloseSuffixSpan = null;
            return;
        }
        mCloseSuffixSpan = new SpannableString(mCloseSuffixStr);
        mCloseSuffixSpan.setSpan(new StyleSpan(android.graphics.Typeface.NORMAL), 0, mCloseSuffixStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        if (mCloseInNewLine) {
            AlignmentSpan alignmentSpan = new AlignmentSpan.Standard(Layout.Alignment.ALIGN_OPPOSITE);
            mCloseSuffixSpan.setSpan(alignmentSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        mCloseSuffixSpan.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                mClickHandled = true; // 标记为已处理
                switchOpenClose();
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(mCloseSuffixColor);
                ds.setUnderlineText(false);
            }
        }, 1, mCloseSuffixStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
    }

    /**
     * 更新展开后 前面文字的监听
     */
    private void myOpenSuffixSpan(CharSequence originalText) {
        if (needFirst.length() > 0 && mOpenSpannableStr.length() > needFirst.length()) {
            setOpenFirstColor();
        } else {
//            mOpenSpannableStr.setSpan(new ClickableSpan() {
//                @Override
//                public void onClick(@NonNull View widget) {
//                    if (mOnClickListener != null) {
//                        mOnClickListener.onClick(widget);
//                    }
//                }
//
//                @Override
//                public void updateDrawState(@NonNull TextPaint ds) {
//                    super.updateDrawState(ds);
//                    //设置文件颜色
//                    ds.setColor(ContextCompat.getColor(getContext(), R.color.color_333333));
////                // 去掉下划线
//                    ds.setUnderlineText(false);
//                }
//            }, 0, originalText.length(), 0);
        }
    }

    /**
     * 更新收起后 前面文字的监听
     */
    private void myCloseSuffixSpan(CharSequence originalText) {
        if (needFirst.length() > 0 && mCloseSpannableStr.length() > needFirst.length()) {
            setCloseFirstColor();
        } else {
//            mCloseSpannableStr.setSpan(new ClickableSpan() {
//                @Override
//                public void onClick(@NonNull View widget) {
//                    if (mOnClickListener != null) {
//                        mOnClickListener.onClick(widget);
//                    }
//                }
//
//                @Override
//                public void updateDrawState(@NonNull TextPaint ds) {
//                    super.updateDrawState(ds);
//                    //设置文件颜色
//                    ds.setColor(ContextCompat.getColor(getContext(), R.color.color_333333));
////                // 去掉下划线
//                    ds.setUnderlineText(false);
//                }
//            }, 0, originalText.length(), 0);
        }
    }

//    @Override
//    public void setOnClickListener(OnClickListener onClickListener) {
//        mOnClickListener = onClickListener;
//    }

    private static final String TAG = "ExpandTextView";

    public void setClickHandled(boolean mClickHandled) {
        this.mClickHandled = mClickHandled;
    }

    @Override
    public void setOnClickListener(@Nullable OnClickListener l) {
        //包装点击事件，防止SpanClick和ClickListener冲突
        super.setOnClickListener(v -> {
            if (mClickHandled) { // 若已处理则直接返回
                mClickHandled = false;
                return;
            }
            Log.e(TAG, "setOnClickListener: ");
            l.onClick(v);
        });
    }

    public OpenAndCloseCallback mOpenCloseCallback;

    public void setOpenAndCloseCallback(OpenAndCloseCallback callback) {
        this.mOpenCloseCallback = callback;
    }

    public interface OpenAndCloseCallback {
        void onOpen();

        void onClose();

        void closeAnimationEnd(int mCLoseHeight);

        void clickWhenDisEnable();
    }

    /**
     * 设置文本内容处理
     *
     * @param handler
     */
    public void setCharSequenceToSpannableHandler(CharSequenceToSpannableHandler handler) {
        mCharSequenceToSpannableHandler = handler;
    }

    public interface CharSequenceToSpannableHandler {
        @NonNull
        SpannableStringBuilder charSequenceToSpannable(CharSequence charSequence);
    }

    class ExpandCollapseAnimation extends Animation {

        private boolean mAnimationEnded = true;

        private final View mTargetView;//动画执行view
        private final int mStartHeight;//动画执行的开始高度
        private int mEndHeight;//动画结束后的高度

        ExpandCollapseAnimation(View target, int startHeight, int endHeight) {
            mTargetView = target;
            mStartHeight = startHeight;
            mEndHeight = endHeight;
            setDuration(400);
        }

        @Override
        protected void applyTransformation(float interpolatedTime, Transformation t) {
            if (!mAnimationEnded) {
                mTargetView.setScrollY(0);
                //计算出每次应该显示的高度,改变执行view的高度，实现动画
                mTargetView.getLayoutParams().height = (int) ((mEndHeight - mStartHeight) * interpolatedTime + mStartHeight);
                mTargetView.requestLayout();
                Timber.e("interpolatedTime:" + interpolatedTime + "  height:" + mTargetView.getLayoutParams().height + "mEndHeight:" + mEndHeight);
            }
        }

        void setAnimationEnded(boolean animationEnded) {
            this.mAnimationEnded = animationEnded;
        }

//        void setEndHeight(int endHeight) {
//            Log.e("setEndHeight", "设置结束高度");
//            this.mEndHeight = endHeight;
//        }

    }

//    @Override
//    protected void onDetachedFromWindow() {
//        Timber.d("onDetachedFromWindow:" + animating);
//        if (!animating) {
//            super.onDetachedFromWindow();
//            return;
//        }
//        if (mCloseAnim != null) {
//            ExpandTextView.super.setMaxLines(mMaxLines);
//            setCloseFirstColor();
//            setText(mCloseSpannableStr);
//            getLayoutParams().height = mCLoseHeight;
//            requestLayout();
//            mCloseAnim = null;
//            animating = false;
//            Timber.d("mCLoseHeight:" + mCLoseHeight);
//        }
//    }
}


