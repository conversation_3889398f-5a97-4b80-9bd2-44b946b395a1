package com.metathought.food_order.casheir.ui.app_dashbord

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.alibaba.fastjson.JSON
import com.google.gson.Gson
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.constant.KitchenCheckTicketType
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.PrintTicketType
import com.metathought.food_order.casheir.constant.WholeDiscountType
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.FeatureMenu
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PreSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.UnReadAndPrintResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.version.VersionCheckResponse
import com.metathought.food_order.casheir.database.WsEventRecord
import com.metathought.food_order.casheir.database.dao.WsEventHelper
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PaymentMethodHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.helper.VersionHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.websocket.ApiWebSocket
import com.metathought.food_order.casheir.network.websocket.WebSocketApi
import com.metathought.food_order.casheir.network.websocket.socket_model.Ack
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketBody
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketRequest
import com.metathought.food_order.casheir.ui.widget.printer.LabelPrinter
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.sunmi.printerx.PrinterSdk
import com.tinder.scarlet.Lifecycle
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.tinder.scarlet.lifecycle.LifecycleRegistry
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Locale
import java.util.concurrent.ExecutionException
import javax.inject.Inject


var selectPrinter: PrinterSdk.Printer? = null

@HiltViewModel
class MainDashboardViewModel @Inject

constructor(private val repository: Repository) : ViewModel() {
    private val _userLoginResponse = MutableLiveData<UserLoginResponse>()
    private val _listMenu = MutableLiveData<ArrayList<FeatureMenu>>()
    private val _versionResponse = MutableLiveData<UIVersionCheckModel>()
    private val _unReadAndPrintResponse = MutableLiveData<ApiResponse<UnReadAndPrintResponse>>()
    private val _unAcceptOrderReadAndPrintResponse =
        MutableLiveData<ApiResponse<UnReadAndPrintResponse>>()
    private val _noticeResponse = MutableLiveData<NoticeResponse>()
    private val _noticeUnReadResponse = MutableLiveData<Int>()
    val listview get() = _listMenu
    val userLoginResponse get() = _userLoginResponse
    val versionResponse get() = _versionResponse
    val unReadAndPrintResponse get() = _unReadAndPrintResponse
    val unAcceptOrderReadAndPrintResponse get() = _unAcceptOrderReadAndPrintResponse
    val noticeResponse get() = _noticeResponse
    val noticeUnReadResponse get() = _noticeUnReadResponse

    /**
     * Initailize list menu
     *
     * @param context
     * @param isTableService
     * @param isInit  是否初始化
     */
    fun initailizeListMenu(context: Context, isTableService: Boolean?, isInit: Boolean? = true) {
        var selectId: Int? = null
        if (isInit == false) {
            if (_listMenu.value?.isNotEmpty() == true) {
                selectId = _listMenu.value?.find { it.isSelected == true }?.id
            }
        }
        _listMenu.value?.clear()
        val arrayListMenu = arrayListOf<FeatureMenu>()
        if (isTableService == true || (isTableService == false && userLoginResponse.value?.isDisplayTable == true)) {
            arrayListMenu.add(
                FeatureMenu(
                    FeatureMenuEnum.TABLE.id,
                    R.drawable.ic_table,
                    context.getString(R.string.table),
                    false
                )
            )
        }

        arrayListMenu.add(
            FeatureMenu(
                FeatureMenuEnum.ORDER.id,
                R.drawable.ic_order,
                context.getString(R.string.order),
                false
            )
        )

        if (userLoginResponse.value?.autoAcceptOrders == false) {
            //不自动接单 显示接单模块
            arrayListMenu.add(
                FeatureMenu(
                    FeatureMenuEnum.RECEIVING_ORDER.id,
                    R.drawable.icon_receiveing_order,
                    context.getString(R.string.receiving_orders)
                )
            )
        }

        arrayListMenu.add(
            FeatureMenu(
                FeatureMenuEnum.ORDER_MANAGEMENT.id,
                R.drawable.ic_order_management,
                context.getString(R.string.order_management)
            )
        )

//        arrayListMenu.add(
//            FeatureMenu(
//                FeatureMenuEnum.PENDING_ORDER.id,
//                R.drawable.ic_pending_order,
//                context.getString(R.string.pending_orders)
//            )
//        )


        if (userLoginResponse.value?.getPermissionList()
                ?.contains(PermissionEnum.VIEW_DATA.type) == true
        ) {
            arrayListMenu.add(
                FeatureMenu(
                    FeatureMenuEnum.MEMBER_MANAGEMENT.id,
                    R.drawable.ic_member,
                    context.getString(R.string.customer)
                )
            )

            arrayListMenu.add(
                FeatureMenu(
                    FeatureMenuEnum.STORE_DASHBOARD.id,
                    R.drawable.ic_dashboard,
                    context.getString(R.string.dashboard)
                )
            )
        }

//        arrayListMenu.add(
//            FeatureMenu(
//                FeatureMenuEnum.PRINTER.id,
//                R.drawable.ic_printer,
//                context.getString(R.string.printer)
//            )
//        )
        arrayListMenu.add(
            FeatureMenu(
                FeatureMenuEnum.NOTICE.id,
                R.drawable.ic_notice,
                context.getString(R.string.announcement)
            )
        )

        if (userLoginResponse.value?.getPermissionList()
                ?.contains(PermissionEnum.OFFLINE_PAY.type) == true
        ) {
            //有线下付款权限能打开钱箱
            arrayListMenu.add(
                FeatureMenu(
                    FeatureMenuEnum.OPEN_CASH_BOX.id,
                    R.drawable.icon_cash_box,
                    context.getString(R.string.open_cashbox)
                )
            )
        }
        viewModelScope.launch {
            val tabId = PreferenceHelper.getCurrentTabId()
            if (tabId != -1) {
                selectId = tabId
            }
            PreferenceHelper.setCurrentTabId(-1)
        }



        if (arrayListMenu.isNotEmpty()) {
            if (selectId != null) {
                arrayListMenu.forEach {
                    if (it.id == selectId) {
                        it.isSelected = true
                    }
                }
            } else {
                arrayListMenu[0].isSelected = true
            }
        }


        listview.value = arrayListMenu
    }

    fun initPrinter(context: Context) {
        try {
            if (selectPrinter == null) {
                PrinterSdk.getInstance().getPrinter(context, object : PrinterSdk.PrinterListen {
                    override fun onDefPrinter(printer: PrinterSdk.Printer?) {
                        Timber.e("初始化闪米打印sdk")
                        if (selectPrinter == null) {
                            selectPrinter = printer
                        }
                    }

                    override fun onPrinters(p0: MutableList<PrinterSdk.Printer>?) {
                        Timber.e("p0 :${p0}")
                    }
                })
            }
        } catch (e: Exception) {
//            e.printStackTrace()
        }
    }

    fun checkVersion(context: Context, isManualTriggering: Boolean? = false) {
        viewModelScope.launch {
            try {
                val versionName = VersionHelper.getLocalVersionName(context)
                val response = repository.checkVersion(versionName, 1)
                if (response is ApiResponse.Success) {
                    _versionResponse.postValue(UIVersionCheckModel(isManualTriggering, response))

                }
            } catch (e: PackageManager.NameNotFoundException) {

            }
        }
    }

    fun clearVersionModel() {
        _versionResponse.postValue(UIVersionCheckModel(false, null))
    }

    fun initConfiguration(context: Context) {
        //获取支付方式
//        getPaymentList()

        //获取USB打印配置
        getPrinterInfoList()

        //
        updateUnRead()

        updateAcceptOrderUnRead()



        getStoreInfo(context)

        requestNotice()
    }

    fun requestNotice() {
        updateNoticeUnRead()
        getLastNotice()
    }


    fun getStoreInfo(context: Context) {
        viewModelScope.launch {
            try {
                val response = repository.getStoreInfo()
                if (response is ApiResponse.Success) {
                    MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance =
                        response.data.isPaymentInAdvance
                    MainDashboardFragment.CURRENT_USER?.serviceChargePercentage =
                        response.data.serviceChargePercentage

                    MainDashboardFragment.CURRENT_USER?.vatPercentage = response.data.vatPercentage

                    MainDashboardFragment.CURRENT_USER?.isDisplayTable =
                        response.data.isDisplayTable

                    MainDashboardFragment.CURRENT_USER?.autoAcceptOrders =
                        response.data.autoAcceptOrders

                    MainDashboardFragment.CURRENT_USER?.cashierShowPic =
                        response.data.cashierShowPic

                    MainDashboardFragment.STORE_INFO = response.data
                    val locale = Locale.getDefault()
                    if (locale == MyApplication.LOCALE_KHMER) {
                        MainDashboardFragment.CURRENT_USER?.storeNameKH = response.data.name
                    } else if (locale == Locale.CHINESE) {
                        MainDashboardFragment.CURRENT_USER?.storeNameZH = response.data.name
                    } else if (locale == Locale.ENGLISH) {
                        MainDashboardFragment.CURRENT_USER?.storeNameEN = response.data.name
                    }

                    PreferenceHelper.setStoreInfo(response.data)

                    PreferenceDataStoreHelper.getInstance().apply {
                        if (MainDashboardFragment.CURRENT_USER != null) {
                            this.putPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                MainDashboardFragment.CURRENT_USER!!.toJson()
                            )
                        }
                    }
                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_STORE, null))
                    initailizeListMenu(context, userLoginResponse.value?.isTableService, false)
                }
            } catch (e: Exception) {

            }
        }
    }

    fun getPrinterInfoList() {
        viewModelScope.launch {
            try {
                PrinterDeviceHelper.getPrinterInfoListFromNet(repository)
            } catch (e: Exception) {

            }
        }
    }

//    private fun getPaymentList() {
//        viewModelScope.launch {
//            try {
//                PaymentMethodHelper.getPaymentMethod(repository) {
//
//                }
//
//            } catch (e: Exception) {
//
//            }
//        }
//    }

    fun updateUnRead() {
        viewModelScope.launch {
            try {
                val response = repository.getUnReadNumAndUnPrint()
                if (response is ApiResponse.Success) {
                    unReadAndPrintResponse.postValue(response)
                }
            } catch (e: Exception) {

            }
        }
    }


    fun updateAcceptOrderUnRead() {
        viewModelScope.launch {
            try {
                val response = repository.getAcceptOrderUnReadNumAndUnPrint()
                if (response is ApiResponse.Success) {
                    unAcceptOrderReadAndPrintResponse.postValue(response)
                }
            } catch (e: Exception) {

            }
        }
    }

    /**
     * 获取公告未读数
     *
     */
    fun updateNoticeUnRead() {
        viewModelScope.launch {
            try {
                val response = repository.getNoticeUnread()
                if (response is ApiResponse.Success) {
                    noticeUnReadResponse.postValue(response.data)
                }
            } catch (e: Exception) {

            }
        }
    }


    /**
     * 获取最后一条公告
     *
     */
    fun getLastNotice() {
        viewModelScope.launch {
            try {
                val response = repository.getLastNotice()
                if (response is ApiResponse.Success) {
                    noticeResponse.postValue(response.data)
                }
            } catch (e: Exception) {

            }
        }
    }


    /**
     * 其他端 下单打印
     *
     * @param context
     * @param res
     * @param isPreSettlement
     */
    fun printTickerFromWs(
        context: Context,
        res: OrderedInfoResponse,
        isPreSettlement: Boolean? = false
    ) {
        viewModelScope.launch {
            val printInfoList = PrinterDeviceHelper.getPrinterList()
            //预结算二维码  用来缓存第一次请求的二维码减少请求
            var qrcode: String? = null

            if (isPreSettlement == true) {
                //这个方法好像不会打预结算小票
                //预结小票翻译
                res.translateGoods()
                //是否有预结算模板
                var printTemplateResponseItem: PrintTamplateResponseItem? = null
                for (printerConfigInfo in printInfoList) {
                    val printTemplateList =
                        printerConfigInfo.printerTemplateList ?: listOf()
                    val tmpPrintTemplateResponseItem =
                        printTemplateList.firstOrNull {
                            it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                        }
                    if (tmpPrintTemplateResponseItem != null) {
                        printTemplateResponseItem = tmpPrintTemplateResponseItem
                    }
                }
                //如果有预结算模板
                if (printTemplateResponseItem != null) {
                    if (res.orderNo != null) {
                        if (printTemplateResponseItem.informationShow?.showKhqrCode == true && PaymentMethodHelper.isSupportOnline() && (res.payStatus == OrderedStatusEnum.UNPAID.id || res.payStatus == OrderedStatusEnum.BE_CONFIRM.id) && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == false && !res.isUniversalTable() && res.isDineIn() && !res.isFromKiosk() && res.getTotalPriceBySelf() != 0L) {
                            //只请求一次预付款二维码
//                        val response = repository.getKhqr(
//                            orderNo = res.orderNo,
//                            couponCode = res.getCurrentCoupon()?.code,
//                            reduceDollar = if (res?.reduceDollar != null) BigDecimal(
//                                res.reduceDollar!!.div(
//                                    100.0
//                                )
//                            ).halfUp(2).stripTrailingZeros() else null,
//                            reduceKhr = if (res?.reduceKhr != null) res.reduceKhr!!.toBigDecimal() else null,
//                            reduceRate = res.reduceRate,
//                            reduceType = res.reduceType,
//                        )
                            val response = repository.getKhqr(
                                PreSettlementRequest(
                                    orderNo = res.orderNo,
                                    couponCode = res.getCurrentCoupon()?.code,
                                    reduceType = res.getWholeDiscountType(),
                                    reduceRate = res.wholeDiscountReduce?.reduceRate,
                                    reduceDollar = res.wholeDiscountReduce?.reduceDollar,
                                    reduceKhr = res.wholeDiscountReduce?.reduceKhr,
                                    reduceVipDollar = res.wholeDiscountReduce?.reduceVipDollar,
                                    reduceVipKhr = res.wholeDiscountReduce?.reduceVipKhr,
                                    discountReduceActivityId = res.discountReduceActivity?.id
                                )
                            )
                            if (response is ApiResponse.Success) {
                                qrcode = response.data.qrcode
                            }
                        }
                    }
                }
            } else {
                repository.orderTranslate(res)
            }

            for (printerConfigInfo in printInfoList) {
                val printTemplateList =
                    printerConfigInfo.printerTemplateList ?: listOf()
                var isPrinter = false
                if (printTemplateList.isNotEmpty()) {
                    //获取厨打模板
                    val kitchen =
                        printTemplateList.filter { it.type == PrintTemplateTypeEnum.KITCHEN.id }
                    val labelPrintTemplate =
                        printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.LABEL.id }
                    if (isPreSettlement == true) {
                        //获取 预结算单 打印模板
                        val printTemplateResponseItem =
                            printTemplateList.firstOrNull {
                                it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                            }
                        //打预结算小票
                        if (printTemplateResponseItem != null) {
                            Printer.printTicket(
                                context,
                                printTemplateResponseItem,
                                res,
                                paymentQrCode = qrcode,
                                printerConfigInfo = printerConfigInfo,
                            )
                            isPrinter = true
//                            delayIndex += 1
                        }
                    } else {
                        val printTemplateResponseItem =
                            printTemplateList.firstOrNull { it.type == res.getPrintTypeFromDiningStyle().id }
                        //是否自动打印结账单
                        val isAutoPrintCheckTicket =
                            printTemplateResponseItem?.type in listOf(
                                PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id,
                                PrintTemplateTypeEnum.PRE_ORDER.id
                            ) && MainDashboardFragment.STORE_INFO?.autoCheckoutTicket() == true
                        val isPaymentInAdvance =
                            MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance

                        if (isPaymentInAdvance == false) {
                            Timber.e("后付款门店 订单已支付打印  ")
                            // 如果是后付款门店，已支付订单(外卖 线下支付 余额支付)

                            if (res.isOrderSuccess()) {
                                if (kitchen.isNotEmpty() && (!res.isDineIn() || res.isUniversalTable() || res.isFromKiosk())) {
                                    //非堂食
                                    kitchen.forEach {
                                        Printer.printTicket(
                                            context,
                                            it, res,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                    }
                                    isPrinter = true
                                }

                                if (labelPrintTemplate != null) {
                                    LabelPrinter.printLabelTicket(
                                        context, labelPrintTemplate, res,
                                        printerConfigInfo = printerConfigInfo,
                                    )
                                    isPrinter = true
                                }

                                Timber.e("已支付打印")
                                //如果是已支付 就打结账小票/预定小票
                                if (printTemplateResponseItem != null && isAutoPrintCheckTicket) {
                                    val printTicketType = Printer.getPrintTicketType()
                                    if (printTicketType == PrintTicketType.NORMAL.id) {
                                        Printer.printTicket(
                                            context,
                                            printTemplateResponseItem,
                                            res,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                    }

                                    isPrinter = true
                                }
                            } else {
                                if (res.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id) {
                                    //待确认单子不用打
                                    return@launch
                                }

                                if (res.diningStyle == DiningStyleEnum.DINE_IN.id) {
                                    if (kitchen.isNotEmpty()) {
                                        kitchen.forEach {
                                            Printer.printTicket(
                                                context,
                                                it, res,
                                                printerConfigInfo = printerConfigInfo,
                                            )
                                        }
                                        isPrinter = true
                                    }

                                    if (labelPrintTemplate != null) {
                                        LabelPrinter.printLabelTicket(
                                            context, labelPrintTemplate, res,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                        isPrinter = true
                                    }

                                    Timber.e("打印堂食小票")
                                    if (printTemplateResponseItem != null) {
                                        Printer.printTicket(
                                            context,
                                            printTemplateResponseItem,
                                            res,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                        isPrinter = true
                                    }

                                }
                            }
                        } else {
                            if (res.isOrderSuccess()) {
                                Timber.e("先付款门店 订单已支付打印  ")
                                if (kitchen.isNotEmpty()) {
                                    kitchen.forEach {
                                        Printer.printTicket(
                                            context,
                                            it, res,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                    }
//                                    delayIndex += 1
                                    isPrinter = true
                                }

                                if (labelPrintTemplate != null) {
                                    LabelPrinter.printLabelTicket(
                                        context, labelPrintTemplate, res,
                                        printerConfigInfo = printerConfigInfo,
                                    )
                                    isPrinter = true
                                }
                                //如果是已支付 就打结账小票
                                if (printTemplateResponseItem != null && !res.isFromKiosk() && isAutoPrintCheckTicket) {
                                    val printTicketType = Printer.getPrintTicketType()
                                    if (printTicketType == PrintTicketType.NORMAL.id) {
                                        Printer.printTicket(
                                            context,
                                            printTemplateResponseItem,
                                            res,
                                            printerConfigInfo = printerConfigInfo,
                                        )
                                    }
                                    isPrinter = true
                                }
                            } else {

                            }
                        }
                    }
                }

                if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                    val connectUSD =
                        PrinterUsbDeviceHelper.isConnectUSB(printerConfigInfo.usbDevice?.deviceName)
                    connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
                        override fun onSuccess(result: Boolean) {
                            Timber.e("connectUSD  onSuccess $result")
                            if (isPrinter && result) {
                                updateOrderPrintState(res.orderNo)
                            }
                        }

                        override fun onFailure(e: ExecutionException) {
                            Timber.e("connectUSD onFailure ${e}")
                        }
                    })
                } else if (printerConfigInfo.type == PrinterTypeEnum.WIFI.type) {
                    val printerState = PrinterDeviceHelper.getWifiConnectState(printerConfigInfo)
                    if (!printerState && isPrinter) {
                        Timber.e("wifi ${printerConfigInfo.ipAddress}   有问题跳过 ")
                        isPrinter = false
                    }
                    if (isPrinter) {
                        updateOrderPrintState(res.orderNo)
                    }
                }
            }
        }
    }

    /**
     * 打印挂账小票
     */
    fun printCreditRecordFromWs(
        context: Context,
        repaymentResponse: RepaymentResponse
    ) {
        viewModelScope.launch {
            Printer.printPrinterCreditRecordReport(
                context,
                if (repaymentResponse.repaymentDate == null) OrderedStatusEnum.CREDIT_UNPAID else OrderedStatusEnum.CREDIT_PAID,
                repaymentResponse,
            )
        }
    }

    /**
     * 其他端手动打印
     *
     * @param context
     * @param res
     * @param isKitchen
     * @param isCashierCheck
     * @param isPreSettlement
     * @param isLabel
     * @param isPrinterAgain
     */
    fun printTickerFromWs(
        context: Context,
        res: OrderedInfoResponse,
        kitchenCheckType: Int = KitchenCheckTicketType.NONE.id,
        isCashierCheck: Boolean? = false,
        isPreSettlement: Boolean? = false,
        isLabel: Boolean? = false,
        isPrinterAgain: Boolean = false,

        ) {
        Timber.e("isKitchen :${kitchenCheckType}   isCashierCheck:${isCashierCheck} isPreSettlement:${isPreSettlement}")
        viewModelScope.launch {
            val printInfoList = PrinterDeviceHelper.getPrinterList()
            //预结算二维码  用来缓存第一次请求的二维码减少请求
            var qrcode: String? = null
            if (isPreSettlement == true) {
                //预结小票翻译
                res.translateGoods()
                var printTemplateResponseItem: PrintTamplateResponseItem? = null
                for (printerConfigInfo in printInfoList) {
                    val printTemplateList =
                        printerConfigInfo.printerTemplateList ?: listOf()
                    val tmpPrintTemplateResponseItem =
                        printTemplateList.firstOrNull {
                            it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                        }
                    if (tmpPrintTemplateResponseItem != null) {
                        printTemplateResponseItem = tmpPrintTemplateResponseItem
                    }
                }
                //如果有预结算模板
                if (printTemplateResponseItem != null) {
                    if (printTemplateResponseItem.informationShow?.showKhqrCode == true && PaymentMethodHelper.isSupportOnline() && (res.payStatus == OrderedStatusEnum.UNPAID.id || res.payStatus == OrderedStatusEnum.BE_CONFIRM.id) && PaymentMethodHelper.isSupportOnline() && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == false && !res.isUniversalTable() && res.isDineIn() && !res.isFromKiosk() && res.getTotalPriceBySelf() != 0L) {
                        //只请求一次预付款二维码
//                        val response = repository.getKhqr(
//                            orderNo = res.orderNo,
//                            couponCode = res.getCurrentCoupon()?.code,
//                            reduceDollar = if (res?.reduceDollar != null) BigDecimal(
//                                res.reduceDollar!!.div(
//                                    100.0
//                                )
//                            ).halfUp(2).stripTrailingZeros() else null,
//                            reduceKhr = if (res?.reduceKhr != null) res.reduceKhr!!.toBigDecimal() else null,
//                            reduceRate = res.reduceRate,
//                            reduceType = res.reduceType,
//                        )
                        val response = repository.getKhqr(
                            PreSettlementRequest(
                                orderNo = res?.orderNo,
                                couponCode = res?.getCurrentCoupon()?.code,
                                reduceType = res?.getWholeDiscountType(),
                                reduceRate = res?.wholeDiscountReduce?.reduceRate,
                                reduceDollar = res?.wholeDiscountReduce?.reduceDollar,
                                reduceKhr = res?.wholeDiscountReduce?.reduceKhr,
                                reduceVipDollar = res?.wholeDiscountReduce?.reduceVipDollar,
                                reduceVipKhr = res?.wholeDiscountReduce?.reduceVipKhr,
                                discountReduceActivityId = res?.discountReduceActivity?.id
                            )
                        )

                        if (response is ApiResponse.Success) {
                            qrcode = response.data.qrcode
                        }
                    }
                }
            } else {
                repository.orderTranslate(res)
            }


            for (printerConfigInfo in printInfoList) {
                val printTemplateList =
                    printerConfigInfo.printerTemplateList ?: listOf()
                var isPrinter = false
                if (printTemplateList.isNotEmpty()) {
                    //获取厨打模板
                    val kitchen =
                        printTemplateList.filter { it.type == PrintTemplateTypeEnum.KITCHEN.id }

                    val printResponseItem =
                        printTemplateList.firstOrNull { it.type == res.getPrintTypeFromDiningStyle().id }

                    val labelPrintTemplate =
                        printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.LABEL.id }

                    if (isPreSettlement == true) {
                        //获取 预结算单 打印模板
                        val printTemplateResponseItem =
                            printTemplateList.firstOrNull {
                                it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                            }
                        //打预结算小票
                        if (printTemplateResponseItem != null) {
                            Printer.printTicket(
                                context,
                                printTemplateResponseItem,
                                res,
                                paymentQrCode = qrcode,
                                printerConfigInfo = printerConfigInfo,
                                isPrinterAgain = isPrinterAgain,
                            )
                            isPrinter = true
                        }
                    }

                    if (kitchenCheckType != KitchenCheckTicketType.NONE.id && kitchen.isNotEmpty()) {
                        if (kitchenCheckType == KitchenCheckTicketType.PART.id) {
                            val orderData = res.clone()
                            //服务端 已经处理好了把最新加购的菜品给我
                            orderData.currentOrderMoreList =
                                res.goods //OrderHelper.getLastAddGoods(res)
                            kitchen.forEach {
                                Printer.printTicket(
                                    context,
                                    it, orderData,
                                    printerConfigInfo = printerConfigInfo,
                                    isPrinterAgain = isPrinterAgain,
                                )
                            }
                        } else {
                            kitchen.forEach {
                                Printer.printTicket(
                                    context,
                                    it, res,
                                    printerConfigInfo = printerConfigInfo,
                                    isPrinterAgain = isPrinterAgain,
                                )
                            }
                        }

                        isPrinter = true
                    }

                    if (isLabel == true && labelPrintTemplate != null) {
                        LabelPrinter.printLabelTicket(
                            context, labelPrintTemplate, res,
                            printerConfigInfo = printerConfigInfo,
                        )
                        isPrinter = true
                    }

                    if (isCashierCheck == true && printResponseItem != null) {
                        Printer.printTicket(
                            context,
                            printResponseItem, res,
                            printerConfigInfo = printerConfigInfo,
                            isPrinterAgain = isPrinterAgain,
                        )
                        isPrinter = true
                    }
                }

                if (printerConfigInfo.type == PrinterTypeEnum.USB.type) {
                    val connectUSD =
                        PrinterUsbDeviceHelper.isConnectUSB(printerConfigInfo.usbDevice?.deviceName)
                    connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
                        override fun onSuccess(result: Boolean) {
                            Timber.e("connectUSD  onSuccess $result")
                            if (isPrinter && result) {
                                updateOrderPrintState(res.orderNo)
                            }
                        }

                        override fun onFailure(e: ExecutionException) {
                            Timber.e("connectUSD onFailure ${e}")
                        }
                    })
                } else if (printerConfigInfo.type == PrinterTypeEnum.WIFI.type) {
                    val printerState = PrinterDeviceHelper.getWifiConnectState(printerConfigInfo)
                    if (!printerState && isPrinter) {
                        Timber.e("wifi ${printerConfigInfo.ipAddress}   有问题跳过 ")
                        isPrinter = false
                    }
                    if (isPrinter) {
                        updateOrderPrintState(res.orderNo)
                    }
                }

            }
        }
    }


    private fun updateOrderPrintState(orderNo: String?) {
        if (orderNo == null) {
            return
        }
        viewModelScope.launch {
            try {
                val printResponse = repository.updatePrintLog(orderNo)
                if (printResponse is ApiResponse.Success) {
                    EventBus.getDefault().post(
                        SimpleEvent(
                            SimpleEventType.UPDATE_UNPRINT_EVENT,
                            orderNo
                        )
                    )
                    EventBus.getDefault().post(
                        SimpleEvent(
                            SimpleEventType.GET_UNREAD_EVENT,
                            null
                        )
                    )
                }
            } catch (e: Exception) {

            }
        }
    }


    ////======WS
    private val _liveDATA = MutableLiveData<WebSocket.Event>()
    val liveDataRespose get() = _liveDATA
    var lifecycleRegistry: LifecycleRegistry? = null
    var apiWebSocketService: WebSocketApi? = null

    //需要回执的命令
    private val needAckCommands = arrayListOf(
        WsCommand.CREATE_ORDER,
        WsCommand.PAY_ORDER,
        WsCommand.USER_ADD_GOODS,
        WsCommand.MODIFY_WEIGHT,
        WsCommand.PLAY_ORDER_SOUND,
    )

    @SuppressLint("CheckResult")
    fun connectWebsocket() {
        lifecycleRegistry = LifecycleRegistry()
        Timber.e("connectWebsocket ${lifecycleRegistry.hashCode()}")
        apiWebSocketService = ApiWebSocket.provideSocketApi(lifecycleRegistry!!)
        apiWebSocketService?.observeConnection()?.observeOn(Schedulers.io())
            ?.map {
                if (it is WebSocket.Event.OnConnectionOpened<*>) {
                    //连接成功拉取离线数据
                    pullOfflineData()
                } else if (it is WebSocket.Event.OnMessageReceived) {
                    try {
                        val message = (it.message as? Message.Text)?.value
                        val newEvent = handleNeedAckMsg(message)
                        if (newEvent != null) {
                            return@map newEvent
                        }
                    } catch (e: Exception) {
                        //防止抛异常导致后续消息无法处理
                        e.printStackTrace()
                        return@map WebSocket.Event.OnMessageReceived(Message.Text(""))
                    }
                }
                return@map it
            }
            ?.subscribe({ response ->
                Log.d("WebSocket", response.toString())

                //过滤掉重复事件或异常，下发的空消息
                if (response is WebSocket.Event.OnMessageReceived) {
                    val message = response.message
                    if (message is Message.Text && message.value.isEmpty()) {
                        return@subscribe
                    }
                }
                viewModelScope.launch {
                    _liveDATA.value = response
                }
            }, { error ->
                Log.e("WebSocket", "e:" + error.message.orEmpty())
            })
    }

    //拉取离线数据
    private fun pullOfflineData() {
        //清理过期事件 1天前的
        WsEventHelper.delExpireRecords(1)
        viewModelScope.launch {
            val response = repository.getUnreadCashierMsg()
            if (response is ApiResponse.Success) {
                Timber.d("拉取离线数据:${response.data.size}")
                if (response.data.isEmpty()) {
                    return@launch
                }
                val offlineEventRecords = response.data.toMutableList()
                //删除数据库已经存在事件
                val eventRecordIds = WsEventHelper.getWsEventRecordIds(response.data.map { it.id })
                Timber.d("数据库已有事件记录:${eventRecordIds?.size ?: 0}")
                if (!eventRecordIds.isNullOrEmpty()) {
                    val it = offlineEventRecords.iterator()
                    while (it.hasNext()) {
                        val r = it.next()
                        if (eventRecordIds.contains(r.id)) {
                            it.remove()
                            websocketSendAck(r.id)
                        }
                    }
                }

                Timber.d("剩余新事件记录:${offlineEventRecords.map { it.type }}")
                offlineEventRecords.forEach {
                    val content = it.content
                    if (needAckCommands.contains(it.type)) {
                        //把content转成ws对应的model，下发到viewmodel

                        val socketModel = JSON.parseObject(content, SocketModel::class.java)
                        val body =
                            JSON.parseObject(socketModel.data?.toJson(), SocketBody::class.java)
                        val newEvent = SocketModel(
                            cmd = socketModel.cmd,
                            code = socketModel.code,
                            msg = socketModel.msg,
                            data = body.data
                        )
                        val msgText = Message.Text(newEvent.toJson())
                        val event = WebSocket.Event.OnMessageReceived(msgText)
                        viewModelScope.launch {
                            _liveDATA.value = event
                        }
                        Timber.d("id:${it.id} ${body.id} orderId:${it.orderId} ${body.orderId}  content:$content")
                        //ACK回执
                        websocketSendAck(body.id)
                        //保存事件记录
                        WsEventRecord(
                            body.id,
                            body.orderId,
                            System.currentTimeMillis()
                        ).save()
                    }
                }

            } else if (response is ApiResponse.Error) {
                Timber.e("pullOfflineData:${response.message}")
            }
        }
    }

    /**
     * 处理需要回执的消息,封装成新的事件返回
     * @param message 原始消息
     * @return WebSocket.Event.OnMessageReceived? 新的事件 null表示不需要处理
     */
    private fun handleNeedAckMsg(message: String?): WebSocket.Event.OnMessageReceived? {
        if (!message.isNullOrEmpty() && message != "ping") {

            val socketModel = JSON.parseObject(message, SocketModel::class.java)
            Timber.e("needAckMsgHandle $message")
            if (needAckCommands.contains(socketModel.cmd)) {
                val body = JSON.parseObject(socketModel.data?.toJson(), SocketBody::class.java)
                //ACK回执
                websocketSendAck(body.id)
                //去除重复事件
                if (WsEventHelper.get(body.id ?: "") != null) {
                    return WebSocket.Event.OnMessageReceived(Message.Text(""))
                }
                //保存事件记录
                WsEventRecord(body.id, body.orderId, System.currentTimeMillis()).save()
                val newEvent = SocketModel(
                    cmd = socketModel.cmd,
                    code = socketModel.code,
                    msg = socketModel.msg,
                    data = body.data
                )
                val msgText = Message.Text(newEvent.toJson())
                return WebSocket.Event.OnMessageReceived(msgText)
            }
        }
        return null
    }

    private fun websocketSendAck(id: String?) {
        if (id == null) {
            return
        }
        val request = SocketRequest("/api/cashierMsg/ack", Ack(id))
        apiWebSocketService?.sendMessage(request.toJson())
    }

    fun testingWebsocketSendMessage() {
        apiWebSocketService?.sendMessage("pong")
        Timber.e("WebSocket send back pong  ${apiWebSocketService.hashCode()}")
    }

    fun destroylifeCycle() {
        lifecycleRegistry?.onNext(Lifecycle.State.Destroyed)
    }

    fun startLifeCycle() {
        lifecycleRegistry?.onNext(Lifecycle.State.Started)
    }

    fun updateShiftHandoverStatus(
        isNeedStartShift: Boolean?, //是否需要开班
        isShiftEmployee: Boolean?, //是否是开班人员
    ) {
        viewModelScope.launch {
            PreferenceDataStoreHelper.getInstance().apply {
                val model = Gson().fromJson(
                    getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        ""
                    ), UserLoginResponse::class.java
                )
                model.isNeedStartShift = isNeedStartShift
                model.isShiftEmployee = isShiftEmployee
                this.putPreference(
                    PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                    model.toJson()
                )
                MainDashboardFragment.CURRENT_USER = model
            }
        }
    }

    fun getPermissions(storeUserId: String?) {
        if (storeUserId.isNullOrEmpty()) {
            return
        }
        viewModelScope.launch {
            try {
                val permissionsResponse = repository.getPermissions(storeUserId)
                if (permissionsResponse is ApiResponse.Success) {
                    MainDashboardFragment.CURRENT_USER?.permissions =
                        permissionsResponse.data.permissions
                    PreferenceDataStoreHelper.getInstance().apply {
                        if (MainDashboardFragment.CURRENT_USER != null) {
                            this.putPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                MainDashboardFragment.CURRENT_USER!!.toJson()
                            )
                        }
                    }
                }
            } catch (e: Exception) {

            }
        }
    }


    data class UIVersionCheckModel(
        val isManualTriggering: Boolean? = false,
        val versionCheckResponse: ApiResponse<VersionCheckResponse>? = null
    )


}