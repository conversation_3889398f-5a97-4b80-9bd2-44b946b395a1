package com.metathought.food_order.casheir.ui.dialog.pay_center

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.request_model.PaymentRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.RechargeRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.DiscountInfo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PayAgainRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.ReduceDiscountDetailRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.RepaymentRequest
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.helper.ShoppingCartHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.CUSTOMER_INFO_REQUIRE
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.TABLE_INFO_REQUIRE
import com.metathought.food_order.casheir.ui.order.paybybalance.PayByBalanceViewModel
import com.metathought.food_order.casheir.ui.order.payment.PaymentQrViewModel
import com.metathought.food_order.casheir.ui.ordered.OrderedViewModel
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.math.BigDecimal
import java.util.Timer
import java.util.concurrent.ExecutionException
import javax.inject.Inject


/**
 *<AUTHOR>
 *@time  2025/3/28
 *@desc
 **/
@HiltViewModel
class PayViewModel @Inject constructor(val repository: Repository) : ViewModel() {

    val uiPaymentChannelModeState get() = _uiPaymentChannelModeState
    private val _uiPaymentChannelModeState = MutableLiveData<UIModel>()

    val uiRequestState get() = _uiRequestState
    private val _uiRequestState = MutableLiveData<UIRequestState>()


    private val _uiState = MutableLiveData<UIOnlinePayModel>()
    val uiState get() = _uiState

    private val _uiMemberState = MutableLiveData<UIMemberModel>()
    val uiMemberState get() = _uiMemberState

    private val _rechangeState = MutableLiveData<ApiResponse<RechargeResponse>>()
    val rechangeState get() = _rechangeState

    private var timer: Timer? = null

    //订单Id 外面传进来 如果外面没传进来 有点过线上支付会赋值
    var orderNo: String? = null

    //是否线上支付
    var isOnlinePay: Boolean? = null

    //是否已经开启查询线上支付结果
    private var isOpenQuery = false


    /**
     * 获取支付渠道
     *
     */
    fun getChannels() {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val result = repository.getOfflineChannels()
                emitUIState(result = result)
            } catch (e: Exception) {
                emitUIState(result = ApiResponse.Error(e.message))
            }
        }
    }

    fun payment(
        payType: PayTypeEnum,
        shareRecord: ShoppingRecord? = null,
        accountId: String? = null,
        balancePayAmount: BigDecimal? = null,
        offlineChannelModel: OfflineChannelModel? = null,
        cashConvertModel: CashConvertModel? = null,
        reduceDiscountDetailRequest: ReduceDiscountDetailRequest? = null,
    ) {
        val connectUSD = Printer.isPosPrinterConnectUSB()
        connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                viewModelScope.launch {
//                    val shareRecord = ShoppingHelper.get(localDingingStyle!!)
//                    var customerInfoVo: CustomerInfoVo? = null
                    var customerInfoVo: CustomerInfoVo? = CustomerInfoVo(
                        name = shareRecord?.name,
                        mobile = shareRecord?.mobile,
                        diningNumber = shareRecord?.diningNumber,
                        diningTime = shareRecord?.diningTime,
                        areaCode = shareRecord?.areaCode
                    )
                    if (shareRecord?.name.isNullOrEmpty() && shareRecord?.mobile.isNullOrEmpty() && (shareRecord?.diningNumber == null || shareRecord?.diningNumber == 0) && shareRecord?.areaCode.isNullOrEmpty() && shareRecord?.diningTime.isNullOrEmpty()) {
                        customerInfoVo = null
                    }

                    if (shareRecord?.diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                        if (customerInfoVo?.mobile.isNullOrEmpty()) {
                            emitUIRequestState(
                                paymentResponse = ApiResponse.Error(
                                    "Please enter customer mobile number",
                                    CUSTOMER_INFO_REQUIRE
                                )
                            )
                            return@launch
                        }
                    }

                    if (shareRecord?.tableUuid.isNullOrEmpty()) {
                        emitUIRequestState(
                            paymentResponse = ApiResponse.Error(
                                "Please select a table number",
                                TABLE_INFO_REQUIRE
                            )
                        )
                        return@launch
                    }
                    emitUIRequestState(paymentResponse = ApiResponse.Loading)
                    val goodsBoList = ShoppingCartHelper.getGoodsBoList(shareRecord)

                    try {
                        val paymentRequest =
                            if (shareRecord?.diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                                //Reservation order
                                PaymentRequest(
                                    accountId = accountId,
                                    balancePayAmount = balancePayAmount,
                                    diningStyle = shareRecord?.diningStyle,
                                    tableUuid = shareRecord?.tableUuid,
                                    payType = payType.id,
                                    customerInfoVo = customerInfoVo,
                                    goodsList = goodsBoList,
                                    isPreOrder = true,
                                    peopleDate = customerInfoVo?.diningTime?.replace("/", "-"),
                                    peopleNum = customerInfoVo?.diningNumber,
                                    note = shareRecord?.note ?: ""
                                )
                            } else if (payType == PayTypeEnum.PAY_OTHER) {
                                //外卖
                                PaymentRequest(
                                    accountId = accountId,
                                    balancePayAmount = balancePayAmount,
                                    diningStyle = DiningStyleEnum.TAKE_OUT.id,
                                    tableUuid = shareRecord?.tableUuid,
                                    payType = payType.id,
                                    customerInfoVo = customerInfoVo,
                                    goodsList = goodsBoList,
                                    isPreOrder = false,
                                    reduceVipDollar = reduceDiscountDetailRequest?.reduceVipDollar,
                                    reduceDollar = reduceDiscountDetailRequest?.reduceDollar,
                                    reduceKhr = reduceDiscountDetailRequest?.reduceKhr,
                                    reduceVipKhr = reduceDiscountDetailRequest?.reduceVipKhr,
                                    reduceType = reduceDiscountDetailRequest?.reduceType,
                                    reduceRate = reduceDiscountDetailRequest?.reduceRate,
                                    reduceReason = reduceDiscountDetailRequest?.reduceReason,
                                    discountType = reduceDiscountDetailRequest?.discountType,
                                    discountReduceActivityId = reduceDiscountDetailRequest?.discountReduceActivityId,
                                    note = shareRecord?.note ?: "",
                                    deliveryOrderNo = shareRecord?.takeOutOrderId,
                                    deliveryPlatformId = shareRecord?.getTakeOutPlatformModel()?.id,
                                )
                            } else if (payType != PayTypeEnum.PAY_AFTER) {
                                //先付款模式：Advance payment mode
                                PaymentRequest(
                                    accountId = accountId,
                                    balancePayAmount = balancePayAmount,
                                    diningStyle = shareRecord?.diningStyle,
                                    tableUuid = shareRecord?.tableUuid,
                                    payType = payType.id,
                                    customerInfoVo = customerInfoVo,
                                    goodsList = goodsBoList,
                                    isPreOrder = false,
                                    reduceVipDollar = reduceDiscountDetailRequest?.reduceVipDollar,
                                    reduceDollar = reduceDiscountDetailRequest?.reduceDollar,
                                    reduceKhr = reduceDiscountDetailRequest?.reduceKhr,
                                    reduceVipKhr = reduceDiscountDetailRequest?.reduceVipKhr,
                                    reduceType = reduceDiscountDetailRequest?.reduceType,
                                    reduceRate = reduceDiscountDetailRequest?.reduceRate,
                                    reduceReason = reduceDiscountDetailRequest?.reduceReason,
                                    discountType = reduceDiscountDetailRequest?.discountType,
                                    discountReduceActivityId = reduceDiscountDetailRequest?.discountReduceActivityId,
                                    note = shareRecord?.note ?: ""
                                )
                            } else {
                                //后付款模式：Post-payment mode
                                //不需要传递payType和accountId
                                //No need payType and accountId
                                PaymentRequest(
                                    diningStyle = shareRecord?.diningStyle,
                                    tableUuid = shareRecord?.tableUuid,
                                    customerInfoVo = customerInfoVo,
                                    goodsList = goodsBoList,
                                    isPreOrder = false,
                                    note = shareRecord?.note ?: ""
                                )
                            }

                        offlineChannelModel?.let {
                            paymentRequest.apply {
                                offlinePayChannelsId = it.id
                                offlinePayChannelsName = it.channelsName
                            }
                        }
                        cashConvertModel?.let {
                            paymentRequest.apply {
                                collectCash = it.collectCash
                                changeAmount = it.changeAmount
                                collectCashDollar = it.collectCashDollar
                                changeAmountDollar = it.changeAmountDollar
                            }
                        }
                        shareRecord?.getCouponInfo()?.let {
                            paymentRequest.apply {
                                couponCode = it.couponCode
                            }
                        }

//                        val singleDiscountItemList =
//                            goodsBoList.filter { it.singleItemDiscount != null }.map {
//                                it.singleItemDiscount!!
//                            }

                        //如果存在本地打印机
                        paymentRequest.isPosPrint = result


                        val response = repository.cartCreateOrder(
                            paymentRequest, payType.id
                        )
//                        var shoppingRecord: ShoppingRecord? = null

//                        if (response is ApiResponse.Success) {
//                            if (shoppingRecord?.diningStyle != null) {
//                                ShoppingHelper.clearNote(shoppingRecord?.diningStyle)
//                                ShoppingHelper.clearCoupon(shoppingRecord?.diningStyle)
//                                shoppingRecord =
//                                    ShoppingHelper.delAndCustomerAndTable(shoppingRecord?.diningStyle!!)
//                            }
//                        }

                        Timber.e("paymentResponse:${response}")

                        emitUIRequestState(
                            paymentResponse = response,
//                            shoppingRecord = shoppingRecord,
//                            reduceDiscountDetailRequest = reduceDiscountDetailRequest,
//                            singleItemDiscountList = singleDiscountItemList,
//                            removeAll = true
                        )

                    } catch (e: Exception) {
                        Timber.e("支付 异常 ${e.message}")
                        emitUIRequestState(paymentResponse = ApiResponse.Error(e.message))
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {
                Timber.e("e:->${e.message}")
            }

        })

    }

    /**
     * 再次付款
     */
    fun payAgain(
        orderInfo: OrderedInfoResponse? = null,
        payType: PayTypeEnum,
        accountId: String? = null,
        balancePayAmount: BigDecimal? = null,
        offlineChannelModel: OfflineChannelModel? = null,
        cashConvert: CashConvertModel? = null
    ) {
        val connectUSD = Printer.isPosPrinterConnectUSB()
        connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
//                if (orderInfo != null) {
                viewModelScope.launch {
                    emitUIRequestState(paymentResponse = ApiResponse.Loading)
                    try {
                        val response = repository.orderedPayAgain(
                            PayAgainRequest(
                                isPosPrint = result,
                                payType = payType.id,
                                accountId = accountId,
                                balancePayAmount = balancePayAmount,
                                orderNo = orderNo,
                                channelsId = offlineChannelModel?.id,
                                channelsName = offlineChannelModel?.channelsName,
                                collectCash = cashConvert?.collectCash,
                                changeAmount = cashConvert?.changeAmount,
                                collectCashDollar = cashConvert?.collectCashDollar,
                                changeAmountDollar = cashConvert?.changeAmountDollar,
                                couponId = if (orderInfo != null) orderInfo.getCurrentCoupon()?.id else null,
                                discount = if (orderInfo != null) DiscountInfo(
                                    reduceType = orderInfo.getWholeDiscountType(),
                                    reduceRate = orderInfo.wholeDiscountReduce?.reduceRate,
                                    reduceDollar = orderInfo.wholeDiscountReduce?.reduceDollar,
                                    reduceKhr = orderInfo.wholeDiscountReduce?.reduceKhr,
                                    reduceVipDollar = orderInfo.wholeDiscountReduce?.reduceVipDollar,
                                    reduceVipKhr = orderInfo.wholeDiscountReduce?.reduceVipKhr,
                                    discountReduceActivityId = orderInfo.discountReduceActivity?.id,
                                    reduceReason = orderInfo.getWholeDiscountReason()
                                ) else null
                            ),
                            payType = payType.id
                        )
                        if (response is ApiResponse.Success) {
                            if (response.data == null) {
                                //0圆购的时候服务端反的null
                                emitUIRequestState(
                                    paymentResponse = ApiResponse.Success(
                                        PaymentResponse(
                                            orderNo = orderNo,
                                            payType = PayTypeEnum.CASH_PAYMENT.id
                                        )
                                    )
                                )
                            } else {
                                emitUIRequestState(paymentResponse = response)
                            }
                        } else {
                            emitUIRequestState(paymentResponse = response)
                        }

                    } catch (e: Exception) {
                        emitUIRequestState(paymentResponse = ApiResponse.Error(e.message))
                    }
//                    }
                }
            }

            override fun onFailure(e: ExecutionException) {

            }
        })
    }


    /**
     * 查询订单状态
     *
     */
    fun checkPaymentStatus() {
        viewModelScope.launch {
            if (orderNo != null) {
                val result = repository.orderedInfo(orderNo)
                if (result is ApiResponse.Success) {
                    if (result.data.isOrderSuccess()) {
                        emitUiState(success = result.data)
                    } else if (result.data.isOrderExpire()) {
                        emitUiState(isExpire = true)
                    } else {
                        delay(2000)
                        checkPaymentStatus()
                    }
                } else if (result is ApiResponse.Error) {
                    emitUiState(error = result.message)
                }
            }
        }
    }

    //查询充值状态
    fun checkRechargeStatus(orderId: String?) {
        viewModelScope.launch {
            val result = repository.getRechargeQRStatus(orderId)
            if (result is ApiResponse.Success) {
                if (result.data.isOrderSuccess()) {
                    emitUiState(rechargeSuccess = result.data)
                } else if (result.data.isOrderExpire()) {
                    timer?.cancel()
                    emitUiState(isExpire = true)
                } else {
                    delay(2000)
                    checkRechargeStatus(orderId)
                }
            } else if (result is ApiResponse.Error) {
                emitUiState(error = result.message)
            }
        }
    }

    //查询在线还款状态
    fun checkRepaymentStatus(outTradeNo: String?) {
        viewModelScope.launch {
            val result = repository.getPaymentStatusPolling(outTradeNo)
            if (result is ApiResponse.Success) {
                if (result.data.isOrderSuccess()) {
                    emitUiState(repaymentSuccess = result.data.repaymentResultDTO)
                } else if (result.data.isOrderExpire()) {
                    timer?.cancel()
                    emitUiState(isExpire = true)
                } else {
                    delay(2000)
                    checkRepaymentStatus(outTradeNo)
                }
            } else if (result is ApiResponse.Error) {
                emitUiState(error = result.message)
            }
        }
    }

    /**
     * Get member account
     *
     * @param phoneNumber
     */
    fun getMemberAccount(phoneNumber: String) {
        viewModelScope.launch {
            emitMemberUiState(ApiResponse.Loading)
            try {
                val response = repository.consumerPayAccount(phoneNumber)
                emitMemberUiState(response)
            } catch (e: Exception) {
                emitMemberUiState(ApiResponse.Error(e.message))
            }
        }
    }

    private suspend fun emitUIState(
        result: ApiResponse<OfflineChannelTotalModel>? = null
    ) {
        withContext(Dispatchers.Main) {
            _uiPaymentChannelModeState.value = UIModel(result = result)
        }
    }

    data class UIModel(
        val result: ApiResponse<OfflineChannelTotalModel>?,
    )

    private suspend fun emitUIRequestState(
        paymentResponse: ApiResponse<PaymentResponse>? = null,
    ) {
        withContext(Dispatchers.Main) {
            _uiRequestState.value = UIRequestState(paymentResponse)
        }
    }

    data class UIRequestState(
        val paymentResponse: ApiResponse<PaymentResponse>?,
    )

    private fun emitUiState(
        time: String? = null,
        isExpire: Boolean? = null,
        error: String? = null,
        success: OrderedInfoResponse? = null,
        rechargeSuccess: RechargeQRStatusResponse? = null,
        repaymentSuccess: RepaymentResponse? = null,
    ) {
        val uiModel =
            UIOnlinePayModel(time, isExpire, error, success, rechargeSuccess, repaymentSuccess)
        _uiState.postValue(uiModel)
    }

    data class UIOnlinePayModel(
        val time: String?,
        val isExpire: Boolean?,
        val error: String?,
        val success: OrderedInfoResponse?,
        val rechargeSuccess: RechargeQRStatusResponse?,
        val repaymentSuccess: RepaymentResponse?,
    )

    fun emitMemberUiState(memberAccountResult: ApiResponse<CustomerMemberResponse?>? = null) {
        val uiModel = UIMemberModel(memberAccountResult)
        _uiMemberState.value = uiModel
    }

    data class UIMemberModel(val memberAccountResult: ApiResponse<CustomerMemberResponse?>?)


    fun rechargeBalance(rechargeRequest: RechargeRequest) {
        viewModelScope.launch {
            _rechangeState.value = ApiResponse.Loading
            _rechangeState.value = repository.rechargeBalance(rechargeRequest)
        }
    }


    //还款
    fun repayment(
        payType: PayTypeEnum,
        consumerId: Long,    //消费者id
        accountId: String? = null,
        isPosPrint: Boolean,
        balancePayAmount: BigDecimal? = null,   //余额支付金额
        offlineChannelModel: OfflineChannelModel? = null, //线下支付
        cashConvertModel: CashConvertModel? = null, //现金
    ) {
        viewModelScope.launch {
            emitUIRepaymentState(prepaymentResponse = ApiResponse.Loading)

            try {
                val repaymentRequest = RepaymentRequest(
                    accountId = accountId,
                    consumerId = consumerId, //消费者id,也就是还款人
                    payType = payType.id, //支付类型:1-线上支付;2-现金支付;3-用户余额支付;4-其他支付(外卖使用);5-组合支付
                    balancePayAmount = balancePayAmount,//v2.14版本，组合支付功能新增字段:余额支付金额
                    isPosPrint = isPosPrint,
                )

                offlineChannelModel?.let {
                    repaymentRequest.apply {
                        offlinePayChannelsId = it.id//线下收款渠道id，线下支付
                    }
                }

                cashConvertModel?.let {
                    repaymentRequest.apply {
                        collectCash = it.collectCash    //收取现金 现金支付时使用,瑞尔
                        changeAmount = it.changeAmount  //找零金额 现金支付时使用，瑞尔
                        collectCashDollar = it.collectCashDollar //收取现金 现金支付时使用,美元
                        changeAmountDollar =
                            it.changeAmountDollar?.toBigDecimal()  //找要金额 现金支付时使用，美元
                    }
                }

                repaymentRequest.isRepayment = true

                val response = repository.repayment(repaymentRequest)

                emitUIRepaymentState(prepaymentResponse = response)

            } catch (e: Exception) {
                emitUIRepaymentState(prepaymentResponse = ApiResponse.Error(e.message))
            }
        }
    }


    val uiRepaymentState get() = _uiRepaymentState
    private val _uiRepaymentState = MutableLiveData<UIRepaymentState>()

    data class UIRepaymentState(
        val paymentResponse: ApiResponse<RepaymentResponse>?,
    )

    private suspend fun emitUIRepaymentState(
        prepaymentResponse: ApiResponse<RepaymentResponse>? = null,
    ) {
        withContext(Dispatchers.Main) {
            _uiRepaymentState.value = UIRepaymentState(prepaymentResponse)
        }
    }
}