package com.metathought.food_order.casheir.data.model.base.response_model.login

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.utils.BusinessHoursChecker
import timber.log.Timber
import java.time.LocalTime
import java.util.Date

data class StoreInfoResponse(

    /**
     * id
     */
    val id: String?,


    /**
     * 创建时间
     */
    val createTime: String?,

    /**
     * 修改时间
     */
    val updateTime: String?,

    /**
     * 店名
     */
    var name: String?,

    /**
     * 地址
     */
    var address: String?,

    /**
     * 省
     */
    val province: String?,

    /**
     * 市
     */
    val city: String?,

    /**
     * 区
     */
    val area: String?,

    /**
     * 营业开始时间
     */
    val startTime: String?,

    /**
     * 营业结束时间
     */
    val endTime: String?,

    /**
     * 经度
     */
    var lng: Double?,

    /**
     * 纬度
     */
    var lat: Double?,

    /**
     * 经纬度坐标
     *
     */
    val latlng: String?,

    /**
     * 电话
     */
    var telephons: String?,

    /**
     * 1总店配置，2门店自己配置
     */
    var configType: Int?,

    /**
     * 1.自营，2.加盟
     */
    var type: Int?,

    /**
     * 1.堂食，2.外卖
     *
     */
    var diningStyle: Int?,

    /**
     * 备注介绍
     */
    var note: String?,

    /**
     * 1-正常营业;2-上线但不营业;3-关闭门店
     */
    var status: Int,

    /**
     * 图片
     */
    var url: String?,

    /**
     * 是否先付款:0-否 1-是
     */
    var isPaymentInAdvance: Boolean,
    /**
     *vat,百分比 0-100%
     */
    var vatPercentage: Int,

    /**
     * 服务费,百分比 0-100%
     */
    var serviceChargePercentage: Int,

    /**
     * 是否餐桌共享:1-是（可多人一起点餐）， 0-否（各自点餐）
     * [Whether the table is shared:(1-yes (multiple people can order together), 0-no (order separately))] | 是否餐桌共享:1-是（可多人一起点餐）， 0-否（各自点餐）
     */
    var isTableService: Boolean?,

    /**
     * 是否显示桌台(餐桌非共享的时候,此字段有效)
     */
    var isDisplayTable: Boolean,

    /**
     * 是否开启临时桌码
     */
    var isTempTableCode: Boolean?,

    /**
     * 临时桌码有效期(天)
     */
    var tempTableCodeExpireDays: Int?,

    /**
     * 临时桌码有效期类型
     */
    var tempTableCodeExpireDateField: Int?,

    /**
     * 描述
     */
    var description: String?,

    /**
     * 是否需要取餐码
     *
     */
    var isNeedPickupCode: Boolean?,

    /**
     * 取餐码顺序(开始)
     *
     */
    var fromPickupCode: Int?,

    /**
     * 取餐码顺序(结束)
     */
    var toPickupCode: Int?,

    /**
     * 是否需要发票流水号
     */
    var isNeedInvoiceNumber: Boolean?,

    /**
     *前缀
     */
    var prefix: String?,

    /**
     *客户点餐主题页面
     */
    var pageViewId: Int?,


    /**
     * 日期类型
     * YYYY
     * YY
     * YYYY_MM
     * YY_MM
     */
    var dateFormat: String?,

    /**
     *发票流水号
     *
     */
    var invoiceSerialNumber: Int?,

    /**
     *发票配置更新时间
     *
     */
    var invoiceUpdateTime: String?,

    /**
     * 是否自动接单
     */
    var autoAcceptOrders: Boolean?,

    /**
     * 接单超时时间配置(默认30分钟)
     */
    var acceptTimeout: Int?,

    /**
     *小票是否需要税务信息
     */
    var ticketShowTaxInfo: Boolean?,

    /**
     *
     */
    var limitOrderDistance: Boolean?,

    /**
     *
     */
    var effectiveDistance: Int?,

    /**
     *  收银端是否显示图片
     */
    var cashierShowPic: Boolean?,

    @SerializedName("companyName")
    var companyName: String?,

    @SerializedName("companyTaxNumber")
    var companyTaxNumber: String?,

    @SerializedName("companyAddress")
    var companyAddress: String?,

    @SerializedName("companyContactNumber")
    var companyContactNumber: String?,

    @SerializedName("companyContactEmail")
    var companyContactEmail: String?,

    /**
     * 用餐人数是否必填
     */
    @SerializedName("isDiningNumber")
    var isDiningNumber: Boolean? = false,

    /**
     * 是否自动打印结账小票
     */
    @SerializedName("isAutoCheckoutTicket")
    var isAutoCheckoutTicket: Boolean? = false,

    /**
     * 美元->瑞尔汇率
     */
    @SerializedName("conversionRatio")
    var conversionRatio: Long? = 4100,

    /**
     * 汇率修改记录
     */
    @SerializedName("conversionRatioRecordList")
    var conversionRatioRecordList: List<ConversionRatioRecord>? = null,

    /**
     * 是否可自主配置优惠活动
     */
    @SerializedName("isCanConfigCoupon")
    var isCanConfigCoupon: Boolean? = null,

    /**
     * 是否外卖白名单
     */
    @SerializedName("isDeliveryWhiteList")
    var isDeliveryWhiteList: Boolean? = null,

    ) {

    /**
     * 外卖功能是否可用
     *
     * @return
     */
    fun isDeliveryEnable(): Boolean {
        return isDeliveryWhiteList == true
    }

    fun getStoreStatus(context: Context): String {
        return when (status) {
            1 -> {
                context.getString(R.string.normal_business_hours)
            }

            2 -> {
                context.getString(R.string.online_but_not_operating)
            }

            3 -> {
                context.getString(R.string.close_store)
            }

            else -> {
                ""
            }
        }
    }

    fun getStoreStatusRes(): Int {
        return when (status) {
            1 -> {
                R.drawable.icon_store_state_green
            }

            2 -> {
                R.drawable.icon_store_state_grey
            }

            3 -> {
                R.drawable.icon_store_state_red
            }

            else -> {
                R.drawable.icon_store_state_green
            }
        }
    }

    fun getStorePaymentMethod(context: Context): String {
        return if (isPaymentInAdvance) {
            context.getString(R.string.pay_first)
        } else {
            context.getString(R.string.post_pay)
        }
    }

    fun getStoreTableService(context: Context): String {
        return if (isTableService == true) {
            context.getString(R.string.yes_desc)
        } else {
            context.getString(R.string.no_desc)
        }
    }

    fun getStoreShowTable(context: Context): String {
        if (isTableService == true) {
            return context.getString(R.string.yes_desc)
        }
        return if (isDisplayTable) {
            context.getString(R.string.yes_desc)
        } else {
            context.getString(R.string.no_desc)
        }
    }


    fun getStoreAutoAccept(context: Context): String {
        return if (autoAcceptOrders == true) {
            context.getString(R.string.yes_desc)
        } else {
            context.getString(R.string.no_desc)
        }
    }

    fun getStoreMenuShowPic(context: Context): String {
        return if (cashierShowPic == true) {
            context.getString(R.string.yes_desc)
        } else {
            context.getString(R.string.no_desc)
        }
    }

    fun getStorePickUpNoDesc(context: Context): String {
        return if (isNeedPickupCode == true) {
            "${context.getString(R.string.every_day_from)} ${getPickUpNoFormat(fromPickupCode)}-${
                getPickUpNoFormat(
                    toPickupCode
                )
            } ${
                context.getString(
                    R.string.loop
                )
            }"
        } else {
            context.getString(R.string.no_desc)
        }
    }


    fun getStoreInvoiceNumberDesc(context: Context): String {
        return if (isNeedInvoiceNumber == true) {
            getInvoiceNumberDesc(prefix, dateFormat, invoiceSerialNumber)
        } else {
            context.getString(R.string.no_desc)
        }
    }

    fun getInvoiceNumberDateType(context: Context): String {
        return if (dateFormat == "1") {
            "yyyy"
        } else if (dateFormat == "2") {
            "yy"
        } else if (dateFormat == "3") {
            "yyyymm"
        } else if (dateFormat == "4") {
            "yymm"
        } else {
            context.getString(R.string.none)
        }
    }

    fun getInvoiceNumberDesc(
        prefix: String?,
        currentDateFormat: String?,
        invoiceSerialNumber: Int?
    ): String {
        val str = if (currentDateFormat == "0") {
            ""
        } else if (currentDateFormat == "1") {
            Date().formatDateStr("yyyy")
        } else if (currentDateFormat == "2") {
            Date().formatDateStr("yy")
        } else if (currentDateFormat == "3") {
            Date().formatDateStr("yyyyMM")
        } else if (currentDateFormat == "4") {
            Date().formatDateStr("yyMM")
        } else {
            ""
        }

        return "${prefix}${str}${getInvoiceSerialNumberFormat(invoiceSerialNumber)}"
    }


    fun getInvoiceSerialNumberFormat(invoiceSerialNumber: Int?): String {
        if (invoiceSerialNumber == null) {
            return ""
        }
        return "%06d".format((invoiceSerialNumber ?: 0))
    }

    fun getPickUpNoFormat(number: Int?): String {
        if (number == null) {
            return ""
        }
        return "%03d".format((number ?: 0))
    }

    fun autoCheckoutTicket(): Boolean {
        return isAutoCheckoutTicket == true
    }

    /**
     * 是否设置营业时间
     *
     * @return
     */
    fun isSetOpenStartTime(): Boolean {
        if (startTime == null) {
            return false
        }
        //有设置过营业时间为00:00:00 或者 "24:00:00" 也算没设置营业时间
        if (startTime == "00:00:00" || startTime == "24:00:00") {
            return false
        }
        return true
    }

    /**
     * 是否在营业时间内
     *
     * @return
     */
    fun isCurrentInOpenTime(): Boolean {
        if (startTime == null) {
            return true
        }
        //有设置过营业时间为00:00:00 或者 "24:00:00" 也算没设置营业时间
        if (startTime == "00:00:00" || startTime == "24:00:00") {
            return true
        }
        Timber.e("startTime ${startTime}")
        Timber.e("endTime ${endTime}")
        Timber.e(
            "BusinessHoursChecker :${
                BusinessHoursChecker.isBusinessTimeReached(
                    startTime,
                    endTime ?: "00:00:00"
                )
            }"
        )
        return BusinessHoursChecker.isBusinessTimeReached(startTime, endTime ?: "00:00:00")
    }


    /**
     * 获取开始的营业时间
     *
     * @return
     */
    fun getStartTime(): List<Int> {
        if (startTime == null) {
            return listOf(0, 0, 0)
        }
        return startTime.split(":").map { it.toIntOrNull() ?: 0 }
    }

    fun getEndTime(): List<Int> {
        if (endTime == null) {
            return listOf(0, 0, 0)
        }
        return endTime.split(":").map { it.toIntOrNull() ?: 0 }
    }
}

data class ConversionRatioRecord(
    val createTime: String? = null,
    val conversionRatio: Long? = null,
    val newConversionRatio: Long? = null,
    val updateUserId: String? = null,
    val updateUserName: String? = null,
    var updateTime: String? = null
)