package com.metathought.food_order.casheir.ui.order.food_detail

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.lxj.xpopup.util.KeyboardUtils
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.DialogMealSetTagSelectBinding
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.showKeyboard
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.adapter.MainSpecificationAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2025/1/7
 *@desc
 **/

@AndroidEntryPoint
class MealSetTagSelectDialog : BaseDialogFragment() {
    private var binding: DialogMealSetTagSelectBinding? = null
    private var onConfirmClick: (() -> Unit)? = null
    private var mealSetGroup: MealSetGroup? = null
    private var mealSetGoods: MealSetGoods? = null
    private var isCanRepeat: Boolean? = false
    private var specificationSubItemAdapter: MainSpecificationAdapter? = null
    private var count = 1

    private var remainingMaximum = 1

    //    private var tags:List
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMealSetTagSelectBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        delayInit()
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain) {
            binding?.apply {
                val inputMethodManager =
                    requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
            }
        }

        initObserver()
        initListener()
        initData()
    }


    private fun initData() {
        binding?.apply {
            tvGoodName.text = mealSetGoods?.name
            count = 1
            remainingMaximum =
                ((mealSetGroup?.enableSelectMaxNum() ?: 0) - (mealSetGroup?.getSelectNum()
                    ?: 0)).toInt()
            Timber.e("remainingMaximum:  $remainingMaximum")
//            setAddBtnEnable(imgPlus, mealSetGroup?.isMaxNum() == false)
            setAddBtnEnable(imgPlus, count < remainingMaximum)
            tvQTY.text = "$count"
            llNumControl.isVisible = isCanRepeat == true

            context?.let { it ->
                lifecycleScope.launch {
                    val isOpenManualInput = PreferenceHelper.getOpenManualInputWeight()
                    var list = mealSetGoods?.tags ?: listOf()
                    if (mealSetGoods?.optionalSpec == false) {
                        //如果是商户固定规格,那就不现实出规格属性
                        list = listOf()
                        //把商家配置的选中
                        mealSetGoods?.tags?.forEach { tag ->
                            tag.goodsTagItems?.forEach { goodsTagItem ->
                                goodsTagItem.isCheck =
                                    mealSetGoods?.tagItemIds?.contains(goodsTagItem.id ?: "")
                            }
                        }
                    }

                    specificationSubItemAdapter =
                        MainSpecificationAdapter(
                            list,
                            it,
                            isShowWeight = mealSetGoods?.isToBeWeighed() == true,
                            currentPrice = mealSetGoods?.priceMarkup,
                            weightUnit = mealSetGoods?.getWeightUnit(),
                            isOpenManualInput = isOpenManualInput,
                            oldWeight = mealSetGoods?.weight
                        ) {
                            checkBtnStatus()
                        }
                    specificationSubItemAdapter?.onSwitchOpenManualInput = { openManualInput ->
                        lifecycleScope.launch {
                            PreferenceHelper.setOpenManualInputWeight(openManualInput)
                        }
                    }
                    recyclerView.adapter = specificationSubItemAdapter
                    /*
                    https://chandao.metathought.co/bug-view-14852.html
                    控制第一次显示时称重输入框弹出键盘
                    直接在adapter中显示的话，会导致键盘弹出后rv还会在上下滑动一下
                    所以在这里判断最后一个是否是称重item,如果是就滑动到这个item在现实出键盘
                     */
                    if (specificationSubItemAdapter?.isShowWeight == true) {
                        recyclerView.viewTreeObserver.addOnGlobalLayoutListener(
                            object : OnGlobalLayoutListener {
                                override fun onGlobalLayout() {
                                    // 确保只执行一次
                                    recyclerView.viewTreeObserver
                                        .removeOnGlobalLayoutListener(this)

                                    val llm = recyclerView.layoutManager as? LinearLayoutManager
                                    llm?.let {
                                        val lastVisiblePosition = it.findLastVisibleItemPosition()
                                        val lastVisibleView =
                                            it.findViewByPosition(lastVisiblePosition)
                                        if (lastVisibleView != null) {
                                            val viewHolder =
                                                recyclerView.getChildViewHolder(lastVisibleView)
                                            if (viewHolder is MainSpecificationAdapter.SpecificationWeightViewHolder) {
                                                if (viewHolder.binding.edtWeight.isEnabled) {
                                                    val bottom = viewHolder.binding.edtWeight.bottom
                                                    Timber.d("edtWeight bottom:$bottom")
                                                    recyclerView.scrollToPosition(
                                                        lastVisiblePosition
                                                    )
                                                    viewHolder.binding.edtWeight.postDelayed({
                                                        KeyboardUtils.showSoftInput(viewHolder.binding.edtWeight)
                                                    }, 200)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        )
                    }
                }
            }
            checkBtnStatus()
        }
    }

    private fun initObserver() {

    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            imgMinus.setOnClickListener {
                count -= 1
                tvQTY.text = "$count"
                setMinusBtnEnable(imgMinus, count > 1)
                setAddBtnEnable(imgPlus, count < remainingMaximum)
            }

            imgPlus.setOnClickListener {
                count += 1
                tvQTY.text = "$count"
                setMinusBtnEnable(imgMinus, count > 1)
                setAddBtnEnable(imgPlus, count < remainingMaximum)
            }

            btnConfirm.setOnClickListener {
                val selectTag = mutableListOf<GoodsTagItem>()
                mealSetGoods?.tags?.forEach { tag ->
                    tag.goodsTagItems?.forEach { goodsTagItem ->
                        if (goodsTagItem.isCheck == true) {
                            goodsTagItem.type = tag.type
                            if (isCanRepeat == true) {
                                //如果是可重复选择的，确认的时候清掉选择状态，下次再打开弹窗是新增
                                goodsTagItem.isCheck = false
                            }
                            selectTag.add(goodsTagItem)
                        }
                    }
                }
                if (mealSetGoods?.isToBeWeighed() == true) {
                    val weightNum = specificationSubItemAdapter?.weight?.toDoubleOrNull() ?: 0.0
                    if (weightNum == 0.0) {
                        mealSetGoods?.weight = null
                        mealSetGoods?.weighingCompleted = false
                    } else {
                        mealSetGoods?.weight = weightNum
                        mealSetGoods?.weighingCompleted = true
                    }
                }
                //不能重复选的时候清空原来选择的规格
//                mealSetGoods?.clearSelect()
                mealSetGoods?.selectCustomTag(
                    "add",
                    selectTag,
                    count
                )
                onConfirmClick?.invoke()
                dismissCurrentDialog()
            }
        }
    }

    override fun onDestroyView() {
        specificationSubItemAdapter?.disconnectScale()
        hideKeyboard()
        super.onDestroyView()
    }

    private fun checkBtnStatus() {
        var enable = true
        //用户自选需要走这个判断 ,商家直接放过
        if (mealSetGoods?.optionalSpec == true) {
            mealSetGoods?.tags?.forEach { tag ->
                if (tag.isMustSelect() && !tag.isTagSelect()) {
                    enable = false
                }
            }
        }
        binding?.apply {
            btnConfirm.setEnableWithAlpha(enable)
        }
    }

    private fun setMinusBtnEnable(imageView: ImageView, visible: Boolean) {
        imageView.isInvisible = !visible
//        imageView.setImageResource(if (enable) R.drawable.ic_add else R.drawable.ic_add_disable)
    }

    private fun setAddBtnEnable(imageView: ImageView, enable: Boolean) {
        imageView.isEnabled = enable
        imageView.setImageResource(if (enable) R.drawable.ic_add else R.drawable.ic_add_disable)
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        Timber.e("onDismiss")
        hideKeyboard()
        //不能重复选的时候清空原来选择的规格
//        if (isCanRepeat == true) {
        mealSetGoods?.clearCheck()
//        }
    }

    override fun dismiss() {
        Timber.e("dismiss")
        hideKeyboard()
        super.dismiss()
    }

    override fun dismissAllowingStateLoss() {
        Timber.e("dismissAllowingStateLoss")
        hideKeyboard()
        super.dismissAllowingStateLoss()
    }

    override fun onResume() {
        super.onResume()
        adjustmentWindow()
    }

    private fun adjustmentWindow() {
        context?.let {
            Timber.e("mealSetGoods?.optionalSpec  ${mealSetGoods?.optionalSpec}")
            val displayMetrics = getDisplayMetrics(it)
            //如果是商家固定,或者没规格属性,弹窗高度小一点
            val percent =
                if (mealSetGoods?.optionalSpec == false || mealSetGoods?.tags.isNullOrEmpty()) {
                    0.55
                } else {
                    0.8
                }
            val screenHeight =
                (displayMetrics.heightPixels * percent).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val TAG = "MealSetTagSelectDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            mealSetGroup: MealSetGroup? = null,
            mealSetGoods: MealSetGoods,
            isCanRepeat: Boolean?,
            onConfirmClick: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                mealSetGroup,
                mealSetGoods,
                isCanRepeat,
                onConfirmClick
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(TAG) as? MealSetTagSelectDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            mealSetGroup: MealSetGroup? = null,
            mealSetGoods: MealSetGoods,
            isCanRepeat: Boolean?,
            onConfirmClick: (() -> Unit)? = null
        ): MealSetTagSelectDialog {
//            val args = Bundle()
            val fragment = MealSetTagSelectDialog()
            fragment.mealSetGroup = mealSetGroup
            fragment.mealSetGoods = mealSetGoods
            fragment.isCanRepeat = isCanRepeat
            fragment.onConfirmClick = onConfirmClick
            return fragment
        }
    }


}