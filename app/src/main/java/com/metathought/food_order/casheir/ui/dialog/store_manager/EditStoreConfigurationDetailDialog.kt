package com.metathought.food_order.casheir.ui.dialog.store_manager

import android.os.Bundle
import android.text.InputFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.databinding.DialogEditStoreConfigurationBinding
import com.metathought.food_order.casheir.databinding.PopupAcceptTimeBinding
import com.metathought.food_order.casheir.databinding.PopupStoreManagerDateformateBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import org.greenrobot.eventbus.EventBus
import timber.log.Timber


/**
 * 门店管理编辑
 *
 * @constructor Create empty Edit store manager detail dialog
 */

@AndroidEntryPoint
class EditStoreConfigurationDetailDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "EditStoreConfigurationD"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? EditStoreConfigurationDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): EditStoreConfigurationDetailDialog {
            val args = Bundle()
            val fragment = EditStoreConfigurationDetailDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private var storeInfoResponse: StoreInfoResponse? = null


    private var binding: DialogEditStoreConfigurationBinding? = null

    private val storeManagerViewModel: StoreManagerViewModel by viewModels()

    private var dateFormat: String? = null
    private var acceptTime: Int? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditStoreConfigurationBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)


        initListener()
        initObserver()
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        storeManagerViewModel.getStoreInfo()
    }

    private fun initView() {
        binding?.apply {
            storeManagerViewModel.headUrl = storeInfoResponse?.url

            when (storeInfoResponse?.status) {
                1 -> {
                    radioStatus.check(R.id.radioNormalBusiness)
                }

                2 -> {
                    radioStatus.check(R.id.radioOnlineNoOperating)
                }

                3 -> {
                    radioStatus.check(R.id.radioCloseStore)
                }
            }

            if (storeInfoResponse?.isPaymentInAdvance == true) {
                radioPayment.check(R.id.radioPayFirst)
                switchAutoAccept.isVisible = false
            } else {
                radioPayment.check(R.id.radioPostPay)
                switchAutoAccept.isVisible = true
            }

            switchPeopleNum.setOpen(storeInfoResponse?.isDiningNumber)

            switchTableShared.setOpen(storeInfoResponse?.isTableService)

            switchShowTable.isVisible = !switchTableShared.isSwitchOpen()
            switchShowTable.setOpen(storeInfoResponse?.isDisplayTable)

            switchAutoAccept.setOpen(storeInfoResponse?.autoAcceptOrders)

            acceptTime = storeInfoResponse?.acceptTimeout ?: 10

            flAcceptTime.isVisible = storeInfoResponse?.autoAcceptOrders == false
            edtAcceptTime.setText(getString(R.string.accept_cancel_time, acceptTime))

            switchShowMenuImage.setOpen(storeInfoResponse?.cashierShowPic)

            switchMealCode.setOpen(storeInfoResponse?.isNeedPickupCode)
            llMealCode.isVisible = storeInfoResponse?.isNeedPickupCode ?: false

            edtStartMealCode.setText(storeInfoResponse?.getPickUpNoFormat(storeInfoResponse?.fromPickupCode))
            edtStartMealCode.setSelection(edtStartMealCode.length())

            edtEndMealCode.setText(storeInfoResponse?.getPickUpNoFormat(storeInfoResponse?.toPickupCode))
            edtEndMealCode.setSelection(edtEndMealCode.length())

            switchInvoiceCode.setOpen(storeInfoResponse?.isNeedInvoiceNumber)
            llInvoiceCodeInfo.isVisible = storeInfoResponse?.isNeedInvoiceNumber ?: false

            edtPrefix.setText(storeInfoResponse?.prefix)
            edtPrefix.setSelection(edtPrefix.length())

            dateFormat = storeInfoResponse?.dateFormat
            edtDateType.setText(storeInfoResponse?.getInvoiceNumberDateType(requireContext()))

            edtInvoiceCode.setText(storeInfoResponse?.getInvoiceSerialNumberFormat(storeInfoResponse?.invoiceSerialNumber))
            edtInvoiceCode.setSelection(edtInvoiceCode.length())
            formatInvoiceCue()

            switchTaxInfo.setOpen(storeInfoResponse?.ticketShowTaxInfo)
            llTaxInfo.isVisible = storeInfoResponse?.ticketShowTaxInfo ?: false

            edtCompanyName.setText(storeInfoResponse?.companyName)
            edtCompanyName.setSelection(edtCompanyName.length())

            edtCompanyVatTin.setText(storeInfoResponse?.companyTaxNumber)
            edtCompanyVatTin.setSelection(edtCompanyVatTin.length())

            edtCompanyAddress.setText(storeInfoResponse?.companyAddress)
            edtCompanyAddress.setSelection(edtCompanyAddress.length())

            edtCompanyPhone.setText(storeInfoResponse?.companyContactNumber)
            edtCompanyPhone.setSelection(edtCompanyPhone.length())

            edtCompanyEmail.setText(storeInfoResponse?.companyContactEmail)
            edtCompanyEmail.setSelection(edtCompanyEmail.length())

            switchAutoCheckoutTicket.setOpen(storeInfoResponse?.isAutoCheckoutTicket)

            edtConversion.setText("${storeInfoResponse?.conversionRatio ?: 4100}")
            edtConversion.setSelection(edtConversion.length())
            ivConversionHistory.isVisible =
                (storeInfoResponse?.conversionRatioRecordList?.size ?: 0) > 0
            dealAutoPrintTickCue()
            edtRemark.setText(storeInfoResponse?.note)
            edtRemark.setSelection(edtRemark.length())
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            radioPayment.setOnCheckedChangeListener { radioGroup, checkedId ->
                switchAutoAccept.isVisible = checkedId != R.id.radioPayFirst
            }


            switchAutoAccept.setSwitchListener {
                flAcceptTime.isVisible = !it
            }

            edtAcceptTime.setOnClickListener {
                showPopupWindowAcceptTime(edtAcceptTime)
            }

            switchTableShared.setSwitchListener {
                switchShowTable.isVisible = !it
            }
            switchMealCode.setSwitchListener {
                hideKeyBoardAndRemoveFocus()
                llMealCode.isVisible = it
            }

            switchInvoiceCode.setSwitchListener {
                hideKeyBoardAndRemoveFocus()
                llInvoiceCodeInfo.isVisible = it
                dealAutoPrintTickCue()
            }

            switchTaxInfo.setSwitchListener {
                hideKeyBoardAndRemoveFocus()
                llTaxInfo.isVisible = it
                dealAutoPrintTickCue()
            }

            edtDateType.setOnClickListener {
                hideKeyBoardAndRemoveFocus()
                showPopupWindowDateType(edtDateType)
            }


            edtStartMealCode.addTextChangedListener {
                checkPickUpNo()
            }
            edtEndMealCode.addTextChangedListener {
                checkPickUpNo()
            }

            edtPrefix.filters =
                arrayOf<InputFilter>(InputFilter { source, start, end, dest, dstart, dend ->
                    // 允许的符号列表
                    val allowedSymbols = ",.';:-_() "
                    for (i in start until end) {
                        val c = source[i]

                        // 判断是否是英文、数字或标点符号
                        if (!(c.toString()
                                .matches("[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]".toRegex()) || c.toString()
                                .matches("[a-zA-Z0-9]+".toRegex()))
                        ) {
                            return@InputFilter "" // 不是则返回空，表示不允许输入
                        }
                    }
                    null // 允许输入
                }, InputFilter.LengthFilter(5))

            edtPrefix.addTextChangedListener {
                formatInvoiceCue()
            }

            edtInvoiceCode.addTextChangedListener {
                checkInvoiceCode()
                formatInvoiceCue()
            }

            switchAutoCheckoutTicket.setSwitchListener {
                dealAutoPrintTickCue()
            }

            ivConversionHistory.setOnClickListener {
                ExchangeRateChangeHistoryDialog.showDialog(
                    parentFragmentManager,
                    storeInfoResponse?.conversionRatioRecordList
                )
            }

            edtConversion.addTextChangedListener {
                checkExchangeRate()
            }

            btnDone.setOnClickListener {
                if (storeInfoResponse == null) {
                    return@setOnClickListener
                }
                SingleClickUtils.isFastDoubleClick {
                    if (!checkInvoiceCode() || !checkPickUpNo() || !checkExchangeRate()) {
                        Timber.e("没通过校验")
                        return@isFastDoubleClick
                    }

                    Timber.e("通过校验")


                    storeInfoResponse?.status = if (radioNormalBusiness.isChecked) {
                        1
                    } else if (radioOnlineNoOperating.isChecked) {
                        2
                    } else {
                        3
                    }

                    storeInfoResponse?.isDiningNumber = switchPeopleNum.isSwitchOpen()

                    storeInfoResponse?.isPaymentInAdvance = radioPayFirst.isChecked

                    storeInfoResponse?.isTableService = switchTableShared.isSwitchOpen()

                    storeInfoResponse?.isDisplayTable = switchShowTable.isSwitchOpen()

                    storeInfoResponse?.autoAcceptOrders = switchAutoAccept.isSwitchOpen()
                    storeInfoResponse?.acceptTimeout = acceptTime

                    storeInfoResponse?.cashierShowPic = switchShowMenuImage.isSwitchOpen()

                    storeInfoResponse?.isNeedPickupCode = switchMealCode.isSwitchOpen()
                    storeInfoResponse?.fromPickupCode =
                        edtStartMealCode.text.toString().toIntOrNull()
                    storeInfoResponse?.toPickupCode = edtEndMealCode.text.toString().toIntOrNull()

                    storeInfoResponse?.isNeedInvoiceNumber = switchInvoiceCode.isSwitchOpen()
                    storeInfoResponse?.prefix = edtPrefix.text.toString()
                    storeInfoResponse?.dateFormat = dateFormat
                    storeInfoResponse?.invoiceSerialNumber =
                        edtInvoiceCode.text.toString().toIntOrNull()

                    storeInfoResponse?.ticketShowTaxInfo = switchTaxInfo.isSwitchOpen()
                    storeInfoResponse?.companyName = edtCompanyName.text.toString()
                    storeInfoResponse?.companyTaxNumber = edtCompanyVatTin.text.toString()
                    storeInfoResponse?.companyAddress = edtCompanyAddress.text.toString()
                    storeInfoResponse?.companyContactNumber = edtCompanyPhone.text.toString()
                    storeInfoResponse?.companyContactEmail = edtCompanyEmail.text.toString()

                    storeInfoResponse?.isAutoCheckoutTicket =
                        switchAutoCheckoutTicket.isSwitchOpen()

                    storeInfoResponse?.conversionRatio =
                        edtConversion.text.toString().toLongOrNull()

                    storeInfoResponse?.note = edtRemark.text.toString()



                    storeManagerViewModel.saveStoreInfo(storeInfoResponse!!)
                }
            }
        }
    }

    private fun hideKeyBoardAndRemoveFocus() {
        binding?.apply {
            hideKeyboard2()
            rootView.requestFocus()
        }
    }

    private fun formatInvoiceCue() {
        binding?.apply {
            if (edtPrefix.text.isNullOrEmpty() && dateFormat == null && edtInvoiceCode.text.isNullOrEmpty()) {
                tvInvoiceCue.text = getString(R.string.set_invoice_code_cue)
            } else {
                tvInvoiceCue.text = getString(
                    R.string.invoice_number_printing_format,
                    storeInfoResponse?.getInvoiceNumberDesc(
                        edtPrefix.text.toString(),
                        dateFormat,
                        edtInvoiceCode.text.toString().toIntOrNull()
                    )
                )
            }
        }
    }

    private fun initObserver() {
        storeManagerViewModel.uiStoreInfoState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                binding?.apply {
                    scrollView.isVisible = true
                    pbLoading.isVisible = false
                    llButtom.isVisible = true
                }
                storeInfoResponse = state.data
                initView()
            }
        }

        storeManagerViewModel.uiSaveState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_STORE, null))
                dismissCurrentDialog()
            } else if (state is ApiResponse.Error) {
                if (!state.message.isNullOrEmpty()) {
                    Toast.makeText(context, "${state.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }


    /**
     * 校验取餐码
     *
     *
     */
    private fun checkPickUpNo(): Boolean {
        var isPassCheck = false
        binding?.apply {
            if (switchMealCode.isSwitchOpen()) {
                textInputLayoutStartMealCode.error =
                    if (edtStartMealCode.text.isNullOrEmpty()) " " else ""

                textInputLayoutEndMealCode.error =
                    if (edtEndMealCode.text.isNullOrEmpty()) " " else ""


                if (!edtStartMealCode.text.isNullOrEmpty() && !edtEndMealCode.text.isNullOrEmpty()) {
                    val startCode = edtStartMealCode.text.toString().toIntOrNull() ?: 0
                    val endCode = edtEndMealCode.text.toString().toIntOrNull() ?: 0
                    if (endCode - startCode < 20) {
                        textInputLayoutStartMealCode.error = " "
                        textInputLayoutEndMealCode.error = " "
                    }
                }
                tvPickUpNoError.text = getString(R.string.please_input_pickup_no_error)
                if (!edtStartMealCode.text.isNullOrEmpty() && edtStartMealCode.text.toString()
                        .toInt() == 0
                ) {
                    textInputLayoutStartMealCode.error = " "
                    tvPickUpNoError.text = getString(R.string.please_input_pickup_no_error_2)
                }
                if (!edtEndMealCode.text.isNullOrEmpty() && edtEndMealCode.text.toString()
                        .toInt() == 0
                ) {
                    textInputLayoutEndMealCode.error = " "
                    tvPickUpNoError.text = getString(R.string.please_input_pickup_no_error_2)
                }

                tvPickUpNoError.isVisible =
                    !textInputLayoutStartMealCode.error.isNullOrEmpty() || !textInputLayoutEndMealCode.error.isNullOrEmpty()

                isPassCheck = !tvPickUpNoError.isVisible
            } else {
                isPassCheck = true
                tvPickUpNoError.isVisible = false
            }
        }

        return isPassCheck
    }

    /**
     * 校验发票
     *
     * @return
     */
    private fun checkInvoiceCode(): Boolean {
        var isPassCheck = false
        binding?.apply {
            if (switchInvoiceCode.isSwitchOpen()) {
                val number = edtInvoiceCode.text.toString().toIntOrNull() ?: 0
                Timber.e("edtInvoiceCode  $number")
                textInputLayoutInvoiceCode.error =
                    if (number == 0) getString(R.string.please_input_invoice_number) else ""
                isPassCheck = textInputLayoutInvoiceCode.error.isNullOrEmpty()
            } else {
                isPassCheck = true
            }
        }
        return isPassCheck
    }

    /**
     * 校验汇率
     *
     * @return
     */
    private fun checkExchangeRate(): Boolean {
        var isPassCheck = true
        binding?.apply {
            if (edtConversion.text.isNotEmpty() && (edtConversion.text.toString().toLongOrNull()
                    ?: 0) in 4000..4200
            ) {
                textInputLayoutConversion.error = null
//                ivConversionHistory.setPadding(
//                    DisplayUtils.dp2px(requireContext(), 20f),
//                    0,
//                    DisplayUtils.dp2px(requireContext(), 20f),
//                    0
//                )
                val lp = ivConversionHistory.layoutParams as ConstraintLayout.LayoutParams
                lp.marginEnd = DisplayUtils.dp2px(requireContext(), 0f)
                ivConversionHistory.layoutParams = lp
                isPassCheck = true
            } else {
                textInputLayoutConversion.error = getString(R.string.exchange_rate_input_error_cue)
//                ivConversionHistory.setPadding(
//                    DisplayUtils.dp2px(requireContext(), 20f),
//                    0,
//                    DisplayUtils.dp2px(requireContext(), 20f),
//                    0
//                )
                val lp = ivConversionHistory.layoutParams as ConstraintLayout.LayoutParams
                lp.marginEnd = DisplayUtils.dp2px(requireContext(), 20f)
                ivConversionHistory.layoutParams = lp
                isPassCheck = false
            }
        }
        return isPassCheck
    }


    private fun showPopupWindowDateType(anchorView: View) {
        activity?.hideKeyboard()

        val popupView = PopupStoreManagerDateformateBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.measuredWidth,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)

        binding?.arrow?.animate()?.rotation(180f)?.setDuration(200)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

        popupView.tvNone.setOnClickListener {
            dateFormat = null
            binding?.edtDateType?.setText(getString(R.string.none))
            formatInvoiceCue()
            popupWindow.dismiss()
        }
        popupView.tvYYYY.setOnClickListener {
            dateFormat = "1"
            binding?.edtDateType?.setText("yyyy")
            formatInvoiceCue()
            popupWindow.dismiss()
        }
        popupView.tvYY.setOnClickListener {
            dateFormat = "2"
            binding?.edtDateType?.setText("yy")
            formatInvoiceCue()
            popupWindow.dismiss()
        }

        popupView.tvYYYYMM.setOnClickListener {
            dateFormat = "3"
            binding?.edtDateType?.setText("yyyymm")
            formatInvoiceCue()
            popupWindow.dismiss()
        }

        popupView.tvYYMM.setOnClickListener {
            dateFormat = "4"
            binding?.edtDateType?.setText("yymm")
            formatInvoiceCue()
            popupWindow.dismiss()

        }
    }

    private fun showPopupWindowAcceptTime(anchorView: View) {
        activity?.hideKeyboard()

        val popupView = PopupAcceptTimeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.measuredWidth,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)

        binding?.arrow?.animate()?.rotation(180f)?.setDuration(200)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

        popupView.tvTen.setOnClickListener {
            acceptTime = 10
            binding?.edtAcceptTime?.setText(getString(R.string.accept_cancel_time, acceptTime))
            popupWindow.dismiss()
        }
        popupView.tvTwenty.setOnClickListener {
            acceptTime = 20
            binding?.edtAcceptTime?.setText(getString(R.string.accept_cancel_time, acceptTime))
            popupWindow.dismiss()
        }
        popupView.tvThirty.setOnClickListener {
            acceptTime = 30
            binding?.edtAcceptTime?.setText(getString(R.string.accept_cancel_time, acceptTime))
            popupWindow.dismiss()
        }

        popupView.tvSixty.setOnClickListener {
            acceptTime = 60
            binding?.edtAcceptTime?.setText(getString(R.string.accept_cancel_time, acceptTime))
            popupWindow.dismiss()
        }
    }

    private fun dealAutoPrintTickCue() {
        binding?.apply {
            if (switchAutoCheckoutTicket.isSwitchOpen()) {
                if (switchTaxInfo.isSwitchOpen() && switchInvoiceCode.isSwitchOpen()) {
                    switchAutoCheckoutTicket.setDesc(getString(R.string.auto_print_checkout_ticket_cue2))
                } else {
                    switchAutoCheckoutTicket.setDesc(getString(R.string.auto_print_checkout_ticket_cue1))
                }
            } else {
                switchAutoCheckoutTicket.setDesc("")
            }
        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * 0.45).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}

