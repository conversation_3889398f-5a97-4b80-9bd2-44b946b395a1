package com.metathought.food_order.casheir.ui.receiving_order


import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.alibaba.fastjson.JSON
import com.google.gson.Gson
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.AcceptOrderedStatusEnum
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsAcceptOrderChangeResponse
import com.metathought.food_order.casheir.databinding.DialogFilterTableListBinding
import com.metathought.food_order.casheir.databinding.FragmentReceivingOrderedBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getAcceptText
import com.metathought.food_order.casheir.extension.getAcceptTypeColor
import com.metathought.food_order.casheir.extension.getSourcePlatform
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.ui.adapter.AcceptOrderedAdapter
import com.metathought.food_order.casheir.ui.adapter.FilterTableAdapter
import com.metathought.food_order.casheir.ui.adapter.FloorAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.CancelOrderDialog
import com.metathought.food_order.casheir.ui.dialog.DiscountActivityDialog
import com.metathought.food_order.casheir.ui.dialog.PackPriceDetailDialog
import com.metathought.food_order.casheir.ui.dialog.ServiceFeeDetailDialog
import com.metathought.food_order.casheir.ui.ordered.OrderedFragment
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TimeUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import java.math.BigDecimal
import java.util.Timer
import java.util.TimerTask


class ReceivingOrderedFragment : BaseFragment() {

    companion object {
        fun newInstance() = ReceivingOrderedFragment()

    }

    private var popupViewTable: DialogFilterTableListBinding? = null

    private val viewModel: ReceivingOrderedViewModel by viewModels()

    private var _binding: FragmentReceivingOrderedBinding? = null
    private var orderedAdapter: AcceptOrderedAdapter? = null


    private var orderedScreen: SecondaryScreenUI? = null
    private val binding get() = _binding
    private var orderedInfoAdapter: OrderedInfoAdapter? = null
    private var selectedValue = AcceptOrderedStatusEnum.WAIT_ACCEPT.id
    private var selectedTableItem = ArrayList<OrderedTableListItem>()
    private var totalPrice: Long = 0
    private var totalVipPrice: BigDecimal = BigDecimal.ZERO

    private var filterTableAdapter: FilterTableAdapter? = null
    private var floorAdapter: FloorAdapter? = null


    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                viewModel.getOrderedList(
                    true,
                    selectedValue,
                    keyword = edtSearch.getSearchContent(),
                )
            }
        } catch (e: Exception) {

        }

    }

    private fun postSearch(duration: Int = 500) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentReceivingOrderedBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initEventBus()
        initSecondary()
        initView()
        initListener()
        initObserver()

    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        stopCountDown()
        super.onDestroy()
    }

    private fun initView() {


        context?.let { context ->
            binding?.apply {
                orderedAdapter = AcceptOrderedAdapter(requireContext(), arrayListOf()) { record ->
                    viewModel.getOrderedInfo(record, isRead = true)
                }
                orderListRecyclerView.adapter = orderedAdapter

                orderedInfoAdapter = OrderedInfoAdapter(arrayListOf())

                orderedInfoRecyclerView.adapter = orderedInfoAdapter


                viewModel.getOrderedList(true, selectedValue)
            }

        }
    }

    private fun initSecondary() {
        context?.let {
            orderedScreen = MyApplication.myAppInstance.orderedScreen
            getOrderedScreen()?.showDefault()
        }
    }

    private fun getOrderedScreen(): SecondaryScreenUI? {
        return orderedScreen
    }

    private fun initListener() {
        binding?.apply {
            layoutOrderInfo.apply {

                refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                    override fun onRefresh(refreshLayout: RefreshLayout) {
                        viewModel.getOrderedList(
                            true,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem
                        )
                    }

                    override fun onLoadMore(refreshLayout: RefreshLayout) {
                        viewModel.getOrderedList(
                            false,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem
                        )
                    }

                })

                dropdownTable.setOnClickListener {
                    arrowTable.animate().rotation(180f).setDuration(200)
                    dropdownTable.setBackgroundResource(R.drawable.background_spinner_top)
                    showPopupTableView(dropdownTable)
                }
                initPopuptableView()


                edtSearch.setTextChangedListenerCallBack {
                    resetFilter()
                    postSearch()
                }

                //cancel order
                btnCancelOrder.setOnClickListener {
                    if (viewModel.uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                        return@setOnClickListener
                    }

                    if (viewModel.uiInfoState.value?.orderedInfoResponse is ApiResponse.Loading) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        CancelOrderDialog.showDialog(
                            parentFragmentManager,
                            viewModel.currentOrderedInfo?.orderNo,
                            getString(R.string.sure_no_accept_this_order)
                        ) { reason, autoInStock ->
                            if (viewModel.currentOrderedInfo != null) {
                                viewModel.cancelAcceptOrder(
                                    viewModel.currentOrderedInfo!!.id!!,
                                    reason.trim()
                                )
                            }
                        }
                    }

                }

                tvClearFilter.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(2000) {
                        resetFilter()
                        edtSearch.setSearchContent("")
                        binding?.apply {
                            activity?.hideKeyboard(edtSearch.getEditText()!!)
                        }
                        viewModel.getOrderedList(true, selectedValue)
                    }
                }

                btnPackPriceCue.setOnClickListener {
                    val list = viewModel.currentOrderedInfo?.goods?.map {
                        it.orderedGoodsConvertToGoods()
                    }

                    if (list.isNullOrEmpty()) {
                        return@setOnClickListener
                    }

                    PackPriceDetailDialog.showDialog(
                        activity?.supportFragmentManager ?: parentFragmentManager,
                        list
                    )
                }

                btnServiceFeeCue.setOnClickListener {
                    val list = viewModel.currentOrderedInfo?.goods?.map {
                        it.setServiceChargePercentage(viewModel.currentOrderedInfo!!.getServicePercentage())
                        it.orderedGoodsConvertToGoods()
                    }

                    if (list.isNullOrEmpty()) {
                        return@setOnClickListener
                    }

                    ServiceFeeDetailDialog.showDialog(
                        activity?.supportFragmentManager ?: parentFragmentManager,
                        list,
                        viewModel.currentOrderedInfo
                    )
                }

                //待确认点击确认
                btnReceiving.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        context?.let {
                            viewModel.receivingOrder(it)
                        }
                    }

                }

                //
                btnDiscountActivityPriceCue.setOnClickListener {
                    DiscountActivityDialog.showDialog(
                        parentFragmentManager,
                        viewModel.currentOrderedInfo?.couponActivityList ?: listOf(),
                        tvVipPrice.isVisible
                    )
                }

            }
        }
    }


    private fun initObserver() {
        viewModel.acceptOrderState.observe(viewLifecycleOwner) {

            val currentFragment =
                parentFragmentManager.fragments.firstOrNull()
            if (currentFragment is MainDashboardFragment) {
                //记录一下该笔接单已打印
                PrinterDeviceHelper.setAcceptOrderPrinter(viewModel.currentOrderedInfo?.orderNo)
                currentFragment.replaceFragmentFromOtherFragment(
                    FeatureMenuEnum.ORDER_MANAGEMENT.id,
                    OrderedFragment(),
                    bundle = Bundle().apply {
                        if (viewModel.currentOrderedInfo?.orderNo != null) {
                            Timber.e("viewModel.currentOrderedInfo?.orderNo : ${viewModel.currentOrderedInfo?.orderNo}")
                            putString(
                                OrderedFragment.LOCAL_ORDER_ID,
                                viewModel.currentOrderedInfo?.orderNo
                            )
                            putBoolean(
                                OrderedFragment.IS_NEED_PRINT,
                                true
                            )
                            putParcelableArrayList(
                                OrderedFragment.ORDER_MORE,
                                it.goodList
                            )
                        }
                    }
                )
            }
        }


        viewModel.uiListState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    pbOrderedList.isVisible = it

                }
                if (it.showEnd) {
                    if (it.isRefresh != false) {
                        layoutMenu.setVisibleGone(false)
                        layoutPendingList.setVisibleGone(false)
                        orderedAdapter?.replaceData(arrayListOf())
                        layoutEmptyList.root.setVisibleGone(true)
                        layoutDetailInfo.setVisibleInvisible(false)
                        layoutEmptyDetail.root.setVisibleGone(true)
                        refreshLayout.finishRefresh()
                        btnCancelOrder.isVisible = false

                        refreshLayout.finishRefresh()
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    pbOrderedList.isGone = true
                    pbOrderedInfo.isVisible = false
                    refreshLayout.finishRefresh()
                    refreshLayout.finishLoadMore()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    pbOrderedList.isGone = true
                    layoutMenu.setVisibleGone(true)
                    layoutPendingList.setVisibleGone(true)
                    layoutEmptyList.root.setVisibleGone(false)
                    layoutEmptyDetail.root.setVisibleGone(false)
                    layoutDetailInfo.setVisibleGone(true)
                    if (it.isRefresh != false) {
                        orderedAdapter?.replaceData(response.records)
                        var indexOf = 0
                        if (viewModel.currentOrderedInfo?.id != null) {
                            //刷新完定位到当前的订单
                            indexOf = getSelectIndex(
                                id = viewModel.currentOrderedInfo?.id,
                                response
                            )
                        }

                        orderedAdapter?.setSelectFirst(indexOf)

                        if (!response.records.isNullOrEmpty()) {
                            val record = response.records[indexOf]
                            context?.let { context ->
                                viewModel.getOrderedInfo(
                                    record,
                                )
                            }
                        }
                        refreshLayout.finishRefresh()
                    } else {
                        orderedAdapter?.addData(response.records)
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }

        viewModel.uiInfoState.observe(viewLifecycleOwner) { state ->
            binding?.apply {
                state.orderedInfoResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
                            pbOrderedInfo.isVisible = true
                            pbOrderedInfo2.isVisible = true
                        }

                        is ApiResponse.Error -> {
                            totalPrice = 0
                            totalVipPrice = BigDecimal.ZERO
                            pbOrderedInfo.isVisible = false
                            pbOrderedInfo2.isVisible = false
                            showToast(it.message ?: "")
                            orderedInfoAdapter?.replaceData(arrayListOf())
                            layoutMenu.setVisibleGone(false)
                            layoutPendingList.setVisibleGone(false)
                            layoutEmptyDetail.root.setVisibleGone(true)
                            layoutDetailInfo.setVisibleGone(false)
                        }

                        is ApiResponse.Success -> {
                            layoutMenu.setVisibleGone(true)
                            layoutPendingList.setVisibleGone(true)
                            layoutEmptyDetail.root.setVisibleGone(false)
                            layoutDetailInfo.setVisibleGone(true)
                            pbOrderedInfo.isGone = true
                            pbOrderedInfo2.isGone = true
                            //second screen-副屏

                            setOrderInfo(it.data)
                            orderedInfoAdapter?.replaceData(
                                it.data.goods as ArrayList<BaseOrderGoods>,
                                it.data.refundGoodsJson,
                                it.data
                            )
                        }

                        else -> {

                        }
                    }
                }

                state.record?.let {
                    orderedAdapter?.updateStatus(it)
                }

            }
        }

        viewModel.uiInfoRequestState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.baseBooleanResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
                            pbOrderedInfo2.isVisible = true
                            Timber.e("取消按钮禁用111")
                            btnCancelOrder.isEnabled = false

                        }

                        is ApiResponse.Success -> {
                            pbOrderedInfo2.isVisible = false
                            btnCancelOrder.isEnabled = true

                        }

                        is ApiResponse.Error -> {
                            pbOrderedInfo2.isVisible = false
                            showToast(it.message ?: "")
                            Timber.e("取消按钮禁用222")
                            btnCancelOrder.isEnabled = true
                        }

                        else -> {

                        }
                    }
                }
            }
        }

        viewModel.removeOrderState.observe(viewLifecycleOwner) { state ->
            /**
             * 删除订单的时候copy 一个list 出来修改，防止定时器循环的时候有问题
             */
            val list = mutableListOf<OrderedRecord>()
            list.addAll(orderedAdapter?.list?.map { it.copy() }?.toList() ?: listOf())
            val index = list.indexOfFirst { it.id == state.id }
            if (index != -1) {
                list.removeAt(index)
                orderedAdapter?.replaceData(ArrayList(list))
                binding?.apply {
                    if (list.isEmpty()) {
                        layoutMenu.setVisibleGone(false)
                        layoutPendingList.setVisibleGone(false)
                        orderedAdapter?.replaceData(arrayListOf())
                        layoutEmptyList.root.setVisibleGone(true)
                        layoutDetailInfo.setVisibleInvisible(false)
                        layoutEmptyDetail.root.setVisibleGone(true)
                        refreshLayout.finishRefresh()
                        btnCancelOrder.isVisible = false
                        refreshLayout.finishRefresh()
                    } else {
                        val record = if (index >= list.size) {
                            //如果旧数据的索引大于删除后的列表长度
                            orderedAdapter?.setSelectFirst(list.size - 1)
                        } else {
                            orderedAdapter?.setSelectFirst(index)
                        }
                        orderedAdapter?.notifyDataSetChanged()
                        if (record != null) {
                            viewModel.getOrderedInfo(record)
                        }

                    }

                }
            }

        }

        viewModel.uiSocketOrderedState.observe(viewLifecycleOwner) {
            it?.let {
                orderedAdapter?.updateRecord(it)
            }
        }
    }

    private fun FragmentReceivingOrderedBinding.getSelectIndex(
        id: String? = null,
        response: OrderedTotalResponse
    ): Int {
        var indexOf = 0
        if (!id.isNullOrEmpty()) {
            indexOf =
                response.records?.indexOfFirst { it.id == id } ?: 0
            if (indexOf != -1) {
                orderListRecyclerView.postDelayed({
                    orderListRecyclerView.scrollToPosition(indexOf)
                }, 500)
            }
            if (indexOf == -1) {
                indexOf = 0
            }
        }
        return indexOf
    }

    private fun FragmentReceivingOrderedBinding.setOrderInfo(ordered: OrderedInfoResponse?) {
        ordered?.let {
            layoutOrderInfo.apply {

                llOrderTable.isVisible = true
                tvOrderTable.text = it.tableName
                if (it.sourcePlatform == SourcePlatformEnum.Kiosk.id) {
                    tvOrderTable.text = getString(R.string.kiosk).uppercase()
                }

                llPickUpNo.isVisible = !it.pickupCode.isNullOrEmpty()
                tvPickUpNo.text = it.pickupCode

//                tvCustomerTitle.isVisible =
//                    !it.customerInfoVo?.name.isNullOrEmpty() || !it.customerInfoVo?.mobile.isNullOrEmpty()
//                llCustomerName.isVisible = !it.customerInfoVo?.name.isNullOrEmpty()
//                llCustomerPhone.isVisible = !it.customerInfoVo?.mobile.isNullOrEmpty()
//                tvCustomerName.text = it.customerInfoVo?.name
//                tvCustomerPhone.text = it.customerInfoVo?.getMobilePhoneDisplay()

                ivEditCustomer.isVisible = false

                llCustomerName.isVisible = it.getLastCustomerName().isNotEmpty()
                tvCustomerName.text = it.getLastCustomerName()

                llCustomerPhone.isVisible = it.getShowPhone().isNotEmpty()
                tvCustomerPhone.text = it.getShowPhone()

                llPeople.isVisible = !(it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString()
                    .isNullOrEmpty() && (it.customerInfoVo?.diningNumber ?: it.peopleNum) != 0
                tvCustomerNum.text =
                    (it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString() ?: ""

                llCustomerTitle.isVisible = llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible



                tvOrderNo.text = it.orderNo

                llInvoiceNumber.isVisible = !it.invoiceNumber.isNullOrEmpty()
                tvInvoiceNumber.text = it.invoiceNumber

                tvOrderTime.text = it.createTime?.formatDate()
                llDiningTime.isVisible = !it.getDingTime().isNullOrEmpty()
                tvDiningTime.text = it.getDingTime()?.formatDate()
                context?.let { context ->
                    tvOrderType.text = it.getDiningStyleStr(context)
                    llOrderStatus.isVisible = true
                    tvOrderStatus.text = it.acceptStatus?.getAcceptText(context)
                    tvOrderStatus.setTextColor(
                        ContextCompat.getColor(
                            context,
                            it.acceptStatus?.getAcceptTypeColor() ?: R.color.ordered_cancel_color
                        )
                    )


                    tvOrderBy.text = it.sourcePlatform?.getSourcePlatform(context)
                }
//                llPeople.isVisible = !it.customerInfoVo?.diningNumber?.toString()
//                    .isNullOrEmpty() && it.customerInfoVo?.diningNumber != 0
//                tvCustomerNum.text = it.customerInfoVo?.diningNumber?.toString() ?: ""


                tvOrderRemark.text =
                    if (it.note.isNullOrEmpty()) getString(R.string.none) else it.getFinalNote()

//                Timber.e("it.isShowVipDiscountIcon(): ${it.isShowVipDiscountIcon()}")
//                //减免折扣相关
//                if (it.isShowVipDiscountIcon()) {
//                    //支付成前切实vip减免折扣类型显示vip标志
//                    val endDrawable = ContextCompat.getDrawable(
//                        requireContext(),
//                        R.drawable.icon_vip
//                    )
//                    tvDiscountAmount.setCompoundDrawablesWithIntrinsicBounds(
//                        endDrawable,
//                        null,
//                        null,
//                        null
//                    )
//                    tvDiscount.setCompoundDrawablesWithIntrinsicBounds(
//                        endDrawable,
//                        null,
//                        null,
//                        null
//                    )
//                } else {
//                    tvDiscountAmount.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
//                    tvDiscount.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
//                }


                /**
                 * 接单的外层价格都没维护 所以取price里面的
                 */

                val subTotalPrice = it.price?.getSubTotalToLong() ?: 0L
                val serviceFeePrice = it.price?.getTotalServiceChargeAmountToLong() ?: 0L
                val vipServiceFeePrice = it.vipPrice?.getTotalServiceChargeAmountToLong() ?: 0L
                val vatPrice = it.price?.getVatToLong() ?: 0L
                val vipVatPrice = it.vipPrice?.getVatToLong() ?: 0L

                val totalPrice = it.price?.getTotalToLong() ?: 0L
                val totalVipPrice = it.vipPrice?.getTotalToLong() ?: 0L


                //=======优惠活动金额================
                if (it.couponActivityList.isNullOrEmpty()) {
                    llDiscountActivity.isVisible = false
                } else {
                    btnDiscountActivityPriceCue.isVisible = true
                    if (it.couponActivityList?.size == 1) {
                        tvDiscountActivityTitle.text =
                            it.couponActivityList?.firstOrNull()?.activityLabelName
                    } else {
                        tvDiscountActivityTitle.text = getString(R.string.discount_activity)
                    }
                    val discountActivityUnWeightList =
                        it.couponActivityList?.filter { it.weightMark == true } ?: listOf()
                    if (discountActivityUnWeightList.isNullOrEmpty()) {
                        tvDiscountActivityAmount.text = "-${
                            it.couponActivityList?.fold(0L) { acc, element ->
                                acc + (element.activityCouponAmount ?: 0)
                            }?.priceFormatTwoDigitZero2()
                        }"
                    } else {
                        tvDiscountActivityAmount.text = getString(R.string.to_be_confirmed)
                    }
                    llDiscountActivity.isVisible = true
                }


                //==============================

                Timber.e("totalVipPrice1111  $totalVipPrice")

                tvPackingAmount.text = "${it.getPackFee()?.priceFormatTwoDigitZero2()}"
                llPackPrice.isVisible = it.getPackFee() > 0


                //小计
                tvSubtotal.text = subTotalPrice.priceFormatTwoDigitZero2()

                //增值税
                tvVat.text = vatPrice.priceFormatTwoDigitZero2()

                Timber.e("serviceFeePrice  $serviceFeePrice")
                //服务费
                tvServiceFee.text = serviceFeePrice.priceFormatTwoDigitZero2()


                //总计2
                tvTotalPrice2.text = totalPrice.priceFormatTwoDigitZero2()
                tvTotalKhrPrice2.isVisible = false

                //总计1
                tvTotalPrice.text = totalPrice.priceFormatTwoDigitZero2()
                tvTotalKhrPrice.isVisible = false

                //会员价
                tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()

                val isShowVip = it.isShowVipPrice()
                tvVipPrice.isVisible = isShowVip


                if (isShowVip) {
                    llServiceFee.isVisible =
                        serviceFeePrice > 0 || vipServiceFeePrice > 0
                    llVat.isVisible = vatPrice > 0 || vipVatPrice > 0

                    if (it.isAfterPayStatus()) {
                        //如果是支付后的状态
                        llServiceFee.isVisible = serviceFeePrice > 0
                        llVat.isVisible = vatPrice > 0
                    }
                } else {
                    tvVipPrice.isVisible = false
                    llServiceFee.isVisible = serviceFeePrice > 0
                    llVat.isVisible = vatPrice > 0
                }


                llCoupon.isVisible = false
                llDiscountAmount.isVisible = false

                //normal
//                btnEdit.isVisible = false
                Timber.e("it.isCanReceiveOrder() : ${it.isCanReceiveOrder()}")
                btnReceiving.isVisible = it.isCanReceiveOrder()
                btnCancelOrder.isVisible = it.isCanReceiveOrder()
                tvAutoCancelTime.text =
                    getString(R.string.receiving_orders).plus(
                        " ${
                            TimeUtils.convertMillisecondsToTime(
                                it.expireCountDown ?: 0L
                            )
                        }"
                    )
                llActualReceiveAmount.isGone = true
                llVatRefundAmount.isGone = true
                llPartialRefundAmount.isGone = true
                llTotalPrice2.isGone = true
                llRefundTime.isGone = true
                llPaymentTime.isGone = it.payTime.isNullOrEmpty()
                tvPaymentTime.text = it.payTime?.formatDate()

                llTotalPrice.isVisible = true
                llRefundAmount.isGone = true
                llCancelTime.isGone = true
                llCancelReason.isGone = true
                llPartialRefundPackFee.isVisible = false
                llPartialRefundServiceFee.isVisible = false

                showTobeProcess(it)
            }
        }
    }

    private fun showTobeProcess(ordered: OrderedInfoResponse?): Boolean {
        //是否有待定价商品
        val isHasNeedProcess = ordered?.isHasNeedProcess() ?: false
        ordered?.let {
            binding?.apply {
                var unProcessNum = 0
                var unProcessServiceWhiteGoods = 0
                if (it.needShowUnProcessState()) {
                    it.goods?.forEach { good ->
                        if (!good.isHasProcessed()) {
                            unProcessNum += 1
                            if (good.serviceChargeWhitelisting == true) {
                                unProcessServiceWhiteGoods += 1
                            }
                        }
                    }
                }

                if (isHasNeedProcess) {
                    layoutOrderInfo.apply {
                        tvSubtotal.text = getString(R.string.to_be_confirmed)
                        tvTotalPrice.text = getString(R.string.to_be_confirmed)

                        tvDiscount.text = getString(R.string.to_be_confirmed)
                        tvDiscountAmount.text = getString(R.string.to_be_confirmed)

                        tvVat.text = getString(R.string.to_be_confirmed)
                        llVat.isVisible =
                            (ordered.price?.vatPercentage ?: BigDecimal(0)) > BigDecimal(0)
                        tvTotalKhrPrice.isVisible = false
                        tvVipPrice.isVisible = false


                        if (!ordered.isTakeAway()) {
                            if (unProcessNum > unProcessServiceWhiteGoods && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)) {
                                tvServiceFee.text = getString(R.string.to_be_confirmed)
                                llServiceFee.isVisible = true
                            }
                        }
                    }
                }
            }
        }
        return isHasNeedProcess
    }


    private fun resetFilter() {
        binding?.apply {
            tvTable.text = getString(R.string.all_table)
            selectedValue = AcceptOrderedStatusEnum.WAIT_ACCEPT.id
            selectedTableItem.clear()
            cardViewFilterTable.isVisible = false
        }
    }


    private fun initObserverTable() {

        viewModel.uiTableState.observe(viewLifecycleOwner) { it ->
            it.response?.let { it ->
                when (it) {
                    is ApiResponse.Loading -> {
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        dismissProgress()
                        popupViewTable?.apply {
                            if (it.data.isEmpty()) {
                                btnConfirm.isEnabled = false
                            }
                            val floorArray: ArrayList<String> = arrayListOf()
                            floorArray.add(getString(R.string.all))
                            it.data.sortedBy { it.id }.forEach { res ->
                                if (!floorArray.contains(res.location)) {
                                    res.location?.let { it1 -> floorArray.add(it1) }
                                }
                            }
                            floorAdapter = context?.let { it1 ->
                                FloorAdapter(floorArray, it1, 0) { floor ->
                                    viewModel.filterFloor(
                                        if (floor == 0) "" else floorArray[floor],
                                    )
                                }
                            }
                            recyclerViewPage?.adapter = floorAdapter
                        }
                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
                        showToast(it.message ?: "")
                    }

                    else -> {

                    }
                }
            }

            it.orderedTableItem?.let {
                selectedTableItem = arrayListOf(it)
                binding?.apply {
                    if (selectedTableItem.isEmpty()) {
                        tvTable.text = getString(R.string.all_table)
                    } else {
                        tvTable.text = selectedTableItem.first().name
                        tvCount.text = "+${selectedTableItem.size - 1}"
                    }
                }
                binding?.apply {
                    viewModel.getOrderedList(
                        true,
                        selectedValue,
                        keyword = edtSearch.getSearchContent(),
                        listTable = selectedTableItem
                    )
                }


            }

        }
        viewModel.filteredTableResponse.observe(viewLifecycleOwner) {
            filterTableAdapter?.updateItems(it)
            popupViewTable?.btnConfirm?.text =
                getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
        }
    }

    private var tablePopupWindow: PopupWindow? = null

    private fun initPopuptableView() {
        initObserverTable()
        popupViewTable = DialogFilterTableListBinding.inflate(layoutInflater)
        tablePopupWindow = PopupWindow(
            popupViewTable?.root,
            (Resources.getSystem().displayMetrics.widthPixels / 2.2).toInt(),
            (Resources.getSystem().displayMetrics.heightPixels / 1.5).toInt(),
            true
        )
        tablePopupWindow?.elevation = 20f
        tablePopupWindow?.animationStyle = R.style.PopupAnimation
        viewModel.getOrderedTableList()
        val tableList = OrderedTableListResponse()
        popupViewTable?.apply {
            filterTableAdapter =
                context?.let {
                    FilterTableAdapter(
                        tableList,
                        it,
                        selectedTableItem
                    ) {
                        btnConfirm.text =
                            getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
                    }
                }
            recyclerViewTable.adapter = filterTableAdapter
            btnConfirm.text =
                getString(R.string.apply_filter).plus(" (${selectedTableItem.size})")
            btnConfirm.setOnClickListener {
                selectedTableItem = ArrayList(filterTableAdapter?.getSelectedArrayList())
                binding?.apply {
                    cardViewFilterTable.setVisibleGone(selectedTableItem.size > 1)
                    if (selectedTableItem.isEmpty()) {
                        tvTable.text = getString(R.string.all_table)
                    } else {
                        tvTable.text = selectedTableItem.first().name
                        tvCount.text = "+${selectedTableItem.size - 1}"
                    }
                }
                binding?.apply {
                    viewModel.getOrderedList(
                        true, selectedValue,
                        keyword = edtSearch.getSearchContent(), listTable = selectedTableItem
                    )
                }

                tablePopupWindow?.dismiss()
            }

            btnCancel.setOnClickListener {
                filterTableAdapter?.resetSelect()
                btnConfirm.text =
                    getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
            }

        }
    }

    private fun showPopupTableView(anchorView: View) {
        binding?.apply {
            activity?.hideKeyboard(edtSearch.getEditText()!!)
        }
        filterTableAdapter?.updateItemCheck(selectedTableItem)
        popupViewTable?.apply {
            btnConfirm.text =
                getString(R.string.apply_filter).plus(" (${selectedTableItem.size})")
        }
        tablePopupWindow?.showAsDropDown(anchorView)
        tablePopupWindow?.setOnDismissListener {
            binding?.arrowTable?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

    }

    override fun onResume() {
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        startCountDown()
        super.onResume()
    }


    private fun wsHandel(event: WebSocket.Event) {
        when (event) {
            is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
            is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
            is WebSocket.Event.OnConnectionClosing -> Timber.e(
                "closing connection.."
            )

            is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
            is WebSocket.Event.OnMessageReceived -> {
                if (event.message is Message.Text) {
                    if ((event.message as Message.Text).value == "ping")
//                        viewModel.testingWebsocketSendMessage()
                    else {
                        try {
                            Timber.e("ws ==> ${(event.message as Message.Text).value}")
                            val socketModel = JSON.parseObject(
                                (event.message as Message.Text).value,
                                SocketModel::class.java
                            )
                            socketModel.cmd?.let { cmd ->
                                when (cmd) {
                                    WsCommand.PAY_ORDER -> {
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(),
                                                OrderedInfoResponse::class.java
                                            )

                                            data?.apply {
                                                if (orderNo != null) {
                                                    binding?.apply {
                                                        //待接单的时候原单支付了
                                                        if (orderedAdapter?.hasModelByOrderNo(
                                                                orderNo
                                                            ) != null
                                                        ) {
                                                            EventBus.getDefault().post(
                                                                SimpleEvent(
                                                                    SimpleEventType.UPDATE_ORDER_LIST,
                                                                    null
                                                                )
                                                            )
                                                        }
                                                    }
                                                }
                                            }

                                        }
                                    }
                                    /**
                                     * 收到取消接单/接单 事件 从列表移出该订单
                                     */
                                    WsCommand.CANCEL_ACCEPT_ORDER, WsCommand.ACCEPT_ORDER, WsCommand.ACCEPT_ORDER_UPDATE -> {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = JSON.parseObject(
                                                    it.toJson(),
                                                    WsAcceptOrderChangeResponse::class.java
                                                )
                                                handlerAfterOrderCancel(data.orderNo.toString())
                                                orderedAdapter?.hasModel(data.id.toString())?.run {
                                                    //延迟500调用 防止服务器信息还没变更
//                                                    requireActivity().HandlerDelayed({
                                                    viewModel.getOrderedList(
                                                        true,
                                                        selectedValue,
                                                        keyword = edtSearch.getSearchContent(),
                                                        listTable = selectedTableItem
                                                    )
//                                                    }, 500)
                                                }
                                            }
                                        }
                                    }

                                    WsCommand.NEW_ACCEPT_ORDER -> {
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(),
                                                OrderedInfoResponse::class.java
                                            )

                                            data?.apply {
                                                if (id != null) {
                                                    binding?.apply {
                                                        //没有过滤的时候才插入
                                                        if (selectedValue == AcceptOrderedStatusEnum.WAIT_ACCEPT.id && selectedTableItem.isEmpty() && edtSearch.getSearchContent()
                                                                .isNullOrEmpty()
                                                        ) {
                                                            //用户端下单 下发订单信息 插入列表
                                                            if (orderedAdapter?.hasModel(id) == null) {
                                                                //如果列表为空直接调用一次列表接口
                                                                if (orderedAdapter?.list.isNullOrEmpty()) {
                                                                    viewModel.getOrderedList(
                                                                        true,
                                                                        selectedValue,
                                                                        keyword = edtSearch.getSearchContent(),
                                                                        listTable = selectedTableItem
                                                                    )
                                                                } else {
                                                                    val isAtTop =
                                                                        !orderListRecyclerView.canScrollVertically(
                                                                            -1
                                                                        )
                                                                    binding?.apply {
                                                                        orderedAdapter?.insetOrderToTop(
                                                                            toOrderedRecord()
                                                                        )
                                                                        //判断一下列表是否在顶部，在顶部就滚动到最上面
                                                                        if (isAtTop) {
                                                                            orderListRecyclerView.postDelayed(
                                                                                {
                                                                                    orderListRecyclerView.smoothScrollToPosition(
                                                                                        0
                                                                                    )
                                                                                },
                                                                                200
                                                                            )

                                                                        }
                                                                    }
                                                                }
                                                            } else {
                                                                Timber.e("不插入数据")
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    else -> {}
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
    }


    private var timer: Timer? = null

    /**
     * 开始倒计时
     *
     */
    private fun startCountDown() {
        stopCountDown()
        val task = object : TimerTask() {
            override fun run() {
                /**
                 * 刷新列表上面的倒计时
                 */
                //是否有过期订单
                var isHasExpiredOrder = false
                requireActivity().runOnUiThread {
                    orderedAdapter?.list?.forEachIndexed { index, orderedRecord ->
                        orderedRecord.expireCountDown = (orderedRecord.expireCountDown ?: 0) - 1000

                        if ((orderedRecord.expireCountDown ?: 0) <= 0) {
                            isHasExpiredOrder = true
                            orderedRecord.expireCountDown = 0
                        }

                        orderedAdapter?.notifyItemChanged(index, "countDown")
                        /**
                         * 把当前订单页刷新一下
                         */
                        binding?.apply {
                            layoutDetailInfo.apply {
                                if (viewModel.currentOrderedInfo?.id == orderedRecord.id) {
                                    if (orderedRecord.isCanReceiveOrder()
                                    ) {
                                        tvAutoCancelTime.text =
                                            getString(R.string.receiving_orders).plus(
                                                " ${
                                                    TimeUtils.convertMillisecondsToTime(
                                                        orderedRecord.expireCountDown ?: 0L
                                                    )
                                                }"
                                            )

                                    } else {
                                        btnCancelOrder.isVisible = false
                                        btnReceiving.isVisible = false
                                        handlerAfterOrderCancel(viewModel.currentOrderedInfo?.orderNo)
                                    }
                                }
                            }
                        }
                    }
                }

                if (isHasExpiredOrder) {
                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_ORDER_LIST, null))
                }
            }
        }
        timer = Timer()
        timer?.schedule(task, 0, 1000)
    }

    /**
     * 停止倒计时
     *
     */
    private fun stopCountDown() {
        if (timer != null) {
            timer?.cancel()
            timer = null
        }
    }


    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    /**
     * 当前订单取消后的一些处理
     *
     * @param orderNo
     */
    private fun handlerAfterOrderCancel(orderNo: String?) {

        try {
            /**
             * 关闭取消订单弹窗
             */
            if (isAdded) {
                val currentCancelOrderNo =
                    CancelOrderDialog.getCurrentCancelOrderDialog(
                        parentFragmentManager
                    )?.getCurrentOrderNo()


                Timber.e("关闭订单弹窗 $currentCancelOrderNo   $orderNo")

                if (currentCancelOrderNo == orderNo.toString()) {
                    CancelOrderDialog.dismissDialog(
                        parentFragmentManager
                    )
                }
            }
        } catch (e: Exception) {

        }

    }

    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
//        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {
            SimpleEventType.UPDATE_UNREAD_EVENT -> {
                requireActivity().runOnUiThread {

                }
            }


            SimpleEventType.UPDATE_ORDER_LIST -> {
                requireActivity().runOnUiThread {
                    binding?.apply {
//                        if (edtSearch.text.isNullOrEmpty()) {
                        viewModel.getOrderedList(
                            isRefresh = true,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem,
                        )
//                        } else {
//                            viewModel.getOrderedList(
//                                isRefresh = true,
//                                selectedValue,
//                                keyword = edtSearch.text.toString(),
//                            )
//                        }
                    }
                }
            }

            else -> {

            }

        }
    }

}