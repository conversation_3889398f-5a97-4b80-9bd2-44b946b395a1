package com.metathought.food_order.casheir.ui.logout

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.logout.CashierLogoutRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.login.LoginUserInfoResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class LogoutViewModel @Inject constructor(val repository: Repository) : ViewModel() {
    private val _logoutUiState = MutableLiveData<ApiResponse<ShiftReportPrint>>()
    val logoutUiState get() = _logoutUiState

    private val _logoutInfoState = MutableLiveData<ApiResponse<LoginUserInfoResponse>>()

    val logoutInfoState get() = _logoutInfoState
    fun logout(usd: BigDecimal, khr: BigDecimal) {
        val logoutRequest = CashierLogoutRequest(usdAmount = usd, khrAmount = khr)
        viewModelScope.launch {
            _logoutUiState.value = ApiResponse.Loading
            _logoutUiState.value = repository.putLogout(logoutRequest)
        }
    }

    fun logout() {
        val logoutRequest = CashierLogoutRequest(usdAmount =null, khrAmount =null)
        viewModelScope.launch {
            _logoutUiState.value = ApiResponse.Loading
            _logoutUiState.value = repository.putLogout(logoutRequest)
        }
    }

    fun getLogoutInfo() {
        viewModelScope.launch {
            _logoutInfoState.value = ApiResponse.Loading
            _logoutInfoState.value = repository.getLoginUserInfo()
        }
    }
}