package com.metathought.food_order.casheir.ui.logout


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.LoginUserInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.databinding.DialogLogoutBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.navigateWithAnim
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.UnReadAndUnPrintHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber


@AndroidEntryPoint
class LogoutDialog : DialogFragment() {
    private var binding: DialogLogoutBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    private val viewModel: LogoutViewModel by viewModels()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogLogoutBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.logoutInfoState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbLogout?.isVisible = true
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLogout.isVisible = false
                        layoutWrap.isVisible = true
                    }
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    setData(it.data)
                }
            }
        }
        viewModel.logoutUiState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbLogout?.isVisible = true
                    binding?.btnLogout?.setEnable(false)
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLogout.isVisible = false
                        btnLogout.setEnable(true)
                    }
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    //如果输入不为null  打印交接班报表
                    moveToLogin()
                }
            }
        }
    }

    private fun setData(data: LoginUserInfoResponse) {
        binding?.apply {
            pbLogout.isVisible = false
            layoutWrap.isVisible = true
            tvLoginTime.text = data.loginTime?.formatDate()
            tvStaffName.text = data.userName
            tvTotalOrder.text = data.totalOrderNumber.toString()
            tvUnpaidOrder.text = data.unpaidOrderNumber.toString()
        }
    }


    private fun initData() {
        val content = arguments?.getString(CONTENT)
//        binding?.apply {
//            btnLogout.setEnable(false)
//            edtUSD.setDecimalFormat()
//            edtKhmer.setMaxLength(12)
//        }
        viewModel.getLogoutInfo()
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }
            btnLogout.setOnClickListener {
//                viewModel.logout(
//                    edtUSD.text.toString().toBigDecimal(),
//                    edtKhmer.text.toString().toBigDecimal()
//                )
                viewModel.logout()
            }
//            edtKhmer.addTextChangedListener { s ->
//                s?.let {
//                    if (it.isNotEmpty() && it.toString().first() == '0') {
//                        if (it.length > 1) {
//                            s.replace(0, 2, it.toString()[1].toString())
//                        }
//                    }
//                }
//                checkLogoutEnable()
//            }
//            edtUSD.addTextChangedListener { s ->
//                s?.let {
//                    if (it.isNotEmpty() && it.toString().first() == '.') {
//                        s.replace(0, 1, "0.")
//                    }
//                    if (it.isNotEmpty() && it.toString().first() == '0') {
//                        if (it.length > 1 && it.toString()[1] != '.') {
//                            s.replace(0, 2, it.toString()[1].toString())
//                        }
//                    }
//                }
//                checkLogoutEnable()
//            }
//            edtKhmer.setOnEditorActionListener { _, actionId, _ ->
//                if (actionId == EditorInfo.IME_ACTION_DONE) {
//                    if (btnLogout.isEnabled) {
//                        viewModel.logout(
//                            edtUSD.text.toString().toBigDecimal(),
//                            edtKhmer.text.toString().toBigDecimal()
//                        )
//                    }
//                }
//                return@setOnEditorActionListener false
//            }
            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

//    private fun checkLogoutEnable() {
//        binding?.apply {
//            btnLogout.setEnable(
//                edtUSD.text.toString().isNotEmpty() && edtKhmer.text.toString().isNotEmpty()
//            )
//        }
//    }

    companion object {
        private const val LOGOUT_DIALOG = "LOGOUT_DIALOG"
        private const val CONTENT = "CONTENT"

        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,

            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener, content)
            fragment.show(fragmentManager, LOGOUT_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG) as? LogoutDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
            content: String? = null
        ): LogoutDialog {
            val args = Bundle()
            args.putString(CONTENT, content)
            val fragment = LogoutDialog()
            fragment.positiveButtonListener = iAgreeListener
            fragment.arguments = args
            return fragment
        }
    }

    private fun moveToLogin() {
        lifecycleScope.launch {
            context?.let { it1 ->
                PreferenceDataStoreHelper.getInstance(it1).apply {
                    val userLoginResponse = Gson().fromJson(
                        getFirstPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            ""
                        ), UserLoginResponse::class.java
                    )
                    userLoginResponse.token = ""
                    this.putPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        userLoginResponse.toJson()
                    )
                    MainDashboardFragment.CURRENT_USER = null
                    UnReadAndUnPrintHelper.clear()
                    findNavController().navigateWithAnim(
                        R.id.action_mainDashboardFragment_to_loginFragment,
                        popupToId = R.id.mainDashboardFragment
                    )
                    dismissAllowingStateLoss()
                }
            }
        }
    }

}
