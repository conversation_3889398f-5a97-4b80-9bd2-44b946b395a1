package com.metathought.food_order.casheir.ui.order.change_num

import android.content.Context
import android.hardware.display.DisplayManager
import android.text.Editable
import android.text.TextWatcher
import android.util.DisplayMetrics
import android.view.Display
import android.widget.Toast
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.animator.PopupAnimator
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.lxj.xpopup.util.KeyboardUtils
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.databinding.DialogEditGoodNumBinding
import com.metathought.food_order.casheir.helper.XpopHelper
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.dialog.BaseCenterDialog
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


@AndroidEntryPoint
class EditGoodNumDialog  //注意：自定义弹窗本质是一个自定义View，但是只需重写一个参数的构造，其他的不要重写，所有的自定义弹窗都是这样。
    (private val act: Context?) : BaseCenterDialog(act!!) {

    private var binding: DialogEditGoodNumBinding? = null

    /**
     * 当前数量
     */
    private var num: Int? = null
    private var onConfirmClickListener: ((String) -> Unit)? = null

    /**
     * 剩余可加数量
     */
    private var remainingNum: Int? = null

    //是否可以输入0
    private var zeroEnable: Boolean = true

    //是否售罄
    private var isSoldOut: Boolean? = true

    //是否预定
    private var isPreOrder: Boolean = false

    fun showDialog(
        num: Int,
        remainingNum: Int,
        zeroEnable: Boolean = true,
        isSoldOut: Boolean? = false,
        isPreOrder: Boolean? = false,
        onConfirmClickListener: ((String) -> Unit),
    ): EditGoodNumDialog {
        this.num = num
        this.remainingNum = remainingNum
        this.zeroEnable = zeroEnable
        this.isSoldOut = isSoldOut
        this.isPreOrder = isPreOrder ?: false
        this.onConfirmClickListener = onConfirmClickListener
        Timber.e("isSoldOut  ${isSoldOut}")
        XPopup.Builder(act)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }

                override fun onDismiss(popupView: BasePopupView?) {
                    XpopHelper.removeToMap(popupView)
                    super.onDismiss(popupView)
                }
            })
            .autoOpenSoftInput(isSoldOut == false)
            .autoFocusEditText(isSoldOut == false)
            .asCustom(this)
            .show()
        XpopHelper.addToMap(this)
        return this
    }


    // 返回自定义弹窗的布局
    override fun getImplLayoutId(): Int {
        return R.layout.dialog_edit_good_num
    }

    // 执行初始化操作，比如：findView，设置点击，或者任何你弹窗内的业务逻辑
    override fun onCreate() {
        super.onCreate()
        binding = DialogEditGoodNumBinding.bind(popupImplView)
        binding?.apply {

            if (isSoldOut == true) {
                edtNum.setText("0")
                edtNum.setSelection(num.toString().length)
                edtNum.isEnabled = false
                edtNum.postDelayed({

                    KeyboardUtils.hideSoftInput(edtNum)
                    edtNum.clearFocus()
                }, 200)


            } else {
                num?.let {
                    binding?.apply {
                        if ((num ?: 0) > GOOD_MAX_NUM) {
                            num = GOOD_MAX_NUM
                        }
                        edtNum.setText("$num")
                        edtNum.setSelection(num.toString().length)
                    }
                }
            }


            btnCancel.setOnClickListener {
                KeyboardUtils.hideSoftInput(binding?.edtNum)
                dismiss() // 关闭弹窗
            }

            btnConfirm.setOnClickListener {
                if (isSoldOut == true) {
                    onConfirmClickListener?.invoke(edtNum.text.toString())
                } else {
                    if (remainingNum != null && edtNum.text?.isNotEmpty() == true && edtNum.text.toString()
                            .toInt() > remainingNum!!
                    ) {
                        if (isPreOrder) {
                            Toast.makeText(
                                context,
                                context.getString(
                                    R.string.you_have_reached_the_maximum_quantity_limit_of,
                                    remainingNum ?: 0
                                ), Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            Toast.makeText(
                                context,
                                context.getString(
                                    R.string.max_num_to_add_cart,
                                    "${remainingNum ?: 0}"
                                ), Toast.LENGTH_SHORT
                            ).show()
                        }

                        return@setOnClickListener
                    }

                    onConfirmClickListener?.invoke(edtNum.text.toString())

                }
                KeyboardUtils.hideSoftInput(binding?.edtNum)
                dismiss() // 关闭弹窗
            }


            edtNum.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    binding?.apply {

                        val text = s.toString()
                        if (text.isNullOrEmpty()) {
                            btnConfirm.isEnabled = false
                            btnConfirm.alpha = 0.5f
                            return
                        }
                        val num = text.toIntOrNull()
                        if (text.trim().length > 1) {
                            if (text.startsWith("0")) {
                                //如果首位是0 去掉0
                                if (num != null) {
                                    s?.replace(0, s.length, "$num")
                                }
                            }
                        }

                        //如果不支持输0
                        if (!zeroEnable) {
                            if (num == 0 || num == null) {
                                btnConfirm.isEnabled = false
                                btnConfirm.alpha = 0.5f
                                return
                            }
                        }


                        btnConfirm.isEnabled = true
                        btnConfirm.alpha = 1f
                    }
                }

                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

                }

                override fun onTextChanged(p0: CharSequence?, start: Int, before: Int, count: Int) {


                }
            })
        }

    }

    // 设置最大宽度，看需要而定，
    override fun getMaxWidth(): Int {
        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val screenWidth = (displayMetrics.widthPixels * 0.30).toInt()
                return screenWidth
            }
        }

        return super.getMaxWidth()
    }

//    // 设置最大高度，看需要而定
//    override fun getMaxHeight(): Int {
//        return super.getMaxHeight()
//    }
//
//    // 设置自定义动画器，看需要而定
//    override fun getPopupAnimator(): PopupAnimator {
//        return super.getPopupAnimator()
//    }
//
//    /**
//     * 弹窗的宽度，用来动态设定当前弹窗的宽度，受getMaxWidth()限制
//     *
//     * @return
//     */
//    override fun getPopupWidth(): Int {
//        return 0
//    }
//
//    /**
//     * 弹窗的高度，用来动态设定当前弹窗的高度，受getMaxHeight()限制
//     *
//     * @return
//     */
//    override fun getPopupHeight(): Int {
//        return 0
//    }
//
//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }
}

