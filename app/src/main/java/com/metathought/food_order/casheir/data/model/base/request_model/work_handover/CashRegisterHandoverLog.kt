package com.metathought.food_order.casheir.data.model.base.request_model.work_handover

import android.content.Context
import android.os.Parcelable
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.extension.priceFormatTwoDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import kotlinx.android.parcel.Parcelize

data class CashRegisterHandoverLog(
    val records: List<CashRegisterHandoverLogVo>?,
    val total: Long,
    val size: Long,
    val current: Long,
    val orders: List<OrderItem>,
    val optimizeCountSql: Boolean,
    val searchCount: <PERSON>olean,
    val optimizeJoinOfCountSql: Boolean,
    val maxLimit: Long,
    val countId: String,
    val pages: Long,
)

@Parcelize
data class CashRegisterHandoverLogVo(
    val id: Long?, //id
    val userId: Long?, //用户id
    val userType: String?, //用户类型 1-商户后台;2-upay;3-员工;4-kiosk;5-平台用户;6-bo后台;7-收银机
    val userName: String?, //用户名称
    val loginTime: String?, //登录时间
    val logoutTime: String?, //登出时间
    val totalOrderNumber: Int?, //本次登录时间内订单总数
    val totalOrderPrice: Double?, //本次登录时间内订单总金额
    val unpaidOrderNumber: Int?, //本次登录时间内未支付订单数
    val onlinePaidPrice: Double?, //本次登录时间内在线支付金额
    val offlinePaidPrice: Double?, //本次登录时间内线下支付金额
    val creditPaidPrice: Double?, //本次登录时间内线下挂账金额
//    val offlinePaidKhrPrice: Long?, //本次登录时间内线下支付KHR金额
    val usdAmount: Double?, //交接金额-美元(单位:分)
    val khrAmount: Long?, //交接金额-瑞尔(单位:分)
    val openingCashUsd: Double?, //备用金(USD)(单位:分)
    val openingCashKhr: Long?, //备用金(KHR)(单位:分)
    val amountPaidUsd: Double?, //支出金额(USD)(单位:分)
    val amountPaidKhr: Long?, //支出金额(KHR)(单位:分)
    val cashPaidPrice: Double?, //本次登录时间内现金支付金额
    val balance: Double?, //当班余额
    val balancePaidPrice: Double?, //当班余额

    val remark: String?, //备注
    var offlinePayMethodData: ArrayList<OfflinePayMethodData>? = null,  //线下支付数据
    var discrepancyPrice: String? = null, //相差金额
) : Parcelable {

    fun getUsdAmount(): String {
        return if ((khrAmount ?: 0) < 0)
            "${
                usdAmount?.priceFormatTwoDigitZero2(
                    "$"
                )
            } ${khrAmount?.priceFormatTwoDigit("៛")}"
        else
            "${
                usdAmount?.priceFormatTwoDigitZero2(
                    "$"
                )
            } + ${khrAmount?.priceFormatTwoDigit("៛")}"
    }


    fun getOpeningCashStr(): String {
        return if ((openingCashKhr ?: 0) < 0)
            "${
                openingCashUsd?.priceFormatTwoDigitZero2(
                    "$"
                )
            } ${openingCashKhr?.priceFormatTwoDigit("៛")}"
        else
            "${
                openingCashUsd?.priceFormatTwoDigitZero2(
                    "$"
                )
            } + ${openingCashKhr?.priceFormatTwoDigit("៛")}"
    }

    fun getAmountPaidUsd(): String {
        return if ((amountPaidKhr ?: 0) < 0)
            "${
                amountPaidUsd?.priceFormatTwoDigitZero2(
                    "$"
                )
            } ${amountPaidKhr?.priceFormatTwoDigit("៛")}"
        else
            "${
                amountPaidUsd?.priceFormatTwoDigitZero2(
                    "$"
                )
            } + ${amountPaidKhr?.priceFormatTwoDigit("៛")}"
    }

}

data class OrderItem(
    val column: String,  // 需要进行排序的字段
    val asc: Boolean, // 是否正序排列，默认 true
)