package com.metathought.food_order.casheir.ui.widget


/**
 *<AUTHOR>
 *@time  2024/12/16
 *@desc
 **/

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import cn.bingoogolapple.badgeview.BGABadgeViewHelper
import com.metathought.food_order.casheir.R

class NumberBadgeView : View {
    private var number = 0
    private var paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var mBadgePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var mBadgeNumberRect = Rect()

    /**
     * 整个徽章所占区域
     */
    private val mBadgeRectF = RectF()
    private var radius = 0f
    private var textSize = 12f
    private var padding = 4f
    private var textColor = Color.WHITE
    private var backgroundColor = Color.RED

    constructor(context: Context) : super(context) {
        init(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(attrs)
    }

    private fun init(attrs: AttributeSet?) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.NumberBadgeView)
            textSize = typedArray.getDimension(R.styleable.NumberBadgeView_textSize, textSize)
            padding = typedArray.getDimension(R.styleable.NumberBadgeView_padding, padding)
            textColor = typedArray.getColor(R.styleable.NumberBadgeView_textColor, textColor)
            backgroundColor =
                typedArray.getColor(R.styleable.NumberBadgeView_backgroundColor, backgroundColor)
            typedArray.recycle()
        }
        paint.textAlign = Paint.Align.CENTER;
        paint.color = backgroundColor
        mBadgePaint.textSize = textSize
        mBadgePaint.color = textColor
        mBadgePaint.style = Paint.Style.FILL
        // 设置mBadgeText居中，保证mBadgeText长度为1时，文本也能居中
        mBadgePaint.textAlign = Paint.Align.CENTER
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val numberString = number.toString()
        mBadgePaint.getTextBounds(numberString, 0, numberString.length, mBadgeNumberRect)
        val textWidth = mBadgeNumberRect.width() + 2 * padding
        val textHeight = mBadgeNumberRect.height() + 2 * padding
        radius = (textWidth.coerceAtLeast(textHeight) / 2) + padding
        val desiredWidth = (2 * radius).toInt()
        val desiredHeight = (2 * radius).toInt()
        val width = resolveSize(desiredWidth, widthMeasureSpec)
        val height = resolveSize(desiredHeight, heightMeasureSpec)
        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
//        val centerX = width / 2f
//        val centerY = height / 2f
//        canvas.drawCircle(centerX, centerY, radius, paint)
//        val numberString = number.toString()
////        textPaint.getTextBounds(numberString, 0, numberString.length, textBounds)
//        mBadgePaint.getTextBounds(numberString, 0, numberString.length, mBadgeNumberRect)
//        val textX = centerX - mBadgeNumberRect.exactCenterX()
//        val textY = centerY - mBadgeNumberRect.exactCenterY()
//        canvas.drawText(numberString, textX, textY, mBadgePaint)
        drawTextBadge(canvas)
    }

    /**
     * 绘制文字徽章
     *
     * @param canvas
     */
    private fun drawTextBadge(canvas: Canvas) {
        var badgeText = ""
        if (!TextUtils.isEmpty(number.toString())) {
            badgeText = number.toString()
        }
        // 获取文本宽所占宽高
        mBadgePaint.getTextBounds(badgeText, 0, badgeText.length, mBadgeNumberRect)
        // 计算徽章背景的宽高
        val badgeHeight: Int = mBadgeNumberRect.height() + padding.toInt() * 2
        // 当mBadgeText的长度为1或0时，计算出来的高度会比宽度大，此时设置宽度等于高度
        val badgeWidth = if (badgeText.length == 1 || badgeText.length == 0) {
            badgeHeight
        } else {
            mBadgeNumberRect.width() + padding.toInt() * 2
        }

        // 计算徽章背景上下的值
        mBadgeRectF.top = 0f
//        mBadgeRectF.bottom = (mBadgeable.getHeight() - mBadgeVerticalMargin).toFloat()
        mBadgeRectF.bottom =
            mBadgeRectF.top + badgeHeight
        // 计算徽章背景左右的值
        mBadgeRectF.right = badgeWidth.toFloat()
        mBadgeRectF.left = mBadgeRectF.right - badgeWidth

        // 设置徽章背景色
        mBadgePaint.color = backgroundColor
        // 绘制徽章背景
        canvas.drawRoundRect(
            mBadgeRectF,
            (badgeHeight / 2).toFloat(),
            (badgeHeight / 2).toFloat(),
            mBadgePaint
        )


        if (!TextUtils.isEmpty(badgeText)) {
            // 设置徽章文本颜色
            mBadgePaint.color = textColor
            // initDefaultAttrs方法中设置了mBadgeText居中，此处的x为徽章背景的中心点y
            val x: Float = mBadgeRectF.left + badgeWidth / 2
            // 注意：绘制文本时的y是指文本底部，而不是文本的中间
            val y: Float = mBadgeRectF.bottom - padding
            // 绘制徽章文本
            canvas.drawText(badgeText, x, y, mBadgePaint)
        }
    }

    fun setNumber(number: Int) {
        this.number = number
        requestLayout()
        invalidate()
    }
}