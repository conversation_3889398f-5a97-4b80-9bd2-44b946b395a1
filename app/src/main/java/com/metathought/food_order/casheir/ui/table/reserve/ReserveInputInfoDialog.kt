package com.metathought.food_order.casheir.ui.table.reserve

import android.app.DatePickerDialog
import android.app.Dialog
import android.content.DialogInterface
import android.icu.util.Calendar
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker
import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITH_SECOND
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.databinding.DialogReserveInputInformationBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.formatDateWithoutSecond
import com.metathought.food_order.casheir.extension.formatPhoneNumber
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.parseDate
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.common.RangeTimePickerDialog
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import kotlinx.coroutines.launch


class ReserveInputInfoDialog : BaseDialogFragment(), CountryCodePicker.DialogEventsListener {
    private var binding: DialogReserveInputInformationBinding? = null
    private var reserveButtonListener: ((ReserveTableRequest) -> Unit)? = null
    private var tableId: String? = null
    private var isRequire = true
    private var isDiningNumber = false

    private var orderInfo: OrderedInfoResponse? = null
    private val peopleMaxNum = 200

    private var preName = ""
    private var preAreaCode = ""
    private var preMobile = ""
    private var preDiningNumber = ""
    private var preDingTime = ""

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogReserveInputInformationBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
        initData()
        initListener()
    }


    private fun initData() {
        val record = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(KEY_RECORD, ShoppingRecord::class.java)
        } else {
            arguments?.getParcelable(KEY_RECORD)
        }
        tableId = arguments?.getString(KEY_TABLE_ID)
        isRequire = arguments?.getBoolean(IS_REQUIRE, true) == true


        binding?.apply {
            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager

            if (orderInfo != null) {
                //如果是预订单 必填
                isRequire = orderInfo?.isPreOrder() == true
            }

            if (!isRequire) {
                textInputLayoutCustomerName.hint = getString(R.string.hint_customer_name)
                textInputLayoutPeople.hint = getString(R.string.hint_number_of_people)
                textInputLayoutPhoneNumber.hint = getString(R.string.hint_phone_number)
                textInputLayoutDiningTime.hint = getString(R.string.hint_dining_time)

                lifecycleScope.launch {
                    /**
                     * 在非必填的情况下  判断一下人数是否必填
                     */
                    isDiningNumber = PreferenceHelper.getStoreInfo()?.isDiningNumber ?: false
                    if (isDiningNumber) {
                        //人数必填
                        val hintText = getString(R.string.hint_number_of_people_required)
                        val spannableString = SpannableString(hintText)
                        val starIndex = hintText.indexOf("*")
                        if (starIndex != -1) {
                            spannableString.setSpan(
                                ForegroundColorSpan(resources.getColor(R.color.RED)),
                                starIndex,
                                starIndex + 1,
                                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                        textInputLayoutPeople.hint = spannableString
                    }
                }
            }

            record?.let {
                edtPhoneNumber.setText(it.mobile ?: "")

                edtCustomerName.setText(it.name)

                if (it.diningTime?.isNotEmpty() == true) {
                    val parseDate = it.diningTime?.formatDateWithoutSecond()
                    parseDate?.let {
                        edtDiningTime.setText(it)

                    }
                }
                if (!it.areaCode.isNullOrEmpty()) {
                    countryCodeHolder.setCountryForPhoneCode(
                        it.areaCode?.replace(
                            " ",
                            ""
                        )!!.toInt()
                    )
                }
                if ((it.diningNumber ?: 0) > 0) {
                    edtPeople.setText(it.diningNumber?.toString() ?: "")
                }


//                if (it.isOrderMore == true) {
//                    // 加购的时候禁用掉 输入客户信息
//                    edtCustomerName.setEnable(false)
//
//                    edtPhoneNumber.setEnable(false)
//                    countryCodeHolder.setEnable(false)
//
//                    edtPeople.setEnable(false)
//
//                    edtDiningTime.setEnable(false)
//
//                    btnReserve.setEnable(false)
//                }
            }
            orderInfo?.let {

                edtCustomerName.setText(it.getLastCustomerName())
                edtCustomerName.setSelection(edtCustomerName.length())
                if (!(it.getDingTime()).isNullOrEmpty()) {
                    edtDiningTime.setText(
                        (it.getDingTime()).formatDate()
                    )
                    edtDiningTime.setSelection(edtDiningTime.length())
                }
                val pair = it.getConsumePhoneNumber()
                if (pair != null) {
                    if (pair.second.isNotEmpty()) {
                        edtPhoneNumber.setText(pair.second)
                        edtPhoneNumber.setSelection(edtPhoneNumber.length())
                    }

                    if (pair.first.isNotEmpty()) {
                        countryCodeHolder.setCountryForPhoneCode(
                            pair.first?.replace(
                                " ",
                                ""
                            )!!.toInt()
                        )
                    }
                }


                if (!(it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString()
                        .isNullOrEmpty() && (it.customerInfoVo?.diningNumber ?: it.peopleNum) != 0
                ) {
                    edtPeople.setText(
                        (it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString() ?: ""
                    )
                    edtPeople.setSelection(edtPeople.length())
                }
            }
            edtPhoneNumber.setSelection(edtPhoneNumber.length())
            edtCustomerName.setSelection(edtCustomerName.length())
            edtDiningTime.setSelection(edtDiningTime.length())
            edtPeople.setSelection(edtPeople.length())

            preName = edtCustomerName.text.toString()
            preAreaCode = countryCodeHolder.selectedCountryCode
            preMobile = edtPhoneNumber.text.toString()
            preDiningNumber = edtPeople.text.toString()
            preDingTime = edtDiningTime.text.toString()

        }

        checkEnable(isRequire)
    }


    private val ontextChange = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            checkEnable(isRequire)
        }
    }
//
//    private val onPhoneChange = object : TextWatcher {
//        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
//        }
//
//        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
//        }
//
//        override fun afterTextChanged(s: Editable?) {
//            s?.let {
//                if ((it.toString().toIntOrNull() ?: 0) > peopleMaxNum) {
//                    it?.clear()
//                    it?.append("${peopleMaxNum}")
//                    editText(it)
//                }
//            }
//            checkEnable(isRequire)
//        }
//    }

    private val onTextChangePhoneNumber = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            s?.let { editText(it) }
        }

    }

    private fun initListener() {
        binding?.apply {
            countryCodeHolder.setDialogEventsListener(this@ReserveInputInfoDialog)
            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
            edtCustomerName.addTextChangedListener(ontextChange)
            edtDiningTime.addTextChangedListener(ontextChange)
//            edtPeople.addTextChangedListener(onPhoneChange)
            edtPeople.addTextChangedListener {
                if ((edtPeople.text.toString().toIntOrNull() ?: 0) > peopleMaxNum) {
                    edtPeople.setText("$peopleMaxNum")
                    edtPeople.setSelection(edtPeople.length())
                }

                checkEnable(isRequire)
            }

            edtPhoneNumber.addTextChangedListener(ontextChange)
            edtPhoneNumber.setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && edtPhoneNumber.text.toString().replace(" ", "").length < 8) {
                    textInputLayoutPhoneNumber.error =
                        getString(R.string.invalid_format_enter_8_to_12_digits)
                    textInputLayoutPhoneNumber.isErrorEnabled = true
                } else if (!hasFocus && edtPhoneNumber.text.toString()
                        .replace(" ", "").length >= 8
                ) {
                    textInputLayoutPhoneNumber.error = ""
                    textInputLayoutPhoneNumber.isErrorEnabled = false
                }
            }
            btnClose.setOnClickListener() {
                dismissCurrentDialog()
            }

            btnReserve.setOnClickListener {
                hideKeyboard2()
                val date =
                    if (edtDiningTime.text.toString().isNotEmpty()) edtDiningTime.text.toString()
                        .parseDate(FORMAT_DATE_TIME_SHOW)
                        ?.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND) else ""

                date?.let { date ->
                    val people =
                        if (edtPeople.text.toString().isNotEmpty()) edtPeople.text.toString()
                            .toInt() else null
                    val reserveTableRequest = ReserveTableRequest(
                        diningTime = date,
                        diningNumber = people,
                        areaCode = countryCodeHolder.selectedCountryCode,
                        mobile = edtPhoneNumber.text.toString().replace(" ", ""),
                        name = edtCustomerName.text?.trim().toString(),
                        tableId = tableId ?: ""
                    )
                    reserveButtonListener?.invoke(reserveTableRequest)
                }

                dismissCurrentDialog()
            }
            edtDiningTime.setOnClickListener {
                showDateTimePicker(edtDiningTime)
            }
        }
    }

    private fun checkEnable(isRequire: Boolean?) {
        binding?.apply {

            if (isRequire == true) {
                var isPeople = false
                if (edtPeople.text?.isNotEmpty() == true) {
                    isPeople = edtPeople.text.toString().toInt() > 0 && edtPeople.text.toString()
                        .toInt() <= peopleMaxNum
                }
                var isPhone = false
                if (edtPhoneNumber.text?.isNotEmpty() == true) {
                    isPhone = edtPhoneNumber.text.toString().replace(" ", "").length >= 8
                }

                btnReserve.setEnable(
                    edtDiningTime.text?.isNotEmpty() == true && isPeople && edtCustomerName.text?.trim()
                        ?.isNotEmpty() == true && isPhone
                )

            } else {
                var isPeople = true
                if (isDiningNumber) {
                    isPeople = if (edtPeople.text?.isNotEmpty() == true) {
                        edtPeople.text.toString().toInt() > 0 && edtPeople.text.toString()
                            .toInt() <= peopleMaxNum
                    } else {
                        false
                    }
                } else {
                    if (edtPeople.text?.isNotEmpty() == true) {
                        isPeople =
                            edtPeople.text.toString().toInt() > 0 && edtPeople.text.toString()
                                .toInt() <= peopleMaxNum
                    }
                }

                var isPhone = true
                if (edtPhoneNumber.text?.isNotEmpty() == true) {
                    isPhone = edtPhoneNumber.text.toString().replace(" ", "").length >= 8
                }


                btnReserve.setEnable(
                    isPeople && isPhone
                )
            }
        }
    }

    companion object {
        private const val RESERVE_INPUT_DIALOG = "RESERVE_INPUT_DIALOG"
        private const val KEY_RECORD = "KEY_RECORD"
        private const val KEY_TABLE_ID = "KEY_TABLE_ID"
        private const val IS_REQUIRE = "IS_REQUIRE"
//        private const val KEY_ORDER_INFO = "KEY_ORDER_INFO"

        fun showDialog(
            fragmentManager: FragmentManager,
            shoppingRecord: ShoppingRecord? = null,
            tableId: String? = null,
            isRequire: Boolean? = null,
            orderInfo: OrderedInfoResponse? = null,
            reserveButtonListener: ((ReserveTableRequest) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(RESERVE_INPUT_DIALOG)
            if (fragment != null) return
            fragment =
                newInstance(
                    iOrderListener = reserveButtonListener,
                    shoppingRecord = shoppingRecord,
                    tableId = tableId,
                    isRequire = isRequire,
                    orderInfo = orderInfo
                )
            fragment.show(fragmentManager, RESERVE_INPUT_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(RESERVE_INPUT_DIALOG) as? ReserveInputInfoDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iOrderListener: ((ReserveTableRequest) -> Unit),
            shoppingRecord: ShoppingRecord? = null,
            tableId: String? = null,
            isRequire: Boolean? = null,
            orderInfo: OrderedInfoResponse? = null,
        ): ReserveInputInfoDialog {
            val args = Bundle()
            args.putParcelable(KEY_RECORD, shoppingRecord)

            args.putString(KEY_TABLE_ID, tableId)
            isRequire?.let { args.putBoolean(IS_REQUIRE, it) }
            val fragment = ReserveInputInfoDialog()
            fragment.reserveButtonListener = iOrderListener
            fragment.arguments = args
            fragment.orderInfo = orderInfo
            return fragment
        }
    }


    val calendar: Calendar = Calendar.getInstance()
    fun showDateTimePicker(editText: EditText) {

        val datePickerDialog = context?.let {
            DatePickerDialog(
                it, { _: DatePicker, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                    calendar.set(Calendar.YEAR, year)
                    calendar.set(Calendar.MONTH, monthOfYear)
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

                    showTimePicker(editText)
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            )
        }
        datePickerDialog?.datePicker?.minDate = System.currentTimeMillis()
        datePickerDialog?.setCancelable(false)
        datePickerDialog?.show()

        datePickerDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.text =
            getString(R.string.confirm2)
        datePickerDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.text =
            getString(R.string.cancel)
    }

    private fun showTimePicker(editText: EditText) {
        val currentCalendar = Calendar.getInstance()
        val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY) // 24-hour format
        val currentMinute = currentCalendar.get(Calendar.MINUTE)
        val timePickerDialog = RangeTimePickerDialog(
            context,
            { view, hourOfDay, minute ->
                calendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                calendar.set(Calendar.MINUTE, minute)
                editText.setText(calendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW))
            },
            currentHour,
            currentMinute,
            false
        )
        if (currentCalendar.time.formatDateStr(FORMAT_DATE) == calendar.time.formatDateStr(
                FORMAT_DATE
            )
        ) {
            timePickerDialog.setMin(currentHour, currentMinute)
        }
        timePickerDialog.setCancelable(false)
        timePickerDialog.show()

        timePickerDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.text =
            getString(R.string.confirm2)
        timePickerDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.text =
            getString(R.string.cancel)
    }

    override fun onCcpDialogOpen(dialog: Dialog?) {

    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.let {
            it.edtPhoneNumber.isFocusable = true
        }
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {
    }


    private fun editText(editable: Editable) {
        binding?.apply {
            edtPhoneNumber.removeTextChangedListener(onTextChangePhoneNumber)
            val formattedPhoneNumber = editable.toString().formatPhoneNumber()
            edtPhoneNumber.setText(formattedPhoneNumber)

            edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
            checkEnable(isRequire)
        }
    }
}
