package com.metathought.food_order.casheir.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.cart.Goods
import com.metathought.food_order.casheir.databinding.SelectedMenuItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.helper.FoundationHelper
import timber.log.Timber


/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class PrevoiusOrderedAdapter(
    val list: ArrayList<Goods> = arrayListOf(),
) : RecyclerView.Adapter<PrevoiusOrderedAdapter.OrderedInfoViewHolder>() {

    inner class OrderedInfoViewHolder(val binding: SelectedMenuItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: Goods) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        tvName.text = it.name
                        if (!resource.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                            tvName.addMealSetTag(itemView.context)
                        }
                        tvSpecification.text = it.getGoodsTagStr()
                        tvSpecification.isVisible = tvSpecification.text.isNotEmpty()
                        tvQTY.text = it.num.toString()
                        layoutPrice.tvVipPrice.text = it.totalVipPrice().priceFormatTwoDigitZero2()
                        layoutPrice.tvVipPrice.isVisible = it.isShowVipPrice()

                        val activityLabel = it?.activityLabels?.firstOrNull()
                        if (activityLabel != null) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = Color.parseColor(
                                    activityLabel.color
                                )
                            )
                            tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (it?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }

                        layoutPrice.tvWeight.isVisible = false
                        layoutPrice.tvTimePriceSign.isVisible = false


                        if (it.isToBeWeighed()) {
                            //如果是称重商品
                            if (resource.isHasCompleteWeight()) {
                                layoutPrice.tvWeight.isVisible = true
                                layoutPrice.tvWeight.text = "(${it.getWeightStr()})"
                                if (it.isMealSet()) {
                                    layoutPrice.tvWeight.isVisible = false
                                }
                            } else {
                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
                                layoutPrice.tvVipPrice.isVisible = false
                            }
                        }

                        if (it.isTimePriceGood()) {
                            //如果是时价菜
                            if (!resource.isHasCompletePricing()) {
                                layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
                            } else {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }


                        if (resource?.isHasProcessed() == true) {
                            val isShowVipPrice = resource.isShowVipPrice()
                            layoutPrice.tvVipPrice.isVisible = isShowVipPrice == true
                            layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                                FoundationHelper.useConversionRatio,
                                resource.totalPrice(),
                                FoundationHelper.isKrh
                            )
                            if (isShowVipPrice) {
                                layoutPrice.tvVipPrice.text =
                                    resource.totalVipPrice().priceFormatTwoDigitZero2()
                            }
                            if (resource.isTimePriceGood()) {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }

                        tvRemark.isVisible = resource.getFinalNote().isNotEmpty()
                        tvRemark.text =
                            "${getString(R.string.remark)} : ${resource.getFinalNote()}"
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedInfoViewHolder {
        val itemView =
            SelectedMenuItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedInfoViewHolder(itemView)
    }

    override fun getItemCount() = list!!.size

    override fun onBindViewHolder(holder: OrderedInfoViewHolder, position: Int) {
        holder.bind(list!![position])
    }

    fun getData(): ArrayList<Goods> {
        return list
    }


    fun replaceData(newData: ArrayList<Goods>?) {
        if (newData != null) {
            this.list!!.clear()
            this.list!!.addAll(newData)
            notifyDataSetChanged()
        }
    }

    fun clearData() {
        this.list!!.clear()
        notifyDataSetChanged()
    }

}