package com.metathought.food_order.casheir.ui.dialog.take_out

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper
import com.metathought.food_order.casheir.databinding.DialogTakeOutPlatformBinding
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.TakeOutPlatformAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 *
 * 外卖平台弹窗
 *
 * **/

@AndroidEntryPoint
class TakeOutPlatformDialog : BaseDialogFragment() {
    private var binding: DialogTakeOutPlatformBinding? = null

    private val viewModel: TakeOutPlatformViewModel by viewModels()

    private var onConfirmClickListener: ((takeOutPlatformModel: TakeOutPlatformModel, dinStyle: Int, orderId: String) -> Unit)? =
        null

    private var takeOutPlatformModel: TakeOutPlatformModel? = null
    private var takeOutOrderId: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogTakeOutPlatformBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)


        initListener()
        initObserver()
        checkBtnStatus()
        viewModel.getDeliveryPlatformList()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) {
            if (it is ApiResponse.Loading) {

            }
            if (it is ApiResponse.Success) {
                initData(it.data.filter { data -> data.isEnable() })
            }
            if (it is ApiResponse.Error) {

            }

        }
    }

    private var takeOutPlatformAdapter: TakeOutPlatformAdapter? = null

    private fun initData(list: List<TakeOutPlatformModel>) {
        binding?.apply {
            val gridLayoutManager = GridLayoutManager(context, 2)

            takeOutPlatformAdapter = TakeOutPlatformAdapter {
                checkBtnStatus()
            }

            rvList.apply {
                layoutManager = gridLayoutManager
                adapter = takeOutPlatformAdapter
            }

            takeOutPlatformAdapter?.replaceData(list)
            if (takeOutPlatformModel != null) {
                takeOutPlatformAdapter?.setSelectItem(takeOutPlatformModel)
            } else {
                if (list.isNotEmpty()) {
                    takeOutPlatformAdapter?.setSelectItem(list.first())
                }
            }

            edtId.setText(takeOutOrderId)
            edtId.setSelection(edtId.length())
        }

    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnYes.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (takeOutPlatformAdapter?.getSelectItem() == null) {
                        return@isFastDoubleClick
                    }
                    if (edtId.text.toString().isNullOrEmpty()) {
                        return@isFastDoubleClick
                    }
                    val dinStyle = TakeOutPlatformToDiningHelper.getTakeOutPlatformDingStyle(
                        takeOutPlatformAdapter?.getSelectItem()?.id!!
                    )
                    Timber.e("dinStyle  ${dinStyle}")
                    onConfirmClickListener?.invoke(
                        takeOutPlatformAdapter?.getSelectItem()!!,
                        dinStyle,
                        edtId.text.toString()
                    )
                    dismissAllowingStateLoss()
                }
            }
            edtId.addTextChangedListener {
                checkBtnStatus()
            }
        }
    }

    private fun checkBtnStatus() {
        binding?.apply {
            if (takeOutPlatformAdapter?.getSelectItem() != null && !edtId.text.trim()
                    .isNullOrEmpty()
            ) {
                btnYes.setEnableWithAlpha(true)
            } else {
                btnYes.setEnableWithAlpha(false)
            }
        }
    }

    companion object {

        private const val TAG = "TakeOutPlatformDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            takeOutPlatformModel: TakeOutPlatformModel? = null,
            takeOutOrderId: String? = null,
            onConfirmClickListener: ((takeOutPlatformModel: TakeOutPlatformModel, dinStyle: Int, takeOutOrderId: String) -> Unit)? =
                null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(takeOutPlatformModel, takeOutOrderId, onConfirmClickListener)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? TakeOutPlatformDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            takeOutPlatformModel: TakeOutPlatformModel? = null,
            takeOutOrderId: String? = null,
            onConfirmClickListener: ((takeOutPlatformModel: TakeOutPlatformModel, dinStyle: Int, orderId: String) -> Unit)? =
                null
        ): TakeOutPlatformDialog {
            val args = Bundle()
            val fragment = TakeOutPlatformDialog()
            fragment.arguments = args
            fragment.onConfirmClickListener = onConfirmClickListener
            fragment.takeOutPlatformModel = takeOutPlatformModel
            fragment.takeOutOrderId = takeOutOrderId
            return fragment
        }
    }
}
