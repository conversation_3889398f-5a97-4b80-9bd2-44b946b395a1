package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class GridSpacingItemDecoration(
    private val spanCount: Int,
    private val spacing: Int,
    private val includeEdge: Boolean = false
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val column = position % spanCount
        val row = position / spanCount

        // 设置左右间距
        when (column) {
            0 -> {
                // 第一列：右边距3dp
                outRect.right = spacing / 2
            }
            spanCount - 1 -> {
                // 最后一列：左边距3dp
                outRect.left = spacing / 2
            }
            else -> {
                // 中间列：左右各3dp
                outRect.left = spacing / 2
                outRect.right = spacing / 2
            }
        }

        // 设置上下间距
        if (row > 0) {
            // 除第一行外，都设置上边距6dp
            outRect.top = spacing
        }
    }
}
