package com.metathought.food_order.casheir.ui.dialog.store_manager

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.response_model.GoogleResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.store.StoreInfoMultLanguageListRespnose
import com.metathought.food_order.casheir.data.model.base.response_model.store.StoreInfoMultLanguageRespnose
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.PrinterTemplateHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.service.GeocodeApiService
import com.metathought.food_order.casheir.network.upload.UploadManager
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.utils.FileUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import timber.log.Timber
import java.io.File
import java.util.Locale
import java.util.UUID
import javax.inject.Inject


@HiltViewModel
class StoreManagerViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _uiUpLoadState = MutableLiveData<UIUploadModel>()
    private val _uiStoreInfoState = MutableLiveData<ApiResponse<StoreInfoResponse>>()
    private val _uiStoreInfoMultLanguageListState =
        MutableLiveData<ApiResponse<StoreInfoMultLanguageListRespnose>>()
    private val _uiSaveState =
        MutableLiveData<ApiResponse<BaseBooleanResponse>>()

    private val _uiAddressState =
        MutableLiveData<String>()

    val uiUpLoadState get() = _uiUpLoadState
    val uiStoreInfoState get() = _uiStoreInfoState
    val uiStoreInfoMultLanguageListState get() = _uiStoreInfoMultLanguageListState
    val uiSaveState get() = _uiSaveState
    val uiAddressState get() = _uiAddressState

    var headUrl: String? = null

    /**
     * 获取商家信息
     *
     */
    fun getStoreInfo() {
        viewModelScope.launch {
            try {
                uiStoreInfoState.postValue(ApiResponse.Loading)
                val response = repository.getStoreInfo()
                if (response is ApiResponse.Success) {
                    uiStoreInfoState.postValue(response)
                    MainDashboardFragment.CURRENT_USER?.serviceChargePercentage =
                        response.data.serviceChargePercentage

                    MainDashboardFragment.CURRENT_USER?.vatPercentage = response.data.vatPercentage

                    MainDashboardFragment.CURRENT_USER?.isDisplayTable =
                        response.data.isDisplayTable

                    MainDashboardFragment.CURRENT_USER?.autoAcceptOrders =
                        response.data.autoAcceptOrders

                    MainDashboardFragment.CURRENT_USER?.cashierShowPic =
                        response.data.cashierShowPic

                    MainDashboardFragment.STORE_INFO = response.data
                    val locale = Locale.getDefault()
                    if (locale == MyApplication.LOCALE_KHMER) {
                        MainDashboardFragment.CURRENT_USER?.storeNameKH = response.data.name
                    } else if (locale == Locale.CHINESE) {
                        MainDashboardFragment.CURRENT_USER?.storeNameZH = response.data.name
                    } else if (locale == Locale.ENGLISH) {
                        MainDashboardFragment.CURRENT_USER?.storeNameEN = response.data.name
                    }
                    PreferenceHelper.setStoreInfo(response.data)

                    PreferenceDataStoreHelper.getInstance().apply {
                        if (MainDashboardFragment.CURRENT_USER != null) {
                            this.putPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                MainDashboardFragment.CURRENT_USER!!.toJson()
                            )
                        }
                    }
                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_STORE, null))
                } else {
                    uiStoreInfoState.postValue(response)
                }
            } catch (e: Exception) {
                uiStoreInfoState.postValue(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 保存店铺信息
     *
     */
    fun saveStoreInfo(storeInfoResponse: StoreInfoResponse) {
        viewModelScope.launch {
            try {
                uiSaveState.postValue(ApiResponse.Loading)
                val response = repository.saveStore(storeInfoResponse)
                if (response is ApiResponse.Success) {
                    MainDashboardFragment.CURRENT_USER?.url =
                        storeInfoResponse.url

                    MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance =
                        storeInfoResponse.isPaymentInAdvance

                    MainDashboardFragment.CURRENT_USER?.serviceChargePercentage =
                        storeInfoResponse.serviceChargePercentage

                    MainDashboardFragment.CURRENT_USER?.vatPercentage =
                        storeInfoResponse.vatPercentage

                    MainDashboardFragment.CURRENT_USER?.isDisplayTable =
                        storeInfoResponse.isDisplayTable

                    MainDashboardFragment.CURRENT_USER?.autoAcceptOrders =
                        storeInfoResponse.autoAcceptOrders

                    MainDashboardFragment.CURRENT_USER?.cashierShowPic =
                        storeInfoResponse.cashierShowPic

//                    PrinterTemplateHelper.modifyAutoPrintCheckoutReceipt(
//                        storeInfoResponse.isAutoCheckoutTicket ?: true
//                    )
                    if (storeInfoResponse.conversionRatio != null) {
                        FoundationHelper.conversionRatio = storeInfoResponse.conversionRatio!!
                    }
                    MainDashboardFragment.STORE_INFO = storeInfoResponse

                    PreferenceHelper.setStoreInfo(storeInfoResponse)

                    EventBus.getDefault().post(
                        SimpleEvent(
                            SimpleEventType.UPDATE_USB_PRINT_INFO,
                            null
                        )
                    )
                }
                uiSaveState.postValue(response)
            } catch (e: Exception) {
                uiSaveState.postValue(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 获取店铺多语言
     *
     */
    fun getI18nData(storeInfoResponse: StoreInfoResponse) {
        viewModelScope.launch {
            try {
                uiStoreInfoMultLanguageListState.postValue(ApiResponse.Loading)
                val response = repository.getI18nData(storeInfoResponse.id!!)

                uiStoreInfoMultLanguageListState.postValue(response)

            } catch (e: Exception) {
                uiStoreInfoMultLanguageListState.postValue(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 保存店铺多语言
     *
     */
    fun saveI18nData(
        storeInfoResponse: StoreInfoResponse?,
        nameZH: String?,
        nameEN: String?,
        nameKM: String?,
        descZH: String?,
        descEN: String?,
        descKM: String?,
    ) {
        viewModelScope.launch {
            try {
//                uiStoreInfoMultLanguageListState.postValue(ApiResponse.Loading)
                val list = mutableListOf<StoreInfoMultLanguageRespnose>()
                if (uiStoreInfoMultLanguageListState.value is ApiResponse.Success) {
                    val data =
                        (uiStoreInfoMultLanguageListState.value as ApiResponse.Success<StoreInfoMultLanguageListRespnose>).data
                    val nameModelZH = data.getNameByLanguage("ZH")
                    val nameModelEN = data.getNameByLanguage("EN")
                    val nameModelKM = data.getNameByLanguage("KM")

                    val descModelZH = data.getDescByLanguage("ZH")
                    val descModelEN = data.getDescByLanguage("EN")
                    val descModelKM = data.getDescByLanguage("KM")

                    if (nameModelZH != null) {
                        nameModelZH.value = nameZH
                        list.add(nameModelZH)
                    } else {
                        if (!nameZH.isNullOrEmpty()) {
                            list.add(
                                StoreInfoMultLanguageRespnose(
                                    columnId = storeInfoResponse?.id,
                                    tableName = "store",
                                    columnName = "name",
                                    language = "ZH",
                                    value = nameZH
                                )
                            )
                        }
                    }
                    if (nameModelEN != null) {
                        nameModelEN.value = nameEN
                        list.add(nameModelEN)
                    } else {
                        if (!nameEN.isNullOrEmpty()) {
                            list.add(
                                StoreInfoMultLanguageRespnose(
                                    columnId = storeInfoResponse?.id,
                                    tableName = "store",
                                    columnName = "name",
                                    language = "EN",
                                    value = nameEN
                                )
                            )
                        }
                    }
                    if (nameModelKM != null) {
                        nameModelKM.value = nameKM
                        list.add(nameModelKM)
                    } else {
                        if (!nameKM.isNullOrEmpty()) {
                            list.add(
                                StoreInfoMultLanguageRespnose(
                                    columnId = storeInfoResponse?.id,
                                    tableName = "store",
                                    columnName = "name",
                                    language = "KM",
                                    value = nameKM
                                )
                            )
                        }
                    }

                    if (descModelZH != null) {
                        descModelZH.value = descZH
                        list.add(descModelZH)
                    } else {
                        if (!descZH.isNullOrEmpty()) {
                            list.add(
                                StoreInfoMultLanguageRespnose(
                                    columnId = storeInfoResponse?.id,
                                    tableName = "store",
                                    columnName = "description",
                                    language = "ZH",
                                    value = descZH
                                )
                            )
                        }
                    }
                    if (descModelEN != null) {
                        descModelEN.value = descEN
                        list.add(descModelEN)
                    } else {
                        if (!descEN.isNullOrEmpty()) {
                            list.add(
                                StoreInfoMultLanguageRespnose(
                                    columnId = storeInfoResponse?.id,
                                    tableName = "store",
                                    columnName = "description",
                                    language = "EN",
                                    value = descEN
                                )
                            )
                        }
                    }
                    if (descModelKM != null) {
                        descModelKM.value = descKM
                        list.add(descModelKM)
                    } else {
                        if (!descKM.isNullOrEmpty()) {
                            list.add(
                                StoreInfoMultLanguageRespnose(
                                    columnId = storeInfoResponse?.id,
                                    tableName = "store",
                                    columnName = "description",
                                    language = "KM",
                                    value = descKM
                                )
                            )
                        }
                    }
                }

                val response = repository.setI18nData(list)
                if (response is ApiResponse.Success) {
                    MainDashboardFragment.CURRENT_USER?.storeNameZH = nameZH

                    MainDashboardFragment.CURRENT_USER?.storeNameEN = nameEN

                    MainDashboardFragment.CURRENT_USER?.storeNameKH = nameKM

                    PreferenceDataStoreHelper.getInstance().apply {
                        if (MainDashboardFragment.CURRENT_USER != null) {
                            this.putPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                MainDashboardFragment.CURRENT_USER!!.toJson()
                            )
                        }
                    }
                }



                _uiSaveState.postValue(response)
            } catch (e: Exception) {
                _uiSaveState.postValue(ApiResponse.Error(e.message))
            }
        }
    }

//    fun requestGoogleMapH5Page(latlng: String) {
//        viewModelScope.launch {
//            val client = OkHttpClient()
//            val key = "AIzaSyAThamUsMTeqv0VAjPIfzxfKAWaD7-NveQ"
//            //24.485410762888723,118.17054441697339
//            val url =
//                "https://maps.googleapis.com/maps/api/geocode/json?latlng=24.485410762888723,118.17054441697339&key=${key}" // 替换为实际的包含Google Map的H5页面地址
//            val request = Request.Builder()
//                .url(url)
//                .build()
//            try {
//                val response: Response = client.newRequest(request)
//                if (response.isSuccessful) {
//                    Timber.e("response: ${response.body?.string()}")
//                } else {
//                    println("请求失败，状态码: ${response.code}")
//
//                }
//            } catch (e: IOException) {
//                e.printStackTrace()
//
//            }
//        }
//    }

    fun requestGeocodeDataWithRetrofit(latlng: String) {
        viewModelScope.launch {
            val baseUrl = "https://maps.googleapis.com/"
            val retrofit = Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
            val apiKey = "AIzaSyAThamUsMTeqv0VAjPIfzxfKAWaD7-NveQ"
//            //24.485410762888723,118.17054441697339
            val apiService = retrofit.create(GeocodeApiService::class.java)
            val call =
                apiService.getGeocodeData(latlng, apiKey, Locale.getDefault().language)
            call.enqueue(object : Callback<GoogleResponse> {
                override fun onResponse(
                    call: Call<GoogleResponse>,
                    response: Response<GoogleResponse>
                ) {
                    if (response.isSuccessful) {
                        val responseBody = response.body()
                        if (!responseBody?.results.isNullOrEmpty()) {
                            Timber.e("坐标 ${responseBody?.results?.firstOrNull()?.formatted_address}")
                            uiAddressState.postValue(responseBody?.results?.firstOrNull()?.formatted_address)
                        } else if (responseBody?.results?.isEmpty() == true) {
                            uiAddressState.postValue("")
                        }
                        Timber.e("responseBody:${responseBody?.plus_code.toString()}")
                        // 在这里可以进行后续的数据解析等操作，例如调用解析函数来提取地理坐标等信息
//                        parseGeocodeResponse(responseBody)
                    } else {
                        println("请求失败，状态码: ${response.code()}")
                    }
                }

                override fun onFailure(call: Call<GoogleResponse>, t: Throwable) {
                    t.printStackTrace()
                }

            })

        }
    }

    /**
     * 获取上传token
     *
     * @param imgPath
     */
    fun getUploadToken(imgPath: String) {
        if (imgPath.isNullOrEmpty()) {
            return
        }
        viewModelScope.launch {
            val file = File(imgPath)

            Timber.e("name:  ${file.name}   length:${file.length()}")
            try {
                val uuid = UUID.randomUUID()
                val uuidString = uuid.toString()
                val fileName = "${uuidString}.${FileUtil.getFileExtension(file)}"
                val response = repository.getUploadPath(
                    fileName,
                    file.length()
                )
                uiUpLoadState.postValue(UIUploadModel(isUploading = true))
                if (response is ApiResponse.Success) {
                    if (response.data.token != null) {
                        UploadManager.getInstance().upload(response.data.token, imgPath, {
                            headUrl = it?.split("?")?.firstOrNull()
                            uiUpLoadState.postValue(UIUploadModel(isUploading = false))
                        }, {
                            uiUpLoadState.postValue(UIUploadModel(isUploading = false, it))
                        })
                    }
                } else if (response is ApiResponse.Error) {
                    uiUpLoadState.postValue(
                        UIUploadModel(
                            isUploading = false,
                            errorMsg = response.message
                        )
                    )
                }
            } catch (e: Exception) {
                uiUpLoadState.postValue(
                    UIUploadModel(
                        isUploading = false,
                        errorMsg = e.message
                    )
                )
//                emitUiState(showError = e.message, showLoading = false)
            }
        }
    }


    //    private fun emitUiState(
//        showLoading: Boolean? = null,
//        showError: String? = null,
//        showSuccess: NoticeListResponse? = null,
//        showEnd: Boolean = false,
//        isRefresh: Boolean? = null,
//    ) {
//        val uiModel = UIListModel(showLoading, showError, showSuccess, showEnd, isRefresh)
//        _uiState.postValue(uiModel)
//    }
    data class UIUploadModel(
        var isUploading: Boolean = false,
        var errorMsg: String? = null
    )

}