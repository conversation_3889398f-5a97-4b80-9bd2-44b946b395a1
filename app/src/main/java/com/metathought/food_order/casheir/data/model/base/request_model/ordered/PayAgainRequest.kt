package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.ConsumerPayRegisterInfo
import java.math.BigDecimal

data class PayAgainRequest(
    @SerializedName("accountId")
    val accountId: String? = null,
    //混合支付 传的余额支付多少
    @SerializedName("balancePayAmount")
    var balancePayAmount: BigDecimal? = null,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("payType")
    val payType: Int,
    @SerializedName("channelsName")
    val channelsName: String? = null,
    @SerializedName("channelsId")
    val channelsId: Int? = null,
    //收款的金额
    @SerializedName("collectCash")
    val collectCash: Long? = null,
    //找零的金额
    @SerializedName("changeAmount")
    val changeAmount: Long? = null,
    //收款的美元
    @SerializedName("collectCashDollar")
    val collectCashDollar: BigDecimal? = null,
    //找零的美元
    @SerializedName("changeAmountDollar")
    val changeAmountDollar: Double? = null,
    @SerializedName("isPosPrint")
    val isPosPrint: Boolean? = null,
    @SerializedName("changeOrderStatus")
    val changeOrderStatus: Boolean? = null,


    //优惠券id
    @SerializedName("couponId")
    val couponId: Long? = null,
    //减免折扣信息
    @SerializedName("discount")
    val discount: DiscountInfo? = null,

    @SerializedName("version")
    val version: String? = "V2",

    @SerializedName("consumerPayRegisterInfo")
    var consumerPayRegisterInfo: ConsumerPayRegisterInfo? = null,//会员注册信息

    @SerializedName("isCredit")
    var isCredit: Boolean = false,    //是否挂账

    @SerializedName("creditReason")
    var creditReason: String? = null,//挂账原因

)

data class DiscountInfo(
//    val reduceType: Int? = null,
//    val reduceRate: BigDecimal? = null,
////    val reduceKhr: Long? = null,
//    val reduceDollar: BigDecimal? = null,  //单位元
//
//    /**
//     * 会员减免
//     */
//    val reduceVipDollar: BigDecimal? = null,

    //减免金额（美元）
    var reduceDollar: BigDecimal? = null,
    //减免金额（瑞尔）
    val reduceKhr: BigDecimal? = null,
    //减免金额（vip美元）
    var reduceVipDollar: BigDecimal? = null,
    //减免金额（vip瑞尔）
    val reduceVipKhr: BigDecimal? = null,

    //减免百分比
    var reduceRate: BigDecimal? = null,
    //类型 1.减免 2.折扣
    var reduceType: Int? = null,


    /**
     * 折扣减免活动Id
     */
    val discountReduceActivityId: String? = null,

    /**
     * 原因
     */
    val reduceReason: String? = null
)