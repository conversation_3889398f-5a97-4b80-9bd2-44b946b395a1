package com.metathought.food_order.casheir.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.CommissionDetailItemBinding
import com.metathought.food_order.casheir.databinding.ServiceFeeDetailItemBinding
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.helper.FoundationHelper

/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class CommissionDetailItemAdapter(
    val list: ArrayList<Goods?>,
    var takeOutPlatformModel: TakeOutPlatformModel? = null,
    var conversionRatio: Long
) : RecyclerView.Adapter<CommissionDetailItemAdapter.OrderedInfoViewHolder>() {

    inner class OrderedInfoViewHolder(val binding: CommissionDetailItemBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: Goods, position: Int) {
            itemView.context.run {
                resource.let { data ->
                    binding.apply {

                        val activityLabel = data?.activityLabels?.firstOrNull()
                        if (activityLabel != null) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = Color.parseColor(
                                    activityLabel.color
                                )
                            )
                            tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (data.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }

                        tvFoodName.text = data.name
                        tvFoodSubName.isVisible = data.feedStr?.isNotEmpty() == true
                        tvFoodSubName.text = data.feedStr
                        tvFoodCount.text = "${data.totalCount}"
                        tvServiceFeePer.text =
                            "${data.getCommissionPercent().halfUp(2)}%"

                        tvCommission.text = FoundationHelper.getPriceStrByUnit(
                            conversionRatio,
                            data.totalCommission ?: 0,
                            takeOutPlatformModel?.isKhr() == true
                        )

                        vLine.isVisible = position < list.size - 1
                    }
                }
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedInfoViewHolder {
        val itemView =
            CommissionDetailItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedInfoViewHolder(itemView)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: OrderedInfoViewHolder, position: Int) {
        holder.bind(list[position]!!, position)
    }


    fun replaceData(newData: ArrayList<Goods>?) {
        if (newData != null) {
            this.list.clear()
            this.list.addAll(newData)
            notifyDataSetChanged()
        }
    }


}