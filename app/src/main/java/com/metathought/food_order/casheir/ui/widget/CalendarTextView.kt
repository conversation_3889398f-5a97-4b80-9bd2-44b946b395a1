package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import com.metathought.food_order.casheir.R



/**
 *<AUTHOR>
 *@time  2024/5/10
 *@desc
 **/

class CalendarTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {


    init {
        updateCalendarColor()
    }

    //修改时间选择icon 和 字的 颜色
    fun updateCalendarColor() {
        val isEmpty = text.isEmpty()
//        setTextColor(
//            ContextCompat.getColor(
//                context,
//                if (isEmpty) R.color.black50 else R.color.black
//            )
//        )

        ContextCompat.getDrawable(context, R.drawable.ic_calendar)?.let {
            it.setBounds(0, 0, it.minimumWidth, it.minimumHeight)
            DrawableCompat.setTintList(
                it,
                resources.getColorStateList(
                    if (isEmpty) R.color.black else R.color.primaryColor,
                    null
                )
            )
            apply {
                setCompoundDrawables(it, null, null, null)
            }
        }

    }

}