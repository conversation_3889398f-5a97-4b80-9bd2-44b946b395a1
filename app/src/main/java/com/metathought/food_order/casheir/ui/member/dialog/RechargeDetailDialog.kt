package com.metathought.food_order.casheir.ui.member.dialog

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.DynamicDrawableSpan
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.BalanceTypeEnum
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.RecordBalance
import com.metathought.food_order.casheir.databinding.DialogRechargeDetailBinding
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getBalanceStatusType
import com.metathought.food_order.casheir.extension.getBalanceType
import com.metathought.food_order.casheir.extension.getBalanceTypeColor
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.RechargeDetailAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.ordered.coupon.GiftCouponDetailDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlin.math.min


/**
 *<AUTHOR>
 *@time  2024/8/25
 *@desc 充值详情弹窗
 **/

@AndroidEntryPoint
class RechargeDetailDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "RechargeDetailDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.6

        fun showDialog(
            fragmentManager: FragmentManager,
            recordBalance: RecordBalance
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(recordBalance)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? RechargeDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            recordBalance: RecordBalance
        ): RechargeDetailDialog {
            val fragment = RechargeDetailDialog()
            fragment.recordBalance = recordBalance
            return fragment
        }
    }


    private val viewModel: RechargeDetailViewModel by viewModels()

    val detailAdapter = RechargeDetailAdapter()

    private var recordBalance: RecordBalance? = null

    private var binding: DialogRechargeDetailBinding? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogRechargeDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) { state ->
            when (state.response) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        val detailInfo = state.response.data
                        detailAdapter.replaceData(buildDetailList(detailInfo))
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                    }

                }

                else -> {

                }
            }

        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
        }
    }

    private fun initData() {

    }

    private fun initView() {
        binding?.apply {
            val gridLayoutManager = GridLayoutManager(context, 2)
            rvList.apply {
                layoutManager = gridLayoutManager
                adapter = detailAdapter
                setItemAnimator(null)

//                detailAdapter.replaceData(buildDetailList(null))
                viewModel.getOrderDetailInfo(recordBalance?.id)
            }
        }
    }

    private fun buildDetailList(info: ConsumerRechargeInfo?): List<RechargeDetailItem> {
        val list = mutableListOf<RechargeDetailItem>()
        //客户昵称
        val nickname =
            if (!info?.nickName.isNullOrEmpty()) info?.nickName
            else info?.telephone?.takeLast(4)
        list.add(RechargeDetailItem(getString(R.string.customer_nickname), nickname ?: ""))
        //客户账号
        list.add(
            RechargeDetailItem(
                getString(R.string.customer_account), info?.telephone ?: ""
            )
        )
        //类型
        list.add(
            RechargeDetailItem(
                getString(R.string.type), info?.type?.getBalanceType(requireContext())
            )
        )
        //金额
        list.add(
            RechargeDetailItem(
                getString(R.string.amount),
                info?.amount?.priceFormatTwoDigitZero2()
            )
        )

        if (info?.type == BalanceTypeEnum.TOP_UP.id) {
            //额外赠送金额
            if (info?.giftAmount != null && !info.giftAmount!!.isZero()) {
                list.add(
                    RechargeDetailItem(
                        getString(R.string.additional_gift_amount),
                        info.giftAmount?.priceFormatTwoDigitZero1()
                    )
                )
            }
            //TODO 额外赠送优惠券
            val totalCouponNum =
                info?.rechargeTierCouponTemplateList?.sumOf { it.num ?: 0 } ?: 0
            if (totalCouponNum > 0) {
                val review = getString(R.string.review)
                val spBuilder = SpannableStringBuilder(
                    getString(R.string.unit_coupon, totalCouponNum.toString()) + " $review"
                )
                val startPos = spBuilder.indexOf(review)
                spBuilder.setSpan(
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            //优惠券弹窗
                            GiftCouponDetailDialog.showDialog(
                                parentFragmentManager,
                                info?.rechargeTierCouponTemplateList
                            )
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.color =
                                ContextCompat.getColor(requireContext(), R.color.primaryColor)
                            ds.isUnderlineText = false
                        }
                    },
                    startPos,
                    startPos + review.length,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
                list.add(
                    RechargeDetailItem(
                        getString(R.string.extra_coupon_giveaway), spBuilder
                    )
                )
            }
        }

        if (info?.couponId != null) {
            list.add(
                RechargeDetailItem(getString(R.string.coupon_name), info.couponName)
            )

            val couponAmount = min(info.amount ?: 0, info.couponAmount ?: 0)
            list.add(
                RechargeDetailItem(
                    getString(R.string.coupon_reduce_price),
                    couponAmount.priceFormatTwoDigitZero2()
                )
            )
        }
        //实付金额
        var realPrice = (info?.amount ?: 0) - (info?.couponAmount ?: 0)
        if (realPrice < 0) {
            realPrice = 0
        }
        list.add(
            RechargeDetailItem(
                getString(R.string.real_pay_price),
                realPrice.priceFormatTwoDigitZero2()
            )
        )


        //支付方式
        list.add(
            RechargeDetailItem(
                getString(R.string.payment_method),
                info?.getPaymentMethod(requireContext())
            )
        )

        if (info?.payChannel == PayTypeEnum.CASH_PAYMENT.id && info.offlinePaymentChannelsId == OfflinePaymentChannelEnum.CASH.id) {

            val collectCashDollar = info.collectCashDollar
            val collectCash = info.collectCash
            val cashCollection =
                if (collectCashDollar != null && !collectCashDollar.isZero() && collectCash != null && !collectCash.isZero()) {
                    "${collectCashDollar.priceFormatTwoDigitZero1()} + ៛${collectCash.decimalFormatZeroDigit()}"
                } else if (collectCashDollar != null && !collectCashDollar.isZero()) {
                    collectCashDollar.priceFormatTwoDigitZero1()
                } else if (collectCash != null && !collectCash.isZero()) {
                    "៛${collectCash.decimalFormatZeroDigit()}"
                } else {
                    "$0.00"
                }
            //收取现金
            list.add(RechargeDetailItem(getString(R.string.cash_collection), cashCollection))


            //找零金额
            val changeAmountStr =
                if (info.changeAmountDollar != null && !info.changeAmountDollar!!.isZero()) {
                    val changeAmount = FoundationHelper.usdConverToKhr(
                        info.conversionRatio, info.changeAmountDollar!!.toLong()
                    )
                    "$${info.changeAmountDollar?.priceFormatTwoDigitZero()} = ៛${changeAmount.decimalFormatZeroDigit()}"
                } else {
                    "$0.00"
                }
            list.add(
                RechargeDetailItem(
                    getString(R.string.back_your_change_amount), changeAmountStr
                )
            )
        }

        //状态
        val originalText = info?.status?.getBalanceStatusType(requireContext())
        val ssb = SpannableStringBuilder(originalText)
        val drawable = ContextCompat.getDrawable(requireContext(), R.drawable.ic_circle)
        drawable?.setBounds(0, 0, 20, 20) // 设置图片尺寸
        drawable?.colorFilter = PorterDuffColorFilter(
            ContextCompat.getColor(
                requireContext(),
                info?.status?.getBalanceTypeColor() ?: R.color.cancel_color
            ), PorterDuff.Mode.SRC_ATOP
        )
        // 在文本前插入图片
        ssb.insert(0, "[dot] ")
        ssb.setSpan(
            ImageSpan(drawable!!, DynamicDrawableSpan.ALIGN_CENTER),
            0, 5, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        list.add(
            RechargeDetailItem(getString(R.string.status), ssb)
        )

        //时间
        list.add(
            RechargeDetailItem(
                getString(R.string.time), info?.createTime?.formatDate()
            )
        )
        //备注
        if (!info?.remark.isNullOrEmpty()) {
            list.add(
                RechargeDetailItem(
                    getString(R.string.remark), info?.remark
                )
            )
        }

        return list
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * PERCENT_85).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    inner class RechargeDetailItem(
        val title: String,
        val content: CharSequence?
    )
}