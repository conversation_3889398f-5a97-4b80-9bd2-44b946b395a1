package com.metathought.food_order.casheir.ui.dialog

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogCreditBinding
import com.metathought.food_order.casheir.extension.formatPhoneNumber
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.network.ApiResponse
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 挂账弹窗
 */
@AndroidEntryPoint
class CreditDialog : BaseDialogFragment(), CountryCodePicker.DialogEventsListener {
    private var binding: DialogCreditBinding? = null
    private var callback: ((AccountInfo) -> Unit)? = null
    private var accountInfo: AccountInfo? = null
    private var totalPrice: Long? = null

    private val viewModel: CreditDialogViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCreditBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
        initData()
        initListener()
        initObserver()

    }

    private fun initObserver() {

        viewModel.uiState.observe(viewLifecycleOwner) {
            it.memberAccountResult?.let { res ->
                binding?.apply {
                    Timber.d(res.toString())
                    when (res) {
                        is ApiResponse.Loading -> {

                        }

                        is ApiResponse.Error -> {
                            textInputLayoutPhoneNumber.error =
                                getString(R.string.unregistered_member_tips)
                            textInputLayoutCustomerName.isVisible = true
                            accountInfo = null
                            checkEnable()
                            btnConfirm.setText(R.string.register_and_credit)
                        }

                        is ApiResponse.Success -> {
                            val resPhone = res.data?.telephone ?: ""
                            val phone =
                                binding?.edtPhoneNumber?.text?.toString()?.replace(" ", "") ?: ""
                            val countryCode = binding?.countryCodeHolder?.selectedCountryCode ?: ""
                            val phoneNumber = "$countryCode$phone"
                            if (resPhone != phoneNumber) {
                                return@let
                            }

                            textInputLayoutCustomerName.isVisible = false
                            textInputLayoutPhoneNumber.error = ""
                            tvCustomerNickname.text = res.data?.nickName
                            edtCustomerName.setText(res.data?.nickName)
                            accountInfo = AccountInfo(
                                accountId = res.data?.accountId,
                                nickName = res.data?.nickName,
                                areaCode = null,
                                telephone = res.data?.telephone,
                            )
                            checkEnable()
                            btnConfirm.setText(R.string.confirm2)
                        }
                    }
                }
            }
        }

    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun initData() {
        binding?.apply {
            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager

            var conversionRatio = FoundationHelper.useConversionRatio
            tvOrderPrice.text = totalPrice?.priceFormatTwoDigitZero2() ?: ""

//            tvCustomerNickname.text =
//                if (accountInfo?.nickName.isNullOrEmpty()) "---" else accountInfo?.nickName
//            edtCustomerName.setText(accountInfo?.nickName)

            if (accountInfo != null) {
                val areaCode = accountInfo?.areaCode ?: ""
                val mobile = accountInfo?.telephone ?: ""
                areaCode.toIntOrNull()?.let {
                    countryCodeHolder.setCountryForPhoneCode(it)
                }
                edtPhoneNumber.setText(mobile.formatPhoneNumber())
                edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
            }
            if (accountInfo?.accountId == null) {
                getMemberAccount()
            }
            checkEnable()
        }


    }

    private val ontextChange = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            checkEnable()
        }

    }
    private val onTextChangePhoneNumber = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            getMemberAccount()
        }

        override fun afterTextChanged(s: Editable?) {
            s?.let { editText(it) }
        }

    }

    private fun getMemberAccount() {
        val phone = binding?.edtPhoneNumber?.text?.toString()?.replace(" ", "") ?: ""
        if (phone.isEmpty()) {
            return
        }
        val countryCode = binding?.countryCodeHolder?.selectedCountryCode ?: ""
        val phoneNumber = "$countryCode$phone"
        viewModel.getMemberAccount(phoneNumber)
    }

    private fun initListener() {
        binding?.apply {
            edtCustomerName.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?, start: Int, count: Int, after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    tvCustomerNickname.text = if (s.isNullOrEmpty()) "---" else s
                }

                override fun afterTextChanged(s: Editable?) {
                    checkEnable()
                }

            })

            countryCodeHolder.setDialogEventsListener(this@CreditDialog)

            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
            edtCustomerName.addTextChangedListener(ontextChange)
//            textInputLayoutPhoneNumber.error = "该手机号未注册为会员，可填写昵称并点注册"
            edtPhoneNumber.addTextChangedListener(ontextChange)
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            btnConfirm.setOnClickListener {
                val aInfo = AccountInfo(
                    accountId = accountInfo?.accountId,
                    nickName = edtCustomerName.text.toString(),
                    areaCode = countryCodeHolder.selectedCountryCode,
                    telephone = edtPhoneNumber.text.toString().replace(" ", ""),
                    reason = edtRemark.text.toString()
                )
                callback?.invoke(aInfo)
                dismissCurrentDialog()
            }
            btnCancel.setOnClickListener {
                dismissCurrentDialog()
            }


//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    private fun checkEnable() {
        binding?.apply {
            btnConfirm.setEnableWithAlpha(
                if (textInputLayoutCustomerName.isVisible) {
                    edtCustomerName.text?.trim()?.isNotEmpty() == true
                            && edtPhoneNumber.text?.isNotEmpty() == true
                } else {
                    edtPhoneNumber.text?.isNotEmpty() == true
                }
            )
        }
    }

    data class AccountInfo(
        var accountId: String?,
        var nickName: String?,
        var areaCode: String?,
        var telephone: String?,
        var reason: String? = null,
    )

    companion object {
        private const val TAG = "EditMemberInfoDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            totalPrice: Long,
            accountInfo: AccountInfo?,
            callback: ((AccountInfo) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(totalPrice, accountInfo, callback)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CreditDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            totalPrice: Long,
            accountInfo: AccountInfo?,
            callback: ((AccountInfo) -> Unit),
        ): CreditDialog {
            val fragment = CreditDialog()
            fragment.totalPrice = totalPrice
            fragment.accountInfo = accountInfo
            fragment.callback = callback

            return fragment
        }
    }

    override fun onCcpDialogOpen(dialog: Dialog?) {

    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.let {
            it.edtPhoneNumber.isFocusable = true
        }
        getMemberAccount()
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {
    }


    private fun editText(editable: Editable) {
        binding?.apply {
            edtPhoneNumber.removeTextChangedListener(onTextChangePhoneNumber)
            val formattedPhoneNumber = editable.toString().formatPhoneNumber()
            edtPhoneNumber.setText(formattedPhoneNumber)

            edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
        }
    }
}
