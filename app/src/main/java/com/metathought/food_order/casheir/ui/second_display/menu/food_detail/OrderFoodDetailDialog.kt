package com.metathought.food_order.casheir.ui.second_display.menu.food_detail

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.denzcoskun.imageslider.constants.ScaleTypes
import com.denzcoskun.imageslider.models.SlideModel
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTag
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.DialogSecondOrderFoodDetailBinding
import com.metathought.food_order.casheir.databinding.DialogSelectedTagPopWindowBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.ui.adapter.MealSetAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondMainSpecificationAdapter
import com.metathought.food_order.casheir.ui.adapter.SecondToppingsAdapter
import com.metathought.food_order.casheir.ui.adapter.SelectedTagPopWindowAdapter
import com.tencent.bugly.crashreport.CrashReport
import com.view.text.addTextTag
import timber.log.Timber

/**
 * 副屏-菜单详情
 * Secondary screen-menu details
 * <AUTHOR>
 * @date 2024/5/1517:20
 * @description
 */
class OrderFoodDetailDialog : Dialog {

    constructor(context: Context) : this(context, 0)
    constructor(context: Context, themeResId: Int) : super(context, themeResId)

    private lateinit var binding: DialogSecondOrderFoodDetailBinding
    private var toppingsAdapter: SecondToppingsAdapter? = null
    private var specificationSubItemAdapter: SecondMainSpecificationAdapter? = null
    private var mealSetAdapter: MealSetAdapter? = null

    private lateinit var mContext: Context
    override fun onCreate(savedInstanceState: Bundle?) {

        /**
         * 防止子弹窗不会显示，还不知道有啥问题
         */
        try {
            if (window != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                } else {
                    window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                }
            }
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }
        super.onCreate(savedInstanceState)
        binding = DialogSecondOrderFoodDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    fun initResource(mContext: Context) {
        this.mContext = mContext
        binding.run {
            tvDishedName.text = mContext.getString(R.string.item_detail)
        }
    }

    fun initData(
        imageList: List<SlideModel>,
        content: Goods,
        serviceChargePercentage: String?,
        discountRate: String?
    ) {
        binding.run {
            imageSlider.setImageList(imageList, ScaleTypes.CENTER_CROP)
            tvDetailInfo.text = content.name
            tvDiscountRate.text = discountRate ?: ""
            tvDiscountRate.isVisible = !discountRate.isNullOrEmpty()
            tvServiceChargePercentage.text = serviceChargePercentage ?: ""
            tvServiceChargePercentage.isVisible = !serviceChargePercentage.isNullOrEmpty()

            val activityLabel = content.activityLabels?.firstOrNull()
            if (activityLabel != null) {
                tvDiscountActivity.setStrokeAndColor(color = Color.parseColor(activityLabel.color))
                tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                tvDiscountActivity.text = activityLabel.name
                tvDiscountActivity.isVisible = true
            } else {
                tvDiscountActivity.isVisible = false
            }

            if (content.isMealSet()) {
                tvDetailInfo.addMealSetTag(mContext)
//                tvDetailInfo.addTextTag {
//                    text = mContext.getString(R.string.set_menu)
//                    this.position = 0
//                    strokeWidth = 1
//                    strokeColor = mContext.getColor(R.color.color_ff7f00)
//                    textSize = 14f
//                    backgroundColor = Color.TRANSPARENT
//                    textColor = mContext.getColor(R.color.color_ff7f00)
//                    marginRight = 4
//                }
            }
        }
    }


    fun initFeeds(feeds: List<Feed>) {
        binding.run {
            tvToppingsTitle.setVisibleGone(feeds.isNotEmpty())
            recyclerTopping.run {
                FlexboxLayoutManager(context).run {
                    flexDirection = FlexDirection.ROW
                    flexWrap = FlexWrap.WRAP
                    justifyContent = JustifyContent.FLEX_START
                    layoutManager = this
                }
                toppingsAdapter = SecondToppingsAdapter(feeds)
                adapter = toppingsAdapter
            }

        }
    }

    fun initGoodsTag(goodsTag: List<GoodsTag>) {
        binding.run {
            recyclerSpecification.run {
                specificationSubItemAdapter = SecondMainSpecificationAdapter(goodsTag)
                adapter = specificationSubItemAdapter
            }
        }
    }

    fun initMealSet(content: Goods) {
        binding?.run {
            rvMealSet.run {
                mealSetAdapter = MealSetAdapter(
                    content.mealSetInfo?.showGoodsPic,
                    content.mealSetInfo?.mealSetGroupList ?: listOf(),
                    content,
                    mContext, isSecondView = true
                )
                adapter = mealSetAdapter
            }

        }
    }

    fun updateMealSet(position: Int) {
        mealSetAdapter?.notifyDataSetChanged()
    }


    fun updateNestedScrollView(dx: Int, dy: Int) {
        binding.run {
            nestedScrollView.scrollTo(dx, dy)
        }
    }

    fun updateToppingAdapter(feed: Feed) {
        toppingsAdapter?.run {
            val index = feeds.indexOf(feed)
            if (index != -1) {
                notifyItemChanged(index)
            }
        }
    }

    fun updateSpecificationAdapter(goods: GoodsTag) {
        specificationSubItemAdapter?.run {
            val index = list.indexOf(goods)
            if (index != -1) {
                notifyItemChanged(index)
            }
        }
    }


    /**
     * 显示弹窗
     *
     */

    private var popupWindow: PopupWindow? = null
    private var selectedTagPopWindowAdapter: SelectedTagPopWindowAdapter? = null
    fun showSelectedTagPopWindow(
        mealSetGroup: MealSetGroup,
        groupIndex: Int,
        goodIndex: Int,
    ) {
        if (popupWindow != null) {
            popupWindow?.dismiss()
            popupWindow = null
        }
//        Timber.e("showSelectedTagPopWindow,${groupIndex} ${goodIndex}")
        binding.apply {
            val view = findPopWindowAttachView(groupIndex, goodIndex)
//            Timber.e("showSelectedTagPopWindow:${view}")
            if (view != null && !mealSetGroup.mealSetGoodsList!![goodIndex].selectItems.isNullOrEmpty()) {
                Timber.e("显示")
                val popupView = DialogSelectedTagPopWindowBinding.inflate(layoutInflater)
                popupWindow = PopupWindow(
                    popupView.root,
                    view.measuredWidth,
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    true
                )
                PopupWindowHelper.addPopupWindow(popupWindow!!)
//                popupWindow!!.animationStyle = R.style.PopupAnimation
                popupWindow!!.setOnDismissListener {
                    PopupWindowHelper.deletePopupWindow(popupWindow!!)
                }
                popupWindow!!.contentView.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                selectedTagPopWindowAdapter = SelectedTagPopWindowAdapter(
                    mealSetGroup.mealSetGoodsList!![goodIndex],
                    mContext, true
                ) {

                }
                popupView.rvSelectedTag.adapter = selectedTagPopWindowAdapter

                popupWindow!!.showAsDropDown(
                    view,
                )

            }
//            mealSetAdapter?.setCurrentGoodSelectIndex(goodIndex)
//            mealSetAdapter?.notifyDataSetChanged()
        }
    }

    fun setCurrentGood(goodIndex: Int) {
        mealSetAdapter?.setCurrentGoodSelectIndex(goodIndex)
        mealSetAdapter?.notifyDataSetChanged()
    }

    fun hideSelectedTagPopWindow() {
        mealSetAdapter?.setCurrentGoodSelectIndex(-1)
        mealSetAdapter?.notifyDataSetChanged()
        popupWindow?.dismiss()
        popupWindow = null
    }

    private fun findPopWindowAttachView(groupIndex: Int, goodIndex: Int): View? {
        val viewHolder = binding.rvMealSet?.findViewHolderForAdapterPosition(groupIndex)
        val goodViewHolder = viewHolder?.itemView?.findViewById<RecyclerView>(R.id.rvMealGood)
            ?.findViewHolderForAdapterPosition(goodIndex)
        return goodViewHolder?.itemView
    }

}