package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterFeedBinding
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.DisplayUtils
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2025/01/10 16:10
 * @description 小票打印 套餐菜品
 */
class PrinterSetMealGoodAdapter(
    val list: List<OrderMealSetGood>,
    val isKitchen: Boolean,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机
    val isShowPrice: Boolean?,
) : RecyclerView.Adapter<PrinterSetMealGoodAdapter.PrinterFeedViewHolder>() {


    inner class PrinterFeedViewHolder(val binding: ItemPrinterFeedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: OrderMealSetGood) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
                var nameString = ""
                val weight =
                    if (resource.isToBeWeighed() && resource.isHasCompleteWeight()) "(${resource.getWeightStr()})" else ""
                localeList.forEachIndexed { index, locale ->
                    val name = resource.getNameByLocale(locale)
                    val num = if ((resource.num ?: 0) > 1) " *${resource.num}" else ""

                    if (isEightyWidth == true) {
                        if (index == 0) {
                            tvFeedNameEn.text = "— $name${num}${weight}"
                            tvFeedNameEn.isVisible = !name.isNullOrEmpty()
                        } else if (index == 1) {
                            tvFeedNameKm.text = "— $name${num}${weight}"
                            tvFeedNameKm.isGone =
                                (tvFeedNameKm.text == tvFeedNameEn.text) || name.isNullOrEmpty()
                        }
                        llItemIndex.isInvisible = true
                    } else {
                        if (nameString.isEmpty()) {
                            nameString = "$name"
                        } else {
                            if (nameString != name) {
                                nameString = "$nameString $name"
                            }
                        }
                    }
                }

                if (isEightyWidth == true) {
                    tvFeedName.isVisible = false
                    llFeedName.isVisible = true
                    llItemIndex.isInvisible = true
//                    llDiscountPrice.isInvisible = true

                    (llItemIndex.layoutParams as LinearLayout.LayoutParams).weight = 1f
                    (llFeedName.layoutParams as LinearLayout.LayoutParams).weight = 2.5f
                    (llFeedFoodCount.layoutParams as LinearLayout.LayoutParams).weight = 1f
                    (llFeedFoodPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
//                    (llDiscountPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                    (llFeedFoodTotal.layoutParams as LinearLayout.LayoutParams).weight =
                        1.5f
//                    tvFeedNameEn.text = "-${tvFeedNameEn.text}"
                    (tvFeedNameEn.layoutParams as ViewGroup.MarginLayoutParams).marginStart =
                        DisplayUtils.dp2px(tvFeedNameEn.context, 0f)
//                    (tvFeedNameEn.layoutParams as ViewGroup.MarginLayoutParams).marginStart =
//                        DisplayUtils.dp2px(tvFeedNameEn.context, 0f)
                    tvFeedNameKm.setPadding(60, 0, 0, 0)
                } else {
                    nameString = "$nameString${weight}"
                    if ((resource.num ?: 1) > 1) {
                        tvFeedName.text = "— $nameString *${resource.num}"
                    } else {
                        tvFeedName.text = "— $nameString"
                    }

//                    tvFeedName.setPadding(20, 0, 0, 0)
                }


                tvFeedFoodCount.text = "x${resource.number}"
                if (isShowPrice == true) {
                    val price = resource.calculateSingleTotalMarkUpPrice()
                    tvFeedFoodPrice.text = price.priceFormatTwoDigitZero2()
                    tvFeedFoodPrice.isVisible = price > 0
                }
            }
        }


    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterFeedBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterSetMealGoodAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}