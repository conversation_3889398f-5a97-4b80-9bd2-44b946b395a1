package com.metathought.food_order.casheir.ui.dialog


import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager

import android.hardware.display.DisplayManager
import android.net.Uri
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogCustomerServiceBinding
import timber.log.Timber



class CustomerServiceDialog : DialogFragment() {

    private var binding: DialogCustomerServiceBinding? = null

    private val url = "https://chat.mos.me/#/chat?id=Mpos_service"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCustomerServiceBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initListener()
    }

    private fun initListener() {
        binding?.apply {
            btnContact.setOnClickListener {
                Timber.e("btnContact")
//                CustomerServiceWebViewDialog.showDialog(parentFragmentManager)

                CustomerServiceWebViewXpopDialog(requireActivity()).showDialog()

                dismissAllowingStateLoss()
            }
        }
    }

    private fun hasBrowser(context: Context): Boolean {
        val packageManager = context.packageManager
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        intent.setData(Uri.parse(url))

        val resolveInfo = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)

        return resolveInfo != null
    }

    companion object {
        private const val TAG = "CustomerServiceDialog"

        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment = fragmentManager.findFragmentByTag(TAG) as? CustomerServiceDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): CustomerServiceDialog {
            val args = Bundle()

            val fragment = CustomerServiceDialog()
            fragment.arguments = args
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}
