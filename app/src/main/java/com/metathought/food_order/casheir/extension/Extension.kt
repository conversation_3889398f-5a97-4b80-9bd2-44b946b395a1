package com.metathought.food_order.casheir.extension

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Outline
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.os.IBinder
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.navOptions
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView.LayoutManager
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.BalanceStatusEnum
import com.metathought.food_order.casheir.constant.BalanceTypeEnum
import com.metathought.food_order.casheir.constant.ChartTimeType
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_REALIZED
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITHOUT_SECOND
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITH_SECOND
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.SessionExpiredDialog
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.view.text.addTextTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.security.MessageDigest
import java.security.Timestamp
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.random.Random


fun NavController.navigateWithAnim(
    idDestination: Int,
    bundle: Bundle? = null,
    popupToId: Int? = null
) {
    val anim = navOptions {
        anim {
            popEnter = R.anim.slide_in_left
            popExit = R.anim.slide_out_right
            enter = R.anim.slide_in_right
            exit = R.anim.slide_out_left
        }
        popupToId?.let {
            popUpTo(popupToId) {
                inclusive = true
            }
        }
    }
    this.navigate(idDestination, bundle, anim)
}


fun Activity?.hideKeyboard() {
    val currentFocusedView = this?.currentFocus
    if (currentFocusedView != null) {
        val inputManager: InputMethodManager =
            this?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputManager.hideSoftInputFromWindow(
            currentFocusedView.windowToken,
            InputMethodManager.HIDE_NOT_ALWAYS
        )
    }
}

fun DialogFragment?.hideKeyboard2() {

    val currentFocusedView = this?.dialog?.currentFocus
    if (currentFocusedView != null) {

        val inputManager: InputMethodManager =
            this!!.requireContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputManager.hideSoftInputFromWindow(
            currentFocusedView.windowToken,
            InputMethodManager.HIDE_NOT_ALWAYS
        )
//        currentFocusedView.clearFocus()
    }
}

fun IBinder.hideKeyboard(context: Context) {
    val inputManager: InputMethodManager =
        context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    inputManager.hideSoftInputFromWindow(this, InputMethodManager.HIDE_NOT_ALWAYS)
}

fun MotionEvent.hideKeyboard(v: View?): Boolean {
    if (action == MotionEvent.ACTION_DOWN) {
        if (isHideInput(v)) {
            v?.clearFocus()
            v?.windowToken?.hideKeyboard(v.context)
        }
    }
    return false
}

fun MotionEvent.isHideInput(v: View?): Boolean {
    if (v != null && (v is EditText)) {
        val l = intArrayOf(0, 0)
        v.getLocationInWindow(l)
        val left = l[0]
        val top: Int = l.get(1)
        val bottom: Int = top + v.getHeight()
        val right: Int = (left
                + v.getWidth())
        return !(x > left && x < right && y > top
                && y < bottom)
    }
    return false
}


fun Activity?.showKeyboard(editText: EditText) {
    editText.requestFocus()
    editText.requestFocusFromTouch()
    val inputManager: InputMethodManager =
        this?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    inputManager.showSoftInput(editText, 0)
}

fun Context?.hideKeyboard(editText: EditText) {
    editText.clearFocus()
    val inputManager: InputMethodManager =
        this?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    inputManager.hideSoftInputFromWindow(
        editText.windowToken, 0
    )
}

fun View.setVisibleGone(visible: Boolean) {
    this.visibility = if (visible) View.VISIBLE else View.GONE
}

fun View.setVisibleInvisible(visible: Boolean) {
    this.visibility = if (visible) View.VISIBLE else View.INVISIBLE
}

fun View.setRadius(radiusDp: Float) {
    val radius = DisplayUtils.dp2px(context, radiusDp)
    //设置圆角大小
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            //设置矩形
            outline.setRoundRect(0, 0, view.width, view.height, radius.toFloat())
        }
    }
    //设置圆角裁切
    clipToOutline = true
}

fun View.setEnable(enable: Boolean) {
    this.isClickable = enable
    this.isEnabled = enable
//    this.alpha = if (enable) 1.0f else 0.5f
}

fun View.setEnableWithAlpha(enable: Boolean) {
    this.isClickable = enable
    this.isEnabled = enable
    this.alpha = if (enable) 1.0f else 0.5f
}

fun ImageView.setEnableAdd(enable: Boolean) {
    this.isClickable = enable
    this.isEnabled = enable
    this.setImageDrawable(
        ContextCompat.getDrawable(
            context,
            if (enable) R.drawable.ic_add else R.drawable.ic_add_disable
        )
    )
}

fun ImageView.setEnableMinus(enable: Boolean) {
    this.isClickable = enable
    this.isEnabled = enable
    this.setImageDrawable(
        ContextCompat.getDrawable(
            context,
            if (enable) R.drawable.ic_minus else R.drawable.ic_minus_disable
        )
    )
}

fun Any.toJson(): String {
    return Gson().toJson(this)
}

fun Any.toRequestBody(): RequestBody {
    val contentType = "application/json; charset=utf-8"
    return toJson().toRequestBody(contentType = contentType.toMediaTypeOrNull())
}

fun Number.decimalFormatZeroDigit(): String {
    val symbols = DecimalFormatSymbols(Locale.US)
    val decimalFormat = DecimalFormat("#,##0", symbols)
    return try {
        decimalFormat.format(this)
    } catch (e: Exception) {
        println(e)
        this.toString()
    }
}

fun Number.decimalFormatTwoDigit(): String {
    val symbols = DecimalFormatSymbols(Locale.US)
    val decimalFormat = DecimalFormat("#,##0.##", symbols)
    return try {
        decimalFormat.format(this)
    } catch (e: Exception) {
        println(e)
        ""
    }

}

fun Number.priceFormatTwoDigit(unit: String): String {
    val formatPrice = decimalFormatTwoDigit()
    val isNegative = formatPrice.startsWith("-")
    return try {
        if (isNegative) {
            "-$unit${formatPrice.substring(1)}"
        } else {
            "$unit${formatPrice}"
        }
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Number.priceFormatTwoDigitZero2(unit: String): String {
    val formatPrice = decimalFormatTwoDigitZero()
    val isNegative = formatPrice.startsWith("-")
    return try {
        if (isNegative) {
            "-$unit${formatPrice.substring(1)}"
        } else {
            "$unit${formatPrice}"
        }
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Number.priceFormatZeroDigit(unit: String): String {
    val formatPrice = decimalFormatZeroDigit()
    val isNegative = formatPrice.startsWith("-")
    return try {
        if (isNegative) {
            "-$unit${formatPrice.substring(1)}"
        } else {
            "$unit${formatPrice}"
        }
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Number.decimalFormatTwoDigitZero(): String {
    val symbols = DecimalFormatSymbols(Locale.US)
    val decimalFormat = DecimalFormat("#,##0.00", symbols)
    return try {
        decimalFormat.format(this)
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Number.decimalFormatTwoDigitZero2(): String {
    val symbols = DecimalFormatSymbols(Locale.US)
    val decimalFormat = DecimalFormat("###0.00", symbols)
    return try {
        decimalFormat.format(this)
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Double.priceDecimalFormatTwoDigitZero(): String {
    val formatPrice = (this / 100).decimalFormatTwoDigitZero()
    return try {
        formatPrice
    } catch (e: Exception) {
        println(e)
        ""
    }
}

//fun Long.priceFormatTwoDigitZeroKHR(): String {
//    val formatPrice = (this / 100.0).decimalFormatZeroDigit()
//    return try {
//        formatPrice
//    } catch (e: Exception) {
//        println(e)
//        ""
//    }
//}

fun Long.priceFormatTwoDigitZero(): String {
    val formatPrice = (this / 100.0).decimalFormatTwoDigitZero()
    return try {
        formatPrice
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Long.priceFormatTwoDigitZero3(): String {
    val formatPrice = (this / 100.0).decimalFormatTwoDigitZero2()
    return try {
        formatPrice
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Int.priceFormatTwoDigitZero(): String {
    val formatPrice = (this / 100.0).decimalFormatTwoDigitZero()
    return try {
        formatPrice
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Int.priceFormatTwoDigitZero2(): String {
    val formatPrice = (this / 100.0).decimalFormatTwoDigitZero()
    return try {
        "$${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Long.priceFormatTwoDigitZero2(): String {
    val formatPrice = (this / 100.0).decimalFormatTwoDigitZero()
    return try {
        "$${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun Double.priceFormatTwoDigitZero2(): String {
    val formatPrice = this.decimalFormatTwoDigitZero()
    return try {
        "$${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun BigDecimal.priceFormatTwoDigitZero(): String {
    val formatPrice = this.divide(BigDecimal(100)).halfUp(2).decimalFormatTwoDigitZero()
    return try {
        "${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun BigDecimal.priceFormatTwoDigitZero1(): String {
    val formatPrice = this.divide(BigDecimal(100)).halfUp(2).decimalFormatTwoDigitZero()
    return try {
        "$${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun BigDecimal.priceFormatTwoDigitZero2(): String {
    val formatPrice = this.decimalFormatTwoDigitZero()
    return try {
        "$${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}


fun BigDecimal.priceFormatTwoDigitZero3(): String {
    val formatPrice = this.divide(BigDecimal(100)).halfUp(2).decimalFormatTwoDigitZero2()
    return try {
        "${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

fun BigDecimal.priceFormatTwoDigitZero4(): String {
    val formatPrice = this.divide(BigDecimal(100)).halfUp(2).decimalFormatTwoDigitZero2()
    return try {
        "$${formatPrice}"
    } catch (e: Exception) {
        println(e)
        ""
    }
}

val Int.dp: Int
    get() = (this / Resources.getSystem().displayMetrics.density).toInt()
val Int.px: Int
    get() = (this * Resources.getSystem().displayMetrics.density).toInt()

fun Int.toDp(resources: Resources): Float {
    return TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this.toFloat(),
        resources.displayMetrics
    )
}

fun String.parseDate(format: String): Date? {
    return try {
        if (isNullOrEmpty()) {
            return null
        }
        val dateFormat = SimpleDateFormat(format, Locale.US)
        try {
            dateFormat.parse(this)
        } catch (e: ParseException) {
//            e.printStackTrace()
            null
        }
    } catch (e: Exception) {
//        Timber.e(e)
        null
    }
}

fun String.getShowingPhoneNumber(): String? {
    return try {
        if (this.length > 3) {
            if (this.startsWith("855")) {
                return this.replace("855", "855 ")
            } else {
                return "+".plus(this)
            }
        } else {
            return this
        }
    } catch (e: Exception) {
        Timber.e(e)
        null
    }
}

fun Date.formatDateStr(
    format: String,
): String {
    return try {
        val dateFormat = SimpleDateFormat(format, Locale.US)
        dateFormat.format(this)
    } catch (e: Exception) {
        Timber.e(e)
        ""
    }
}

fun generateRandomList(size: Int): ArrayList<String> {
    val itemList = mutableListOf<String>()
    val random = Random(System.currentTimeMillis())

    val chars = ('A'..'Z') + ('a'..'z') + ('0'..'9') // Characters for generating random strings

    repeat(size) {
        val randomString = (1..Random.nextInt(30)).map { chars.random(random) }
            .joinToString("") // Generate a random string of length 10
        itemList.add(randomString)
    }

    return ArrayList(itemList)
}

fun String.formatPhoneNumber(): String {
    val digits = this.replace("[^\\d]".toRegex(), "")
    val formatted = StringBuilder()
    for (i in digits.indices) {
        if (i == 2 || i == 5 || i == 8) {
            formatted.append(' ')
        }
        formatted.append(digits[i])
    }
    return formatted.toString()
}


fun String?.getConsumePhoneNumber(): Pair<String, String> {
    if (!this.isNullOrEmpty()) {
        val list = (this).split(" ")
        if (list.size == 2) {
            return Pair(list[0], if (list[1] == "null") "" else list[1])
        }
    }
    return Pair("", "")
}

fun String.formatPhoneNumber3Digit(): String {
    val digits = this.replace("[^\\d]".toRegex(), "")
    val formatted = StringBuilder()
    for (i in digits.indices) {
        if (i % 3 == 0) {
            formatted.append(' ')
        }
        formatted.append(digits[i])
    }
    return formatted.toString()
}

fun String.toHexStr(): String {
    val digest = MessageDigest.getInstance("SHA-1").digest(this.toByteArray())
    return with(StringBuffer()) {
        digest.forEach {
            val hex = it.toInt() and (0xFF)
            val hexStr = Integer.toHexString(hex)
            if (hexStr.length == 1) append("0").append(hexStr)
            else append(hexStr)
        }
        toString()
    }
}


fun Int.getPayTypeColor(): Int {
    return when (this) {
        1 -> {
            R.color.unpaid_text_color
        }

        2 -> {
            R.color.paid_text_color
        }

        3 -> {
            R.color.partial_refund_text_color
        }

        4 -> {
            R.color.refund_text_color
        }

        5 -> {
            R.color.canceled_text_color
        }

        6 -> {
            R.color.confirm_text_color
        }

        7 -> {
            R.color.paid_text_color
        }

        8 -> {
            R.color.confirm_text_color
        }

        10 -> {//挂账未支付
            R.color.confirm_text_color
        }

        11 -> {//挂账已支付
            R.color.paid_text_color
        }

        else -> {
            R.color.ordered_cancel_color
        }
    }
}

fun Int.getPayTypeBackGroundColor(): Int {
    return when (this) {
        1 -> {
            R.color.unpaid_backgroud_color
        }

        2 -> {
            R.color.paid_backgroud_color
        }

        3 -> {
            R.color.partial_refund_backgroud_color
        }

        4 -> {
            R.color.refund_backgroud_color
        }

        5 -> {
            R.color.canceled_backgroud_color
        }

        6 -> {
            R.color.confirm_backgroud_color
        }

        7 -> {
            R.color.paid_backgroud_color
        }

        8 -> {
            R.color.confirm_backgroud_color
        }

        10 -> {//挂账未支付
            R.color.confirm_backgroud_color
        }

        11 -> {//挂账已支付
            R.color.paid_backgroud_color
        }

        else -> {
            R.color.ordered_cancel_color
        }
    }
}

fun Int.getSourcePlatform(context: Context): String {
    return when (this) {
        SourcePlatformEnum.H5.id -> {
            context.getString(R.string.h5_user)
        }

        SourcePlatformEnum.Employee.id -> {
            context.getString(R.string.employee_terminal)
        }

        SourcePlatformEnum.Kiosk.id -> {
            context.getString(R.string.kiosk)
        }

        SourcePlatformEnum.Cashier.id -> {
            context.getString(R.string.cashier)
        }

        else -> {
            context.getString(R.string.unknown)
        }
    }
}

fun Int.getSourcePlatformByLocale(context: Context, locale: Locale): String {
    return when (this) {
        SourcePlatformEnum.H5.id -> {
            context.getStringByLocale(R.string.h5_user, locale)
        }

        SourcePlatformEnum.Employee.id -> {
            context.getStringByLocale(R.string.employee_terminal, locale)
        }

        SourcePlatformEnum.Kiosk.id -> {
            context.getStringByLocale(R.string.kiosk, locale)
        }

        SourcePlatformEnum.Cashier.id -> {
            context.getStringByLocale(R.string.cashier, locale)
        }

        else -> {
            context.getStringByLocale(R.string.unknown, locale)
        }
    }
}

fun Int.getPaymentMethodString(context: Context): String {
    return when (this) {
        PayTypeEnum.CREDIT.id -> context.getString(R.string.credit_payment)
        PayTypeEnum.ONLINE_PAYMENT.id -> context.getString(R.string.online_payment)
        PayTypeEnum.CASH_PAYMENT.id -> context.getString(R.string.pay_by_cash)
        PayTypeEnum.USER_BALANCE.id -> context.getString(R.string.pay_by_balance)
        else -> {
            context.getString(R.string.n_a)
        }
    }
}

fun Int.getPrinterPaymentMethodString(context: Context, locale: Locale): String {
    return when (this) {
        PayTypeEnum.CREDIT.id -> context.getStringByLocale(R.string.credit_payment, locale)
        PayTypeEnum.ONLINE_PAYMENT.id -> context.getStringByLocale(R.string.online_payment, locale)
        PayTypeEnum.CASH_PAYMENT.id -> context.getStringByLocale(R.string.pay_by_cash, locale)
        PayTypeEnum.USER_BALANCE.id -> context.getStringByLocale(R.string.pay_by_balance, locale)
        else -> {
            context.getString(R.string.unknown)
        }
    }
}

fun Int.getDiningStyleString(context: Context): String {
    return when (this) {
        DiningStyleEnum.DINE_IN.id -> context.getString(R.string.dine_in)
        DiningStyleEnum.PRE_ORDER.id -> context.getString(R.string.pre_order)
        DiningStyleEnum.TAKE_AWAY.id -> context.getString(R.string.take_away)
        DiningStyleEnum.TAKE_OUT.id -> context.getString(R.string.take_out)
        else -> {
            context.getString(R.string.dine_in)
        }
    }
}

fun Int.getPrinterDiningStyleString(context: Context, locale: Locale): String {
    return when (this) {
        DiningStyleEnum.DINE_IN.id -> context.getStringByLocale(R.string.dine_in, locale)
        DiningStyleEnum.PRE_ORDER.id -> context.getStringByLocale(R.string.pre_order, locale)
        DiningStyleEnum.TAKE_AWAY.id -> context.getStringByLocale(R.string.take_away, locale)
        DiningStyleEnum.TAKE_OUT.id -> context.getStringByLocale(R.string.take_out, locale)
        else -> {
            context.getString(R.string.dine_in)
        }
    }
}

fun Context.getStringByLocale(id: Int, locale: Locale): String {
    return createConfigurationContext(Configuration(resources.configuration).apply {
        setLocale(locale)
    }).resources.getString(id)
}

fun Int.getAcceptTypeColor(): Int {
    return when (this) {
        0, 1 -> {
            R.color.unpaid_text_color
        }


        2 -> {
            R.color.canceled_text_color
        }

        else -> {
            R.color.ordered_cancel_color
        }
    }
}

fun Int.getAcceptTypeBackGroundColor(): Int {
    return when (this) {
        0, 1 -> {
            R.color.unpaid_backgroud_color
        }

        2 -> {
            R.color.canceled_backgroud_color
        }

        else -> {
            R.color.ordered_cancel_color
        }
    }
}

fun Int.getAcceptText(context: Context): String {
    return when (this) {
        0 -> {
            context.getString(R.string.wait_accept_order)
        }

        1 -> {
            context.getString(R.string.has_accept_order)
        }

        2 -> {
            context.getString(R.string.order_cancel)
        }

        else -> {
            context.getString(R.string.unknown)
        }
    }
}

fun Int.getPayText(context: Context): String {
    return when (this) {
        1 -> {
            context.getString(R.string.unpaid)
        }

        2 -> {
            context.getString(R.string.paid)
        }

        3 -> {
            context.getString(R.string.partial_refund)
        }

        4 -> {
            context.getString(R.string.refunded)
        }

        5 -> {
            context.getString(R.string.order_cancel)
        }

        6 -> {
            context.getString(R.string.ordered_confirmed)
        }

        7 -> {
            context.getString(R.string.pre_order)
        }

        8 -> {
            context.getString(R.string.to_be_confirmed)
        }

        10 -> {//挂账未支付
            context.getString(R.string.credit_unpaid)
        }

        11 -> {//挂账已支付
            context.getString(R.string.credit_paid)
        }

        else -> {
            context.getString(R.string.unknown)
        }
    }
}

//fun Int.getPrintTypeFromDiningStyle(): PrintTemplateTypeEnum {
//    return when (this) {
//        DiningStyleEnum.DINE_IN.id -> PrintTemplateTypeEnum.DINE_IN
//        DiningStyleEnum.TAKE_AWAY.id -> PrintTemplateTypeEnum.TAKE_AWAY
//        DiningStyleEnum.PRE_ORDER.id -> PrintTemplateTypeEnum.PRE_ORDER
//        else -> {
//            PrintTemplateTypeEnum.DINE_IN
//        }
//    }
//}

fun Bitmap.addBackgroundColor(): Bitmap {
    val newBitmap = Bitmap.createBitmap(width, height, config)
    val canvas = Canvas(newBitmap)
    canvas.drawColor(Color.WHITE)
    canvas.drawBitmap(this, 0F, 0F, null)
    return newBitmap
}

fun Int.getBalanceType(context: Context): String {
    return when (this) {
        BalanceTypeEnum.PAID.id -> context.getString(R.string.consumption)
        BalanceTypeEnum.REFUND.id -> context.getString(R.string.refund)
        BalanceTypeEnum.TOP_UP.id -> context.getString(R.string.top_up)
        else -> {
            context.getString(R.string.unknown)
        }
    }
}

fun Int.getBalanceStatusType(context: Context): String {
    return when (this) {
        BalanceStatusEnum.SUCCESS.id -> context.getString(R.string.success)
        BalanceStatusEnum.FAIL.id -> context.getString(R.string.fail)
        BalanceStatusEnum.CANCEL.id -> context.getString(R.string.cancel)
        else -> {
            context.getString(R.string.unknown)
        }
    }
}

fun Int.getBalanceTypeColor(): Int {
    return when (this) {
        BalanceStatusEnum.SUCCESS.id -> R.color.paid_text_color
        BalanceStatusEnum.FAIL.id -> R.color.refund_text_color
        BalanceStatusEnum.CANCEL.id -> R.color.cancel_color
        else -> {
            R.color.cancel_color
        }
    }
}

fun String.getBalanceTypeEnum(context: Context): Int {
    return when (this) {
        context.getString(R.string.consumption) -> BalanceTypeEnum.PAID.id
        context.getString(R.string.refund) -> BalanceTypeEnum.REFUND.id
        context.getString(R.string.top_up) -> BalanceTypeEnum.TOP_UP.id
        else -> {
            BalanceTypeEnum.All.id
        }
    }
}

fun String.getDateTypeEnum(context: Context): String {
    return when (this) {
        context.getString(R.string.today) -> ChartTimeType.TODAY.type
        context.getString(R.string.this_week) -> ChartTimeType.WEEK.type
        context.getString(R.string.this_month) -> ChartTimeType.MONTH.type
        context.getString(R.string.this_quarter) -> ChartTimeType.QUARTER.type
        context.getString(R.string.this_year) -> ChartTimeType.YEAR.type
        else -> {
            ""
        }
    }
}

fun AppCompatActivity.ShowSessionExpiredDialog(msg: String? = null) {
    SessionExpiredDialog.showDialog(this.supportFragmentManager, msg)
}

//fun Int.getType(): String {
//    return when (this) {
//        R.id.radioToday -> ChartTimeType.TODAY.type
//        R.id.radioThisWeek -> ChartTimeType.WEEK.type
//        R.id.radioMonth -> ChartTimeType.MONTH.type
//        R.id.radioQuarter -> ChartTimeType.QUARTER.type
//        R.id.radioYear -> ChartTimeType.YEAR.type
//        else -> ""
//    }
//}

/**
 * Format date   yyyy/MM/dd HH:mm:ss
 *
 * @return
 */
fun String.formatDate(): String {
    try {
        if (this.contains("/")) {
            val parseDate = this.parseDate(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
            parseDate?.let {
                return it.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
            }
        } else {
            val parseDate = this.parseDate(FORMAT_DATE_TIME_REALIZED)
            parseDate?.let {
                return it.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
            }
        }
        return this
    } catch (e: Exception) {
        return this
    }
}

/**
 * Format date   yyyy/MM/dd
 *
 * @return
 */
fun String.formatDate2(): String {
    try {
        if (this.contains("/")) {
            val parseDate = this.parseDate(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
            parseDate?.let {
                return it.formatDateStr(FORMAT_DATE)
            }
        } else {
            val parseDate = this.parseDate(FORMAT_DATE_TIME_REALIZED)
            parseDate?.let {
                return it.formatDateStr(FORMAT_DATE)
            }
        }
        return this
    } catch (e: Exception) {
        return this
    }
}

fun String.formatDate3(): String {
    try {
        val parseDate = this.parseDate(FORMAT_DATE_TIME_SHOW)
        parseDate?.let {
            return it.formatDateStr(FORMAT_DATE_TIME_REALIZED)
        }
        return this
    } catch (e: Exception) {
        return this
    }
}

fun String.formatDate4(): String {
    try {
        val parseDate = this.parseDate(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
        parseDate?.let {
            return it.formatDateStr(FORMAT_DATE_TIME_REALIZED)
        }
        return this
    } catch (e: Exception) {
        return this
    }
}


fun String.formatDateWithoutSecond(): String {
    try {
        if (this.contains("/")) {
            val parseDate = this.parseDate(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
            parseDate?.let {
                return it.formatDateStr(FORMAT_DATE_TIME_SHOW_WITHOUT_SECOND)
            }
        } else {
            val parseDate = this.parseDate(FORMAT_DATE_TIME_REALIZED)
            parseDate?.let {
                return it.formatDateStr(FORMAT_DATE_TIME_SHOW_WITHOUT_SECOND)
            }
        }
        return this
    } catch (e: Exception) {
        return this
    }
}

fun String.convertToTimestamp(): Long {
    try {
        val str = this.replace("-", "/")
        val format = SimpleDateFormat(FORMAT_DATE_TIME_SHOW_WITHOUT_SECOND)
        val date = format.parse(str)
        return date.time
    } catch (e: Exception) {
        return Date().time
    }
}

fun String.convertToTimestamp2(): Long {
    try {
        val str = this.replace("-", "/")
        val format = SimpleDateFormat(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
        val date = format.parse(str)
        return date.time
    } catch (e: Exception) {
        return Date().time
    }
}

//格式化时间戳时间到分钟 yyyy/MM/dd HH:mm
fun Long.formatTimestamp(): String {
    val dateFormat = SimpleDateFormat(FORMAT_DATE_TIME_SHOW)
    return try {
        dateFormat.format(Date(this))
    } catch (e: Exception) {
        dateFormat.format(Date())
    }
}

//格式化时间戳时间到分钟 yyyy/MM/dd HH:mm:ss
fun Long.formatTimestamp2(): String {
    val dateFormat = SimpleDateFormat(FORMAT_DATE_TIME_REALIZED)
    return try {
        dateFormat.format(Date(this))
    } catch (e: Exception) {
        dateFormat.format(Date())
    }
}

fun Int.getColor(context: Context): Int {
    return ContextCompat.getColor(context, this)
}

fun Double.isInt(): Boolean {
    return this.isFinite() && this.toInt().toDouble() == this
}

fun LayoutManager.getScrollPosition(dy: Int): Int {
    val manager = this as LinearLayoutManager
    return if (dy < 0) {
        //onScrolledUp
        manager.findFirstCompletelyVisibleItemPosition()
    } else {
        //onScrolledDown
        manager.findLastCompletelyVisibleItemPosition()
    }
}

fun String.md5(): String {
    val md5 = MessageDigest.getInstance("MD5")
    val digest = md5.digest(this.toByteArray())
    return BigInteger(1, digest).toString(16).padStart(32, '0')
}

//double类型的字符串转成int
fun String.doubleStrToIntStr(): String? {
    return try {
        this.toDoubleOrNull()?.toInt().toString()
    } catch (e: Exception) {
        this
    }
}

fun BaseFragment.countDown(
    time: Int = 30,
    start: (scop: CoroutineScope) -> Unit,
    next: (time: String) -> Unit,
    end: () -> Unit
) {
    lifecycleScope.launch {
        // 在这个范围内启动的协程会在Lifecycle被销毁的时候自动取消
        flow {
            (time downTo 0).forEach {
                delay(1000)
                emit(it)
            }
        }.onStart {
            // 倒计时开始 ，在这里可以让Button 禁止点击状态
            start(this@launch)
        }.onCompletion {
            // 倒计时结束 ，在这里可以让Button 恢复点击状态
            end()
        }.catch {
            //错误
            Timber.e("", it.message ?: "Unkown Error")
        }.collect {
            // 在这里 更新值来显示到UI
            next(it.toString())
        }
    }
}

fun View.setStrokeAndColor(width: Int? = 1, color: Int) {
    val drawable = GradientDrawable()
    drawable.shape = GradientDrawable.RECTANGLE
    drawable.setColor(Color.WHITE)
    drawable.cornerRadius = 4f // 设置圆角半径
    drawable.setStroke(1, color) // 可选：设置边框宽度和颜色
    background = drawable
}

fun Double.roundToTwoDecimalPlaces(): Double {
    return BigDecimal(this).halfUp(2).toDouble()
}

fun BigDecimal.halfUp(scale: Int): BigDecimal {
    return this.setScale(scale, RoundingMode.HALF_UP)
}

/**
 * 瑞尔的四舍五入
 *
 * @return
 */
fun Long.halfUpKhr(): Long {
//    val s = BigDecimal(this.toString()).divide(BigDecimal("100.0"))
    return BigDecimal(this).divide(BigDecimal(100.0)).halfUp(0)
        .times(BigDecimal(100)).toLong()
}

/**
 * 格式化带时区的时间
 *
 * @param time
 * @return
 */
fun String.formatDateFromTimeZone(): String? {
    try {
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        val split = this?.split("+") ?: listOf()
        sdf.timeZone = java.util.TimeZone.getTimeZone("GMT+${split[1]}")
        val date = sdf.parse(this)
//        println("转换后的日期对象: ${date.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND)}")
        return date.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
    } catch (e: ParseException) {
        e.printStackTrace()
        return this
    }
}

fun String.getRedStar(resources: Resources): SpannableString? {
    try {
        val spannableString = SpannableString(this)
        val starIndex = this.indexOf("*")
        if (starIndex != -1) {
            spannableString.setSpan(
                ForegroundColorSpan(resources.getColor(R.color.RED)),
                starIndex,
                starIndex + 1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        return spannableString
    } catch (e: ParseException) {
        e.printStackTrace()
        return SpannableString(this)
    }
}

fun BigDecimal.isZero(): Boolean {
    return this.compareTo(BigDecimal.ZERO) == 0
}

fun TextView.addMealSetTag(context: Context) {
    this.addTextTag {
        text = context.getString(R.string.set_menu)
        this.position = 0
        strokeWidth = 1
        strokeColor = context.getColor(R.color.color_ff7f00)
        textSize = DisplayUtils.dpf2(context, 12f)
        backgroundColor = Color.TRANSPARENT
        textColor = context.getColor(R.color.color_ff7f00)
        marginRight = 4
    }
}

