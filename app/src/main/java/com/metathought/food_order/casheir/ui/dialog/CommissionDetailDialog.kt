package com.metathought.food_order.casheir.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.DialogCommissionDetailBinding
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.ui.adapter.CommissionDetailItemAdapter
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 *  佣金详情
 *
 * @constructor Create empty Commission detail dialog
 */
@AndroidEntryPoint
class CommissionDetailDialog : BaseDialogFragment() {
    private var binding: DialogCommissionDetailBinding? = null
    private var goods: List<Goods?>? = null
    private var takeOutPlatformModel: TakeOutPlatformModel? = null
    private var conversionRatio: Long? = null
    private var adapter: CommissionDetailItemAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCommissionDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.6).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initObserver() {

    }


    private fun initData() {
        binding?.apply {
            topBar.setTitle(takeOutPlatformModel?.name ?: "")

            context?.let {
                Timber.e("goods => ${goods?.size}")
                goods?.let { it1 ->

                    adapter = CommissionDetailItemAdapter(
                        ArrayList(it1),
                        takeOutPlatformModel,
                        conversionRatio ?: FoundationHelper.useConversionRatio!!
                    )
                    recyclerOrderedFood.adapter = adapter

                    countTotalPrice()
                }
            }
        }
    }

    private fun countTotalPrice() {
        var totalCommission = 0L
        goods?.forEach {
            totalCommission += (it?.totalCommission ?: 0L)
        }

        binding?.apply {

            tvCommissionPrice.text = FoundationHelper.getPriceStrByUnit(
                conversionRatio ?: FoundationHelper.useConversionRatio!!,
                totalCommission,
                takeOutPlatformModel?.isKhr() == true
            )
        }
    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener() {
                dismissAllowingStateLoss()
            }

        }
    }

    companion object {
        private const val TAG = "CommissionDetailDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            goods: List<Goods?>?,
            takeOutPlatformModel: TakeOutPlatformModel? = null,
            conversionRatio: Long? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return

            fragment = newInstance(
                goods = goods,
                takeOutPlatformModel = takeOutPlatformModel,
                conversionRatio = conversionRatio
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CommissionDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            goods: List<Goods?>?,
            takeOutPlatformModel: TakeOutPlatformModel? = null,
            conversionRatio: Long? = null,
        ): CommissionDetailDialog {
            val args = Bundle()
            val fragment = CommissionDetailDialog()
            fragment.goods = goods
            fragment.arguments = args
            fragment.takeOutPlatformModel = takeOutPlatformModel
            fragment.conversionRatio = conversionRatio
            return fragment
        }
    }

}
