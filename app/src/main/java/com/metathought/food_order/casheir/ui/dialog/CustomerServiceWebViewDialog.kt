package com.metathought.food_order.casheir.ui.dialog


import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.net.Uri
import android.net.http.SslError
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.webkit.ConsoleMessage
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.content.ContextCompat.getSystemService
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogCustomerServiceWebviewBinding
import timber.log.Timber
import kotlin.math.abs

/**
 * 客服webview （弃用，改用CustomerServiceWebViewXpopDialog）
 *
 * @constructor Create empty Customer service web view dialog
 */
class CustomerServiceWebViewDialog : BaseDialogFragment() {

    private var binding: DialogCustomerServiceWebviewBinding? = null

    private val url = "https://chat.mos.me/#/chat?id=Mpos_service"
//    private val url = "https://chat-test.mos.me/#/chat?id=Mpos_service"
//    private val url = "https://www.baidu.com"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCustomerServiceWebviewBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        openKeyBoardListener()
        onTouchOutSide(binding?.root) {
            //隐藏webview 弹出的键盘
            binding?.apply {
                val inputMethodManager =
                    requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(webView.windowToken, 0)
            }
        }

        Timber.e("打开客服webview")
        binding?.apply {
            observeKeyboardChanges(root.rootView)
            webView.webChromeClient = object : WebChromeClient() {
                override fun onConsoleMessage(consoleMessage: ConsoleMessage): Boolean {
                    if (consoleMessage.message() == "'webkitIndexedDB' is deprecated") {
                        // 可以在这里进行处理，例如记录日志或显示警告信息
                        println("警告：webkitIndexedDB 已弃用")
                    }
                    return super.onConsoleMessage(consoleMessage)
                }
            }
            webView.settings.javaScriptEnabled = true
            // 启用 DOM 存储
            webView.settings.domStorageEnabled = true

            webView.loadUrl(url)
        }
        Timber.e("是否有浏览器 ${hasBrowser(requireContext())}")
    }

    private var lastKeyboardHeight = -1
    private val handler = Handler(Looper.getMainLooper())
    private val keyboardRunnable = Runnable { /* 处理键盘高度变化 */
        Timber.e("lastKeyboardHeight  $lastKeyboardHeight")
        if (isShowKeyBoard()) {
            adjustDialogHeight(
                rootView = binding?.root?.rootView!!,
                defaultHeight - lastKeyboardHeight
            )
        } else {
            resetDialogHeight(rootView = binding?.root?.rootView!!)
        }
    }

    private fun observeKeyboardChanges(rootView: View) {
        rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val rect = Rect()
            rootView.getWindowVisibleDisplayFrame(rect)
            val screenHeight = rootView.height
            var keyboardHeight = screenHeight - rect.bottom
            // 只有键盘高度变化超过阈值时才处理（防抖 + 去重）
            Timber.e(
                "abs(keyboardHeight - lastKeyboardHeight):${keyboardHeight} ${lastKeyboardHeight} ${
                    abs(
                        keyboardHeight - lastKeyboardHeight
                    )
                }"
            )
            if (abs(keyboardHeight - lastKeyboardHeight) > 10) { // 阈值设为10px
                handler.removeCallbacks(keyboardRunnable)
                handler.postDelayed(keyboardRunnable, 100) // 延迟100ms执行Y

                lastKeyboardHeight = abs(keyboardHeight)
                Timber.e("lastKeyboardHeight 前 :${lastKeyboardHeight}")
                if (lastKeyboardHeight < 0) {
                    lastKeyboardHeight = 0
                }
                Timber.e("lastKeyboardHeight 后 :${lastKeyboardHeight}")
            }
        }
    }

    private fun adjustDialogHeight(rootView: View, availableHeight: Int) {
        // 获取弹窗的 Window 并设置高度
        dialog?.window?.let { window ->
            val params = window.attributes
            params.height = (defaultHeight * 0.6).toInt()
            window.attributes = params

        }
    }

    private fun resetDialogHeight(rootView: View) {
        dialog?.window?.let { window ->
            val params = window.attributes
            params.height = defaultHeight // 恢复默认高度
            window.attributes = params
        }
    }

    private var defaultHeight = 0

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            defaultHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenHeight = defaultHeight
            val screenWidth = defaultHeight / 4 * 3
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    private fun hasBrowser(context: Context): Boolean {
        val packageManager = context.packageManager
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        intent.setData(Uri.parse(url))

        val resolveInfo = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)

        return resolveInfo != null
    }

    companion object {
        private const val TAG = "CustomerServiceWebViewD"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? CustomerServiceWebViewDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): CustomerServiceWebViewDialog {
            val args = Bundle()

            val fragment = CustomerServiceWebViewDialog()
            fragment.arguments = args
            return fragment
        }
    }

}
