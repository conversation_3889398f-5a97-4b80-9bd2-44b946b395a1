package com.metathought.food_order.casheir.ui.member.balace.topup

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.data.model.base.request_model.member.TopUpCanUseCouponRequest
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.DialogTopupBinding
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setRadius
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.CouponHelper
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.RechargeLevelAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import com.metathought.food_order.casheir.ui.ordered.coupon.GiftCouponDetailDialog
import com.metathought.food_order.casheir.ui.ordered.coupon.CouponListDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.math.BigDecimal


/**
 * 充值弹窗
 *
 * @constructor Create empty Topup balance dialog
 */

@AndroidEntryPoint
class TopupBalanceDialog : BaseDialogFragment() {
    private var binding: DialogTopupBinding? = null
    private var callBackPayClick: ((Boolean) -> Unit)? = null
    private var menuOrderScreen: SecondaryScreenUI? = null
    private var memberInfo: CustomerMemberResponse? = null
    private val viewModel: MemberMainViewModel by viewModels()

    private val rechargeLevelAdapter = RechargeLevelAdapter().apply {
        itemClick = { rechargeTier, position ->
            if (rechargeTier != null) {
                postSearch(500)
                viewModel.updateRechargeData(rechargeTier, null)
            } else {
                viewModel.cleanRechargeTier()
                clearSelectIndex()
                CustomTopupDialog.showDialog(parentFragmentManager) { consumerRechargeTier ->
                    setCustomMembershipAmount(consumerRechargeTier)
                    postSearch(500)
                    viewModel.updateRechargeData(consumerRechargeTier, null)
                }
            }
        }
        itemCouponClick = { rechargeTier, position ->
            //优惠券列表弹窗
            GiftCouponDetailDialog.showDialog(
                parentFragmentManager,
                rechargeTier?.rechargeTierCouponTemplateList
            )
        }
        editCustomCouponClick = { rechargeTier, position ->
            //编辑自定义优惠券弹窗
            if (rechargeTier != null) {
                CustomTopupDialog.showDialog(
                    parentFragmentManager, rechargeTier.toJson()
                ) { consumerRechargeTier ->
                    setCustomMembershipAmount(consumerRechargeTier)
                    postSearch(500)
                    viewModel.updateRechargeData(consumerRechargeTier, null)
                }
            }
        }
    }

    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                viewModel.findTopUpCanUseCoupon(
                    addNum = getTopUpAmount(),
                    id = memberInfo?.accountId
                )
            }
        } catch (e: Exception) {

        }
    }

    private fun postSearch(duration: Int) {
        binding?.apply {
            layoutMain.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                layoutMain.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogTopupBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        lifecycleScope.launch {
            PreferenceHelper.getStoreInfo()?.conversionRatio?.let {
                FoundationHelper.conversionRatio = it
            }
        }
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
        binding?.layoutMain?.setRadius(20f)
        binding?.rvRechargeLevelList?.adapter = rechargeLevelAdapter
        initData()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        viewModel.uiCouponListState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                        btnTopUp.setEnable(false)
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        btnTopUp.setEnableWithAlpha(false)
                    }
                    if (it.message?.isNotEmpty() == true) Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        val couponId = viewModel.uiRechargeData.value?.currentCouponInfo?.id
                        val currentCouponInfo = it.data.firstOrNull { it.id == couponId }
                        viewModel.updateRechargeData(
                            currentCouponInfo = currentCouponInfo,
                            couponList = it.data
                        )
                        if (currentCouponInfo == null) {
                            viewModel.cleanCurrentCoupon()
                        }
                    }
                }

                else -> {}
            }

        }


//        viewModel.rechangeState.observe(viewLifecycleOwner) {
//            when (it) {
//                is ApiResponse.Loading -> {
//                    binding?.apply {
//                        progressBar.isVisible = true
//                        btnTopUp.setEnable(false)
//                    }
//                }
//
//                is ApiResponse.Error -> {
//                    binding?.apply {
//                        progressBar.isVisible = false
//                        btnTopUp.setEnable(true)
//                    }
//                    if (it.message?.isNotEmpty() == true) Toast.makeText(
//                        context,
//                        it.message,
//                        Toast.LENGTH_SHORT
//                    ).show()
//                }
//
//                is ApiResponse.Success -> {
//                    binding?.apply {
//                        progressBar.isVisible = false
//                        btnTopUp.setEnable(true)
//                    }
//                    callBackPayClick?.invoke()
//                    dismissAllowingStateLoss()
//                }
//
//                else -> {}
//            }
//        }

        viewModel.uiRechargeDetailState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        progressBar.isVisible = true
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        progressBar.isVisible = false
                    }
                    if (it.message?.isNotEmpty() == true) Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        progressBar.isVisible = false
                        rechargeLevelAdapter.replaceData(
                            it.data.isCustomMembershipAmount ?: false,
                            it.data.consumerRechargeTierVoList
                        )
                        val isEmpty = rechargeLevelAdapter.itemCount == 0
                        layoutRechargeEmpty.isVisible = isEmpty
                        layoutRecharge.isVisible = !isEmpty
                        btnTopUp.isVisible = !isEmpty
                        layoutEmpty.tvEmptyText.text = getString(R.string.no_recharge_level)
                    }

                }

                else -> {}
            }
        }
        viewModel.uiRechargeData.observe(viewLifecycleOwner) {
            binding?.apply {
                //当前充值等级
                val rechargeTier = it.rechargeTier
                //当前优惠券信息
                val currentCouponInfo = it.currentCouponInfo
                if (rechargeTier == null) {
                    tvTopupAmount.text = "$--"
                    groupAddGiftAmount.isVisible = false
                    groupExtraCouponGiveaway.isVisible = false
                    groupTotalRechargeAmount.isVisible = false
                    tvPayableAmount.text = "--"
                } else {
                    tvTopupAmount.text = rechargeTier.amount?.priceFormatTwoDigitZero1() ?: "$--"
                    //额外赠送金额
                    tvAddGiftAmount.text = rechargeTier.giftAmount?.priceFormatTwoDigitZero1()
                    groupAddGiftAmount.isVisible =
                        rechargeTier.giftAmount != null && !rechargeTier.giftAmount!!.isZero()
                    //额外赠送的优惠卷
                    val totalCouponNum =
                        rechargeTier.rechargeTierCouponTemplateList?.sumOf { it.num ?: 0 } ?: 0
                    tvExtraCouponGiveaway.text =
                        getString(R.string.unit_coupon, totalCouponNum.toString())
                    groupExtraCouponGiveaway.isVisible = totalCouponNum > 0
                    //总充值金额
                    val giftAmount = rechargeTier.giftAmount ?: BigDecimal.ZERO
                    groupTotalRechargeAmount.isVisible = !giftAmount.isZero()
                    val total = rechargeTier.amount?.add(giftAmount) ?: BigDecimal.ZERO
                    tvTotalRechargeAmount.text = total.priceFormatTwoDigitZero1()
                    //应支付金额
                    tvPayableAmount.text = (rechargeTier.amount?.subtract(
                        BigDecimal(currentCouponInfo?.couponPrice ?: 0)
                    ))?.priceFormatTwoDigitZero() ?: "--"
                }

                tvCoupon.text = CouponHelper.getCouponDesc(
                    requireContext(),
                    it.couponList.isNullOrEmpty(),
                    currentCouponInfo,
                    rechargeTier?.amount?.divide(BigDecimal(100))?.halfUp(2) ?: BigDecimal.ZERO,
                    true
                )

                btnTopUp.setEnableWithAlpha(rechargeTier != null)
            }
            Timber.d("updateTopupBalance")
            menuOrderScreen?.updateTopupBalance(it)
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, screenHeight)
        }
    }

    private fun initData() {
        arguments?.let {
            memberInfo = Gson().fromJson(it.getString(CONTENT), CustomerMemberResponse::class.java)

        }

        binding?.apply {
            btnTopUp.setEnableWithAlpha(false)

            postSearch(0)
            viewModel.getRechargeDetailPage(memberInfo?.accountId)

            Timber.d("initTopupBalance")
            menuOrderScreen?.initTopupBalance(memberInfo)
        }
    }

    fun getTopUpAmount(): BigDecimal? {
        binding?.apply {
            return viewModel.uiRechargeData.value?.rechargeTier?.amount ?: BigDecimal.ZERO
        }
        return BigDecimal.ZERO
    }

    //区分支付弹窗是否调用的关闭事件，防止重复调用
    private var isCallClose = false

    override fun onDismiss(dialog: DialogInterface) {
        if (!isCallClose) {
            callBackPayClick?.invoke(false)
        }
        super.onDismiss(dialog)
    }

    private fun initListener() {
        binding?.apply {
            tvNickname.text = memberInfo?.nickName
            tvPhoneNumber.text = memberInfo?.telephone
            tvBalance.text = "${memberInfo?.balance?.priceFormatTwoDigitZero2()}"

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            btnTopUp.setOnClickListener {
                val rechargeData = viewModel.uiRechargeData.value
                val currentCouponInfo = rechargeData?.currentCouponInfo
                val amount = rechargeData?.rechargeTier?.amount ?: BigDecimal.ZERO
                val totalPrice = amount?.subtract(BigDecimal(currentCouponInfo?.couponPrice ?: 0))

                val giftAmount =
                    viewModel.uiRechargeData.value?.rechargeTier?.giftAmount ?: BigDecimal.ZERO
                val rechargeTier = rechargeLevelAdapter.getSelectLevel()
                PayDialog.showDialog(
                    parentFragmentManager,
                    currentScene = SceneEnum.TOPUP_BALANCE.id,
                    totalPrice = totalPrice?.toLong(),
                    menuOrderScreen = menuOrderScreen,
//                    countryCode = memberInfo.areaCode,
                    phone = memberInfo?.telephone,
                    conversionRatio = FoundationHelper.conversionRatio,
                    rechargeData = PayDialog.RechargeData(
                        rechargeMemberInfo = memberInfo,//余额充值会员信息
                        rechargeCoupon = viewModel.uiRechargeData.value?.currentCouponInfo,//余额充值优惠券信息
                        rechargeTierId = rechargeTier?.id, //档位id
                        amount = amount, //充值金额（单位：分）
                        giftAmount = giftAmount, //储值赠送金额（单位：分）
                        giftCouponTemplateList = rechargeTier?.rechargeTierCouponTemplateList, //储值赠送优惠券list
                        remark = edtRemark.text.toString(),    //备注
                    ),
                    rechargeSuccessListener = { response ->
                        binding?.apply {
                            progressBar.isVisible = false
                            btnTopUp.setEnable(true)
                        }
                        isCallClose = true
                        callBackPayClick?.invoke(true)
                        menuOrderScreen?.updateTopupSuccess()
                        dismissAllowingStateLoss()
                    },
                    onCloseListener = {
                        isCallClose = true
                        callBackPayClick?.invoke(false)
                        dismissAllowingStateLoss()
                    }
                )


//
//                Timber.d("充值金额：$amount")
//                viewModel.rechargeBalance(
//                    RechargeRequest(
//                        memberInfo?.accountId,
//                        amount.toLong(),
//                        1,
//                        viewModel.uiRechargeData.value?.currentCouponInfo?.couponCode,
//                    )
//                )
            }
            //
            tvCoupon.setOnClickListener {
                val couponId = viewModel.uiRechargeData.value?.currentCouponInfo?.id
                CouponListDialog.showDialog(
                    parentFragmentManager,
                    currentCouponId = couponId,
                    topUpCanUseCouponRequest = TopUpCanUseCouponRequest(
                        getTopUpAmount(),
                        memberInfo?.accountId
                    )
                ) {
                    if (it == null) {
                        viewModel.cleanCurrentCoupon()
                    } else {
                        viewModel.updateRechargeData(null, it)
                    }
                }
            }
        }
    }


    companion object {
        private const val RESERVE_DIALOG = "RESERVE_DIALOG"
        private const val CONTENT = "CONTENT"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            menuOrderScreen: SecondaryScreenUI? = null,
            callBackClickListener: ((Boolean) -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(RESERVE_DIALOG)
            if (fragment != null) return
            fragment = newInstance(content = content, menuOrderScreen, callBackClickListener)
            fragment.show(fragmentManager, RESERVE_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(RESERVE_DIALOG) as? TopupBalanceDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            content: String? = null,
            menuOrderScreen: SecondaryScreenUI? = null,
            callBackClickListener: ((Boolean) -> Unit)? = null,
        ): TopupBalanceDialog {
            val args = Bundle()
            content?.let { args.putString(CONTENT, it) }
            val fragment = TopupBalanceDialog()
            fragment.arguments = args
            fragment.callBackPayClick = callBackClickListener
            fragment.menuOrderScreen = menuOrderScreen
            return fragment
        }
    }

}
