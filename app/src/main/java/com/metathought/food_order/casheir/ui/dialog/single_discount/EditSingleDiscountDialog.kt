package com.metathought.food_order.casheir.ui.dialog.single_discount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.constant.SingleDiscountType
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.databinding.DialogEditSingleDiscountBinding
import com.metathought.food_order.casheir.extension.getRedStar
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableAdd
import com.metathought.food_order.casheir.extension.setEnableMinus
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.filter.CashierInputFilter
import com.metathought.food_order.casheir.filter.CashierInputFilterV2
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.PreconfigurationDiscountListAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.ordered.discount.ModifyDiscountViewModel
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.math.BigDecimal
import kotlin.math.min


/**
 * Tmp good dialog
 *
 * @constructor 编辑单品减免
 */
@AndroidEntryPoint
class EditSingleDiscountDialog : BaseDialogFragment() {

    private var binding: DialogEditSingleDiscountBinding? = null
    private var positiveButtonListener: ((SingleDiscountGoods?) -> Unit)? = null
    private var modifyLocalListener: ((SingleDiscountGoods?, SingleDiscountGoods?) -> Unit)? = null
    private var singleDiscountGoods: SingleDiscountGoods? = null

    private var type = SingleDiscountType.MODIFY_PRICE.id
    private var reduceRatio: Double? = null
    private var saleReduce: Double? = null    //销售减免
    private var vipReduce: Double? = null    //会员减免
    private var adjustSalePrice: Double? = null    //销售减免
    private var adjustVipPrice: Double? = null    //会员减免

    private var modifyCount: Int = 0    //修改的数量

    private var sourceHashKey = ""   //不带单品折扣的hashkey

    //是否自定义
    private var isCustomize = false

    private var cartGoods: GoodsRequest? = null

    private var cartGoodsList: ArrayList<GoodsRequest>? = null

    private val viewModel: ModifyDiscountViewModel by viewModels()

    private var preconfigurationDiscountListAdapter: PreconfigurationDiscountListAdapter? = null

    private var isOnlyModifyPrice = false

    private var orderNo: String? = null

    private var dingStyle: Int? = null

    private var minInput = BigDecimal(0.01).halfUp(2)

//    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
//        val dialog = super.onCreateDialog(savedInstanceState)
//        dialog.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
//
//        return dialog
//    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditSingleDiscountBinding.inflate(layoutInflater)

        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        arguments?.getSerializable(CART_GOODS)?.let {
            cartGoods = it as GoodsRequest
        }

        arguments?.getSerializable(SINGLE_DISCOUNT_GOODS)?.let {
            singleDiscountGoods = it as SingleDiscountGoods
        }
        arguments?.getBoolean(ONLY_MODIFY_PRICE)?.let {
            isOnlyModifyPrice = it
        }

        arguments?.getInt(DINGSTTLE)?.let {
            dingStyle = it
        }

        arguments?.getString(ORDER_NO)?.let {
            orderNo = it
        }

        arguments?.getString(SOURCE_HASH_KEY)?.let {
            sourceHashKey = it
        }

        preconfigurationDiscountListAdapter =
            PreconfigurationDiscountListAdapter(listOf()) {
                if (type == SingleDiscountType.FIXED_AMOUNT.id) {
                    if (viewModel.localReduce?.id == it.id) {
                        viewModel.localReduce = null
                    } else {
                        viewModel.localReduce = it
                    }
                } else if (type == SingleDiscountType.PERCENTAGE.id) {
                    if (viewModel.localDiscount?.id == it.id) {
                        viewModel.localDiscount = null
                    } else {
                        viewModel.localDiscount = it
                    }
                }
                preconfigurationDiscountListAdapter?.setSelectItem(it)
                updateViewByType()
            }

        initListener()

        initData()

        initObserver()
        viewModel.getOrderDiscountInfoList(
            orderNo = orderNo,
            cartGoods = if (cartGoodsList != null) cartGoodsList else null,
            applyRange = 1,
            dingStyle = dingStyle
        )
    }


    private fun initData() {
        Timber.d("initData current type:${type}")
        binding?.apply {
            type = singleDiscountGoods?.type ?: SingleDiscountType.MODIFY_PRICE.id
            //如果没有选择折扣的权限
            if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.SELECT_DISCOUNT.type) != true
            ) {
                Timber.e("没有选择权限")
                isCustomize = true
                tvCustomize.isVisible = false
                llBottom.isVisible = false
                viewKeyBoard.isVisible = true
                viewChooseList.isVisible = false
                topBar.setTitle(getString(R.string.customize))
            }
            //如果没有自定义权限
            if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.CUSTOMIZE_DISCOUNT.type) != true
            ) {
                Timber.e("没有自定义权限")
                radioModify.isVisible = false
                // 改价 跟着自定义权限  如果当前类型是改价 ，则修改为减免
                if (type == SingleDiscountType.MODIFY_PRICE.id) {
                    type = SingleDiscountType.FIXED_AMOUNT.id
                }
                tvCustomize.isVisible = false
            }


            modifyCount = singleDiscountGoods?.num ?: 0
            tvName.text = singleDiscountGoods?.name
            tvCount.text = "$modifyCount"

            tvPrice.text = singleDiscountGoods?.totalPrice?.priceFormatTwoDigitZero2()
            tvVipPrice.isVisible = singleDiscountGoods?.isShowVipPrice == true
            tvVipPrice.text = singleDiscountGoods?.vipPrice?.priceFormatTwoDigitZero2()

            tvNewPrice.text = "-"
            tvNewVipPrice.isVisible = false
            if (singleDiscountGoods?.discountReduceActivityId == null) {
                reduceRatio = singleDiscountGoods?.reduceRatio
            }
            saleReduce = singleDiscountGoods?.saleReduce
            vipReduce = singleDiscountGoods?.vipReduce
            adjustSalePrice = singleDiscountGoods?.adjustSalePrice
            adjustVipPrice = singleDiscountGoods?.adjustVipPrice

            updateGoodNum()

            edtRemark.setText(singleDiscountGoods?.remark)
            edtRemark.setSelection(edtRemark.length())
            updateViewByType()


//            if (isOnlyModifyPrice) {
//                radioGroupType.isVisible = false
//            }
        }
    }

    private fun updateViewByType() {
        binding?.apply {
            when (type) {
                SingleDiscountType.MODIFY_PRICE.id -> {
                    radioGroupType.check(R.id.radioModify)
                    showModifyPriceView()
                }

                SingleDiscountType.FIXED_AMOUNT.id -> {
                    radioGroupType.check(R.id.radioFixedAmount)
                    showFixedAmountView()
                }

                SingleDiscountType.PERCENTAGE.id -> {
                    radioGroupType.check(R.id.radioPercentage)
                    showPercentageView()
                }
            }
        }
    }

    private fun initObserver() {
        viewModel.uiModeState.observe(viewLifecycleOwner) { state ->
            state.discountList?.apply {
                if (state.discountList is ApiResponse.Loading) {
                    binding?.apply {
                        progressBar.isVisible = true
                        llContent.isVisible = false
                    }
                }
                if (state.discountList is ApiResponse.Success) {
                    binding?.apply {
                        progressBar.isVisible = false
                        llContent.isVisible = true
                        rvList.adapter = preconfigurationDiscountListAdapter
                        var model =
                            state.discountList.data.firstOrNull { it.id == singleDiscountGoods?.discountReduceActivityId }
                        var tmp: DiscountReduceInfo? = null
                        if (model == null) {
                            //如果列表没返回 把订单内的model 插入
                            if (singleDiscountGoods?.discountReduceInfo != null) {
                                tmp = singleDiscountGoods?.discountReduceInfo
                                viewModel.inertDataToList(singleDiscountGoods?.discountReduceInfo!!)
                            }
                        }

                        if (tmp != null) {
                            model = tmp
                        }

                        if (model == null) {
                            singleDiscountGoods?.discountReduceActivityId = null
                            singleDiscountGoods?.discountReduceInfo = null
                        } else {
//                            type = model.type ?: WholeDiscountType.DEDUCT.id
                            if (type == SingleDiscountType.FIXED_AMOUNT.id) {
                                viewModel.localReduce = model
                            } else if (type == SingleDiscountType.PERCENTAGE.id) {
                                viewModel.localDiscount = model
                            }
                            preconfigurationDiscountListAdapter?.setSelectItem(model)
                        }

                        initList()
                        initData()
                    }
                }

                if (state.discountList is ApiResponse.Error) {
                    binding?.apply {
                        progressBar.isVisible = false
                        llContent.isVisible = true
                    }
                }
            }

        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            radioDiscountGroupType.setOnCheckedChangeListener { radioGroup, i ->
                updateRemarkHint()
            }

            edtPercent.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘444444")
                    hideKeyboard2()
                }
            }
            edtPercent.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        Timber.e("edtPercent 隐藏键盘  $b")
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(minInput, BigDecimal(100.00))
                    viewKeyBoard.setCurrentEditText(edtPercent)
                }
            }

            edtUsd.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘2222")
                    hideKeyboard2()
                }
            }

            edtUsd.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        Timber.e("edtUsd 隐藏键盘  $b")
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        minInput,
                        BigDecimal(((getTotalPrice()).toDouble().div(100.0))),
                    )
                    viewKeyBoard.setCurrentEditText(edtUsd)
                }
            }

            edtVipUsd.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘5555")
                    hideKeyboard2()
                }
            }
            edtVipUsd.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        Timber.e("edtVipUsd 隐藏键盘  $b")
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        minInput,
                        BigDecimal(((getTotalVipPrice()).toDouble().div(100.0))),
                    )
                    viewKeyBoard.setCurrentEditText(edtVipUsd)
                }
            }

            edtNewSalePrice.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘77777")
                    hideKeyboard2()
                }
            }

            edtNewSalePrice.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        Timber.e("edtNewSalePrice 隐藏键盘  $b")
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(10000000 - 0.01),
                    )
                    viewKeyBoard.setCurrentEditText(edtNewSalePrice)
                }
            }

            edtNewVipPrice.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘9999")
                    hideKeyboard2()
                }
            }

            edtNewVipPrice.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        Timber.e("edtNewVipPrice 隐藏键盘  $b")
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(10000000 - 0.01),
                    )
                    viewKeyBoard.setCurrentEditText(edtNewVipPrice)
                }
            }

            edtRemark.setOnFocusChangeListener { view, b ->
                Timber.e("备注输入框获取焦点 ${b}")
            }

            edtRemark.addTextChangedListener {
                checkEnable()
            }


            edtPercent.filters = arrayOf(CashierInputFilter(false, 100, true))
            edtPercent.addTextChangedListener {
                Timber.e("edtPercent 变化")
                val percent = it.toString().trim().toDoubleOrNull()
                Timber.e("edtPercent percent:$percent")
                updateNewPricePercent(percent)
                checkEnable()
            }

            setFixedAmountListener()
            edtUsd.addTextChangedListener {
                val discountPercent = it.toString().trim().toDoubleOrNull()
                updateNewPrice(discountPercent)
                updateNewVipPrice(vipReduce)
                updateShowView()
                checkEnable()
            }

            edtVipUsd.addTextChangedListener {
                val discountVipPercent = it.toString().trim().toDoubleOrNull()
                updateNewVipPrice(discountVipPercent)
                updateNewPrice(saleReduce)
                updateShowView()
                checkEnable()
            }

            edtNewSalePrice.addTextChangedListener {
                val price = it.toString().trim().toDoubleOrNull()
                adjustSalePrice = price
                updateAdjustPrice()
            }

            edtNewVipPrice.addTextChangedListener {
                val price = it.toString().trim().toDoubleOrNull()
                adjustVipPrice = price
                updateAdjustPrice()
            }

            radioGroupType.setOnCheckedChangeListener { _, checkedId ->
                Timber.e("checkedId  ->${checkedId}")
                when (checkedId) {
                    R.id.radioModify -> {
                        preconfigurationDiscountListAdapter?.setSelectItem(null)
                        showModifyPriceView()
                    }

                    R.id.radioPercentage -> {
                        //百分比
                        preconfigurationDiscountListAdapter?.setSelectItem(viewModel.localDiscount)
                        showPercentageView()
                    }

                    R.id.radioFixedAmount -> {
                        //固定金额
                        preconfigurationDiscountListAdapter?.setSelectItem(viewModel.localReduce)
                        showFixedAmountView()
                    }
                }
            }

            viewKeyBoard.setonConfirmClick {
                setDone()
            }

            tvConfirm.setOnClickListener {
                setDone()
            }

            imgMinus.setOnClickListener {
                if (modifyCount > 1) {
                    modifyCount -= 1
                    updateGoodNum()
                }
            }
            imgPlus.setOnClickListener {
                if (modifyCount < (singleDiscountGoods?.num ?: 0)) {
                    modifyCount += 1
                    updateGoodNum()
                }
            }


            tvCustomize.setOnClickListener {
                isCustomize = true
                tvCustomize.isVisible = false
                llBottom.isVisible = false
                viewKeyBoard.isVisible = true
                viewChooseList.isVisible = false
                topBar.setTitle(getString(R.string.customize))
                updateViewByType()
            }
        }
    }

    private fun initList() {
        binding?.apply {
            var list: List<DiscountReduceInfo> = listOf()
            list = when (type) {
                SingleDiscountType.FIXED_AMOUNT.id -> {
                    viewModel.reduceList ?: listOf()
                }

                SingleDiscountType.PERCENTAGE.id -> {
                    viewModel.discountList ?: listOf()
                }

                else -> {
                    listOf()
                }
            }

            if (list.isNotEmpty()) {
                rvList.isVisible = true
                layoutEmpty.root.isVisible = false
                tvTitle.isVisible = true
                preconfigurationDiscountListAdapter?.updateData(list)
            } else {
                rvList.isVisible = false
                layoutEmpty.root.isVisible = true
                layoutEmpty.tvEmptyText.text =
                    getString(R.string.empty_data)
                tvTitle.isVisible = false
            }
        }
    }

    private fun setDone() {
        binding?.apply {
            //旧的折扣
            val oldDiscount = singleDiscountGoods?.clone()
            //旧的折扣清掉
            oldDiscount?.reduceRatio = null
            oldDiscount?.saleReduce = null
            oldDiscount?.vipReduce = null
            oldDiscount?.discountReduceActivityId = null
            oldDiscount?.discountReduceInfo = null
            oldDiscount?.adjustSalePrice = null
            oldDiscount?.adjustVipPrice = null
            oldDiscount?.discountType = null
            oldDiscount?.type = null
            oldDiscount?.remark = null
            oldDiscount?.goodsHashKey = sourceHashKey
            oldDiscount?.num = (oldDiscount?.num ?: 0) - modifyCount


            singleDiscountGoods?.reduceRatio = null
            singleDiscountGoods?.saleReduce = null
            singleDiscountGoods?.vipReduce = null
            singleDiscountGoods?.adjustSalePrice = null
            singleDiscountGoods?.adjustVipPrice = null
            singleDiscountGoods?.discountType = null
            singleDiscountGoods?.discountReduceActivityId = null
            singleDiscountGoods?.discountReduceInfo = null

            singleDiscountGoods?.type = type

            if (type == SingleDiscountType.PERCENTAGE.id) {
                if (isCustomize) {
                    singleDiscountGoods?.reduceRatio =
                        if (edtPercent.text.isNullOrEmpty()) null else edtPercent.text.toString()
                            .toDoubleOrNull()
                } else {
                    singleDiscountGoods?.discountReduceActivityId = viewModel?.localDiscount?.id
                    singleDiscountGoods?.discountReduceInfo = viewModel?.localDiscount
                }

            } else if (type == SingleDiscountType.FIXED_AMOUNT.id) {
                if (isCustomize) {
                    singleDiscountGoods?.saleReduce = saleReduce
                    singleDiscountGoods?.vipReduce = vipReduce
                } else {
                    singleDiscountGoods?.discountReduceActivityId = viewModel?.localReduce?.id
                    singleDiscountGoods?.discountReduceInfo = viewModel?.localReduce
                }
            } else if (type == SingleDiscountType.MODIFY_PRICE.id) {
                singleDiscountGoods?.adjustSalePrice = adjustSalePrice
                singleDiscountGoods?.adjustVipPrice = adjustVipPrice
            }

            singleDiscountGoods?.num = modifyCount
            singleDiscountGoods?.remark = edtRemark.text.toString()

            singleDiscountGoods?.discountType =
                if (radioDiscountGroupType.checkedRadioButtonId == R.id.radioDiscount) 0 else 1

            if (modifyLocalListener != null) {
                //购物车的回调
                modifyLocalListener?.invoke(oldDiscount, singleDiscountGoods)
            } else {
                positiveButtonListener?.invoke(singleDiscountGoods)
            }
            dismissCurrentDialog()
        }
    }


    private fun setFixedAmountListener() {
        binding?.apply {
            edtUsd.filters = arrayOf(
                CashierInputFilterV2(
                    false,
                    BigDecimal((getTotalPrice()).toDouble().div(100.0)).halfUp(2).toDouble(),
                    true
                )
            )

            edtVipUsd.filters = arrayOf(
                CashierInputFilterV2(
                    false,
                    BigDecimal((getTotalVipPrice()).toDouble().div(100.0)).halfUp(2).toDouble(),
                    true
                )
            )

            if (edtUsd.isFocused) {
                viewKeyBoard.setRange(
                    minInput,
                    BigDecimal(((getTotalPrice()).toDouble().div(100.0)))
                )
            }
            if (edtVipUsd.isFocused) {
                viewKeyBoard.setRange(
                    minInput,
                    BigDecimal(((getTotalVipPrice()).toDouble().div(100.0)))
                )
            }
        }
    }

    /**
     * 显示打折对应的界面
     *
     */
    private fun showPercentageView() {
        binding?.apply {
            Timber.e("showPercentageView")
            type = SingleDiscountType.PERCENTAGE.id
            updateRemarkHint()
            tvPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
            tvVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()

            tvNewPrice.text = "-"
            tvNewVipPrice.isVisible = false

            llDeductInput.isVisible = false
            llModifyInput.isVisible = false
            llOriginalSinglePrice.isVisible = false

            tvTitle.text = getString(R.string.choose_discount_require2).getRedStar(resources)
            if (isCustomize) {
                textInputLayoutRemark.isVisible = true
                llReasonType.isVisible = true
                checkEnable()
            } else {
                if (viewModel.discountList.isNullOrEmpty()) {
                    textInputLayoutRemark.isInvisible = true
                    llReasonType.isInvisible = true
                    tvConfirm.setEnableWithAlpha(false)
                } else {
                    textInputLayoutRemark.isVisible = true
                    llReasonType.isVisible = true
                    checkEnable()
                }
            }

            if (reduceRatio != null) {
                edtPercent.setText("${if (reduceRatio!!.isInt()) reduceRatio!!.toInt() else reduceRatio}")
                edtPercent.setSelection(edtPercent.length())
            }

            if (isCustomize) {
                edtPercent.requestFocus()
                flPercent.isVisible = true
                llBottom.isVisible = false
                viewKeyBoard.isVisible = true
                viewChooseList.isVisible = false
            } else {
                flPercent.isVisible = false
                llBottom.isVisible = true
                viewKeyBoard.isVisible = false
                viewChooseList.isVisible = true
            }

            initList()
            updateNewPricePercent(reduceRatio)
        }
    }

    /**
     * 显示减免固定价格对应的界面
     *
     */
    private fun showFixedAmountView() {
        binding?.apply {
            Timber.e("showFixedAmountView  ${isCustomize}")
            type = SingleDiscountType.FIXED_AMOUNT.id
            updateRemarkHint()
            tvPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
            tvVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()

            tvNewPrice.text = "-"
            tvNewVipPrice.isVisible = false
            flPercent.isVisible = false

            textInputLayoutVipUsd.isVisible =
                singleDiscountGoods?.isShowVipPrice == true
            llModifyInput.isVisible = false
            llOriginalSinglePrice.isVisible = false

            tvTitle.text = getString(R.string.choose_discount_require).getRedStar(resources)
            if (isCustomize) {
                textInputLayoutRemark.isVisible = true
                llReasonType.isVisible = true
                checkEnable()
            } else {
                if (viewModel.reduceList.isNullOrEmpty()) {
                    textInputLayoutRemark.isInvisible = true
                    llReasonType.isInvisible = true
                    tvConfirm.setEnableWithAlpha(false)
                } else {
                    textInputLayoutRemark.isVisible = true
                    llReasonType.isVisible = true
                    checkEnable()
                }
            }

            if (saleReduce != null) {
                edtUsd.setText(saleReduce.toString())
                edtUsd.setSelection(edtUsd.length())
            }
            if (vipReduce != null) {
                edtVipUsd.setText(vipReduce.toString())
                edtVipUsd.setSelection(edtVipUsd.length())
            }
            if (isCustomize) {
                edtUsd.requestFocus()
                llDeductInput.isVisible = true
                llBottom.isVisible = false
                viewKeyBoard.isVisible = true
                viewChooseList.isVisible = false
            } else {
                llDeductInput.isVisible = false
                llBottom.isVisible = true
                viewKeyBoard.isVisible = false
                viewChooseList.isVisible = true
            }

            initList()
            updateNewVipPrice(vipReduce)
            updateNewPrice(saleReduce)

            updateShowView()
            setFixedAmountListener()
        }
    }

    /**
     * 显示改价对应的界面
     *
     */
    private fun showModifyPriceView() {
        binding?.apply {
            type = SingleDiscountType.MODIFY_PRICE.id
            updateRemarkHint()
            llBottom.isVisible = false
            viewChooseList.isVisible = false
            viewKeyBoard.isVisible = true
            llModifyInput.isVisible = true
            tvPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
            tvVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()

            llOriginalSinglePrice.isVisible = true

            textInputLayoutRemark.isVisible = true
            llReasonType.isVisible = true
            checkEnable()

            textInputLayoutNewVipPrice.isVisible =
                singleDiscountGoods?.isShowVipPrice == true

            tvOriginalSingleVipPrice.isVisible =
                singleDiscountGoods?.isShowVipPrice == true

            tvOriginalSinglePrice.text =
                singleDiscountGoods?.singlePrice?.priceFormatTwoDigitZero2()

            tvOriginalSingleVipPrice.text =
                singleDiscountGoods?.singleVipPrice?.priceFormatTwoDigitZero2()
            flPercent.isVisible = false
            llDeductInput.isVisible = false
            tvNewPrice.text = "-"
            tvNewVipPrice.isVisible = false
            if (adjustVipPrice != null) {
                edtNewVipPrice.setText(adjustVipPrice.toString())
                edtNewVipPrice.setSelection(edtNewVipPrice.length())
            }
            if (adjustSalePrice != null) {
                edtNewSalePrice.setText(adjustSalePrice.toString())
                edtNewSalePrice.setSelection(edtNewSalePrice.length())
            }

            edtNewSalePrice.requestFocus()


            Timber.e("showModifyPriceView")
            updateAdjustPrice()
        }
    }

    private fun updateAdjustPrice() {
        Timber.e("updateAdjustPrice")
        if (type != SingleDiscountType.MODIFY_PRICE.id) {
            return
        }
        binding?.apply {
            if (adjustSalePrice == null && adjustVipPrice == null) {
                tvNewPrice.text = "-"
                tvNewVipPrice.isVisible = false
            } else if (adjustSalePrice == null) {
                tvNewPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
                tvNewVipPrice.text =
                    (adjustVipPrice ?: 0.0).times((modifyCount).toDouble())
                        .priceFormatTwoDigitZero2()
                tvNewVipPrice.isVisible = singleDiscountGoods?.isShowVipPrice == true
            } else if (adjustVipPrice == null) {
                tvNewPrice.text = (adjustSalePrice ?: 0.0).times((modifyCount).toDouble())
                    .priceFormatTwoDigitZero2()

                tvNewVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()
                tvNewVipPrice.isVisible = singleDiscountGoods?.isShowVipPrice == true
            } else {
                tvNewPrice.text = (adjustSalePrice ?: 0.0).times((modifyCount).toDouble())
                    .priceFormatTwoDigitZero2()
                tvNewVipPrice.isVisible = singleDiscountGoods?.isShowVipPrice == true
                tvNewVipPrice.text =
                    (adjustVipPrice ?: 0.0).times((modifyCount).toDouble())
                        .priceFormatTwoDigitZero2()
            }

        }
    }


    //更新新价格 减免金额
    private fun updateNewPrice(price: Double?) {
        Timber.e("updateNewPrice discountPercent:$price type:$type")
        if (type != SingleDiscountType.FIXED_AMOUNT.id) {
            return
        }
        binding?.apply {
            saleReduce = price
            tvNewPrice.text = "-"
            if (isCustomize) {
                if (saleReduce != null) {
                    var newTotalPrice = (BigDecimal(
                        getTotalPrice() ?: 0
                    ) - BigDecimal(saleReduce!! * 100)).toLong()
                    if (newTotalPrice < 0) {
                        newTotalPrice = 0
                    }
                    tvNewPrice.text = newTotalPrice.priceFormatTwoDigitZero2()
                }
            } else {

                if (viewModel.localReduce != null) {
                    var newTotalPrice = (BigDecimal(
                        getTotalPrice()
                    ) - viewModel.localReduce?.reduceRateAmount!!.times(BigDecimal(100.0))).toLong()
                    if (newTotalPrice < 0) {
                        newTotalPrice = 0
                    }
                    tvNewPrice.text = newTotalPrice.priceFormatTwoDigitZero2()
                }
            }
        }
    }

    private fun updateNewVipPrice(price: Double?) {
        Timber.e("updateVipPercent updateVipPercent:$price type:$type")
        if (type != SingleDiscountType.FIXED_AMOUNT.id) {
            return
        }
        binding?.apply {
            vipReduce = price
            if (isCustomize) {
                if (vipReduce != null) {
                    var newVipPrice = (BigDecimal(
                        getTotalVipPrice() ?: 0
                    ) - BigDecimal(vipReduce!! * 100)).toLong()
                    if (newVipPrice < 0) {
                        newVipPrice = 0
                    }
                    tvNewVipPrice.text = newVipPrice.priceFormatTwoDigitZero2()
                }
            } else {

                if (viewModel.localReduce != null) {
                    var newVipPrice = (BigDecimal(
                        getTotalVipPrice()
                    ) - viewModel.localReduce?.reduceRateAmount!!.times(BigDecimal(100.0))).toLong()
                    if (newVipPrice < 0) {
                        newVipPrice = 0
                    }
                    tvNewVipPrice.text = newVipPrice.priceFormatTwoDigitZero2()
                }
            }

            tvNewVipPrice.isVisible = singleDiscountGoods?.isShowVipPrice == true
        }
    }

    private fun updateShowView() {
        binding?.apply {
            Timber.e("vipReduce:${vipReduce}  saleReduce:${saleReduce}  localReduce:${viewModel.localReduce}")
            if (isCustomize) {
                if (vipReduce == null && saleReduce == null) {
                    tvNewVipPrice.isVisible = false
                    tvNewPrice.text = "-"
                }
            } else {
                if (viewModel.localReduce == null) {
                    tvNewVipPrice.isVisible = false
                    tvNewPrice.text = "-"
                }
            }
        }

    }

    //更新新价格 根据百分比
    private fun updateNewPricePercent(percent: Double?) {
        Timber.d("updateNewPrice percent:$percent   ${isCustomize}")
        if (type != SingleDiscountType.PERCENTAGE.id) {
            return
        }
        binding?.apply {

            tvNewPrice.text = "-"
            tvNewVipPrice.isVisible = false
            singleDiscountGoods?.apply {
                if (isCustomize) {
                    reduceRatio = percent
                    singleDiscountGoods?.reduceRatio = percent
                    if (percent != null) {
                        tvNewVipPrice.isVisible = isShowVipPrice == true
                        val newTotalPrice =
                            getTotalPrice() - BigDecimal(getTotalPrice() * percent).divide(
                                BigDecimal(100.0)
                            ).halfUp(0)
                                .toLong()
                        val newVipPrice =
                            getTotalVipPrice() - BigDecimal(getTotalVipPrice() * percent).divide(
                                BigDecimal(100.0)
                            ).halfUp(
                                0
                            ).toLong()
                        tvNewPrice.text = newTotalPrice.priceFormatTwoDigitZero2()
                        tvNewVipPrice.text = newVipPrice.priceFormatTwoDigitZero2()
                    }
                } else {
                    if (viewModel.localDiscount != null) {
                        tvNewVipPrice.isVisible = isShowVipPrice == true
                        val maxLimitPrice = viewModel.localDiscount?.reduceAmountLimit

                        var saleDiscountPrice = BigDecimal(getTotalPrice()).times(
                            viewModel.localDiscount?.reduceRateAmount ?: BigDecimal.ZERO
                        ).divide(BigDecimal(100)).halfUp(0).toLong()
                        var vipDiscountPrice = BigDecimal(getTotalVipPrice()).times(
                            viewModel.localDiscount?.reduceRateAmount ?: BigDecimal.ZERO
                        ).divide(BigDecimal(100)).halfUp(0).toLong()
                        if (maxLimitPrice != null) {
                            saleDiscountPrice = min(
                                saleDiscountPrice,
                                viewModel.localDiscount?.reduceAmountLimit!!.times(BigDecimal(100))
                                    .toLong()
                            )
                            vipDiscountPrice = min(
                                vipDiscountPrice,
                                viewModel.localDiscount?.reduceAmountLimit!!.times(BigDecimal(100))
                                    .toLong()
                            )
                        }
                        val newTotalPrice =
                            getTotalPrice() - saleDiscountPrice
                        val newVipPrice =
                            getTotalVipPrice() - vipDiscountPrice

                        tvNewPrice.text = newTotalPrice.priceFormatTwoDigitZero2()
                        tvNewVipPrice.text = newVipPrice.priceFormatTwoDigitZero2()
                    }
                }
            }
        }
    }


    private fun updateGoodNum() {
        binding?.apply {
            tvCount.text = "$modifyCount"
            imgMinus.isVisible = modifyCount > 0

            imgMinus.setEnableMinus(modifyCount > 1)
            imgPlus.setEnableAdd(modifyCount < (singleDiscountGoods?.num ?: 0))

            when (type) {
                SingleDiscountType.MODIFY_PRICE.id -> {
                    tvPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
                    tvVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()
                    updateAdjustPrice()
                }

                SingleDiscountType.FIXED_AMOUNT.id -> {
                    tvPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
                    tvVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()

                    //如果数量变动的时候减免的价格大于原价格 就按原价格展示
                    if ((vipReduce ?: 0.0).times(100.0) > getTotalVipPrice()) {
                        vipReduce =
                            BigDecimal(getTotalVipPrice()).divide(BigDecimal(100)).toDouble()
                        binding?.apply {
                            edtVipUsd.setText("$vipReduce")
                        }
                    }

                    if ((saleReduce ?: 0.0).times(100.0) > getTotalPrice()) {
                        saleReduce =
                            BigDecimal(getTotalPrice()).divide(BigDecimal(100)).toDouble()
                        binding?.apply {
                            edtUsd.setText("$saleReduce")
                        }
                    }

                    updateNewVipPrice(vipReduce)
                    updateNewPrice(saleReduce)
                    updateShowView()
                    setFixedAmountListener()
                }

                SingleDiscountType.PERCENTAGE.id -> {
                    tvPrice.text = getTotalPrice().priceFormatTwoDigitZero2()
                    tvVipPrice.text = getTotalVipPrice().priceFormatTwoDigitZero2()
                    Timber.e("reduceRatio: $reduceRatio")
                    updateNewPricePercent(reduceRatio)
                }
            }
        }
    }

    private fun getTotalPrice(): Long {
        return (singleDiscountGoods?.singlePrice ?: 0).times(modifyCount)
    }

    private fun getTotalVipPrice(): Long {
        return (singleDiscountGoods?.singleVipPrice ?: 0).times(modifyCount)
    }

    private fun updateRemarkHint() {
        binding?.apply {

            if (radioDiscountGroupType.checkedRadioButtonId == R.id.radioDiscount) {
                textInputLayoutRemark.hint = getString(R.string.please_enter_the_reason)
            } else {
                textInputLayoutRemark.hint =
                    getString(R.string.please_enter_the_reason_require).getRedStar(resources)
            }
            checkEnable()
        }
    }

    private fun checkEnable() {
        binding?.apply {
            //备注是否满足
            var isSatisfyRemark = false
            if (radioDiscountGroupType.checkedRadioButtonId == R.id.radioVoid) {
                //必选的时候有填就满足
                isSatisfyRemark = edtRemark.text.toString().trim().isNotEmpty()
            } else {
                isSatisfyRemark = true
            }
            if (type == SingleDiscountType.MODIFY_PRICE.id) {
                viewKeyBoard.setConfirm(isSatisfyRemark)
                tvConfirm.setEnableWithAlpha(isSatisfyRemark)
            } else if (type == SingleDiscountType.FIXED_AMOUNT.id) {
                if (isCustomize) {
                    val isSatisfySalePrice =
                        edtUsd.text.isNullOrEmpty() || (!edtUsd.text.isNullOrEmpty() && ((edtUsd.text.toString()
                            .toBigDecimalOrNull() ?: BigDecimal.ZERO) >= minInput))
                    val isSatisfyVipPrice =
                        edtVipUsd.text.isNullOrEmpty() || (!edtVipUsd.text.isNullOrEmpty() && ((edtVipUsd.text.toString()
                            .toBigDecimalOrNull() ?: BigDecimal.ZERO) >= minInput))
                    viewKeyBoard.setConfirm(isSatisfySalePrice && isSatisfyVipPrice && isSatisfyRemark)
                    tvConfirm.setEnableWithAlpha(isSatisfySalePrice && isSatisfyVipPrice && isSatisfyRemark)
                } else {
                    if (viewModel.reduceList.isNullOrEmpty()) {
                        viewKeyBoard.setConfirm(false)
                        tvConfirm.setEnableWithAlpha(false)
                    } else {
                        viewKeyBoard.setConfirm(isSatisfyRemark)
                        tvConfirm.setEnableWithAlpha(isSatisfyRemark)
                    }
                }
            } else if (type == SingleDiscountType.PERCENTAGE.id) {
                if (isCustomize) {
                    val isSatisfyPercent =
                        edtPercent.text.isNullOrEmpty() || (!edtPercent.text.isNullOrEmpty() && ((edtPercent.text.toString()
                            .toBigDecimalOrNull() ?: BigDecimal.ZERO) >= minInput))

                    viewKeyBoard.setConfirm(isSatisfyPercent && isSatisfyRemark)
                    tvConfirm.setEnableWithAlpha(isSatisfyPercent && isSatisfyRemark)
                } else {
                    if (viewModel.discountList.isNullOrEmpty()) {
                        viewKeyBoard.setConfirm(false)
                        tvConfirm.setEnableWithAlpha(false)
                    } else {
                        viewKeyBoard.setConfirm(isSatisfyRemark)
                        tvConfirm.setEnableWithAlpha(isSatisfyRemark)
                    }

                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.7).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    companion object {
        private const val TAG = "SingleDiscountDialog"
        private const val ONLY_MODIFY_PRICE = "only_modify_price"
        private const val CART_GOODS = "cartGoods"
        private const val SINGLE_DISCOUNT_GOODS = "single_discount_goods"
        private const val SOURCE_HASH_KEY = "sourceHashKey"
        private const val ORDER_NO = "order_no"
        private const val DINGSTTLE = "dingstyle"


        /**
         * Show dialog
         *
         * @param fragmentManager
         * @param isOnlyModifyPrice  是否只需要改价
         * @param singleDiscountGoods
         * @param cartGoods 购物车菜品
         * @param cartGoodsList 购物车菜品列表
         * @param sourceHashKey  不带单品折扣的hashkey
         * @param positiveButtonListener
         * @param modifyLocalListener
         */
        fun showDialog(
            fragmentManager: FragmentManager,
            isOnlyModifyPrice: Boolean? = false,
            singleDiscountGoods: SingleDiscountGoods,
            cartGoods: GoodsRequest? = null,
            cartGoodsList: ArrayList<GoodsRequest>? = null,
            orderNo: String? = null,
            sourceHashKey: String? = null,
            dingStyle: Int? = null,
            positiveButtonListener: ((SingleDiscountGoods?) -> Unit)? = null,
            modifyLocalListener: ((SingleDiscountGoods?, SingleDiscountGoods?) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                isOnlyModifyPrice,
                singleDiscountGoods,
                cartGoods,
                cartGoodsList,
                orderNo,
                sourceHashKey,
                dingStyle,
                positiveButtonListener,
                modifyLocalListener
            )
            fragment.show(fragmentManager, TAG)
        }


        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? EditSingleDiscountDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            isOnlyModifyPrice: Boolean? = false,
            singleDiscountGoods: SingleDiscountGoods,
            cartGoods: GoodsRequest? = null,
            cartGoodsList: ArrayList<GoodsRequest>? = null,
            orderNo: String? = null,
            sourceHashKey: String? = null,
            dingStyle: Int? = null,
            positiveButtonListener: ((SingleDiscountGoods?) -> Unit)? = null,
            modifyLocalListener: ((SingleDiscountGoods?, SingleDiscountGoods?) -> Unit)? = null
        ): EditSingleDiscountDialog {
            val args = Bundle()
            val fragment = EditSingleDiscountDialog()
            args.putBoolean(ONLY_MODIFY_PRICE, isOnlyModifyPrice ?: false)
            if (cartGoods != null) {
                args.putSerializable(CART_GOODS, cartGoods)
            }
            args.putSerializable(SINGLE_DISCOUNT_GOODS, singleDiscountGoods)
            if (sourceHashKey != null) {
                args.putString(SOURCE_HASH_KEY, sourceHashKey)
            }
            if (dingStyle != null) {
                args.putInt(DINGSTTLE, dingStyle)
            }
            if (orderNo != null) {
                args.putString(ORDER_NO, orderNo)
            }
            fragment.cartGoodsList = cartGoodsList
            fragment.arguments = args
            fragment.positiveButtonListener = positiveButtonListener
            fragment.modifyLocalListener = modifyLocalListener
            return fragment
        }
    }

}
