package com.metathought.food_order.casheir.ui.widget

import androidx.recyclerview.widget.RecyclerView

/**
 * RecyclerView同步滚动管理器 - 支持多个RecyclerView同步
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class RecyclerViewSyncManager {

    private val recyclerViews = mutableListOf<RecyclerView>()
    private val scrollingStates = mutableMapOf<RecyclerView, Boolean>()

    /**
     * 添加需要同步的RecyclerView
     */
    fun addRecyclerView(recyclerView: RecyclerView) {
        if (!recyclerViews.contains(recyclerView)) {
            recyclerViews.add(recyclerView)
            setupScrollListener(recyclerView)
        }
    }

    /**
     * 设置两个RecyclerView的同步（保持向后兼容）
     */
    fun setupSync(mainRecyclerView: RecyclerView, syncRecyclerView: RecyclerView) {
        addRecyclerView(mainRecyclerView)
        addRecyclerView(syncRecyclerView)
    }

    private fun setupScrollListener(recyclerView: RecyclerView) {
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (scrollingStates[recyclerView] != true) {
                    // 同步所有其他RecyclerView
                    syncAllOthers(recyclerView, dx, dy)
                }
            }
        })
    }

    private fun syncAllOthers(sourceRecyclerView: RecyclerView, dx: Int, dy: Int) {
        recyclerViews.forEach { targetRecyclerView ->
            if (targetRecyclerView != sourceRecyclerView) {
                scrollingStates[targetRecyclerView] = true
                targetRecyclerView.scrollBy(dx, dy)
                scrollingStates[targetRecyclerView] = false
            }
        }
    }
}
