package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.annotation.MainThread
import androidx.core.view.isVisible
import com.metathought.food_order.casheir.R


class VerificationCodeViewPsw @kotlin.jvm.JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val codes: ArrayList<TextView> = ArrayList(6)
    private val containers: ArrayList<View> = ArrayList(6)
    private var listener: OnCodeEnteredListener? = null
    private var index = 0
    private var codeLength = 6

    init {
        initialize(context)
    }

    companion object {
        private fun setInactive(views: List<View>) {
            views.forEach {
                it.isSelected = false
            }
        }

        private fun setInactive(container: View) {
            container.isSelected = false
        }

        private fun setActive(container: View) {
            container.isSelected = true
        }
    }

    private fun initialize(context: Context) {
        inflate(context, R.layout.verification_code_view_psw, this)

        codes.add(findViewById(R.id.code_zero))
        codes.add(findViewById(R.id.code_one))
        codes.add(findViewById(R.id.code_two))
        codes.add(findViewById(R.id.code_three))
        codes.add(findViewById(R.id.code_four))
        codes.add(findViewById(R.id.code_five))


        containers.add(findViewById(R.id.container_zero))
        containers.add(findViewById(R.id.container_one))
        containers.add(findViewById(R.id.container_two))
        containers.add(findViewById(R.id.container_three))
        containers.add(findViewById(R.id.container_four))
        containers.add(findViewById(R.id.container_five))

    }

    fun setViewBg(resid: Int) {
        for (c in containers) {
            c.setBackgroundResource(resid)
        }
    }

//    fun changeBackImage(isShowF5: Boolean) {
//        containers.forEach {
//            if (isShowF5) {
//                it.setBackgroundResource(R.drawable.selector_verification_code)
//            } else {
//                it.setBackgroundResource(R.drawable.selector_verification_code_whit)
//            }
//        }
//    }

    @MainThread
    fun setOnCompleteListener(listener: OnCodeEnteredListener) {
        this.listener = listener
    }

    @MainThread
    fun append(value: Int) {
        if (index >= codes.size)
            return
//        setInactive(containers)
        setActive(containers[index])
        val codeView = codes[index++]
//        val translateIn=TranslateAnimation(0F,0F,codeView.height.toFloat(),0F)
//        translateIn.interpolator = OvershootInterpolator()
//        translateIn.duration=500
//
//        val fadeIn=AlphaAnimation(0F,1F)
//        fadeIn.duration=200
//
//        val animationSet=AnimationSet(false)
//        animationSet.run {
//            addAnimation(fadeIn)
//            addAnimation(translateIn)
//            reset()
//            startTime=0
//        }
//
        codeView.run {
            text = "$value"
//            clearAnimation()
//            startAnimation(animationSet)
        }

        val sbf = StringBuilder()
        codes.forEach {
            sbf.append(it.text)
        }
        listener?.onCodeComplete(sbf.toString(), index == codeLength)

    }

    fun setupCodeLength(length: Int) {
        if (length == codeLength)
            return
        codeLength = length
        clear()
        containers.forEachIndexed { index, view ->
            view.isVisible = index < codeLength
        }
    }

    @MainThread
    fun delete() {
        if (index <= 0) return
        codes[--index].text = ""
        setInactive(containers[index])
//        setActive(containers[index])

        val sbf = StringBuilder()
        codes.forEach {
            sbf.append(it.text)
        }
        listener?.onCodeComplete(sbf.toString(), false)
    }

    @MainThread
    fun clear() {
        if (index != 0) {
            codes.forEach {
                it.text = ""
            }
            index = 0
        }
        setInactive(containers)
    }


    interface OnCodeEnteredListener {
        fun onCodeComplete(code: String, isComplete: Boolean)
    }
}