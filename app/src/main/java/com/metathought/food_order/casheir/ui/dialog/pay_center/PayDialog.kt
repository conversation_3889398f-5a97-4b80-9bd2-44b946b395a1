package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PaymentChannel
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.model.base.request_model.member.GiftCoupon
import com.metathought.food_order.casheir.data.model.base.request_model.member.RechargeRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecord
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeTierCouponTemplate
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.databinding.DialogPayBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getColor
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.roundToTwoDecimalPlaces
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setNumberRange
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.PayChannelAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.order.payment.PaymentQrDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.math.BigDecimal
import java.nio.charset.Charset
import kotlin.math.absoluteValue
import kotlin.math.round


/**
 *<AUTHOR>
 *@time  2025/3/27
 *@desc 支付弹窗
 **/


@AndroidEntryPoint
class PayDialog : BaseDialogFragment(), CountryCodePicker.DialogEventsListener {

    data class RechargeData(
        var rechargeMemberInfo: CustomerMemberResponse? = null,//余额充值会员信息
        var rechargeCoupon: CouponModel? = null,//余额充值优惠券信息
        val rechargeTierId: Long?, //档位id
        var amount: BigDecimal?,    //	储值金额（单位：分）	number
        val giftAmount: BigDecimal?, //储值赠送金额（单位：分）
        val giftCouponTemplateList: List<RechargeTierCouponTemplate>?, //储值赠送优惠券list
        val remark: String?,    //备注
    )

    companion object {
        private const val TAG = "PayDialog"
        private const val CONVERSION_RATIO = "conversionRatio"
        private const val TOTAL_PRICE = "total_price"
        private const val TOTAL_VIP_PRICE = "total_vip_price"
        private const val COUNTRY_CODE = "countryCode"
        private const val PHONE = "phone"
        private const val SHOPPING_RECORD = "shopping_record"
        private const val SCENE = "scene"

        fun showDialog(
            fragmentManager: FragmentManager,
            menuOrderScreen: SecondaryScreenUI? = null,
            currentScene: Int? = null,
            orderInfo: OrderedInfoResponse? = null,
            shoppingRecord: ShoppingRecord? = null,
            conversionRatio: Long? = null,  //汇率
            totalPrice: Long? = null,   //总价
            totalVipPrice: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            rechargeData: RechargeData? = null,
            creditRecord: CreditRecord? = null,
            paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
            repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
            onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null,
            onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
            rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null,
            onCloseListener: ((String?) -> Unit)? = null,
            onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
            onClearShoppingListener: (() -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    menuOrderScreen = menuOrderScreen,
                    currentScene = currentScene,
                    orderInfo = orderInfo,
                    shoppingRecord = shoppingRecord,
                    conversionRatio = conversionRatio,
                    totalPrice = totalPrice,
                    totalVipPrice = totalVipPrice,
                    paymentResponse = paymentResponse,
                    repaymentResponse = repaymentResponse,
                    onlineRepaymentSuccessListener = onlineRepaymentSuccessListener,
                    countryCode = countryCode,
                    phone = phone,
                    rechargeData = rechargeData,
                    creditRecord = creditRecord,
                    onlineSuccessListener = onlineSuccessListener,
                    rechargeSuccessListener = rechargeSuccessListener,
                    onCloseListener = onCloseListener,
                    onTopUpListener = onTopUpListener,
                    onClearShoppingListener = onClearShoppingListener
                )
            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            menuOrderScreen: SecondaryScreenUI? = null,
            currentScene: Int? = null,
            orderInfo: OrderedInfoResponse? = null,
            shoppingRecord: ShoppingRecord? = null,
            conversionRatio: Long? = null,
            totalPrice: Long? = null,
            totalVipPrice: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            rechargeData: RechargeData? = null,
            creditRecord: CreditRecord? = null,
            paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
            repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
            onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null,
            onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
            rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null,
            onCloseListener: ((String?) -> Unit)? = null,
            onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
            onClearShoppingListener: (() -> Unit)? = null,
        ): PayDialog {
            val args = Bundle()
            val fragment = PayDialog()
            if (conversionRatio != null) {
                args.putLong(CONVERSION_RATIO, conversionRatio)
            }
            if (totalPrice != null) {
                args.putLong(TOTAL_PRICE, totalPrice)
            }
            if (totalVipPrice != null) {
                args.putLong(TOTAL_VIP_PRICE, totalVipPrice)
            }
            if (countryCode != null) {
                args.putString(COUNTRY_CODE, countryCode)
            }
            if (phone != null) {
                args.putString(PHONE, phone)
            }
            if (shoppingRecord != null) {
                args.putParcelable(SHOPPING_RECORD, shoppingRecord)
            }
            if (currentScene != null) {
                args.putInt(SCENE, currentScene)
            }


            fragment.arguments = args
            fragment.menuOrderScreen = menuOrderScreen
            fragment.orderInfo = orderInfo
            fragment.paymentResponse = paymentResponse
            fragment.repaymentResponse = repaymentResponse
            fragment.onlineRepaymentSuccessListener = onlineRepaymentSuccessListener
            fragment.onlineSuccessListener = onlineSuccessListener
            fragment.rechargeSuccessListener = rechargeSuccessListener
            fragment.onCloseListener = onCloseListener
            fragment.onTopUpListener = onTopUpListener
            fragment.onClearShoppingListener = onClearShoppingListener

            fragment.rechargeData = rechargeData
            fragment.creditRecord = creditRecord
            return fragment
        }

        fun getDialog(fragmentManager: FragmentManager): PayDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? PayDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? PayDialog
            fragment?.dismissAllowingStateLoss()
        }


    }

    private val payViewModel: PayViewModel by viewModels()

    private var binding: DialogPayBinding? = null

    private var mAdapter: PayChannelAdapter? = null

    private var menuOrderScreen: SecondaryScreenUI? = null

    private var paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null

    private var repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null

    private var onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null

    //线上支付成功
    private var onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null

    //线上充值成功
    private var rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null

    //弹窗隐藏
    private var onCloseListener: ((String?) -> Unit)? = null

    //去充值
    private var onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null

    //清空购物车
    private var onClearShoppingListener: (() -> Unit)? = null

    //汇率
    private var conversionRatio: Long = FoundationHelper.conversionRatio

    //总的销售价
    private var totalPrice: Long? = null

    //总的会员价
    private var totalVipPrice: Long? = null


    //区号
    private var countryCode: String? = null

    //手机号
    private var phone: String? = null

    //现金支付model
    private var cashConvertModel: CashConvertModel? = null

    private var orderInfo: OrderedInfoResponse? = null

    private var orderAmountKHR: Long? = null
    private var edtUsdAmountHint: String? = null
    private var edtKhrAmountHint: String? = null

    //线上支付二维码
    private var qrCode: String? = null
    private var qrCodeDrawable: Drawable? = null

    //是否支付成功
    private var isPaySuccess = false

    //是否充值成功
    private var isRechangeSuccess = false

    //会员信息
    private var customerMemberResponse: CustomerMemberResponse? = null

    //余额是否不足
    private var isBalanceInsufficient: Boolean = false

    //先付款购物车
    private var shoppingRecord: ShoppingRecord? = null

    private var offlineChannelList: List<OfflineChannelModel>? = null

    //当前进支付的场景
    private var scene: Int? = null

    private var rechargeData: RechargeData? = null

    //挂账记录
    private var creditRecord: CreditRecord? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPayBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initView()
        initListener()
        initObserver()

        payViewModel.getChannels()
    }


    private fun initView() {
        conversionRatio = arguments?.getLong(CONVERSION_RATIO, FoundationHelper.conversionRatio)
            ?: FoundationHelper.conversionRatio
        scene = arguments?.getInt(SCENE)
        totalPrice = arguments?.getLong(TOTAL_PRICE) ?: 0L
        totalVipPrice =
            if (arguments?.containsKey(TOTAL_VIP_PRICE) == true) arguments?.getLong(TOTAL_VIP_PRICE) else null
        countryCode = arguments?.getString(COUNTRY_CODE)
        phone = arguments?.getString(PHONE)
        shoppingRecord =
            if (arguments?.containsKey(SHOPPING_RECORD) == true) arguments?.getParcelable(
                SHOPPING_RECORD
            ) else null
        payViewModel.orderNo = orderInfo?.orderNo

        binding?.apply {
            btnMixedPayment.setEnableWithAlpha(totalPrice!! > 1)
            mAdapter = PayChannelAdapter(arrayListOf()) {
                if (payViewModel.uiRequestState.value?.paymentResponse is ApiResponse.Loading) {
                    return@PayChannelAdapter
                }
                mAdapter?.setSelectIndex(it)
                hideKeyboard2()
                updateViewByPaymentType()
            }
            rvPaymentChannel.layoutManager =
                LinearLayoutManager(requireActivity(), LinearLayoutManager.HORIZONTAL, false)
            rvPaymentChannel.adapter = mAdapter
        }

    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            edtPhoneNumber.addTextChangedListener {
                phone = edtPhoneNumber.text.toString()
                if (phone.isNullOrEmpty()) {
                    resetCustomerInfo()
                } else {
                    btnSearch.setEnableWithAlpha(true)
                }
            }

            countryCodeHolder.setDialogEventsListener(this@PayDialog)
            countryCodeHolder.setOnCountryChangeListener {
                countryCode = countryCodeHolder.selectedCountryCode
                edtPhoneNumber.text?.isNotEmpty()?.let {
                    btnSearch.setEnableWithAlpha(it)
                }
                getCustomerInfo()
            }

            btnSearch.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    hideKeyboard2()
                    getCustomerInfo()
                }
            }

            btnMixedPayment.setOnClickListener {
                clearSecondUi()
                SingleClickUtils.isFastDoubleClick {
                    MixedPayDialog.showDialog(
                        parentFragmentManager,
                        menuOrderScreen = menuOrderScreen,
                        orderId = payViewModel.orderNo,
                        orderInfo = orderInfo,
                        shoppingRecord = shoppingRecord,
                        conversionRatio = conversionRatio,
                        totalPrice = totalPrice,
                        countryCode = countryCode,
                        phone = phone,
                        offlineChannelList = offlineChannelList,
                        paymentResponse = paymentResponse,
                        scene = scene,
                        creditRecord = creditRecord,
                        repaymentResponse = repaymentResponse,
                        onlineSuccessListener = onlineSuccessListener,
                        onCloseListener = onCloseListener,
                        onTopUpListener = onTopUpListener,
                        onClearShoppingListener = onClearShoppingListener,
                        onUpdateCustomizeKhr = {
                            binding?.viewCustomInput?.getCustomValue(viewLifecycleOwner)
                        }
                    )
                }
            }

            btnDone.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    Timber.e("点击")
                    val selectItem = mAdapter?.getSelect()
                    if (selectItem != null) {
                        if (selectItem.id == PaymentChannel.BALANCE.id) {
                            //余额支付
                            if (isBalanceInsufficient) {
                                customerMemberResponse?.let { it1 ->
                                    onTopUpListener?.invoke(it1)
                                }
//                                dismissAllowingStateLoss()
                            } else {
                                customerMemberResponse?.let { it1 ->
                                    if (scene == SceneEnum.MEMBER_CREDIT.id) {
                                        payViewModel.repayment(
                                            consumerId = creditRecord?.consumerId ?: 0,
                                            accountId = it1.accountId,
                                            isPosPrint = false,
                                            payType = PayTypeEnum.USER_BALANCE,
                                        )
                                    } else if (payViewModel.orderNo != null) {
                                        payViewModel.payAgain(
                                            orderInfo = orderInfo,
                                            payType = PayTypeEnum.USER_BALANCE,
                                            accountId = it1.accountId
                                        )
                                    } else {
                                        payViewModel.payment(
                                            payType = PayTypeEnum.USER_BALANCE,
                                            shareRecord = shoppingRecord,
                                            accountId = it1.accountId
                                        )
                                    }
                                }
                            }

                        } else if (selectItem.id == PaymentChannel.ONLINE.id) {
                            //线上支付
                        } else if (selectItem.id == OfflinePaymentChannelEnum.CASH.id) {
                            //现金支付
                            if (cashConvertModel != null) {
                                Printer.openCashierBox()
                                if (scene == SceneEnum.MEMBER_CREDIT.id) {
                                    payViewModel.repayment(
                                        payType = PayTypeEnum.CASH_PAYMENT,
                                        consumerId = creditRecord?.consumerId ?: 0,
                                        offlineChannelModel = selectItem,
                                        isPosPrint = false,
                                        cashConvertModel = cashConvertModel!!
                                    )
                                } else if (scene == SceneEnum.TOPUP_BALANCE.id) {
                                    cashRecharge(selectItem, cashConvertModel!!)
                                } else {
                                    if (payViewModel.orderNo != null) {
                                        payViewModel.payAgain(
                                            orderInfo = orderInfo,
                                            payType = PayTypeEnum.CASH_PAYMENT,
                                            offlineChannelModel = selectItem,
                                            cashConvert = cashConvertModel!!
                                        )
                                    } else {
                                        payViewModel.payment(
                                            payType = PayTypeEnum.CASH_PAYMENT,
                                            shareRecord = shoppingRecord,
                                            offlineChannelModel = selectItem,
                                            cashConvertModel = cashConvertModel!!
                                        )
                                    }
                                }
                            }
                        } else {
                            //其他线下支付
                            if (scene == SceneEnum.MEMBER_CREDIT.id) {
                                payViewModel.repayment(
                                    consumerId = creditRecord?.consumerId ?: 0,
                                    payType = PayTypeEnum.CASH_PAYMENT,
                                    isPosPrint = false,
                                    offlineChannelModel = selectItem,
                                )
                            } else if (scene == SceneEnum.TOPUP_BALANCE.id) {
                                offlineRecharge(selectItem)
                            } else {
                                if (payViewModel.orderNo != null) {
                                    payViewModel.payAgain(
                                        orderInfo = orderInfo, payType = PayTypeEnum.CASH_PAYMENT,
                                        offlineChannelModel = selectItem,
                                    )
                                } else {
                                    payViewModel.payment(
                                        payType = PayTypeEnum.CASH_PAYMENT,
                                        shareRecord = shoppingRecord,
                                        offlineChannelModel = selectItem,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun offlineRecharge(offlineChannel: OfflineChannelModel) {
        payViewModel.rechargeBalance(
            RechargeRequest(
                id = rechargeData?.rechargeMemberInfo?.accountId,//账户id
                addNum = rechargeData?.amount?.toLong(),//充值门店余额(变更) 单位:分
                type = 2,//1-在线充值 2-线下充值
                couponCode = rechargeData?.rechargeCoupon?.couponCode, //优惠券编码

                offlinePayChannelsId = offlineChannel.id.toLong(), //线下收款渠道id，线下支付
                collectCash = null, //收取现金 现金支付时使用,瑞尔
                changeAmount = null, //找零金额 现金支付时使用，瑞尔
                collectCashDollar = null, //收取现金 现金支付时使用,美元
                changeAmountDollar = null, //收取现金 现金支付时使用,美元

                rechargeTierId = if (rechargeData?.rechargeTierId == -1L) null else rechargeData?.rechargeTierId, //档位id
                giftAmount = rechargeData?.giftAmount, //储值赠送金额（单位：分）
                giftCouponTemplateList = rechargeData?.giftCouponTemplateList?.map {
                    GiftCoupon(it.couponTemplate?.id, it.num)
                }, //储值赠送优惠券list
                remark = rechargeData?.remark,   //备注
            )
        )
    }

    private fun cashRecharge(
        offlineChannel: OfflineChannelModel,
        cashConvertModel: CashConvertModel
    ) {
        payViewModel.rechargeBalance(
            RechargeRequest(
                id = rechargeData?.rechargeMemberInfo?.accountId,//账户id
                addNum = rechargeData?.amount?.toLong(),//充值门店余额(变更) 单位:分
                type = 2,//1-在线充值 2-线下充值
                couponCode = rechargeData?.rechargeCoupon?.couponCode, //优惠券编码

                offlinePayChannelsId = offlineChannel.id.toLong(), //线下收款渠道id，线下支付
                collectCash = cashConvertModel.collectCash?.toBigDecimal(), //收取现金 现金支付时使用,瑞尔
                changeAmount = cashConvertModel.changeAmount?.toBigDecimal(), //找零金额 现金支付时使用，瑞尔
                changeAmountDollar = cashConvertModel.changeAmountDollar?.toBigDecimal(), //收取现金 现金支付时使用,美元
                collectCashDollar = cashConvertModel.collectCashDollar, //收取现金 现金支付时使用,美元

                rechargeTierId = if (rechargeData?.rechargeTierId == -1L) null else rechargeData?.rechargeTierId, //档位id
                giftAmount = rechargeData?.giftAmount, //储值赠送金额（单位：分）
                giftCouponTemplateList = rechargeData?.giftCouponTemplateList?.map {
                    GiftCoupon(it.couponTemplate?.id, it.num)
                }, //储值赠送优惠券list
                remark = rechargeData?.remark,   //备注
            )
        )
    }


    private fun initObserver() {
        binding?.apply {
            payViewModel.uiRequestState.observe(viewLifecycleOwner) { state ->
                if (state.paymentResponse is ApiResponse.Loading) {
                    btnDone.setEnableWithAlpha(false)
                    paymentResponse?.invoke(state.paymentResponse)
                } else if (state.paymentResponse is ApiResponse.Success) {

                    if (shoppingRecord != null) {
                        onClearShoppingListener?.invoke()
                    }
                    if (state.paymentResponse.data.payType == PayTypeEnum.ONLINE_PAYMENT.id) {
                        //如果是线上支付  保存下二维码
                        payViewModel.orderNo = state.paymentResponse.data.orderNo

                        MixedPayDialog.getDialog(parentFragmentManager)
                            ?.setOrderId(state.paymentResponse.data.orderNo)

                        qrCode = state.paymentResponse.data.qrcode
                        if (mAdapter?.getSelect()?.id == PaymentChannel.ONLINE.id) {
                            onlinePaymentView()
                        }
                    } else {
                        isPaySuccess = true
//                        dismissAllowingStateLoss()
                    }
                    paymentResponse?.invoke(state.paymentResponse)
                } else if (state.paymentResponse is ApiResponse.Error) {
                    btnDone.setEnableWithAlpha(true)
                    paymentResponse?.invoke(state.paymentResponse)
                    dismissAllowingStateLoss()
                }
            }
            payViewModel.uiRepaymentState.observe(viewLifecycleOwner) { state ->
                Timber.e("uiRepaymentState: $state")
                if (state.paymentResponse is ApiResponse.Loading) {
                    btnDone.setEnableWithAlpha(false)
                    repaymentResponse?.invoke(state.paymentResponse)
                } else if (state.paymentResponse is ApiResponse.Success) {
                    if (state.paymentResponse.data.payType == PayTypeEnum.ONLINE_PAYMENT.id) {
                        //如果是线上支付  保存下二维码
                        qrCode = state.paymentResponse.data.khqrCode?.qrcode
                        if (mAdapter?.getSelect()?.id == PaymentChannel.ONLINE.id) {
                            onlinePaymentView()
                            //T轮询查询还款成功状态
                            val outTradeNo =
                                state.paymentResponse.data.khqrCode?.outTradeNo
                            payViewModel.checkRepaymentStatus(outTradeNo)
                        }
                    } else {
                        isPaySuccess = true
//                        dismissAllowingStateLoss()
                    }
                    repaymentResponse?.invoke(state.paymentResponse)
                } else if (state.paymentResponse is ApiResponse.Error) {
                    btnDone.setEnableWithAlpha(true)
                    repaymentResponse?.invoke(state.paymentResponse)
//                    dismissAllowingStateLoss()
                }
            }
            payViewModel.rechangeState.observe(viewLifecycleOwner) {
                when (it) {
                    is ApiResponse.Loading -> {
                        btnDone.setEnableWithAlpha(false)
                    }

                    is ApiResponse.Error -> {
                        btnDone.setEnableWithAlpha(true)
                        if (it.message?.isNotEmpty() == true) Toast.makeText(
                            context,
                            it.message,
                            Toast.LENGTH_SHORT
                        ).show()
                        isRechangeSuccess = false
                        dismissAllowingStateLoss()
                    }

                    is ApiResponse.Success -> {
                        val response = it.data.data
                        if (response?.isOnline() == true) {
                            //如果是线上支付  保存下二维码
                            qrCode = response.qrCode
                            if (mAdapter?.getSelect()?.id == PaymentChannel.ONLINE.id) {
                                onlinePaymentView()
                                payViewModel.checkRechargeStatus(response.orderId)
                            }
                        } else {
                            isRechangeSuccess = true
                            isPaySuccess = true
                            rechargeSuccessListener?.invoke(null)
                            dismissAllowingStateLoss()
                        }
                    }

                    else -> {}
                }
            }

            payViewModel.uiPaymentChannelModeState.observe(viewLifecycleOwner) { state ->
                state.result?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
                            binding?.apply {
                                pbLoading.isVisible = true
                                llContent.isVisible = false
                                llBottom.isVisible = false
                            }
                        }

                        is ApiResponse.Error -> {
                            binding?.apply {
                                pbLoading.isVisible = false
                                llContent.isVisible = true
                                llBottom.isVisible = true
                                setChannelAdapter(ArrayList())
                            }
                        }

                        is ApiResponse.Success -> {
                            binding?.apply {
                                pbLoading.isVisible = false
                                llContent.isVisible = true
                                llBottom.isVisible = true
//                                setChannelAdapter(ArrayList())
                                setChannelAdapter(ArrayList(it.data.filter { it.selected == true }))
                            }
                        }
                    }
                }
            }

            payViewModel.uiState.observe(viewLifecycleOwner) { state ->
                state.error?.let {
                    Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
                    dismissAllowingStateLoss()
                }
                state.isExpire?.let {
                    Toast.makeText(context, getString(R.string.order_expired), Toast.LENGTH_SHORT)
                        .show()
                    dismissAllowingStateLoss()
                }
                state.time?.let {
                }
                state.success?.let {
                    isPaySuccess = true
                    onlineSuccessListener?.invoke(it)
                    dismissAllowingStateLoss()
                }
                state.rechargeSuccess?.let {
                    isPaySuccess = true
                    rechargeSuccessListener?.invoke(it)
                    dismissAllowingStateLoss()
                }

                state.repaymentSuccess?.let {
                    isPaySuccess = true
                    onlineRepaymentSuccessListener?.invoke(it)
                    dismissAllowingStateLoss()
                }
            }

            payViewModel.uiMemberState.observe(viewLifecycleOwner) {
                it.memberAccountResult?.let { res ->
                    binding?.apply {
                        when (res) {
                            is ApiResponse.Loading -> {
                                btnDone.setEnableWithAlpha(false)
                                pbLoadMember.isVisible = true
                                tvCustomerNameTitle.isVisible = false
                                tvCustomerName.isVisible = false
                                tvCustomerBalanceTitle.isVisible = false
                                tvCustomerBalance.isVisible = false
                                tvInsufficientBalance.isVisible = false
                                tvNotFound.isVisible = false
                            }

                            is ApiResponse.Error -> {
                                btnDone.setEnableWithAlpha(false)
                                pbLoadMember.isVisible = false
                                tvNotFound.isVisible = true
                                tvCustomerNameTitle.isVisible = false
                                tvCustomerName.isVisible = false
                                tvCustomerBalanceTitle.isVisible = false
                                tvCustomerBalance.isVisible = false
                                tvInsufficientBalance.isVisible = false
                            }

                            is ApiResponse.Success -> {
                                customerMemberResponse = res.data
                                pbLoadMember.setVisibleGone(false)
                                if (customerMemberResponse != null) {
                                    btnDone.setEnableWithAlpha(true)
                                    isBalanceInsufficient =
                                        (getNowPrice() ?: 0) > (res.data?.balance ?: 0)
                                    tvCustomerName.text = res.data?.nickName
                                    tvCustomerBalance.text =
                                        "${res.data?.balance?.priceFormatTwoDigitZero2()}"
                                    tvInsufficientBalance.isVisible = isBalanceInsufficient
                                    if (scene == SceneEnum.MEMBER_CREDIT.id) {
                                        btnDone.setEnableWithAlpha(!isBalanceInsufficient)
                                    }
                                } else {
                                    btnDone.setEnableWithAlpha(false)
                                    pbLoadMember.isVisible = false
                                    tvNotFound.isVisible = true
                                    tvCustomerName.text = "— —"
                                    tvCustomerBalance.text = "— —"
                                    tvInsufficientBalance.isVisible = false
                                }
                                tvCustomerNameTitle.isVisible = true
                                tvCustomerName.isVisible = true
                                tvCustomerBalanceTitle.isVisible = true
                                tvCustomerBalance.isVisible = true
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     *  获取当前支付类型对应的价格
     *
     * @return
     */
    private fun getNowPrice(): Long? {
        if (mAdapter?.getSelect()?.id == PaymentChannel.BALANCE.id) {
            if (totalVipPrice != null) {
                return totalVipPrice
            }
        }

        return totalPrice
    }

    /**
     * 更新顶部价格区域
     *
     */
    private fun updatePriceView() {
        binding?.apply {
            tvPrice.text = getNowPrice()?.priceFormatTwoDigitZero2()
            tvKhrPrice.text =
                FoundationHelper.getPriceStrByUnit(conversionRatio, getNowPrice(), true)
        }
    }

    //设置支付渠道数据
    private fun setChannelAdapter(list: ArrayList<OfflineChannelModel>) {
        val toJson = list.toJson()
        val listType = object : TypeToken<ArrayList<OfflineChannelModel>?>() {}.type
        offlineChannelList = Gson().fromJson(toJson, listType)
        val finalList = ArrayList<OfflineChannelModel>()
        if ((totalPrice ?: 0L) > 0L) {
            finalList.add(
                OfflineChannelModel(
                    channelsName = getString(R.string.online_payment),
                    id = PaymentChannel.ONLINE.id,
                    selected = true
                )
            )
        }

        //余额充值不显示余额支付
        if (scene == SceneEnum.TOPUP_BALANCE.id) {
            binding?.btnMixedPayment?.isVisible = false
        } else {
            finalList.add(
                OfflineChannelModel(
                    channelsName = getString(R.string.pay_by_balance),
                    id = PaymentChannel.BALANCE.id,
                    selected = true
                )
            )
        }

        if (list.isNotEmpty()) {
            //如果线下列表不为空
            finalList.add(0, list.removeFirst())
            finalList.addAll(list)
        }

        mAdapter?.updateData(finalList)
        updateViewByPaymentType()
    }

    /**
     * 根据支付类型 显示界面
     *
     */
    private fun updateViewByPaymentType() {
        clearSecondUi()
        updatePriceView()
        payViewModel.isOnlinePay = false
        when (mAdapter?.getSelect()?.id) {
            PaymentChannel.ONLINE.id -> {
                payViewModel.isOnlinePay = true
                if (scene == SceneEnum.TOPUP_BALANCE.id) {
                    //在线余额充值 二维码获取
                    payViewModel.rechargeBalance(
                        RechargeRequest(
                            id = rechargeData?.rechargeMemberInfo?.accountId,//账户id
                            addNum = rechargeData?.amount?.toLong(),//充值门店余额(变更) 单位:分
                            type = 1,//1-在线充值 2-线下充值
                            couponCode = rechargeData?.rechargeCoupon?.couponCode, //优惠券编码

                            offlinePayChannelsId = null,//线下收款渠道id，线下支付
                            collectCash = null, //收取现金 现金支付时使用,瑞尔
                            changeAmount = null, //找零金额 现金支付时使用，瑞尔
                            collectCashDollar = null, //收取现金 现金支付时使用,美元
                            changeAmountDollar = null, //收取现金 现金支付时使用,美元

                            rechargeTierId = if (rechargeData?.rechargeTierId == -1L) null else rechargeData?.rechargeTierId, //档位id
                            giftAmount = rechargeData?.giftAmount, //储值赠送金额（单位：分）
                            giftCouponTemplateList = rechargeData?.giftCouponTemplateList?.map {
                                GiftCoupon(it.couponTemplate?.id, it.num)
                            }, //储值赠送优惠券list
                            remark = rechargeData?.remark,   //备注
                        )
                    )
                } else {
                    if (qrCode.isNullOrEmpty()) {
                        if (scene == SceneEnum.MEMBER_CREDIT.id) {
                            payViewModel.repayment(
                                consumerId = creditRecord?.consumerId ?: 0,
                                isPosPrint = true,
                                payType = PayTypeEnum.ONLINE_PAYMENT,
                            )
                        } else if (payViewModel.orderNo != null) {
                            payViewModel.payAgain(
                                orderInfo = orderInfo, payType = PayTypeEnum.ONLINE_PAYMENT,
                            )
                        } else {
                            payViewModel.payment(
                                shareRecord = shoppingRecord, payType = PayTypeEnum.ONLINE_PAYMENT,
                            )
                        }
                    }
                }
                onlinePaymentView()
            }

            PaymentChannel.BALANCE.id -> {
                balancePaymentView()
            }

            OfflinePaymentChannelEnum.CASH.id -> {
                cashPaymentView()
            }

            else -> {
                otherOfflinePaymentView()
            }
        }

    }

    /**
     * 线上支付
     *
     */
    private var qrCodeLoadingJob: Job? = null

    private fun onlinePaymentView() {
        binding?.apply {
            // 先更新UI状态，确保界面响应迅速
            clCash.isVisible = false
            clQrCode.isVisible = true
            clBalance.isVisible = false
            llOther.isVisible = false
            llBottom.isVisible = false
            tvScanQRAmount.text = tvPrice.text
            tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()

            // 立即显示加载状态
            imgQR.isVisible = false
            pbQrLoading.isVisible = true

            // 取消之前的加载任务（如果有）
            qrCodeLoadingJob?.cancel()

            if (!qrCode.isNullOrEmpty()) {
                // 使用lifecycleScope确保生命周期安全
                qrCodeLoadingJob = lifecycleScope.launch(Dispatchers.Default) {
                    try {
                        // 检查是否已经取消
                        if (!isActive) return@launch

                        // 如果drawable为空，在后台线程创建
                        if (qrCodeDrawable == null) {
                            qrCodeDrawable = QrCodeDrawable(
                                QrData.Url(qrCode ?: ""),
                                PaymentQrDialog.getQROption(
                                    R.mipmap.icon_qr_bakong,
                                    requireContext()
                                ),
                                Charset.forName("UTF-8")
                            )
                        }

//                        // 检查是否已经取消
                        if (!isActive) return@launch
//                        isd
                        // 切回主线程更新UI
                        withContext(Dispatchers.Main) {
                            // 再次检查视图是否还存在
                            if (!isActive || binding == null) return@withContext

                            // 使用into(CustomTarget)而不是直接into(ImageView)
                            // 这样可以更精确地控制何时显示图像
                            Glide.with(requireContext())
                                .load(qrCodeDrawable)
                                .diskCacheStrategy(DiskCacheStrategy.NONE)
                                .skipMemoryCache(true)
                                .into(object : CustomTarget<Drawable>() {
                                    override fun onResourceReady(
                                        resource: Drawable,
                                        transition: Transition<in Drawable>?
                                    ) {
                                        // 确保视图仍然存在
                                        binding?.apply {
                                            imgQR.setImageDrawable(resource)
                                            imgQR.isVisible = true
                                            pbQrLoading.isVisible = false
                                        }
                                    }

                                    override fun onLoadCleared(placeholder: Drawable?) {
                                        // 不需要实现
                                    }

                                    override fun onLoadFailed(errorDrawable: Drawable?) {
                                        binding?.pbQrLoading?.isVisible = false
                                    }
                                })

                            // 开始检查支付状态
                            payViewModel.checkPaymentStatus()
                            menuOrderScreen?.showQRPayment(qrCode)
                        }
                    } catch (e: Exception) {
                        // 在主线程处理异常
                        withContext(Dispatchers.Main) {
                            binding?.pbQrLoading?.isVisible = false
                            Timber.e(e, "QR码加载失败")
                        }
                    }
                }
            }
        }
    }

//    private fun onlinePaymentView() {
//        binding?.apply {
//            clCash.isVisible = false
//            clQrCode.isVisible = true
//            clBalance.isVisible = false
//            llOther.isVisible = false
//            llBottom.isVisible = false
//            tvScanQRAmount.text = tvPrice.text
//            tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
//
//            binding?.run {
//                if (!qrCode.isNullOrEmpty()) {
//                    if (qrCodeDrawable == null) {
//                        qrCodeDrawable = QrCodeDrawable(
//                            QrData.Url(qrCode ?: ""),
//                            PaymentQrDialog.getQROption(
//                                R.mipmap.icon_qr_bakong,
//                                requireContext()
//                            ),
//                            Charset.forName("UTF-8")
//                        )
//                    }
//                    /**
//                     * 这个会引起卡顿 后续优化
//                     */
//                    Glide.with(requireActivity()).load(qrCodeDrawable).into(imgQR)
//                    menuOrderScreen?.showQRPayment(qrCode)
//                    payViewModel.checkPaymentStatus()
//                    imgQR.isVisible = true
//                    pbQrLoading.isVisible = false
//                } else {
//                    imgQR.isVisible = false
//                    pbQrLoading.isVisible = true
//                }
//            }
//        }
//    }

    // 在对话框关闭或视图销毁时取消加载任务
    override fun onDestroyView() {
        qrCodeLoadingJob?.cancel()
        qrCodeLoadingJob = null
        super.onDestroyView()
    }

    /**
     * 余额支付
     *
     */
    private fun balancePaymentView() {
        binding?.apply {
            if (scene == SceneEnum.MEMBER_CREDIT.id) {
                edtPhoneNumber.isEnabled = false
                countryCodeHolder.setCcpClickable(false)
                btnSearch.isVisible = false
            }

            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager

            clCash.isVisible = false
            clQrCode.isVisible = false
            clBalance.isVisible = true
            llOther.isVisible = false
            llBottom.isVisible = true
            btnDone.setEnableWithAlpha(false)
            binding?.apply {
                if (!countryCode.isNullOrEmpty() && !phone.isNullOrEmpty()) {
                    countryCodeHolder.setDefaultCountryUsingPhoneCode(
                        countryCode!!.replace(" ", "").toInt()
                    )
                    countryCodeHolder.setCountryForPhoneCode(countryCode!!.replace(" ", "").toInt())
                }
                edtPhoneNumber.setText(phone)
                edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
                getCustomerInfo()
            }
        }
    }

    /**
     * 获取用户信息
     *
     */
    private fun getCustomerInfo() {
        binding?.apply {
            if (edtPhoneNumber.text.toString().isNotEmpty()) {
                payViewModel.getMemberAccount(
                    countryCodeHolder.selectedCountryCode.plus(
                        edtPhoneNumber.text.toString()
                    )
                )
            } else {
                resetCustomerInfo()
            }
        }
    }

    /**
     * 重置客户信息
     *
     */
    private fun resetCustomerInfo() {
        binding?.apply {
            pbLoadMember.isVisible = false
            tvNotFound.isVisible = false
            tvCustomerName.text = "- -"
            tvCustomerBalance.text = "- -"
            tvCustomerName.isVisible = true
            tvCustomerNameTitle.isVisible = true
            tvCustomerBalance.isVisible = true
            tvCustomerBalanceTitle.isVisible = true
            customerMemberResponse = null
            btnSearch.setEnableWithAlpha(false)
        }
    }

    /**
     * 现金支付方式
     *
     */
    private fun cashPaymentView() {
        binding?.apply {
            clCash.isVisible = true
            clQrCode.isVisible = false
            clBalance.isVisible = false
            llOther.isVisible = false
            llBottom.isVisible = true

            edtUsdAmount.clearFocus()
            edtKhrAmount.clearFocus()
            requireActivity().hideKeyboard(edtUsdAmount)
            requireActivity().hideKeyboard(edtKhrAmount)

            viewCustomInput.getCustomValue(viewLifecycleOwner)
            viewCustomInput.setOnItemEditClickListener { index, longs ->
                PayCustomKhrInputDialog.showDialog(parentFragmentManager, index, longs) {
                    viewCustomInput.getCustomValue(viewLifecycleOwner)
                }
            }
            btnDone.setEnableWithAlpha(true)
            initCashData()
        }
    }

    /**
     * 剩下的线下支付方式
     *
     */
    private fun otherOfflinePaymentView() {
        binding?.apply {
            clCash.isVisible = false
            clQrCode.isVisible = false
            clBalance.isVisible = false
            llOther.isVisible = true
            llBottom.isVisible = true

            tvOtherPayment.text =
                getString(R.string.channel_pay_amount, mAdapter?.getSelect()?.channelsName ?: "")
            tvOtherPaymentAmount.text = "${totalPrice?.priceFormatTwoDigitZero2()}"

            btnDone.setEnableWithAlpha(true)
        }
    }


    private fun initCashData() {
        binding?.apply {

            edtUsdAmount.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘2222")
                    hideKeyboard2()
                }
            }
            edtUsdAmount.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        Timber.e("edtUsd 隐藏键盘  $b")
                        hideKeyboard2()
                    }
                }

                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(99999999.99),
                    )
                    viewKeyBoard.setCurrentEditText(edtUsdAmount)
                }
            }

            edtKhrAmount.setOnClickListener {
                root.post {
                    Timber.e("隐藏键盘2222")
                    hideKeyboard2()
                }
            }
            edtKhrAmount.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(999999900),
                    )
                    viewKeyBoard.setCurrentEditText(edtKhrAmount)
                }
            }

            viewCustomInput.setCurrentEditText(edtKhrAmount)

            tvConversionRatio.text = "$1 = ៛${conversionRatio}"

            totalPrice?.let { orderPrice ->
                //预值都显示成瑞尔  四舍五入后的结果
                orderAmountKHR =
                    round(orderPrice.times(conversionRatio).div(10000.0)).times(100).toLong()

                edtUsdAmountHint = ""
                edtKhrAmountHint = "${orderAmountKHR?.decimalFormatZeroDigit()}"

                edtUsdAmount.hint = edtUsdAmountHint
                edtKhrAmount.hint = edtKhrAmountHint

                cashConvertModel = CashConvertModel(
                    collectCash = orderAmountKHR,
                    changeAmount = 0L,
                    collectCashDollar = BigDecimal.ZERO,
                    changeAmountDollar = 0.0
                )
                //找零金额 The amount of change
                val changeAmountStr = "$0.00"
                tvChangeAmount.text = changeAmountStr
                //限制输入范围 Limit input range
//                edtUsdAmount.setNumberRange(0, 99999999)
                edtUsdAmount.addTextChangedListener {
                    calculateChange()
                }
                edtKhrAmount.setNumberRange(0, 999999900)
                edtKhrAmount.addTextChangedListener {
                    calculateChange()
                }
            }


            menuOrderScreen?.getSecondCashConvertDialog()?.updatePrice(
                tvPrice.text.toString(),
                tvConversionRatio.text.toString(),
                "៛${orderAmountKHR?.decimalFormatZeroDigit()}",
                edtUsdAmount.text.toString(),
                edtKhrAmount.hint.toString(),
                tvChangeAmount.text.toString()
            )
//
            menuOrderScreen?.getSecondCashConvertDialog()?.isErrorStatus(false)

            edtUsdAmount.requestFocus()
        }
    }

    /**
     * 现金支付计算
     *
     */
    private fun calculateChange() {
        binding?.apply {
            totalPrice?.let { orderPrice ->
                val usdAmountStr = edtUsdAmount.text?.trim().toString()
                var paidAmountUSD = if (usdAmountStr.isNullOrEmpty()) {
                    BigDecimal.ZERO
                } else {
                    usdAmountStr.toBigDecimalOrNull() ?: BigDecimal.ZERO
                }
                //限制美元的范围0到99999 Limit the range of USD to 0 to 99999
                if (paidAmountUSD < BigDecimal.ZERO || paidAmountUSD > BigDecimal.valueOf(
                        99999999.99
                    )
                ) {
                    paidAmountUSD = BigDecimal.ZERO
                }

                val khrAmountStr = edtKhrAmount.text?.trim().toString()
                val paidAmountKHR = if (khrAmountStr.isNullOrEmpty()) {
                    0L
                } else {
                    khrAmountStr.toLongOrNull() ?: 0L
                }

                //总的收取多少美元  整体计算完以后四舍五入
                val totalReceiveUsd =
                    (paidAmountUSD.toDouble() + paidAmountKHR.times(1.0) / conversionRatio).roundToTwoDecimalPlaces()

                //找零的美元
                val changeUSD = (BigDecimal(totalReceiveUsd) - BigDecimal(
                    (totalPrice ?: 0).div(100.0)
                )).toDouble().roundToTwoDecimalPlaces()

                //找零的美元换算成瑞尔
                val changeKHRAfterCeil = FoundationHelper.usdConverToKhr(
                    conversionRatio,
                    changeUSD.times(100).toLong()
                )

                val changeAmountStr =
                    if (changeUSD < 0) "-$${changeUSD.absoluteValue.decimalFormatTwoDigitZero()} = -៛${changeKHRAfterCeil.absoluteValue.decimalFormatZeroDigit()}" else {
                        "$${changeUSD.absoluteValue.decimalFormatTwoDigitZero()} = ៛${changeKHRAfterCeil.absoluteValue.decimalFormatZeroDigit()}"
                    }

                if (edtUsdAmount.text.toString().trim()
                        .isNullOrEmpty() && edtKhrAmount.text.toString().trim().isNullOrEmpty()
                ) {
                    cashConvertModel = CashConvertModel(
                        collectCash = orderAmountKHR,
                        changeAmount = 0,
                        collectCashDollar = BigDecimal.ZERO,
                        changeAmountDollar = 0.0
                    )

                    btnDone.alpha = 1f
                    btnDone.isEnabled = true

                    edtUsdAmount.hint = edtUsdAmountHint
                    edtKhrAmount.hint = edtKhrAmountHint

                    tvChangeAmount.text = "$0.00"
                    tvChangeAmountTip.isVisible = false
                    context?.let {
                        tvChangeAmount.setTextColor(R.color.black.getColor(it))
                    }
                    menuOrderScreen?.getSecondCashConvertDialog()?.isErrorStatus(false)
                } else {
                    cashConvertModel = CashConvertModel(
                        collectCash = paidAmountKHR,
                        changeAmount = 0,
                        collectCashDollar = paidAmountUSD.halfUp(2),
                        changeAmountDollar = changeUSD
                    )
                    val enable = changeUSD >= 0
                    btnDone.alpha = if (enable) 1f else 0.5f
                    btnDone.isEnabled = enable
                    tvChangeAmountTip.isVisible = !enable
                    if (!enable) {
                        context?.let {
                            tvChangeAmount.setTextColor(R.color.main_red.getColor(it))
                        }
                    } else {
                        context?.let {
                            tvChangeAmount.setTextColor(R.color.black.getColor(it))
                        }
                    }
                    menuOrderScreen?.getSecondCashConvertDialog()?.isErrorStatus(!enable)
                    tvChangeAmount.text = changeAmountStr
                    edtUsdAmount.hint = ""
                    edtKhrAmount.hint = ""
                }
            }
            menuOrderScreen?.getSecondCashConvertDialog()?.updatePrice(
                tvPrice.text.toString(),
                tvConversionRatio.text.toString(),
                "៛${orderAmountKHR?.decimalFormatZeroDigit()}",
                edtUsdAmount.text.toString(),
                edtKhrAmount.text.toString(),
                tvChangeAmount.text.toString()
            )
        }
    }


    fun getOrderNo(): String? {
        return payViewModel?.orderNo
    }

    fun getCreditRecord(): CreditRecord? {
        return creditRecord
    }


    private fun clearSecondUi() {
        Timber.d("clearSecondUi:$scene")
        menuOrderScreen?.dismissSecondCashConvertDialog()
        if (scene == SceneEnum.ORDER.id) {
            menuOrderScreen?.showOrder()
        } else if (scene == SceneEnum.MENU.id) {
            menuOrderScreen?.showMenu()
        } else if (scene == SceneEnum.TOPUP_BALANCE.id) {
            menuOrderScreen?.showTopupBalance()
        } else if (scene == SceneEnum.MEMBER_CREDIT.id) {
            menuOrderScreen?.showDefault()
        }
    }

    override fun onResume() {
        super.onResume()
        Timber.e("onResume")
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * 0.7).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)

        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    override fun onDismiss(dialog: DialogInterface) {

        if (!isPaySuccess && payViewModel.orderNo != null) {
            onCloseListener?.invoke(payViewModel.orderNo)
        }

        clearSecondUi()
        if (!isPaySuccess && scene == SceneEnum.MEMBER_CREDIT.id) {
            onCloseListener?.invoke("")
        }
        if (scene == SceneEnum.TOPUP_BALANCE.id && !isRechangeSuccess) {
            onCloseListener?.invoke("")
        }
        hideKeyboard2()
        super.onDismiss(dialog)

    }

    override fun onCcpDialogOpen(dialog: Dialog?) {
        binding?.apply {
            countryCodeHolder.setCcpClickable(false)
        }
    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.apply {
            edtPhoneNumber.isFocusable = true
            countryCodeHolder.setCcpClickable(true)
        }
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {
        binding?.apply {
            countryCodeHolder.setCcpClickable(true)
        }
    }

}