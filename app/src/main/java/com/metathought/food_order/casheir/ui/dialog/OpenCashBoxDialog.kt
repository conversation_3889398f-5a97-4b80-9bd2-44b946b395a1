package com.metathought.food_order.casheir.ui.dialog


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.databinding.DialogOpenCashBoxBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.md5
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.widget.VerificationCodeViewPsw
import com.metathought.food_order.casheir.ui.widget.VerificationPinKeyboard2
import kotlinx.coroutines.launch

/**
 *
 * 打开钱箱
 *
 * **/

class OpenCashBoxDialog : BaseDialogFragment() {
    private var binding: DialogOpenCashBoxBinding? = null

    private var openButtonListener: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogOpenCashBoxBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels).toInt()
            val screenWidth = (displayMetrics.widthPixels).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {


    }

    private fun initListener() {
        binding?.apply {

            keyboard.setOnKeyPressListener(object : VerificationPinKeyboard2.OnKeyPressListener {
                override fun onKeyPress(keyCode: Int) {
                    if (keyCode >= 0) {
                        verificationCode.append(keyCode)
                    } else if (keyCode == -1000) {
                        dismissAllowingStateLoss()
                    } else {
                        verificationCode.delete()
                    }
                }
            })

            verificationCode.setOnCompleteListener(object :
                VerificationCodeViewPsw.OnCodeEnteredListener {
                override fun onCodeComplete(code: String, isComplete: Boolean) {
                    if (!isComplete) {
                        llError.isVisible = false
                        return
                    }
                    lifecycleScope.launch {
                        val pwd = code.toString().md5()
                        if (pwd == PreferenceHelper.getCashBoxPwd(requireContext())) {
                            openButtonListener?.invoke()
                            dismissAllowingStateLoss()
                        } else {
                            verificationCode.clear()
                            llError.isVisible = true
                        }
                    }
                }
            })

//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    companion object {
        private const val TAG = "OpenCashBoxDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            openButtonListener: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(openButtonListener)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment = fragmentManager.findFragmentByTag(TAG) as? OpenCashBoxDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            openButtonListener: (() -> Unit)? = null
        ): OpenCashBoxDialog {
            val args = Bundle()
            val fragment = OpenCashBoxDialog()
            fragment.arguments = args
            fragment.openButtonListener = openButtonListener

            return fragment
        }
    }

}
