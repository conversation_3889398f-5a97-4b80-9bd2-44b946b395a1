package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.util.DisplayMetrics
import android.view.Display
import com.lxj.xpopup.animator.PopupAnimator
import com.lxj.xpopup.core.CenterPopupView


/**
 *<AUTHOR>
 *@time  2024/10/28
 *@desc
 **/

open class BaseCenterDialog(private val act: Context?) : CenterPopupView(act!!) {

    /**
     * 弹窗的宽度，用来动态设定当前弹窗的宽度，受getMaxWidth()限制
     *
     * @return
     */
    override fun getPopupWidth(): Int {
        return 0
    }

    /**
     * 弹窗的高度，用来动态设定当前弹窗的高度，受getMaxHeight()限制
     *
     * @return
     */
    override fun getPopupHeight(): Int {
        return 0
    }


    // 设置最大高度，看需要而定
    override fun getMaxHeight(): Int {
        return super.getMaxHeight()
    }

    // 设置自定义动画器，看需要而定
    override fun getPopupAnimator(): PopupAnimator {
        return super.getPopupAnimator()
    }

    fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}