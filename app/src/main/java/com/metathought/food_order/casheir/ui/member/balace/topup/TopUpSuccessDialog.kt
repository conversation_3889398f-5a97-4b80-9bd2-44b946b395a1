package com.metathought.food_order.casheir.ui.member.balace.topup

import android.content.Context
import android.content.DialogInterface
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogTopupSuccessBinding
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/3/2213:00
 * @description
 */
@AndroidEntryPoint
class TopUpSuccessDialog : DialogFragment() {

    private var binding: DialogTopupSuccessBinding? = null
    private var onCloseListener: (() -> Unit)? = null

    companion object {
        private const val KEY_PAYMENT_SUCCESS = "KEY_PAYMENT_SUCCESS"
        private const val PERCENT_85 = 0.85
        fun showDialog(
            fragmentManager: FragmentManager,
            onCloseListener: (() -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(KEY_PAYMENT_SUCCESS)
            if (fragment != null) return
            fragment = newInstance(onCloseListener)

            fragment.show(fragmentManager, KEY_PAYMENT_SUCCESS)
        }

        private fun newInstance(onCloseListener: (() -> Unit)? = null): TopUpSuccessDialog {
            return TopUpSuccessDialog().apply {
                this.onCloseListener = onCloseListener
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogTopupSuccessBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        binding?.run {
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        onCloseListener?.invoke()
        super.onDismiss(dialog)
    }
    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

}