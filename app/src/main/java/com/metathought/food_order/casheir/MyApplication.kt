package com.metathought.food_order.casheir

import android.app.Application
import android.content.Context
import android.media.MediaRouter
import android.widget.Toast
import coil3.ImageLoader
import coil3.SingletonImageLoader
import coil3.disk.DiskCache
import coil3.disk.directory
import coil3.memory.MemoryCache
import coil3.request.crossfade
import com.facebook.stetho.Stetho
import com.google.gson.Gson
import com.jakewharton.threetenabp.AndroidThreeTen
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.sankuai.mtcashboxsdk.MTCashboxSdkHelper
import com.tencent.bugly.crashreport.CrashReport
import com.wecloud.im.common.context.AppContextWrapper
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.litepal.LitePal
import rx_activity_result2.RxActivityResult
import timber.log.Timber
import java.io.File
import java.util.Locale
import javax.inject.Inject

@HiltAndroidApp
class MyApplication : Application(), SingletonImageLoader.Factory {
    override fun onCreate() {
        super.onCreate()
        myAppInstance = this
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
    }

    //初始化
    fun initData() {
        LitePal.initialize(this)
        try {
            CrashReport.initCrashReport(applicationContext, "b46f2db3f9", true);
            //init Xprinter
            PrinterDeviceHelper.initPrinter(this)
            //初始化美团钱箱
            MTCashboxSdkHelper.Init()
            AndroidThreeTen.init(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }


        RxActivityResult.register(this)
        Stetho.initializeWithDefaults(this)

//        CrashReport.testJavaCrash()
    }

    var orderedScreen: SecondaryScreenUI? = null

    fun initSecondary(context: Context) {
        Timber.d("initSecondary initSecond")
        try {
            val mediaRouter =
                applicationContext.getSystemService(Context.MEDIA_ROUTER_SERVICE) as MediaRouter
            val route = mediaRouter.getSelectedRoute(MediaRouter.ROUTE_TYPE_LIVE_VIDEO)
            val display = route.presentationDisplay
            if (display != null) {
                orderedScreen = SecondaryScreenUI(context, display)
                GlobalScope.launch {
                    PreferenceDataStoreHelper.getInstance().apply {
                        val model = Gson().fromJson(
                            getFirstPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                ""
                            ), UserLoginResponse::class.java
                        )
                        orderedScreen?.userInfo = model
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun showToast(content: String) {
        mainScope.launch {
            Toast.makeText(this@MyApplication, content, Toast.LENGTH_LONG).show()
        }
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(AppContextWrapper.init(base))
    }

    @Inject
    lateinit var repository: Repository

    private val scope = CoroutineScope(Dispatchers.IO)
    private val mainScope = CoroutineScope(Dispatchers.Main)

//    fun uploadLog(request: UploadLogRequest) {
//        scope.launch {
//            if (MainDashboardFragment.CURRENT_USER != null) {
//                repository.uploadLog(request)
//            }
//        }
//    }

    override fun onTerminate() {
        // 释放USB打印机资源
        try {
            PrinterUsbDeviceHelper.releaseAllUsbConnect()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        scope.cancel()
        mainScope.cancel()
        super.onTerminate()
    }

    override fun newImageLoader(context: Context): ImageLoader {
        return ImageLoader.Builder(context)
            .memoryCache {
                MemoryCache.Builder()
                    .maxSizePercent(context, 0.25) // 占用应用内存的 25%
                    .build()
            }
            .diskCache {
                DiskCache.Builder()
                    .directory(File(context.cacheDir, "coil_v3_cache"))
                    .maxSizeBytes(100L * 1024 * 1024) // 100MB 磁盘缓存
                    .build()
            }
            .crossfade(true)
            .build()
    }

    companion object {
        val LOCALE_KHMER = Locale("km", "")
        lateinit var myAppInstance: MyApplication
        val globalGson = Gson()
    }


}