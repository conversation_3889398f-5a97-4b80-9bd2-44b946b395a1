package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R

class GridAdapter(
    private var items: List<GridItem>,
    private val onItemClick: (GridItem, Int) -> Unit
) : RecyclerView.Adapter<GridAdapter.GridViewHolder>() {

    class GridViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val icon: ImageView = itemView.findViewById(R.id.gridIcon)
        val title: TextView = itemView.findViewById(R.id.gridTitle)
        val container: View = itemView
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_grid, parent, false)
        return GridViewHolder(view)
    }

    override fun onBindViewHolder(holder: GridViewHolder, position: Int) {
        val item = items[position]
        
        holder.icon.setImageResource(item.iconRes)
        holder.title.text = item.title
        
        // 设置选中状态
        if (item.isSelected) {
            holder.container.setBackgroundResource(R.drawable.background_primary_radius_8)
            holder.title.setTextColor(holder.itemView.context.getColor(R.color.white))
        } else {
            holder.container.setBackgroundResource(R.drawable.background_white_border_gray_radius_8)
            holder.title.setTextColor(holder.itemView.context.getColor(R.color.black))
        }
        
        holder.container.setOnClickListener {
            onItemClick(item, position)
        }
    }

    override fun getItemCount(): Int = items.size

    fun updateData(newItems: List<GridItem>) {
        items = newItems
        notifyDataSetChanged()
    }
}
