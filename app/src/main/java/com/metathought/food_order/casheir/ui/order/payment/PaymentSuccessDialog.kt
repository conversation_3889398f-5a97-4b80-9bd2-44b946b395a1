package com.metathought.food_order.casheir.ui.order.payment

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogPaymentSuccessBinding
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/3/2213:00
 * @description
 */
@AndroidEntryPoint
class PaymentSuccessDialog : DialogFragment() {

    private var binding: DialogPaymentSuccessBinding? = null
    private var successText: String? = null
    private var orderedInfo: OrderedInfoResponse? = null
    private var onCloseListener: (() -> Unit)? = null
    private var blackBoard: SecondaryScreenUI? = null


    companion object {
        private const val KEY_PAYMENT_SUCCESS = "KEY_PAYMENT_SUCCESS"

        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            orderedInfo: OrderedInfoResponse? = null,
            blackBoard: SecondaryScreenUI? = null,
            successText: String? = null,
            onCloseListener: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(KEY_PAYMENT_SUCCESS)
            if (fragment != null) return
            fragment = newInstance(orderedInfo, blackBoard, successText, onCloseListener)
            fragment.show(fragmentManager, KEY_PAYMENT_SUCCESS)
        }

        private fun newInstance(
            orderedInfo: OrderedInfoResponse?,
            blackBoard: SecondaryScreenUI? = null,
            successText: String? = null,
            onCloseListener: (() -> Unit)? = null
        ): PaymentSuccessDialog {
            return PaymentSuccessDialog().apply {
                this.successText = successText
                this.orderedInfo = orderedInfo
                this.blackBoard = blackBoard
                this.onCloseListener = onCloseListener
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPaymentSuccessBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        binding?.run {
            if (!successText.isNullOrEmpty()) {
                tvContent.text = successText
            }
            btnClose.setOnClickListener {
                onCloseListener?.invoke()
                dismissAllowingStateLoss()
            }
        }
        context?.let {
            orderedInfo?.let { info ->
//                SecondaryManager.showPaymentResult(it, info)
//                SecondaryManagerV2.showPaymentResult(it, info)
                blackBoard?.showPaymentResult(info)

            }
        }

    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

}