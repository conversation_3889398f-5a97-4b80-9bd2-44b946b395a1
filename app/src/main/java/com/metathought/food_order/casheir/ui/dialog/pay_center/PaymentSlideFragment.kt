package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PaymentChannel
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.databinding.FragmentPaymentSlideBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.PayChannelAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecord
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentSlideFragment : Fragment() {

    companion object {
        private const val ARG_SHOPPING_RECORD = "shopping_record"
        private const val ARG_CONVERSION_RATIO = "conversion_ratio"
        private const val ARG_TOTAL_PRICE = "total_price"
        private const val ARG_TOTAL_VIP_PRICE = "total_vip_price"
        private const val ARG_COUNTRY_CODE = "country_code"
        private const val ARG_PHONE = "phone"
        private const val ARG_SCENE = "scene"
        private const val ARG_ORDER_ID = "order_id"

        fun newInstance(
            menuOrderScreen: SecondaryScreenUI? = null,
            currentScene: Int? = null,
            orderInfo: OrderedInfoResponse? = null,
            shoppingRecord: ShoppingRecord? = null,
            conversionRatio: Long? = null,
            totalPrice: Long? = null,
            totalVipPrice: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            rechargeData: PayDialog.RechargeData? = null,
            creditRecord: CreditRecord? = null,
            paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
            repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
            onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null,
            onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
            rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null,
            onCloseListener: ((String?) -> Unit)? = null,
            onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
            onClearShoppingListener: (() -> Unit)? = null,
        ): PaymentSlideFragment {
            return PaymentSlideFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(ARG_SHOPPING_RECORD, shoppingRecord)
                    if (conversionRatio != null) putLong(ARG_CONVERSION_RATIO, conversionRatio)
                    if (totalPrice != null) putLong(ARG_TOTAL_PRICE, totalPrice)
                    if (totalVipPrice != null) putLong(ARG_TOTAL_VIP_PRICE, totalVipPrice)
                    putString(ARG_COUNTRY_CODE, countryCode)
                    putString(ARG_PHONE, phone)
                    putInt(ARG_SCENE, currentScene ?: SceneEnum.MENU.id)
                    putString(ARG_ORDER_ID, orderInfo?.orderNo)
                }

                // Set callback properties
                this.menuOrderScreen = menuOrderScreen
                this.orderInfo = orderInfo
                this.creditRecord = creditRecord
                this.rechargeData = rechargeData
                this.paymentResponse = paymentResponse
                this.repaymentResponse = repaymentResponse
                this.onlineRepaymentSuccessListener = onlineRepaymentSuccessListener
                this.onlineSuccessListener = onlineSuccessListener
                this.rechargeSuccessListener = rechargeSuccessListener
                this.onCloseListener = onCloseListener
                this.onTopUpListener = onTopUpListener
                this.onClearShoppingListener = onClearShoppingListener
            }
        }
    }

    private var _binding: FragmentPaymentSlideBinding? = null
    private val binding get() = _binding

    private val payViewModel: PayViewModel by viewModels()
    private var mAdapter: PayChannelAdapter? = null
    private var gridAdapter: PaymentGridAdapter? = null
    private var keypadAdapter: KeypadAdapter? = null

    // Arguments
    private var shoppingRecord: ShoppingRecord? = null
    private var conversionRatio: Long? = null
    private var totalPrice: Long? = null
    private var totalVipPrice: Long? = null
    private var countryCode: String? = null
    private var phone: String? = null
    private var scene: Int? = null
    private var orderId: String? = null
    private var orderInfo: OrderedInfoResponse? = null
    private var creditRecord: CreditRecord? = null
    private var rechargeData: PayDialog.RechargeData? = null

    // Secondary screen
    var menuOrderScreen: SecondaryScreenUI? = null

    // Callbacks - 与PayDialog保持一致
    var paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null
    var repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null
    var onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null
    var onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null
    var rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null
    var onCloseListener: ((String?) -> Unit)? = null
    var onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null
    var onClearShoppingListener: (() -> Unit)? = null


    private var offlineChannelList: List<OfflineChannelModel>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            shoppingRecord = it.getParcelable(ARG_SHOPPING_RECORD)
            conversionRatio = it.getLong(ARG_CONVERSION_RATIO)
            totalPrice = it.getLong(ARG_TOTAL_PRICE)
            totalVipPrice = it.getLong(ARG_TOTAL_VIP_PRICE)
            countryCode = it.getString(ARG_COUNTRY_CODE)
            phone = it.getString(ARG_PHONE)
            scene = it.getInt(ARG_SCENE)
            orderId = it.getString(ARG_ORDER_ID)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPaymentSlideBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        initObservers()

        payViewModel.getChannels()

    }

    private fun initViews() {
        binding?.apply {
            // 设置状态栏高度
//            setupStatusBar()

            topBar.getCloseBtn()?.setOnClickListener {
                closeFragment()
            }

            // 初始化RecyclerView
            setupRecyclerView()

            // 初始化GridView
            setupGridView()
        }
    }

    private fun setupRecyclerView() {
        binding?.rightBlockTop?.let { recyclerView ->
            // 设置GridLayoutManager，3列
            val layoutManager = GridLayoutManager(requireContext(), 3)
            recyclerView.layoutManager = layoutManager

            // 取消滑动效果
            recyclerView.isNestedScrollingEnabled = false
            recyclerView.overScrollMode = View.OVER_SCROLL_NEVER

            // 添加间距装饰器，6dp间距
            val spacingInPixels = (6 * resources.displayMetrics.density).toInt()
            val itemDecoration = GridSpacingItemDecoration(3, spacingInPixels, false)
            recyclerView.addItemDecoration(itemDecoration)

            // 创建适配器
            gridAdapter = PaymentGridAdapter(listOf()) { item, position ->
                // 处理点击事件
                onGridItemClick(item, position)
            }

            recyclerView.adapter = gridAdapter
        }
    }

    private fun setupGridView() {
        binding?.rightBlockBottom?.let { gridView ->
            // 创建键盘数据
            val keypadItems = listOf(
                KeypadItem(1, "1", KeypadType.NUMBER),
                KeypadItem(2, "2", KeypadType.NUMBER),
                KeypadItem(3, "3", KeypadType.NUMBER),
                KeypadItem(4, "+", KeypadType.OPERATOR),
                KeypadItem(5, "4", KeypadType.NUMBER),
                KeypadItem(6, "5", KeypadType.NUMBER),
                KeypadItem(7, "6", KeypadType.NUMBER),
                KeypadItem(8, "-", KeypadType.OPERATOR),
                KeypadItem(9, "7", KeypadType.NUMBER),
                KeypadItem(10, "8", KeypadType.NUMBER),
                KeypadItem(11, "9", KeypadType.NUMBER),
                KeypadItem(12, "×", KeypadType.OPERATOR),
                KeypadItem(13, "0", KeypadType.ZERO),
                KeypadItem(14, "确认", KeypadType.CONFIRM, 3) // 占3列
            )

            // 创建适配器
            keypadAdapter = KeypadAdapter(requireContext(), keypadItems) { item ->
                onKeypadItemClick(item)
            }

            gridView.adapter = keypadAdapter
        }
    }

    private fun onKeypadItemClick(item: KeypadItem) {
        // 处理键盘按钮点击事件
        when (item.type) {
            KeypadType.NUMBER, KeypadType.ZERO -> {
                // 处理数字输入
            }
            KeypadType.OPERATOR -> {
                // 处理操作符
            }
            KeypadType.CONFIRM -> {
                // 处理确认按钮
            }
        }
    }

    //设置支付渠道数据
    private fun setChannelAdapter(list: ArrayList<OfflineChannelModel>) {
        val toJson = list.toJson()
        val listType = object : TypeToken<ArrayList<OfflineChannelModel>?>() {}.type
        offlineChannelList = Gson().fromJson(toJson, listType)
        val finalList = ArrayList<OfflineChannelModel>()
        if ((totalPrice ?: 0L) > 0L) {
            finalList.add(
                OfflineChannelModel(
                    channelsName = getString(R.string.online_payment),
                    id = PaymentChannel.ONLINE.id,
                    selected = true
                )
            )
        }

        //余额充值不显示余额支付
        if (scene == SceneEnum.TOPUP_BALANCE.id) {
            //禁止混合支付
//            binding?.btnMixedPayment?.isVisible = false
        } else {
            finalList.add(
                OfflineChannelModel(
                    channelsName = getString(R.string.pay_by_balance),
                    id = PaymentChannel.BALANCE.id,
                    selected = true
                )
            )
        }

        if (list.isNotEmpty()) {
            //如果线下列表不为空
            finalList.add(0, list.removeFirst())
            finalList.addAll(list)
        }

        gridAdapter?.updateData(finalList.toList())
//        updateViewByPaymentType()
    }


    private fun onGridItemClick(item: OfflineChannelModel, position: Int) {
        // 处理网格项点击事件
        // 可以在这里添加具体的业务逻辑
        gridAdapter?.setSelectItems(item)
    }

    private fun initObservers() {
        // 观察支付状态
        payViewModel.uiRequestState.observe(viewLifecycleOwner) { response ->
            when (response?.paymentResponse) {
                is ApiResponse.Loading -> {
                    // 显示加载状态
                }

                is ApiResponse.Success -> {
                    // 支付成功处理
                    paymentResponse?.invoke(response.paymentResponse)
                    closeFragment()
                }

                is ApiResponse.Error -> {
                    // 支付失败处理
                    paymentResponse?.invoke(response.paymentResponse)
                }

                else -> {}
            }
        }
        payViewModel.uiPaymentChannelModeState.observe(viewLifecycleOwner) { state ->
            state.result?.let {
                when (it) {
                    is ApiResponse.Loading -> {
                        binding?.apply {
//                            pbLoading.isVisible = true
//                            llContent.isVisible = false
//                            llBottom.isVisible = false
                        }
                    }

                    is ApiResponse.Error -> {
                        binding?.apply {
//                            pbLoading.isVisible = false
//                            llContent.isVisible = true
//                            llBottom.isVisible = true
                            setChannelAdapter(ArrayList())
                        }
                    }

                    is ApiResponse.Success -> {
                        binding?.apply {
//                            pbLoading.isVisible = false
//                            llContent.isVisible = true
//                            llBottom.isVisible = true
//                                setChannelAdapter(ArrayList())
                            setChannelAdapter(ArrayList(it.data.filter { it.selected == true }))
                        }
                    }
                }
            }
        }

    }

    private fun setupPaymentChannels() {
        // Setup payment channels similar to PayDialog
        val channels = arrayListOf<OfflineChannelModel>()

        // Add online payment channel
//        if ((totalPrice ?: 0L) > 0L) {
//            channels.add(OfflineChannelModel().apply {
//                channelsName = getString(R.string.online_payment)
//                id = PaymentChannel.ONLINE.id
//                selected = true
//            })
//        }
//
//        // Add balance payment channel (except for balance top-up scene)
//        if (scene != SceneEnum.TOPUP_BALANCE.id) {
//            channels.add(OfflineChannelModel().apply {
//                channelsName = getString(R.string.pay_by_balance)
//                id = PaymentChannel.BALANCE.id
//                selected = true
//            })
//        }

        // Add cash payment channel
//        channels.add(OfflineChannelModel().apply {
//            channelsName = getString(R.string.pay_by_cash)
//            id = OfflinePaymentChannelEnum.CASH.id
//            selected = true
//        })

        mAdapter?.updateData(channels)
        mAdapter?.setSelectIndex(0)
//        updateViewByPaymentType()
    }


    private fun closeFragment() {
        // 查找MainDashboardFragment并调用其隐藏方法
        val mainDashboardFragment = parentFragment as? MainDashboardFragment
        mainDashboardFragment?.hidePaymentSlideFragment()

        //todo 支付成功后关闭页面
//        onCloseListener?.invoke(null)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
