package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PaymentChannel
import com.metathought.food_order.casheir.databinding.FragmentPaymentSlideBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.PayChannelAdapter
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecord
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class PaymentSlideFragment : Fragment() {

    companion object {
        private const val ARG_SHOPPING_RECORD = "shopping_record"
        private const val ARG_CONVERSION_RATIO = "conversion_ratio"
        private const val ARG_TOTAL_PRICE = "total_price"
        private const val ARG_TOTAL_VIP_PRICE = "total_vip_price"
        private const val ARG_COUNTRY_CODE = "country_code"
        private const val ARG_PHONE = "phone"
        private const val ARG_SCENE = "scene"
        private const val ARG_ORDER_ID = "order_id"

        fun newInstance(
            menuOrderScreen: SecondaryScreenUI? = null,
            currentScene: Int? = null,
            orderInfo: OrderedInfoResponse? = null,
            shoppingRecord: ShoppingRecord? = null,
            conversionRatio: Long? = null,
            totalPrice: Long? = null,
            totalVipPrice: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            rechargeData: PayDialog.RechargeData? = null,
            creditRecord: CreditRecord? = null,
            paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
            repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
            onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null,
            onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
            rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null,
            onCloseListener: ((String?) -> Unit)? = null,
            onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
            onClearShoppingListener: (() -> Unit)? = null,
        ): PaymentSlideFragment {
            return PaymentSlideFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(ARG_SHOPPING_RECORD, shoppingRecord)
                    if (conversionRatio != null) putLong(ARG_CONVERSION_RATIO, conversionRatio)
                    if (totalPrice != null) putLong(ARG_TOTAL_PRICE, totalPrice)
                    if (totalVipPrice != null) putLong(ARG_TOTAL_VIP_PRICE, totalVipPrice)
                    putString(ARG_COUNTRY_CODE, countryCode)
                    putString(ARG_PHONE, phone)
                    putInt(ARG_SCENE, currentScene ?: SceneEnum.MENU.id)
                    putString(ARG_ORDER_ID, orderInfo?.orderNo)
                }

                // Set callback properties
                this.menuOrderScreen = menuOrderScreen
                this.orderInfo = orderInfo
                this.creditRecord = creditRecord
                this.rechargeData = rechargeData
                this.paymentResponse = paymentResponse
                this.repaymentResponse = repaymentResponse
                this.onlineRepaymentSuccessListener = onlineRepaymentSuccessListener
                this.onlineSuccessListener = onlineSuccessListener
                this.rechargeSuccessListener = rechargeSuccessListener
                this.onCloseListener = onCloseListener
                this.onTopUpListener = onTopUpListener
                this.onClearShoppingListener = onClearShoppingListener
            }
        }
    }

    private var _binding: FragmentPaymentSlideBinding? = null
    private val binding get() = _binding

    private val payViewModel: PayViewModel by viewModels()
    private var mAdapter: PayChannelAdapter? = null

    // Arguments
    private var shoppingRecord: ShoppingRecord? = null
    private var conversionRatio: Long? = null
    private var totalPrice: Long? = null
    private var totalVipPrice: Long? = null
    private var countryCode: String? = null
    private var phone: String? = null
    private var scene: Int? = null
    private var orderId: String? = null
    private var orderInfo: OrderedInfoResponse? = null
    private var creditRecord: CreditRecord? = null
    private var customerMemberResponse: CustomerMemberResponse? = null
    private var offlineChannelList: List<OfflineChannelModel>? = null
    private var isBalanceInsufficient = false
    private var rechargeData: PayDialog.RechargeData? = null

    // Secondary screen
    var menuOrderScreen: SecondaryScreenUI? = null

    // Callbacks - 与PayDialog保持一致
    var paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null
    var repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null
    var onlineRepaymentSuccessListener: ((RepaymentResponse) -> Unit)? = null
    var onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null
    var rechargeSuccessListener: ((RechargeQRStatusResponse?) -> Unit)? = null
    var onCloseListener: ((String?) -> Unit)? = null
    var onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null
    var onClearShoppingListener: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            shoppingRecord = it.getParcelable(ARG_SHOPPING_RECORD)
            conversionRatio = it.getLong(ARG_CONVERSION_RATIO)
            totalPrice = it.getLong(ARG_TOTAL_PRICE)
            totalVipPrice = it.getLong(ARG_TOTAL_VIP_PRICE)
            countryCode = it.getString(ARG_COUNTRY_CODE)
            phone = it.getString(ARG_PHONE)
            scene = it.getInt(ARG_SCENE)
            orderId = it.getString(ARG_ORDER_ID)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPaymentSlideBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        initObservers()
        setupPaymentChannels()
    }

    private fun initViews() {
        binding?.apply {
            btnMixedPayment.setEnableWithAlpha(totalPrice!! > 1)
            mAdapter = PayChannelAdapter(arrayListOf()) {
                if (payViewModel.uiRequestState.value?.paymentResponse is ApiResponse.Loading) {
                    return@PayChannelAdapter
                }
                mAdapter?.setSelectIndex(it)
                hideKeyboard2()
                updateViewByPaymentType()
            }
            rvPaymentChannel.layoutManager =
                LinearLayoutManager(requireActivity(), LinearLayoutManager.HORIZONTAL, false)

            rvPaymentChannel.adapter = mAdapter

            topBar.getCloseBtn()?.setOnClickListener {
                closeFragment()
            }

//            btnMixedPayment.setOnClickListener {
//                SingleClickUtils.isFastDoubleClick {
//                    MixedPayDialog.showDialog(
//                        parentFragmentManager,
//                        orderId = payViewModel.orderNo,
//                        orderInfo = orderInfo,
//                        shoppingRecord = shoppingRecord,
//                        conversionRatio = conversionRatio,
//                        totalPrice = totalPrice,
//                        countryCode = countryCode,
//                        phone = phone,
//                        offlineChannelList = offlineChannelList,
//                        scene = scene,
//                        creditRecord = creditRecord,
//                        repaymentResponse = repaymentResponse,
//                        onlineSuccessListener = onlineSuccessListener,
//                        onCloseListener = onCloseListener,
//                        onTopUpListener = onTopUpListener,
//                        onClearShoppingListener = onClearShoppingListener,
//                        onUpdateCustomizeKhr = {
////                            binding?.viewCustomInput?.getCustomValue(viewLifecycleOwner)
//                        }
//                    )
//                }
//            }

//            btnDone.setOnClickListener {
//                SingleClickUtils.isFastDoubleClick {
//                    Timber.e("点击支付")
//                    val selectItem = mAdapter?.getSelect()
//                    if (selectItem != null) {
//                        handlePayment(selectItem)
//                    }
//                }
//            }
        }
    }

    private fun initObservers() {
        // 观察支付状态
        payViewModel.uiRequestState.observe(viewLifecycleOwner) { response ->
            when (response?.paymentResponse) {
                is ApiResponse.Loading -> {
                    // 显示加载状态
                }

                is ApiResponse.Success -> {
                    // 支付成功处理
                    paymentResponse?.invoke(response.paymentResponse)
                    closeFragment()
                }

                is ApiResponse.Error -> {
                    // 支付失败处理
                    paymentResponse?.invoke(response.paymentResponse)
                }

                else -> {}
            }
        }
    }

    private fun setupPaymentChannels() {
        // Setup payment channels similar to PayDialog
        val channels = arrayListOf<OfflineChannelModel>()

        // Add online payment channel
//        if ((totalPrice ?: 0L) > 0L) {
//            channels.add(OfflineChannelModel().apply {
//                channelsName = getString(R.string.online_payment)
//                id = PaymentChannel.ONLINE.id
//                selected = true
//            })
//        }
//
//        // Add balance payment channel (except for balance top-up scene)
//        if (scene != SceneEnum.TOPUP_BALANCE.id) {
//            channels.add(OfflineChannelModel().apply {
//                channelsName = getString(R.string.pay_by_balance)
//                id = PaymentChannel.BALANCE.id
//                selected = true
//            })
//        }

        // Add cash payment channel
//        channels.add(OfflineChannelModel().apply {
//            channelsName = getString(R.string.pay_by_cash)
//            id = OfflinePaymentChannelEnum.CASH.id
//            selected = true
//        })

        mAdapter?.updateData(channels)
        mAdapter?.setSelectIndex(0)
        updateViewByPaymentType()
    }

    private fun updateViewByPaymentType() {
        val selectItem = mAdapter?.getSelect()
        when (selectItem?.id) {
            PaymentChannel.ONLINE.id -> {
                onlinePaymentView()
            }

            PaymentChannel.BALANCE.id -> {
                balancePaymentView()
            }

            OfflinePaymentChannelEnum.CASH.id -> {
                cashPaymentView()
            }

            else -> {
                otherOfflinePaymentView()
            }
        }
    }

    private fun onlinePaymentView() {
        binding?.apply {
            clQrCode.isVisible = true
            clBalance.isVisible = false
            clCash.isVisible = false
            llOther.isVisible = false
            llBottom.isVisible = true
        }
    }

    private fun balancePaymentView() {
        binding?.apply {
            clQrCode.isVisible = false
            clBalance.isVisible = true
            clCash.isVisible = false
            llOther.isVisible = false
            llBottom.isVisible = true
        }
    }

    private fun cashPaymentView() {
        binding?.apply {
            clCash.isVisible = true
            clQrCode.isVisible = false
            clBalance.isVisible = false
            llOther.isVisible = false
            llBottom.isVisible = true

            edtUsdAmount.clearFocus()
            edtKhrAmount.clearFocus()
            requireActivity().hideKeyboard(edtUsdAmount)
            requireActivity().hideKeyboard(edtKhrAmount)

//            viewCustomInput.getCustomValue(viewLifecycleOwner)
            btnDone.setEnableWithAlpha(true)
        }
    }

    private fun otherOfflinePaymentView() {
        binding?.apply {
            clCash.isVisible = false
            clQrCode.isVisible = false
            clBalance.isVisible = false
            llOther.isVisible = true
            llBottom.isVisible = true
        }
    }

    private fun handlePayment(selectItem: OfflineChannelModel) {
        when (selectItem.id) {
            PaymentChannel.ONLINE.id -> {
                // Handle online payment
                payViewModel.payment(
                    shareRecord = shoppingRecord,
                    payType = PayTypeEnum.ONLINE_PAYMENT,
                )
            }

            PaymentChannel.BALANCE.id -> {
                // Handle balance payment
                if (isBalanceInsufficient) {
                    customerMemberResponse?.let { it1 ->
                        onTopUpListener?.invoke(it1)
                    }
                } else {
                    // Process balance payment
                }
            }

            OfflinePaymentChannelEnum.CASH.id -> {
                // Handle cash payment
                payViewModel.payment(
                    payType = PayTypeEnum.CASH_PAYMENT,
                    shareRecord = shoppingRecord,
                    offlineChannelModel = selectItem,
                )
            }

            else -> {
                // Handle other offline payment
                payViewModel.payment(
                    payType = PayTypeEnum.CASH_PAYMENT,
                    shareRecord = shoppingRecord,
                    offlineChannelModel = selectItem,
                )
            }
        }
    }

    private fun hideKeyboard2() {
//        requireActivity().hideKeyboard(binding?.edtUsdAmount)
//        requireActivity().hideKeyboard(binding?.edtKhrAmount)
    }

    private fun closeFragment() {
        // 查找MainDashboardFragment并调用其隐藏方法
        val mainDashboardFragment = parentFragment as? MainDashboardFragment
        mainDashboardFragment?.hidePaymentSlideFragment()

        //todo 支付成功后关闭页面
//        onCloseListener?.invoke(null)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
