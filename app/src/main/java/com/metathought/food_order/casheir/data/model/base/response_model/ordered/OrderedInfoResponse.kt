package com.metathought.food_order.casheir.data.model.base.response_model.ordered


import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.AcceptOrderedStatusEnum
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.OrderCouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.doubleStrToIntStr
import com.metathought.food_order.casheir.extension.formatDateWithoutSecond
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber
import java.math.BigDecimal
import java.util.Locale
import kotlin.math.min


data class OrderedInfoResponse(

    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("createdUserId")
    val createdUserId: Any?,
    @SerializedName("diningStyle")
    val diningStyle: Int?,
    @SerializedName("getOrderNumber")
    val getOrderNumber: Any?,
    @SerializedName("goods")
    var goods: ArrayList<OrderedGoods>?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("isPreOrder")
    val isReservation: Boolean?,
    @SerializedName("note")
    var note: String?,
    @SerializedName("orderNo")
    val orderNo: String?,
    @SerializedName("payStatus")
    var payStatus: Int?,
    @SerializedName("payTime")
    val payTime: String?,
    @SerializedName("payType")
    var payType: Int?,
    @SerializedName("peopleDate")
    val peopleDate: String?,
    @SerializedName("peopleNum")
    val peopleNum: Int?,
    @SerializedName("pickupCode")
    val pickupCode: String?,

    @SerializedName("refundGoodsJson")
    val refundGoodsJson: ArrayList<OrderedGoods>?,

    @SerializedName("sourcePlatform")
    val sourcePlatform: Int?,
    //线下支付渠道ID
    @SerializedName("offlinePaymentChannelsId")
    val offlinePaymentChannelsId: String?,

    //线下支付渠道名称
    @SerializedName("offlinePaymentChannelsName")
    var offlinePaymentChannelsName: String?,
    //线下支付渠道名称(小票打印 翻译用)
    @SerializedName("offlinePaymentChannelsNameEn")
    var offlinePaymentChannelsNameEn: String?,
    //线下支付渠道名称(小票打印 翻译用)
    @SerializedName("offlinePaymentChannelsNameKm")
    var offlinePaymentChannelsNameKm: String?,

    @SerializedName("tableName")
    val tableName: String?,
    @SerializedName("tableUuid")
    val tableUuid: String?,
    @SerializedName("tableType")
    val tableType: Int?,

    @SerializedName("customerInfoVo")
    val customerInfoVo: CustomerInfoVo?,
    @SerializedName("refundDateTime")
    val refundDateTime: String?,
    @SerializedName("totalVatPrice")
    val totalVatPrice: Long?,
    @SerializedName("refundNote")
    val refundNote: String?,
    @SerializedName("cancelTime")
    val cancelTime: String?,
    /**
     * 总的会员价  没会员价的时候等于totalDiscountPrice
     * **/
    @SerializedName("totalVipPrice")
    val totalVipPrice: Long?,
    @SerializedName("totalVipVatPrice")
    val totalVipVatPrice: Long?,
    //打包费
    @SerializedName("totalPackingFee")
    val totalPackingFee: Int?,

    //堂食服务费
    @SerializedName("totalServiceCharge")
    val totalServiceCharge: Long?,
    //折扣堂食服务费
    @SerializedName("totalDiscountServiceCharge")
    val totalDiscountServiceCharge: Long?,
    //Vip堂食服务费
    @SerializedName("totalVipServiceCharge")
    val totalVipServiceCharge: Long?,
    //vip 单品减免后的堂食服务费
    @SerializedName("totalGoodsReduceVipServiceCharge")
    val totalGoodsReduceVipServiceCharge: Long?,
    //现价 单品减免后的堂食服务费
    @SerializedName("totalGoodsReduceServiceCharge")
    val totalGoodsReduceServiceCharge: Long?,


    //小票打印用的名字
    @SerializedName("consumerName")
    val consumerName: String?,

    //总金额，KHR
    @SerializedName("totalRielPrice")
    val totalRielPrice: BigDecimal? = null,
    //是否有使用会员优惠
    @SerializedName("isUseDiscount")
    val isUseDiscount: Boolean?,

    //是否需要厨房制作
    @SerializedName("kitchenMaking")
    val kitchenMaking: Boolean?,

    /**
     * 2.16.0以前 0:有称重商品未称重, 1:有称重商品已称重，2:菜品列表没有称重商品,订单是已确认状态
     * 2.16.0后改为 0:有待处理商品, 1:有待处理商品已处理，2:菜品列表没有待处理商品,订单是已确认状态（弃用）
     * **/
//    @SerializedName("weighMark")
//    val weighMark: Int?,

    //当前加购的菜，本地设置，后端没有返回
    @Transient
    var currentOrderMore: ArrayList<GoodsBo>?,


    @SerializedName("currentOrderMoreList")
    var currentOrderMoreList: ArrayList<OrderedGoods>? = null,

    //汇率
    @SerializedName("conversionRatio")
    val conversionRatio: Long?,

    //总的折扣价
    @SerializedName("totalDiscountPrice")
    val totalDiscountPrice: Long?,

    //现金单品减免后的总价
    @SerializedName("totalGoodsReducePrice")
    val totalGoodsReducePrice: Long?,

    //vip单品减免后总价
    @SerializedName("totalGoodsReduceVipPrice")
    val totalGoodsReduceVipPrice: Long?,

    //总的折扣价vat
    @SerializedName("totalDiscountVatPrice")
    val totalDiscountVatPrice: Long?,
    //vip 单品减免后的vat
    @SerializedName("totalGoodsReduceVipVatPrice")
    val totalGoodsReduceVipVatPrice: Long?,
    //现价 单品减免后的vat
    @SerializedName("totalGoodsReduceVatPrice")
    val totalGoodsReduceVatPrice: Long?,


//    //减免金额类型
//    @SerializedName("reduceType")
//    var reduceType: Int?,
//
//    //减免百分比
//    @SerializedName("reduceRate")
//    var reduceRate: Double?,
//
//    //减免百分比的金额
//    @SerializedName("reduceAmount")
//    var reduceAmount: Long?,
//
//    //，translate/v2，  againPay， getKhqr， preSettlement 这几个接口传 这个要除100
//    //减免usd金额
//    @SerializedName("reduceDollar")
//    var reduceDollar: Long?,
//
//    //减免khr金额
//    @SerializedName("reduceKhr")
//    var reduceKhr: Long?,

    /**
     * 整单减免金额 普通价 信息
     */
    @SerializedName("wholeDiscountReduce")
    var wholeDiscountReduce: WholeDiscountReduce? = null,
//    /**
//     * 整单减免金额 vip价 信息
//     */
//    @SerializedName("wholeDiscountVipReduce")
//    var wholeDiscountVipReduce: WholeDiscountReduce? = null,


    //ws=36的时候有这个 用来菜品翻译 因为那时候没orderNo调用不了orderTranslate 方法
    @SerializedName("translateInfoVo")
    var translateInfoVo: OrderedTranslateResponse? = null,

    @SerializedName("cancelReason")
    var cancelReason: String? = null,

    @SerializedName("serviceChargePercentage")
    var serviceChargePercentage: Int? = null,

    //发票号
    @SerializedName("invoiceNumber")
    var invoiceNumber: String? = null,

    //加购接口返回的 订单菜品字符串
    @SerializedName("goodsJson")
    var goodsJson: String? = null,

    @SerializedName("couponType")
    var couponType: String? = null,

    @SerializedName("couponId")
    var couponId: Long? = null,

    @SerializedName("giftGoods")
    var giftGoods: List<UsageGoods>? = null,

    @SerializedName("coupon")
    var coupon: OrderCouponModel? = null,


    /**
     * 订单过期时间 接单信息接口会返回
     */
    @SerializedName("expireTime")
    var expireTime: String? = null,

    /**
     * 接单状态 0待接单 1已接单
     */
    @SerializedName("acceptStatus")
    var acceptStatus: Int? = null,

    /**
     * 接单剩余倒计时时间（毫秒）接单信息接口会返回
     */
    @SerializedName("expireCountDown")
    var expireCountDown: Long? = null,

    /**
     * 已取消的待接单
     */
    @SerializedName("cancelAcceptAddOrderList")
    var cancelAcceptAddOrderList: List<OrderedInfoResponse>? = null,

    /**
     * 待接单
     */
    @SerializedName("waitAcceptAddOrder")
    var waitAcceptAddOrder: OrderedInfoResponse? = null,

    /**
     * true显示 false不显示    是否显示入库按钮
     */
    @SerializedName("showAutoInStockBtn")
    var showAutoInStockBtn: Boolean? = null,

    /**
     * 反结账原因
     */
    @SerializedName("reverseReason")
    var reverseReason: String? = null,

    /**
     * 是否先付款订单
     */

    @SerializedName("isPaymentAdvance")
    var isPaymentAdvance: Boolean? = null,

    /**
     * 已退菜列表
     */
    @SerializedName("removeGoodList")
    var removeGoodList: ArrayList<OrderedGoods>?,


    /**
     * 合单订单id集合  id 按合单顺序逗号隔开
     */
    @SerializedName("mergeOrderIds")
    var mergeOrderIds: String?,


    /**
     *  有效优惠活动列表
     */
    @SerializedName("couponActivityList")
    var couponActivityList: List<CouponActivityModel>?,

    @Transient
    var couponActivityListEn: List<CouponActivityModel>?,

    @Transient
    var couponActivityListKm: List<CouponActivityModel>?,


    /**
     * 收银员
     */
    @SerializedName("employeeName")
    var employeeName: String?,

    /**
     * 单品减免 备注
     */
    @SerializedName("singleReduceReason")
    var singleReduceReason: String? = null,


    /**
     * 接单ids，用逗号分开，用于保证商品下单时间顺序的区分  2.12.0 之前的版本是null， 2.12.0以后  acceptOrderIds 是不包含2.12.0以前的订单
     */
    @SerializedName("acceptOrderIds")
    var acceptOrderIds: String? = null,

    /**
     *
     * 以下新版字段整合
     */

    /**
     * 小计价格
     */
    @SerializedName("totalPrice")
    var totalPrice: Long? = null,

    /**
     *  优惠活动金额
     */
    @SerializedName("couponActivityAmount")
    var couponActivityAmount: Long?,

    /**
     * 优惠券 优惠金额
     */
    @SerializedName("couponAmount")
    var couponAmount: Long? = null,

//    /**
//     * 整单优惠金额
//     */
//    @SerializedName("wholeDiscountAmount")
//    var wholeDiscountAmount: Long? = null,

    /**
     * 总优惠金额
     */
    @SerializedName("couponPrice")
    var couponPrice: Long? = null,

    /**
     * vat
     */
    @SerializedName("vat")
    var vat: Long? = null,

    /**
     * 服务费
     */
    @SerializedName("serviceChargeAmount")
    var serviceChargeAmount: Long? = null,

    /**
     * 打包费
     */
    @SerializedName("packageFee")
    var packageFee: Long? = null,

    /**
     * 实付(应付)金额
     */
    @SerializedName("realPrice")
    var realPrice: Long? = null,

    /**
     * 退款总金额
     */
    @SerializedName("refundPrice")
    val refundPrice: Long? = null,

    /**
     * 线上退款金额
     */
    @SerializedName("onlineRefundPrice")
    val onlineRefundPrice: Long? = null,

    /**
     * 现金退款金额
     */
    @SerializedName("cashRefundPrice")
    val cashRefundPrice: Long? = null,

    /**
     * 收取现金 现金支付时使用 瑞尔
     */
    @SerializedName("collectCash")
    val collectCash: Long? = null,

    /**
     * 找零金额 现金支付时使用瑞尔
     */
    @SerializedName("changeAmount")
    val changeAmount: Long? = null,

    /**
     * 收取现金 现金支付时使用 美元单位分
     */
    @SerializedName("collectCashDollar")
    val collectCashDollar: Long? = null,

    /**
     * 找零现金 现金支付时使用 美元单位分
     */
    @SerializedName("changeAmountDollar")
    val changeAmountDollar: Long? = null,

    /**
     * 余额付款的时候账户剩余余额
     */
    @SerializedName("surplusStoreBalance")
    var surplusStoreBalance: Long? = null,

    /**
     * price 相关金额类
     */
    @SerializedName("price")
    var price: OrderPrice? = null,

    /**
     * 会员价 相关金额类
     */
    @SerializedName("vipPrice")
    var vipPrice: OrderPrice? = null,

    /**
     * 整单减免原因
     */
//    @SerializedName("reduceReason")
//    var reduceReason: String? = null,

    /**
     * 整单减免 选择后台配置的对象
     */
    @SerializedName("discountReduceActivity")
    var discountReduceActivity: DiscountReduceInfo? = null,

    /**
     * 整单减免原因
     */
    @SerializedName("discountType")
    var discountType: Int? = null,

    /**
     * 外卖平台名
     */
    @SerializedName("deliveryPlatformName")
    var deliveryPlatformName: String? = null,

    /**
     * 外卖平台Id
     */
    @SerializedName("deliveryPlatformId")
    var deliveryPlatformId: String? = null,

    /**
     * 外卖订单号
     */
    @SerializedName("deliveryOrderNo")
    var deliveryOrderNo: String? = null,

    /**
     * 外卖平台币种  1-usd  2-khr
     */
    @SerializedName("deliveryPlatformCurrencyType")
    var deliveryPlatformCurrencyType: Int? = null,

    /**
     * 下单的顾客姓名 后台返的
     */
    @SerializedName("customerName")
    val customerName: String?,

    /**
     * 用户手机号余额支付带入用  最终需要展示的手机号
     */
    @SerializedName("consumePhoneNumber")
    val consumePhoneNumber: String?,

    /**
     * 组合支付下 余额支付多少钱
     */
    @SerializedName("balancePayAmount")
    val balancePayAmount: BigDecimal? = null,
    /**
     * 组合支付下 线下支付多少钱
     */
    @SerializedName("offlinePayAmount")
    val offlinePayAmount: BigDecimal? = null,

    /**
     * 是否一次性退款
     */
    @SerializedName("oneTimeRefund")
    val oneTimeRefund: Boolean? = null,

    /**
     * 购物车中是否有称重商品true是false否
     */
    @SerializedName("hasWeighingGoods")
    val hasWeighingGoods: Boolean? = null,
    /**
     * 购物车中是否有待定价商品true是false否
     */
    @SerializedName("hasPricingGoods")
    val hasPricingGoods: Boolean? = null,

    /**
     * 购物车中是否有待处理商品true是false否 = hasWeighingGoods || hasPendingGoods
     */
    @SerializedName("hasPendingGoods")
    val hasPendingGoods: Boolean? = null,

    @SerializedName("creditReason")
    val creditReason: String? = null, //挂账原因
    @SerializedName("cancelCreditReason")
    val cancelCreditReason: String? = null, //取消挂账原因
    @SerializedName("creditConsumerTelephone")
    val creditConsumerTelephone: String? = null, //挂账客户账号
    @SerializedName("creditConsumerNickname")
    val creditConsumerNickname: String? = null, //挂账客户昵称
    @SerializedName("repaymentTime")
    val repaymentTime: String? = null, //挂账还款时间
) {

    fun getGoodsVo(): OrderedGoodJson {
        return MyApplication.globalGson.fromJson<OrderedGoodJson>(
            goodsJson,
            object : TypeToken<OrderedGoodJson>() {}.type
        ) as OrderedGoodJson
    }

    /**
     * 获取顾客的手机号 优先获取填入的，如果没填入的获取注册用户的
     *
     * @return
     */
    fun getConsumePhoneNumber(): Pair<String, String>? {
        if (!customerInfoVo?.mobile.isNullOrEmpty() && !customerInfoVo?.areaCode.isNullOrEmpty()) {
            return Pair(customerInfoVo?.areaCode ?: "", customerInfoVo?.mobile ?: "")
        }

        if (!consumePhoneNumber.isNullOrEmpty()) {
            val list = (consumePhoneNumber ?: "").split(" ")
            if (list.size == 2) {
                return Pair(list[0], if (list[1] == "null") "" else list[1])
            }
        }

        return null
    }

    /**
     * 显示顾客的手机号
     *
     */
    fun getShowPhone(): String {
        val phonePair = getConsumePhoneNumber() ?: return ""
        if (phonePair.second?.isNullOrEmpty() == true) {
            return ""
        }
        return "+${phonePair.first ?: ""} ${phonePair.second ?: ""}"
    }

    /**
     * 获取顾客姓名  优先获取 手动输入的，如果没填，则获取注册用户的名字
     *
     * @return
     */
    fun getLastCustomerName(): String {
        if (!customerInfoVo?.name.isNullOrEmpty()) {
            return customerInfoVo?.name ?: ""
        }
        return customerName ?: ""
    }

    /**
     * 是否通用桌
     *
     * @return
     */
    fun isUniversalTable(): Boolean {
        return tableType == 2
    }

    fun isKhr(): Boolean {
        return deliveryPlatformCurrencyType == 2
    }

    /**
     * 获取现金收款的金额
     *
     * @return
     */
    fun getReceiveAmountToTicket(): String {
        return if (collectCashDollar != null && collectCashDollar > 0 && collectCash != null && collectCash > 0) {
            "${collectCashDollar.priceFormatTwoDigitZero2()} + ៛${collectCash.decimalFormatZeroDigit()}"
        } else if (collectCashDollar != null && collectCashDollar > 0) {
            collectCashDollar.priceFormatTwoDigitZero2()
        } else if (collectCash != null && collectCash > 0) {
            "៛${collectCash.decimalFormatZeroDigit()}"
        } else {
            "$0.00"
        }
    }

    /**
     * 获取找零的金额
     *
     * @return
     */
    fun getChangeAmountToTicket(): String {
        var result = ""
        if (changeAmountDollar == 0L) {
            return "$0.00"
        }
        result =
            "${changeAmountDollar?.priceFormatTwoDigitZero2()} = ${getChangeTotalKhrToTicket()}"
        return result

    }

    //找零美元转成khr 总计
    private fun getChangeTotalKhrToTicket(): String {
        return "៛${
            FoundationHelper.usdConverToKhr(
                (conversionRatio ?: 0),
                (changeAmountDollar ?: 0)
            ).decimalFormatZeroDigit()
        }"
    }

    //是否kiosk 下的单
    fun isFromKiosk(): Boolean {
        return sourcePlatform == SourcePlatformEnum.Kiosk.id
    }

    /**
     * 待确认状态
     *
     * @return
     */
    fun isToBeConfirmState(): Boolean {
        return payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id
    }

    /**
     * 是否待支付
     *
     * @return
     */
    fun isToBePaid(): Boolean {
        return payStatus == OrderedStatusEnum.UNPAID.id
    }

    /**
     * 是否挂账-未支付
     *
     * @return
     */
    fun isCreditUnPaid(): Boolean {
        return payStatus == OrderedStatusEnum.CREDIT_UNPAID.id
    }

    /**
     * 是否挂账-已支付
     *
     * @return
     */
    fun isCreditPaid(): Boolean {
        return payStatus == OrderedStatusEnum.CREDIT_PAID.id
    }

    fun isOrderExpire(): Boolean {
        return payStatus == OrderedStatusEnum.CANCEL_ORDER.id
    }

    /**
     * 订单是否支付成功
     *
     * @return
     */
    fun isOrderSuccess(): Boolean {
        //2：已支付，预订单支付成功：7  挂账-未支付  挂账-已支付
        return payStatus in listOf(
            OrderedStatusEnum.PAID.id,
            OrderedStatusEnum.PREORDER.id,
            OrderedStatusEnum.CREDIT_UNPAID.id,
            OrderedStatusEnum.CREDIT_PAID.id,
        )
    }

    /**
     * 是否支付之后的结果
     *
     * @return
     */
    fun isAfterPayStatus(): Boolean {
        return payStatus in listOf(
            OrderedStatusEnum.PARTIAL_REFUND.id,
            OrderedStatusEnum.FULL_REFUND.id,
        ) || isOrderSuccess()
    }

    /**
     * 需要显示待定价状态
     *
     * @return
     */
    fun needShowUnProcessState(): Boolean {
        if (acceptStatus == AcceptOrderedStatusEnum.WAIT_ACCEPT.id) {
            /**
             * 待接单状态下需要显示待待定价状态
             * **/
            return true
        }
        return payStatus in listOf(
            OrderedStatusEnum.TO_BE_CONFIRM.id,
            OrderedStatusEnum.BE_CONFIRM.id,
            OrderedStatusEnum.UNPAID.id,
            OrderedStatusEnum.CANCEL_ORDER.id,
        )
    }


    /**
     * 需要是否隐藏打印状态
     *
     * @return
     */
    fun needResetUnReadAndPrintStatus(): Boolean {
        return payStatus in listOf(
            OrderedStatusEnum.CANCEL_ORDER.id,
            OrderedStatusEnum.PARTIAL_REFUND.id,
            OrderedStatusEnum.FULL_REFUND.id
        )
    }

    fun isPreOrder(): Boolean {
        return isReservation == true
    }

    fun isTakeAway(): Boolean {
        return diningStyle == DiningStyleEnum.TAKE_AWAY.id
    }

    /**
     * 是否外卖
     *
     * @return
     */
    fun isTakeOut(): Boolean {
        return diningStyle == DiningStyleEnum.TAKE_OUT.id
    }

    fun isDineIn(): Boolean {
        return diningStyle == DiningStyleEnum.DINE_IN.id
    }

    fun getDiningStyleStr(context: Context): String {
        return if (isReservation == true) {
            context.getString(R.string.pre_order)
        } else {
            if (diningStyle == DiningStyleEnum.DINE_IN.id) {
                context.getString(R.string.dine_in)
            } else if (diningStyle == DiningStyleEnum.TAKE_OUT.id) {
                context.getString(R.string.take_out)
            } else {
                context.getString(R.string.take_away)
            }
        }

    }

    /**
     * 新版本统一方法
     */
    /**
     * 获取小计
     *
     * @return
     */
    fun getSubTotal(): Long {
        return totalPrice ?: 0L
    }


    /**
     * 获取优惠券 优惠金额
     *
     * @return
     */
    fun getCouponAmount(): Long {
        return couponAmount ?: 0L
    }

    //获取实际需要付款
    fun getRealPayPrice(): Long {
        return realPrice ?: 0L
    }

    //获取实际的增值税
    fun getRealVatPrice(): Long {
        return vat ?: 0L
    }

    //获取会员对应的增值税
    fun getVipVat(): BigDecimal {
        return vipPrice?.vat ?: BigDecimal.ZERO
    }

    //获取会员对应的服务费
    fun getVipServiceFeePrice(): BigDecimal {
        return vipPrice?.totalServiceChargeAmount ?: BigDecimal.ZERO
    }

    //获取Vip小计
    fun getVipSubTotal(): BigDecimal {
        return vipPrice?.subTotal ?: BigDecimal.ZERO
    }

    //获取打包费
    fun getPackFee(): Long {
        return packageFee ?: 0L
    }

    //获取可参与整单减免的金额
    fun getParticipatingWholeDiscountsAmount(): Long {
        return (price?.participatingWholeDiscountsAmount
            ?: BigDecimal.ZERO).times(BigDecimal(100)).toLong()
    }

    //获取vip可参与整单减免的金额
    fun getVipParticipatingWholeDiscountsAmount(): Long {

        return (vipPrice?.participatingWholeDiscountsAmount
            ?: BigDecimal.ZERO).times(BigDecimal(100)).toLong()
    }

    fun getFinalRealPrice(): Long? {
        if (!isAfterPayStatus()) {
            return getTotalPriceBySelf()
        }
        return realPrice
    }

    /**
     * 获取优惠活动vip 优惠金额
     *
     * @return
     */
    fun getTotalVipCouponActivityAmount(): BigDecimal {
        if (vipPrice?.totalCouponActivityAmount == null) {
            var price = BigDecimal.ZERO
            couponActivityList?.forEach {
                price.plus(BigDecimal(it.activityVipCouponAmount ?: 0L).div(BigDecimal(100)))
            }
            return price
        }
        return vipPrice?.totalCouponActivityAmount ?: BigDecimal.ZERO
    }

    /**
     * 获取优惠活动金额
     *
     * @return
     */
    fun getTotalCouponActivityAmount(): Long {
        if (price?.totalCouponActivityAmount == null) {
            var price = 0L
            couponActivityList?.forEach {
                price += (it.activityCouponAmount ?: 0L)
            }
            return price
        }
        return price?.totalCouponActivityAmount?.times(BigDecimal(100))?.toLong() ?: 0
    }


    fun getTotalVipPriceBySelf(): BigDecimal {
        if ((vipPrice?.totalWholeDiscountAmount ?: BigDecimal.ZERO).isZero()) {
            //小计+服务费+打包费
            var totalPrice =
                (vipPrice?.subTotal ?: BigDecimal.ZERO).plus(
                    vipPrice?.totalServiceChargeAmount
                        ?: BigDecimal.ZERO
                ).plus(vipPrice?.totalPackingAmount ?: BigDecimal.ZERO)

            //扣去优惠金额
            totalPrice = totalPrice.minus(getTotalVipCouponActivityAmount())
            //扣去优惠券金额
            totalPrice =
                totalPrice.minus(BigDecimal.valueOf(getCouponVipPrice().div(100.0))) //BigDecimal((getCurrentCoupon()?.vipCouponPrice ?: 0L).div(100.0))
            //扣去整单减免金额
            totalPrice = totalPrice.minus(vipPrice?.totalWholeDiscountAmount ?: BigDecimal.ZERO)

            if (totalPrice < BigDecimal.ZERO) {
                totalPrice = BigDecimal.ZERO
            }

            vipPrice?.vat = totalPrice.times(vipPrice?.vatPercentage ?: BigDecimal.ZERO).halfUp(2)

            totalPrice += (vipPrice?.vat ?: BigDecimal.ZERO)
            vipPrice?.total = totalPrice
        }

        return (vipPrice?.total ?: BigDecimal.ZERO).times(BigDecimal(100))
    }

    /**
     * 获取优惠券金额 如果优惠卷的优惠金额 和剩余可优惠金额取小的
     */
    fun getCouponPrice(): Long {
        if (getCurrentCoupon()?.isZsCoupon() == true) {
            return 0L
        } else {
            var totalPrice = (totalPrice ?: 0) + (serviceChargeAmount ?: 0) + (packageFee ?: 0)
            totalPrice -= getTotalCouponActivityAmount()
            return min(totalPrice, (getCurrentCoupon()?.couponPrice ?: 0L))
        }
    }

    fun getCouponVipPrice(): Long {
        if (getCurrentCoupon()?.isZsCoupon() == true) {
            return 0L
        } else {
            //小计+服务费+打包费
            var totalPrice =
                (vipPrice?.subTotal ?: BigDecimal.ZERO) + (vipPrice?.totalServiceChargeAmount
                    ?: BigDecimal.ZERO) + (vipPrice?.totalPackingAmount ?: BigDecimal.ZERO)

            //扣去优惠金额
            totalPrice -= getTotalVipCouponActivityAmount()

            return min(
                totalPrice.times(BigDecimal(100)).toLong(),
                (getCurrentCoupon()?.vipCouponPrice ?: 0L)
            )
        }
    }

    //自己算一遍应付金额
    fun getTotalPriceBySelf(): Long {
        //如果没有整单减免自己算金额
        if ((price?.totalWholeDiscountAmount ?: BigDecimal.ZERO).isZero()) {
            //小计+服务费+打包费
            var totalPrice = (totalPrice ?: 0).plus(serviceChargeAmount ?: 0).plus(packageFee ?: 0)
            totalPrice -= getTotalCouponActivityAmount()
            totalPrice -= getCouponPrice()

            vat = (BigDecimal(totalPrice).times(price?.vatPercentage ?: BigDecimal.ZERO)
                .halfUp(0)).toLong()

            totalPrice += (vat ?: 0L)
            realPrice = totalPrice
        } else {
            //如果有整单减免 后面离线版本这里要实现计算逻辑
        }


        return realPrice ?: 0L
    }

    //获取最终总Vip 价格
    fun getFinalTotalVipPrice(): BigDecimal {
        return vipPrice?.total ?: BigDecimal.ZERO
    }

    //获取实际的服务费
    fun getRealServiceFeePrice(): Long {
        return serviceChargeAmount ?: 0L
    }

    /**
     * ==============================================
     */


    //获取退款vat
    fun getVatRefundAmount(): Long {
        var subTotalVat = 0L

        refundGoodsJson?.forEach {
            if (it.finalReduceSingleVatPrice != null) {
                subTotalVat += ((it.finalReduceSingleVatPrice ?: 0) * (it.num ?: 0))
            } else {
                if (isUseDiscount == true) {
                    subTotalVat += ((it.finalVipVatPrice ?: 0) * (it.num ?: 0))
                } else {
                    subTotalVat += it.totalVatPrice()  //  (it.getCurrentPriceVat() * (it.num ?: 0))
                }
            }

        }
        return subTotalVat
    }


    fun getPackRefundAmount(): Long {
        var packFee = 0L
        refundGoodsJson?.forEach {
            if (it.finalReduceSinglePackingFee != null) {
                packFee += ((it.finalReduceSinglePackingFee ?: 0) * (it.num ?: 0))
            } else {
                packFee += ((it.packingFee ?: 0) * (it.num ?: 0))
            }
        }
        return packFee
    }

    fun getServiceFeeRefundAmount(): Long {
        if (isTakeAway()) {
            //这边服务端。外带的时候  finalReduceSingleServiceCharge会返值。所以 这里前置return掉
            return 0L
        }
        var serviceFee = 0L
        refundGoodsJson?.forEach {
            if (it.finalReduceSingleServiceCharge != null) {
                serviceFee += ((it.finalReduceSingleServiceCharge ?: 0) * (it.num ?: 0))
            } else {
                if (isUseDiscount == true) {
                    serviceFee += ((it.finalVipServiceCharge ?: 0) * (it.num ?: 0))
                } else {
                    serviceFee += it.totalServiceFee()
                }
            }
        }
        return serviceFee
    }


    fun getPaymentMethod(context: Context): String {
        return when (payType) {
            PayTypeEnum.CREDIT.id -> {
                context.getString(R.string.credit_payment)
            }

            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getString(R.string.online_payment)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                getOfflinePayMethod(context)
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getString(R.string.pay_by_balance)
            }

            PayTypeEnum.PAY_OTHER.id -> {
                context.getString(R.string.pay_by_other)
            }

            else -> ""
        }
    }

    fun getRefundType(context: Context): String {
        return when (payType) {
            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getString(R.string.offline_refund)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                context.getString(R.string.offline_refund)
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getString(R.string.balance)
            }

            PayTypeEnum.PAY_OTHER.id -> {
                context.getString(R.string.pay_by_other)
            }

            else -> ""
        }
    }


    fun getOfflinePayMethod(context: Context): String {
        if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.pay_by_cash)}"
        } else if (offlinePaymentChannelsId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true) {
            return "${context.getString(R.string.offline_payments)} - ${context.getString(R.string.accounts_receivable)}"
        } else {
            return "${context.getString(R.string.offline_payments)} - $offlinePaymentChannelsName"
        }
    }


    fun getPaymentMethodByLocal(context: Context, locale: Locale): String {
        return when (payType) {
            PayTypeEnum.CREDIT.id -> {
                context.getStringByLocale(R.string.credit_payment, locale)
            }

            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getStringByLocale(R.string.online_payment, locale)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
                getOfflinePayMethodByLocal(context, locale)
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getStringByLocale(R.string.pay_by_balance, locale)
            }

            PayTypeEnum.PAY_OTHER.id -> {
                context.getStringByLocale(R.string.pay_by_other, locale)
            }

            else -> ""
        }
    }

    fun getOfflinePayMethodByLocal(context: Context, locale: Locale): String {
        Timber.e("locale  ${locale.language}")
        return "${context.getStringByLocale(R.string.offline, locale)}-${
            if (isCash()) {
                context.getStringByLocale(R.string.print_pay_by_cash, locale)
            } else if (isAccountsReceivable()) {
                context.getStringByLocale(R.string.accounts_receivable, locale)
            } else {
                if (locale.language.startsWith("zh")) {
                    offlinePaymentChannelsName ?: ""
                } else if (locale.language.startsWith("en")) {
                    offlinePaymentChannelsNameEn ?: ""
                } else if (locale.language.startsWith("km")) {
                    offlinePaymentChannelsNameKm ?: ""
                } else {
                    offlinePaymentChannelsName ?: ""
                }
            }
        }"
    }

    //是否现金支付
    fun isCash(): Boolean {
        val channelId = try {
            offlinePaymentChannelsId?.doubleStrToIntStr()
        } catch (e: Exception) {
            offlinePaymentChannelsId
        }
        return channelId?.equals(OfflinePaymentChannelEnum.CASH.id.toString()) == true
    }

    //是否挂账
    private fun isAccountsReceivable(): Boolean {
        val channelId = try {
            offlinePaymentChannelsId?.doubleStrToIntStr()
        } catch (e: Exception) {
            offlinePaymentChannelsId
        }
        return channelId?.equals(OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id.toString()) == true
    }

    fun getPartialRefundAmount(): Long {
        return (refundPrice
            ?: 0) - getVatRefundAmount() - getPackRefundAmount() - getServiceFeeRefundAmount()
    }


    /**
     * 是否有待定价商品
     *
     * @return
     */
    fun isHasNeedProcess(): Boolean {
        var hasUnProcess = false
        //判断是否含有 待定价商品
        goods?.forEach {
            if (!it.isHasProcessed()) {
                hasUnProcess = true
            }
        }
        return hasUnProcess
    }

    /**
     * 是否有称重商品
     *
     * @return
     */
    fun isHasWeightGood(): Boolean {
        var has = false
        goods?.forEach {
            if (it.isToBeWeighed()) {
                has = true
            }
        }
        return has
    }

    /**
     * 是否有时价商品
     *
     * @return
     */
    fun isHasTimePriceGood(): Boolean {
        var has = false
        goods?.forEach {
            if (it.isTimePriceGood()) {
                has = true
            }
        }
        return has
    }


    fun getTotalGoodsNum(): Int {
        var num = 0;
        goods?.forEach {
            num += (it.num ?: 0)
        }
        return num
    }

    fun getFinalNote(): String {
        return (note?.replace(";<br/>", "\n") ?: "").trimStart()
    }

    fun getPayTimeFormat(): String? {
        return try {
            payTime?.replace("-", "/")
        } catch (e: Exception) {
            payTime
        }
    }

    /**
     * 获取打印模板
     *
     * @return
     */
    fun getPrintTypeFromDiningStyle(): PrintTemplateTypeEnum {
        if (isReservation == true) {
            return PrintTemplateTypeEnum.PRE_ORDER
        }

        //如果是已支付 就打结账小票
        if (payStatus == OrderedStatusEnum.PAID.id || payStatus == OrderedStatusEnum.CREDIT_PAID.id || payStatus == OrderedStatusEnum.CREDIT_UNPAID.id) {
            return PrintTemplateTypeEnum.CHECKOUT_RECEIPT
        }

        return when (diningStyle) {
            DiningStyleEnum.DINE_IN.id -> PrintTemplateTypeEnum.DINE_IN
            DiningStyleEnum.TAKE_AWAY.id -> PrintTemplateTypeEnum.TAKE_AWAY
            DiningStyleEnum.PRE_ORDER.id -> PrintTemplateTypeEnum.PRE_ORDER
            else -> {
                PrintTemplateTypeEnum.DINE_IN
            }
        }
    }

    fun clone(): OrderedInfoResponse {
        val stringJson = MyApplication.globalGson.toJson(this, OrderedInfoResponse::class.java)
        return MyApplication.globalGson.fromJson<OrderedInfoResponse>(
            stringJson,
            OrderedInfoResponse::class.java
        )
    }


    fun isHasDiscountPrice(): Boolean {
        return totalDiscountPrice != null && totalDiscountPrice != totalPrice
    }

    fun isShowVipPrice(): Boolean {
        if (payStatus == OrderedStatusEnum.CREDIT_UNPAID.id || payStatus == OrderedStatusEnum.CREDIT_PAID.id) {
            return false
        }
        var hasVip = false
        goods?.forEach {
            if (it.isShowVipPrice()) {
                hasVip = true
            }
        }
        return hasVip
    }


    //预结算单用到  翻译菜品
    fun translateGoods() {
        val goodsZhList =
            translateInfoVo?.ordersZh?.getOrderedGoodJson()?.goodsList ?: arrayListOf()
        val goodsEnList =
            translateInfoVo?.ordersEn?.getOrderedGoodJson()?.goodsList ?: arrayListOf()
        val goodsKmList =
            translateInfoVo?.ordersKm?.getOrderedGoodJson()?.goodsList ?: arrayListOf()

        offlinePaymentChannelsName =
            translateInfoVo?.ordersZh?.offlinePaymentChannelsName
        offlinePaymentChannelsNameEn =
            translateInfoVo?.ordersEn?.offlinePaymentChannelsName
        offlinePaymentChannelsNameKm =
            translateInfoVo?.ordersKm?.offlinePaymentChannelsName


        goods?.forEach {
            val zhIndex = goodsZhList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
            if (zhIndex != -1) {
                val goodsZh = goodsZhList[zhIndex]
                it.name = goodsZh.name
                it.tagItems = goodsZh.tagItems
                val feeds = goodsZh.feeds ?: arrayListOf()
                it.feeds?.forEach { feed ->
                    val feedZhIndex = feeds.indexOf(feed)
                    if (feedZhIndex != -1) {
                        feeds[feedZhIndex].let { zh ->
                            feed.name = zh.name
                        }
                    }
                    Timber.e("feedZhIndex $feedZhIndex  ${feed.name}")
                }
                val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
                it.orderMealSetGoodsDTOList?.forEach { good ->
                    val goodZhIndex =
                        orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                    if (goodZhIndex != -1) {
                        orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
                            good.mealSetGoodsName = zh.mealSetGoodsName
                            good.mealSetTagItemList = zh.mealSetTagItemList
                        }
                    }
                }
            }

            val enIndex = goodsEnList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
            if (enIndex != -1) {
                val goodsEn = goodsEnList[enIndex]
                it.nameEn = goodsEn.name
                it.tagItemsEn = goodsEn.tagItems
                val feeds = goodsEn.feeds ?: arrayListOf()
                it.feeds?.forEach { feed ->
                    val feedEnIndex = feeds.indexOf(feed)
                    if (feedEnIndex != -1) {
                        feeds[feedEnIndex].let { en ->
                            feed.nameEn = en.name
                        }
                    }
                    Timber.e("feedEnIndex $feedEnIndex  ${feed.nameEn}")
                }
                val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
                it.orderMealSetGoodsDTOList?.forEach { good ->
                    val goodEnIndex =
                        orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                    if (goodEnIndex != -1) {
                        orderMealSetGoodsDTOList[goodEnIndex].let { en ->
                            good.mealSetGoodsNameEn = en.mealSetGoodsName
                            good.mealSetTagItemListEn = en.mealSetTagItemList
                        }
                    }
                }
            }

            val kmIndex = goodsKmList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
            if (kmIndex != -1) {
                val goodsKm = goodsKmList[kmIndex]
                it.nameKm = goodsKm.name
                it.tagItemsKm = goodsKm.tagItems
                val feeds = goodsKm.feeds ?: arrayListOf()
                it.feeds?.forEach { feed ->
                    val feedKhIndex = feeds.indexOf(feed)
                    if (feedKhIndex != -1) {
                        feeds[feedKhIndex].let { kh ->
                            feed.nameKh = kh.name
                        }
                    }
                    Timber.e("feedKhIndex $feedKhIndex  ${feed.nameKh}")
                }
                val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
                it.orderMealSetGoodsDTOList?.forEach { good ->
                    val goodKmIndex =
                        orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                    if (goodKmIndex != -1) {
                        orderMealSetGoodsDTOList[goodKmIndex].let { en ->
                            good.mealSetGoodsNameKm = en.mealSetGoodsName
                            good.mealSetTagItemListKm = en.mealSetTagItemList
                        }
                    }
                }
            }
        }

        val giftGoodsListZh =
            translateInfoVo?.ordersZh?.getOrderedGoodJson()?.giftGoodsList ?: arrayListOf()
        val giftGoodsListEn =
            translateInfoVo?.ordersEn?.getOrderedGoodJson()?.giftGoodsList ?: arrayListOf()
        val giftGoodsListKm =
            translateInfoVo?.ordersKm?.getOrderedGoodJson()?.giftGoodsList ?: arrayListOf()

        getGiftGoodsList()?.forEach {
            val zhIndex = giftGoodsListZh.indexOfFirst { value -> it.id == value.id }
            if (zhIndex != -1) {
                val goodsZh = giftGoodsListZh[zhIndex]
                it.name = goodsZh.name
            }

            val enIndex = giftGoodsListEn.indexOfFirst { value -> it.id == value.id }
            if (enIndex != -1) {
                val goodsEn = giftGoodsListEn[enIndex]
                it.nameEn = goodsEn.name
            }
            val kmIndex = giftGoodsListKm.indexOfFirst { value -> it.id == value.id }
            if (kmIndex != -1) {
                val goodsKm = giftGoodsListKm[kmIndex]
                it.nameKm = goodsKm.name
            }
        }

        removeGoodList?.forEach {
            val zhIndex = goodsZhList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
            if (zhIndex != -1) {
                val goodsZh = goodsZhList[zhIndex]
                it.name = goodsZh.name
                it.tagItems = goodsZh.tagItems
                val feeds = goodsZh.feeds ?: arrayListOf()
                it.feeds?.forEach { feed ->
                    val feedZhIndex = feeds.indexOf(feed)
                    if (feedZhIndex != -1) {
                        feeds[feedZhIndex].let { zh ->
                            feed.name = zh.name
                        }
                    }
                    Timber.e("removeGoodList feedZhIndex $feedZhIndex  ${feed.name}")
                }
                val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
                it.orderMealSetGoodsDTOList?.forEach { good ->
                    val goodZhIndex =
                        orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                    if (goodZhIndex != -1) {
                        orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
                            good.mealSetGoodsName = zh.mealSetGoodsName
                            good.mealSetTagItemList = zh.mealSetTagItemList
                        }
                    }
                }
            }

            val enIndex = goodsEnList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
            if (enIndex != -1) {
                val goodsEn = goodsEnList[enIndex]
                it.nameEn = goodsEn.name
                it.tagItemsEn = goodsEn.tagItems
                val feeds = goodsEn.feeds ?: arrayListOf()
                it.feeds?.forEach { feed ->
                    val feedEnIndex = feeds.indexOf(feed)
                    if (feedEnIndex != -1) {
                        feeds[feedEnIndex].let { en ->
                            feed.nameEn = en.name
                        }
                    }
                    Timber.e("feedEnIndex ${feedEnIndex}  ${feed.nameEn}")
                }
                val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
                it.orderMealSetGoodsDTOList?.forEach { good ->
                    val goodEnIndex =
                        orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                    if (goodEnIndex != -1) {
                        orderMealSetGoodsDTOList[goodEnIndex].let { en ->
                            good.mealSetGoodsNameEn = en.mealSetGoodsName
                            good.mealSetTagItemListEn = en.mealSetTagItemList
                        }
                    }
                }
            }

            val kmIndex = goodsKmList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
            if (kmIndex != -1) {
                val goodsKm = goodsKmList[kmIndex]
                it.nameKm = goodsKm.name
                it.tagItemsKm = goodsKm.tagItems
                val feeds = goodsKm.feeds ?: arrayListOf()
                it.feeds?.forEach { feed ->
                    val feedKhIndex = feeds.indexOf(feed)
                    if (feedKhIndex != -1) {
                        feeds[feedKhIndex].let { kh ->
                            feed.nameKh = kh.name
                        }
                    }
                    Timber.e("feedKhIndex $feedKhIndex  ${feed.nameKh}")
                }
                val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
                it.orderMealSetGoodsDTOList?.forEach { good ->
                    val goodKmIndex =
                        orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                    if (goodKmIndex != -1) {
                        orderMealSetGoodsDTOList[goodKmIndex].let { en ->
                            good.mealSetGoodsNameKm = en.mealSetGoodsName
                            good.mealSetTagItemListKm = en.mealSetTagItemList
                        }
                    }
                }
            }
        }

//        couponActivityList =
//            translateInfoVo?.ordersZh?.getOrderedGoodJson()?.couponActivityList ?: arrayListOf()
//        couponActivityListEn =
//            translateInfoVo?.ordersEn?.getOrderedGoodJson()?.couponActivityList ?: arrayListOf()
//        couponActivityListKm =
//            translateInfoVo?.ordersKm?.getOrderedGoodJson()?.couponActivityList ?: arrayListOf()

    }

//    //格式化减免百分比
//    fun getReduceRateFomat(): String {
//        return if (reduceRate == null) "" else "${if ((reduceRate ?: 0.0).isInt()) (reduceRate ?: 0.0).toInt() else reduceRate}%"
//    }

    fun getServicePercentage(): Int {
        return serviceChargePercentage
            ?: MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() ?: 0
    }

    //获取优惠券展示内容
    fun getCouponDesc(
        context: Context,
        isCouponListEmpty: Boolean,
        isHasNeedWeight: Boolean,
        isNeedShowVip: Boolean? = false
    ): SpannableStringBuilder {
        var desc = SpannableStringBuilder()
        if (getCurrentCoupon() == null) {
            if (isCouponListEmpty) {
                //无可用优惠券
                val hint = context.getString(R.string.identify_coupons)
                desc.append(hint)
                desc.setSpan(
                    ForegroundColorSpan(context.getColor(R.color.black40)),
                    0,
                    hint.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                //有可用优惠券
                val hint = context.getString(R.string.hint_can_use_coupon)
                desc.append(hint)
                desc.setSpan(
                    ForegroundColorSpan(context.getColor(R.color.color_ff7f00)),
                    0,
                    hint.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        } else {
            if (getCurrentCoupon()?.isZsCoupon() == false) {
                if (isAfterPayStatus()) {
                    //已支付
                    val price = "-${couponAmount?.priceFormatTwoDigitZero2()}"
                    desc.append(price)
                } else {
                    Timber.e("coupon  ${getCurrentCoupon()?.category}")
                    if (isHasNeedWeight) {
                        desc.append(context.getString(R.string.to_be_confirmed))
                    } else {
                        //未支付
                        val price =
                            "    -${getCurrentCoupon()?.couponPrice?.priceFormatTwoDigitZero2()}"
                        if (isNeedShowVip == true) {
                            val vipPrice =
                                "-${getCurrentCoupon()?.vipCouponPrice?.priceFormatTwoDigitZero2()}"
                            if (isShowVipPrice()) {
                                val drawable = context.resources.getDrawable(R.drawable.icon_vip)
                                drawable.setBounds(
                                    0,
                                    0,
                                    drawable.intrinsicWidth,
                                    drawable.intrinsicHeight

                                )
                                val imageSpan = ImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
                                desc.append("  ")
                                desc.setSpan(imageSpan, 0, 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                                desc.append(vipPrice)
                                desc.setSpan(
                                    ForegroundColorSpan(context.getColor(R.color.member_price_color)),
                                    desc.length - vipPrice.length,
                                    desc.length,
                                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                            }
                        }
                        desc.append(price)
                    }
                    if (getCurrentCoupon()?.isValid == false && getCurrentCoupon()?.isVipValid == false) {
                        desc.clear()
                        desc.append(context.getString(R.string.this_coupon_not_support))
                    }
                }
            } else {
                if (isAfterPayStatus()) {
                    //已支付
                    val hint = context.getString(R.string.give_away_goods)
                    desc.append(hint)
                    desc.setSpan(
                        ForegroundColorSpan(context.getColor(R.color.black)),
                        0,
                        hint.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                } else {
                    //有赠品
                    if (getCurrentCoupon()?.isValid == true || getCurrentCoupon()?.isVipValid == true) {
                        val hint = context.getString(R.string.give_away_goods)
                        desc.append(hint)
                        desc.setSpan(
                            ForegroundColorSpan(context.getColor(R.color.black)),
                            0,
                            hint.length,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    } else {
                        desc.append(context.getString(R.string.this_coupon_not_support))
                    }
                }
            }
        }
        return desc
    }

    fun getGiftGoodsList(): List<UsageGoods> {
        //多取一层 保险
        var tmpGiftGoods = getCurrentCoupon()?.giftGoods
            ?: getCurrentCoupon()?.templateSDK?.rule?.discount?.giveGoods
        if (isAfterPayStatus()) {
            tmpGiftGoods = giftGoods
        }
        return tmpGiftGoods ?: listOf()
    }

    //赠送券是否有效
    fun getZsCouponValid(): Boolean {
        if (isAfterPayStatus()) {
            return getGiftGoodsList().isNotEmpty()
        }
        return getGiftGoodsList().isNotEmpty() && (getCurrentCoupon()?.isValid == true || getCurrentCoupon()?.isVipValid == true)
    }


    /**
     * 余额支付
     *
     * @return
     */
    fun isPayByBalance(): Boolean {
        return payType == PayTypeEnum.USER_BALANCE.id
    }

    /**
     * 组合支付
     *
     * @return
     */
    fun isPayByMix(): Boolean {
        return payType == PayTypeEnum.MIXED_PAYMENT.id
    }

    fun getCurrentCoupon(): OrderCouponModel? {
        return coupon
    }

//    fun isShowVipDiscountIcon(): Boolean {
//        return if (isAfterPayStatus()) {
//            //支付成功后如果是vip减免折扣
//            if (reduceType == 2) {
//                isShowVipPrice()
//            } else {
//                false
//            }
//        } else {
//            //支付成功前是会员减免折扣就显示icon
//            reduceType == 2
//        }
//    }

    /**
     * 是否设置了整单减免
     *
     */
    fun isSetWholeDiscount(): Boolean {
        if (discountReduceActivity != null) {
            //设置了手动选择的
            return true
        }
        val isSetNormal = isSetNormalWholeDiscount()
        if (wholeDiscountReduce?.reduceRate == null && wholeDiscountReduce?.reduceDollar == null && wholeDiscountReduce?.reduceVipDollar == null && wholeDiscountReduce?.reduceKhr == null && wholeDiscountReduce?.reduceVipKhr == null) {
            return false
        }

        return isSetNormal  //|| isSetVip
    }

    /**
     * 是否设置了现价整单减免
     *
     */
    fun isSetNormalWholeDiscount(): Boolean {
        return (wholeDiscountReduce?.reduceType
            ?: 0) > 0
    }

    /**
     * 获取整单减免类型
     *
     * @return
     */
    fun getWholeDiscountType(): Int? {
        return wholeDiscountReduce?.reduceType
    }

    /**
     * 是否有自定义固定金额减免
     *
     */
    fun isSetCustomizeReduce(): Boolean {
        return getNormalWholeReduceDollar() != BigDecimal.ZERO || getVipWholeReduceDollar() != BigDecimal.ZERO || getNormalWholeReduceKhr() != BigDecimal.ZERO || getVipWholeReduceKhr() != BigDecimal.ZERO
    }

    /**
     * 获取现价整单减免固定金额
     *
     * @return
     */
    fun getNormalWholeReduceDollar(): BigDecimal {
        if (isAfterPayStatus()) {
            if (isUseDiscount == true) {
                return getVipWholeReduceDollar()
            }
        }
        return wholeDiscountReduce?.reduceDollar ?: BigDecimal.ZERO
    }

    /**
     * 获取会员价整单减免固定金额
     *
     * @return
     */
    fun getVipWholeReduceDollar(): BigDecimal {
        return wholeDiscountReduce?.reduceVipDollar ?: BigDecimal.ZERO
    }


    fun getNormalWholeReduceKhr(): BigDecimal {
        if (isAfterPayStatus()) {
            if (isUseDiscount == true) {
                return getVipWholeReduceKhr()
            }
        }
        return wholeDiscountReduce?.reduceKhr ?: BigDecimal.ZERO
    }

    fun getVipWholeReduceKhr(): BigDecimal {
        return wholeDiscountReduce?.reduceVipKhr ?: BigDecimal.ZERO
    }

    /**
     * 获取平摊后的整单减免 输入usd
     *
     * @return
     */
    fun getNormalWholeItemReduceDollar(): BigDecimal {
        if (isAfterPayStatus()) {
            if (isUseDiscount == true) {
                return getNormalWholeItemReduceVipDollar()
            }
        }
        return wholeDiscountReduce?.itemReduceDollar ?: BigDecimal.ZERO
    }


    fun getNormalWholeItemReduceVipDollar(): BigDecimal {
        return wholeDiscountReduce?.itemReduceVipDollar ?: BigDecimal.ZERO
    }


    /**
     * 获取平摊后的整单减免 输入khr
     *
     * @return
     */
    fun getNormalWholeItemReduceKhr(): BigDecimal {
        if (isAfterPayStatus()) {
            if (isUseDiscount == true) {
                return getNormalWholeItemReduceVipKhr()
            }
        }
        return wholeDiscountReduce?.itemReduceKhr ?: BigDecimal.ZERO
    }

    fun getNormalWholeItemReduceVipKhr(): BigDecimal {
        return wholeDiscountReduce?.itemReduceVipKhr ?: BigDecimal.ZERO
    }


    /**
     * 获取整单减免的百分数
     */
    fun getWholePercentDiscount(): BigDecimal {
        return wholeDiscountReduce?.reduceRate ?: BigDecimal.ZERO
    }

    /**
     * 获取整单减免的百分数描述
     *
     * @return
     */
    fun getWholePercentDiscountStr(): String {
        val reduceRate = getWholePercentDiscount().toDouble()
        return if (wholeDiscountReduce?.reduceRate == null) "" else "${if ((reduceRate).isInt()) (reduceRate).toInt() else reduceRate}%"
    }

    /**
     * 获取现价减免折扣率 对应的减免金额
     *
     * @return
     */
    fun getWholePercentDiscountAmount(): BigDecimal {
        if (isAfterPayStatus()) {
            if (isUseDiscount == true) {
                return wholeDiscountReduce?.itemReduceVipAmount ?: BigDecimal.ZERO
            }
        }
        return wholeDiscountReduce?.itemReduceAmount
            ?: BigDecimal.ZERO
    }

    /**
     * 获取vip 减免折扣率 对应的减免金额
     *
     * @return
     */
    fun getVipWholePercentDiscountAmount(): BigDecimal {
        return wholeDiscountReduce?.itemReduceVipAmount
            ?: BigDecimal.ZERO
    }


    /**
     * 获取整单减免原因
     *
     * @return
     */
    fun getWholeDiscountReason(): String {
        return wholeDiscountReduce?.reduceReason ?: ""
    }


    /**
     * 待接订单是否可接单
     *
     */
    fun isCanReceiveOrder(): Boolean {
        if (expireCountDown == null || (expireCountDown
                ?: 0) <= 0L
        ) {
            return false
        }
        return true
    }

    fun getCouponActivityNameByLocal(context: Context, locale: Locale): String? {
        return context.getStringByLocale(R.string.discount_activity, locale)
    }


    fun isHasCouponActivityGood(): Boolean {
        var isHas = false
        goods?.forEach {
            if (!isHas) {
                isHas = !it.activityLabels.isNullOrEmpty()
            }
        }
        return isHas
    }

    /**
     * 获取格式化后的佣金
     *
     * @return
     */
    fun getCommissionPriceStr(): String? {
        return "-${price?.getCommissionToLong()?.priceFormatTwoDigitZero2()}"
    }

    /**
     * 获取用餐时间  不要展示秒
     *
     * @return
     */
    fun getDingTime(): String {
        var timeStr = customerInfoVo?.diningTime ?: peopleDate ?: ""
        if (timeStr.count { it.toString() == ":" } > 1) {
            timeStr = timeStr.formatDateWithoutSecond()
        }
        return timeStr
    }

    fun toOrderedRecord(): OrderedRecord {
        Timber.e("sourcePlatform $sourcePlatform")

        var isPrint = false
        //TODO 这段逻辑有问题 应该判断所有已连接的usb里面有没呀对应模版
        //后台没配置的话算已打印
        if (PrinterDeviceHelper.getUsbPrinterInfo() == null) {
            isPrint = true
        } else {
            val printerTemplateList = PrinterDeviceHelper.getUsbPrinterInfo()?.printerTemplateList
            if (printerTemplateList.isNullOrEmpty()) {
                isPrint = true
            } else {
                //判断一下有没有设置对应模板
                val currentTmp = getPrintTypeFromDiningStyle()
                val printTemplate =
                    printerTemplateList.firstOrNull { it.type == currentTmp.id }
                if (printTemplate == null) {
                    isPrint = true
                }
            }
        }

        return OrderedRecord(
            id = id,
            tableName = tableName,
            tableType = tableType,
            tableUuid = tableUuid,
            sourcePlatform = sourcePlatform,
            orderNo = orderNo,
            createTime = createTime,
            payStatus = payStatus,
            realPrice = realPrice,
            isPreOrder = isReservation,
            pickupCode = pickupCode,
            diningStyle = diningStyle,
            goodsTotalNum = getTotalGoodsNum(),
            isRead = sourcePlatform != SourcePlatformEnum.H5.id && sourcePlatform != SourcePlatformEnum.Kiosk.id,
            //是否打印
            isPrinted = isPrint,
            //是否有待称重
            isOrdersWeightMark = isHasNeedProcess(),
            expireTime = expireTime,
            expireCountDown = expireCountDown,
            acceptStatus = acceptStatus,
            deliveryPlatformCurrencyType = deliveryPlatformCurrencyType,
            conversionRatio = conversionRatio,
        )
    }


}