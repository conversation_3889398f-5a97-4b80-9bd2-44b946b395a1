package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.OrderedItemBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TimeUtils
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class OrderedAdapter(
    val act: Context,
    var list: ArrayList<OrderedRecord>,
    val onItemClickListener: (OrderedRecord) -> Unit
) : RecyclerView.Adapter<OrderedAdapter.OrderedViewHolder>() {
    companion object {
        const val COUNT_DOWN = "COUNT_DOWN"
    }

    private var selectedOrderId: String? = null

    inner class OrderedViewHolder(val binding: OrderedItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {
                bindingAdapterPosition.let {
                    SingleClickUtils.isFastDoubleClick(500) {
                        if (it != -1) {
                            if (selectedOrderId != null) {
                                val index = list.indexOfFirst { it.orderNo == selectedOrderId }
                                if (index != -1) {
                                    list[index].select = false
                                    notifyItemChanged(index)
                                }
                            }

                            selectedOrderId = list[it].orderNo
                            list[it].select = true
                            notifyItemChanged(it)

                            onItemClickListener.invoke(list[it])
                        }
                    }
                }

            }
        }

        fun bind(resource: OrderedRecord, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        Timber.e("刷新第${position}  -> ${resource.payStatus}")
                        tvPickUpNo.isVisible = !it.pickupCode.isNullOrEmpty()
                        tvPickUpNo.text =
                            "${getString(R.string.print_title_pick_up_no)} ${it.pickupCode}"

                        ivPrintPreSettlement.isVisible = it.hasPrintPreSettlement == true

                        ivNew.isVisible = !(it.isRead ?: false)

                        ivPrint.isVisible = !(it.isPrinted ?: false)

                        clContent.setBackgroundColor(
                            ContextCompat.getColor(
                                this@run,
                                if (resource.select) R.color.color_e7f5ee else android.R.color.transparent
                            )
                        )
                        tvTableID.text = it.tableName ?: ""

                        if (it.sourcePlatform == SourcePlatformEnum.Kiosk.id) {
                            tvTableID.text = getString(R.string.kiosk).uppercase()
                        }


                        val oderIdValue = "${getString(R.string.order_id)}: ${it.orderNo}"
                        tvOrderedID.text = oderIdValue

//                        val orderedGoodJson = it.getOrderedGoodJson()

                        val itemsValue =
                            "${getString(R.string.items)}: ${it?.goodsTotalNum}"
                        tvItems.text = itemsValue

                        val timeValue =
                            "${getString(R.string.ordering_time)}: ${it.createTime?.formatDate()}"
                        tvTime.text = timeValue

                        tvPrice.text = it.getShowPrice(itemView.context)

                        tvOrderType.text = it.getDiningStyleStr(itemView.context)


                        tvOrderType.setCompoundDrawablesWithIntrinsicBounds(
                            ContextCompat.getDrawable(
                                itemView.context,
                                it.getDiningStyleIcon()
                            ), null, null, null
                        )


                        tvStatus.text = it.payStatus?.getPayText(this@run)
                        tvStatus.setTextColor(
                            ContextCompat.getColor(
                                this@run,
                                it.payStatus?.getPayTypeColor() ?: R.color.ordered_cancel_color
                            )
                        )
                        statusCardView.setCardBackgroundColor(
                            ContextCompat.getColor(
                                this@run,
                                it.payStatus?.getPayTypeBackGroundColor()
                                    ?: R.color.ordered_cancel_color
                            )
                        )

                        updateTime(tvCountDownTime, list[position])
                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderedViewHolder {
        val itemView =
            OrderedItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderedViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: OrderedViewHolder, position: Int) {
        Timber.e("onBindViewHolder  ${position}")
        holder.bind(list[position], position)
    }

    override fun onBindViewHolder(
        holder: OrderedViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            Timber.e("这里？？？  ${position}")
            super.onBindViewHolder(holder, position, payloads)
        } else {
            holder.binding.apply {
                updateTime(tvCountDownTime, list[position])
            }
        }
    }

    /**
     * 更新倒计时
     *
     * @param textview
     * @param orderedRecord
     */
    fun updateTime(textview: TextView, orderedRecord: OrderedRecord) {
        textview.isVisible = orderedRecord.isCanReceiveOrder()
        textview.text = act.getString(
            R.string.new_add_info,
            TimeUtils.convertMillisecondsToTime(orderedRecord.expireCountDown ?: 0L)
        )
    }


    fun setSelectFirst(indexOf: Int) {
        if (this.list.isNotEmpty()) {
            this.list[indexOf].select = true
            selectedOrderId = this.list[indexOf].orderNo
        }
    }


    fun replaceData(list: ArrayList<OrderedRecord>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<OrderedRecord>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


    fun updateStatus(record: OrderedRecord) {
//        if (selectedOrderId != null && selectedOrderId == record.orderNo) {
        val index = list.indexOfFirst { it.orderNo == record.orderNo }
        Timber.e("updateStatus  $index")
        if (index != -1) {
            list[index] = record
            notifyItemChanged(index)
        }
//        }
    }

    fun updateRecord(record: OrderedRecord) {
        val indexOf = list.indexOf(record)
        Timber.e("updateRecord  $indexOf")
        if (indexOf != -1) {
            list[indexOf] = record
            notifyItemChanged(indexOf)
        }
    }


    fun hasModel(orderNo: String): OrderedRecord? {
        val index = list.indexOf(OrderedRecord(orderNo = orderNo))
        if (index != -1) {
            return list[index]
        }
        return null
    }

    //订单换桌 全部换桌修改订单桌子
    fun updateOrdersTable(oldTableUuid: String?, table: TableResponseItem?) {
        list.forEach {
            if ((it.payStatus == OrderedStatusEnum.UNPAID.id || it.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || it.payStatus == OrderedStatusEnum.BE_CONFIRM.id) && it.tableUuid == oldTableUuid) {
                it.tableName = table?.name
                it.tableUuid = table?.uuid
                it.tableType = table?.type
            }
        }
        notifyDataSetChanged()
    }


    fun insetOrderToTop(orderedRecord: OrderedRecord) {
        list.add(0, orderedRecord)
//        if (selectedPosition != -1) {
//            //说明有选择 ，那前面插入 原来选择的索引就要往后移动一下
//            selectedPosition += 1
//        }
        notifyItemInserted(0)
    }


    fun updatePrintStatus(orderNo: String) {
        val index = list.indexOf(OrderedRecord(orderNo = orderNo))
        if (index != -1) {
            if (list[index].isPrinted != true) {
                list[index].isPrinted = true
                notifyItemChanged(index)
            }
        }
    }

}