package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesOrdersDetailVo
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding
import com.metathought.food_order.casheir.databinding.ItemSaleReportListBinding

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description 销售报表预览adapter
 */
class SalesReportListAdapter(
    val list: ArrayList<SalesOrdersDetailVo>,
) : RecyclerView.Adapter<SalesReportListAdapter.ProduceReportItemViewHolder>() {


    inner class ProduceReportItemViewHolder(val binding: ItemSaleReportListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: SalesOrdersDetailVo, position: Int) {
            binding.apply {
                tvOrderNo.text = "${resource.orderNo ?: ""}"
                tvTable.text = "${resource.tableName ?: ""}"
                tvOrderAmount.text = "${resource.totalPrice ?: ""}"
                tvReduceAmount.text = "${resource.discountAmount ?: ""}"
                tvVat.text = "${resource.totalVat ?: ""}"
                tvReceiveAmount.text = "${resource.receiveAmount ?: ""}"
                rvList.adapter =
                    SalesReportListDetailItemAdapter(ArrayList(resource.productBasicVoList))

                vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemSaleReportListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: SalesReportListAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<SalesOrdersDetailVo>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}