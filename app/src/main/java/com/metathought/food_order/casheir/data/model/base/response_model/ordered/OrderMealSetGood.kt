package com.metathought.food_order.casheir.data.model.base.response_model.ordered

import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.PricingMethodEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.extension.isInt
import java.math.BigDecimal
import java.util.Locale


/**
 *<AUTHOR>
 *@time  2025/1/9
 *@desc 订单内套餐商品
 **/

data class OrderMealSetGood(
    @SerializedName("kitchenMaking")
    var kitchenMaking: Boolean? = null,
    @SerializedName("materialId")
    var materialId: String? = null,
    @SerializedName("mealSetGoodsId")
    var mealSetGoodsId: String? = null,
    @SerializedName("mealSetGoodsName")
    var mealSetGoodsName: String? = null,
    @SerializedName("mealSetGoodsNumber")
    var mealSetGoodsNumber: String? = null,
    @SerializedName("mealSetGoodsSellPrice")
    var mealSetGoodsSellPrice: Long? = null,
    @SerializedName("mealSetGroupId")
    var mealSetGroupId: String? = null,
    @SerializedName("mealSetTagItemList")
    var mealSetTagItemList: List<GoodsTagItem>? = null,
    /**
     * 后台设置的数量
     */
    @SerializedName("num")
    var num: Int? = null,
    /**
     * 用户选的数量
     */
    @SerializedName("number")
    var number: Int? = null,
    @SerializedName("priceMarkup")
    var priceMarkup: Long? = null,
    @SerializedName("printLabel")
    var printLabel: Boolean? = null,
    @SerializedName("seriesNo")
    var seriesNo: String? = null,
    @SerializedName("soldOut")
    var soldOut: Boolean? = null,
    @SerializedName("storeKitchenId")
    var storeKitchenId: String? = null,
    @SerializedName("warehouseId")
    var warehouseId: String? = null,

    @SerializedName("mealSetGoodsNameEn")
    var mealSetGoodsNameEn: String? = null,
    @SerializedName("mealSetGoodsNameKm")
    var mealSetGoodsNameKm: String? = null,

    @SerializedName("mealSetTagItemListEn")
    var mealSetTagItemListEn: List<GoodsTagItem>? = null,

    @SerializedName("mealSetTagItemListKm")
    var mealSetTagItemListKm: List<GoodsTagItem>? = null,

    //TODO 2.16.10 新增字段
    /**
     * 计价方式
     * WHOLE_UNIT(0, "整份计费", ""),
     * PER_KILOGRAM(1, "每公斤", "KG"),
     * PER_POUND(2, "每磅", "LB"),
     * PER_LITER(3, "每升", "L"),
     * PER_OUNCE(4, "每盎司", "OZ"),
     * PER_GALLON(5, "每加仑", "GAL"),
     * PER_GRAM(6, "每克", "G");
     * PER_GRAM(7, "时价菜", ""); 7 的时候是时价菜 ，价格另外设置
     */
    @SerializedName("pricingMethod")
    var pricingMethod: Int? = null,
    //商品重量(称重商品使用)
    @SerializedName("weight")
    var weight: Double? = null,
    //商品唯一标识(称重商品使用)
    @SerializedName("uuid")
    var uuid: String? = null,
    /**
     * 是否称重完成true是false否
     */
    var weighingCompleted: Boolean? = false,
    @SerializedName("isProcessed")
    var isProcessed: Boolean?= null,//商品是否处理完成(时价菜和称重商品使用)
    @SerializedName("hashKey")
    var hashKey: String? = null,//套餐内商品的hashKey(用于唯一标识套餐内具体商品)
    ) {

    /**
     * 是否已经设置重量
     *
     * @return
     */
    fun isHasCompleteWeight(): Boolean {
        if (weighingCompleted == null) {
            return ((weight ?: 0.0) > 0.0)
        }
        return weighingCompleted ?: false
    }

    fun getWeightStr(): String {
        if (weight?.isInt() == true) {
            return "${(weight ?: 0).toInt()}${getWeightUnit()}"
        }
        return "${weight ?: 0}${getWeightUnit()}"
    }

     fun getWeightUnit(): String {
        return PricingMethodEnum.entries.find {
            it.id == (pricingMethod ?: 0)
        }?.unit ?: ""
    }



    /**
     * 是否称重菜
     *
     * @return
     */
    fun isToBeWeighed(): Boolean {
        return (pricingMethod ?: 0) > PricingMethodEnum.WHOLE_UNIT.id
    }

    fun getNameByLocale(locale: Locale): String? {
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (mealSetGoodsNameKm.isNullOrEmpty()) {
                mealSetGoodsName
            } else {
                mealSetGoodsNameKm
            }
        } else if (locale == Locale.CHINESE) {
            mealSetGoodsName
        } else if (locale == Locale.ENGLISH) {
            if (mealSetGoodsNameEn.isNullOrEmpty()) {
                mealSetGoodsName
            } else {
                mealSetGoodsNameEn
            }
        } else {
            mealSetGoodsName
        }
    }


    /**
     * 计算单个商品加价
     *
     * @return
     */
    fun calculateSingleTotalMarkUpPrice(): Long {
        var price = (priceMarkup ?: 0).times(weight?:1.0).toLong()
        mealSetTagItemList?.forEach {
            price += ((it.price ?: 0.0).toLong()).times(num ?: 1)
        }
        return price
    }

    fun getTagStr(locale: Locale): String {
        var str = ""
        mealSetTagItemList?.forEach {
            if (str.isEmpty()) {
                str = it.name ?: ""
            } else {
                str = "${str},${it.name ?: ""}"
            }
        }
        return str
    }


    fun getTagIds(): String {
        var str = ""
        mealSetTagItemList?.sortedBy { it.id }
        mealSetTagItemList?.forEach {
            str = "${str}${it.id}"
        }
        return str
    }

}