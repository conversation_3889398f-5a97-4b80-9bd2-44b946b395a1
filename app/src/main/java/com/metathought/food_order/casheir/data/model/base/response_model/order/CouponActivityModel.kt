package com.metathought.food_order.casheir.data.model.base.response_model.order

import com.google.gson.annotations.SerializedName


/**
 *<AUTHOR>
 *@time  2024/11/25
 *@desc
 **/

data class CouponActivityModel(
    /**
     * 优惠活动  本地购物车用
     */
    var activityLabel: ActivityLabel? = null,

    @SerializedName("activityTemplateId")
    var activityTemplateId: String? = null,
    @SerializedName("activityLabelName")
    var activityLabelName: String? = null,

    /**
     * 优惠活动 现价优惠掉的金额
     */
    @SerializedName("activityCouponAmount")
    var activityCouponAmount: Long? = null,
    /**
     * 优惠活动 会员价优惠掉的金额
     */
    @SerializedName("activityVipCouponAmount")
    var activityVipCouponAmount: Long? = null,
    /**
     * 优惠活动 是否含有待定价商品  true 就是含有未定价商品
     */
    @SerializedName("weightMark")
    var weightMark: Boolean? = null,

    /**
     * 优惠活动 是否有使用会员价
     */
    @SerializedName("vipMark")
    var vipMark: Boolean? = null,
)