package com.metathought.food_order.casheir.ui.dialog


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.databinding.DialogResetCashBoxBinding
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.showKeyboard
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import kotlinx.coroutines.launch

/**
 * 重设钱箱密码
 * **/

class ResetCashBoxDialog : BaseDialogFragment() {
    private var binding: DialogResetCashBoxBinding? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogResetCashBoxBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initData()
        initListener()
    }


    private fun initData() {
        binding?.apply {
            checkInput()
            requireActivity().showKeyboard(edAccount)
        }
    }

    private fun initListener() {
        binding?.apply {
            edAccount.doOnTextChanged { text, start, before, count ->
                checkInput()
            }
            edtPassword.setOnTextChangeListener { text, isComplete ->
                checkInput()
            }
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            btnYes.setOnClickListener {
                lifecycleScope.launch {
                    val account = MainDashboardFragment.CURRENT_USER?.userAccount
                    if (edAccount.text.toString() == account) {
                        PreferenceHelper.setCashBoxPwd(
                            requireContext(),
                            edtPassword.text.toString()
                        )
                        dismissCurrentDialog()
                    } else {
                        llError.isVisible = true
                    }
                }


            }
//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    private fun checkInput() {
        binding?.apply {
            btnYes.setEnableWithAlpha(edtPassword.text.length == 6 && !edAccount.text.isNullOrEmpty())
            llError.isVisible = false
        }
    }

    companion object {
        private const val TAG = "ResetCashBoxDialog"

        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment = fragmentManager.findFragmentByTag(TAG) as? ResetCashBoxDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): ResetCashBoxDialog {
            val args = Bundle()
            val fragment = ResetCashBoxDialog()
            fragment.arguments = args
            return fragment
        }
    }

}
