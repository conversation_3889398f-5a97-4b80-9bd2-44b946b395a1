package com.metathought.food_order.casheir.ui.dialog.store_manager


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.databinding.DialogStoreDetailBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.StoreData
import com.metathought.food_order.casheir.ui.adapter.StoreManageDataAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


@AndroidEntryPoint
class StoreDetailDialog() : BaseDialogFragment() {

    companion object {
        private const val TAG = "StoreDetailDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? StoreDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(): StoreDetailDialog {
            val args = Bundle()

            val fragment = StoreDetailDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private var binding: DialogStoreDetailBinding? = null

    private val storeManagerViewModel: StoreManagerViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogStoreDetailBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initEventBus()
        initView()
        initListener()
        initObserver()
    }

    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    private fun initView() {
        storeManagerViewModel?.getStoreInfo()

    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnMultLanguage.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (storeManagerViewModel.uiStoreInfoState.value is ApiResponse.Success) {
                        EditMutlLanguageDialog.showDialog(
                            parentFragmentManager,
                            (storeManagerViewModel.uiStoreInfoState.value as ApiResponse.Success<StoreInfoResponse>).data
                        )

                    }

                }
            }

            btnEdit.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (storeManagerViewModel.uiStoreInfoState.value is ApiResponse.Success) {
                        EditStoreDetailDialog.showDialog(
                            parentFragmentManager,
                            (storeManagerViewModel.uiStoreInfoState.value as ApiResponse.Success<StoreInfoResponse>).data
                        )

                    }

                }
            }

        }
    }


    private fun initObserver() {
        storeManagerViewModel.uiStoreInfoState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                updateView(state.data)
            }

        }

    }

    //更新头像
    private fun updateView(storeInfo: StoreInfoResponse) {
        binding?.apply {
            tvStoreName.setContent(storeInfo.name)
            tvStorePhone.setContent(storeInfo.telephons)

            tvStoreLocation.setContent(storeInfo.address)
            tvStoreIntro.setContent(storeInfo.description)

            tvDistanceLimit.setContent(
                if (storeInfo.limitOrderDistance == true) {
                    getString(
                        R.string.show_location_distance,
                        storeInfo.effectiveDistance ?: ""
                    )
                } else getString(R.string.no_desc)
            )

            Glide.with(requireActivity()).load(storeInfo.url)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .placeholder(R.color.color_e5e5e5)
                .transition(DrawableTransitionOptions.withCrossFade())
                .error(R.color.color_e5e5e5)
                .into(ivLogo)

            val list = arrayListOf<StoreData>()
            list.add(
                StoreData(
                    title = getString(R.string.manager_store_status),
                    content = storeInfo.getStoreStatus(requireContext()),
                    startDraw = storeInfo.getStoreStatusRes()
                )
            )
            list.add(
                StoreData(
                    title = getString(R.string.manager_store_payment),
                    content = storeInfo.getStorePaymentMethod(requireContext())
                )
            )

            list.add(
                StoreData(
                    title = getString(R.string.manager_store_table_service),
                    content = storeInfo.getStoreTableService(requireContext())
                )
            )

            if (storeInfo?.isTableService == false) {
                list.add(
                    StoreData(
                        title = getString(R.string.manager_store_show_table),
                        content = storeInfo.getStoreShowTable(requireContext())
                    )
                )
            }

            if (storeInfo?.isPaymentInAdvance == false) {
                list.add(
                    StoreData(
                        title = getString(R.string.manager_store_auto_accept),
                        content = storeInfo.getStoreAutoAccept(requireContext())
                    )
                )
            }


            list.add(
                StoreData(
                    title = getString(R.string.manager_store_casher_menu_show_image),
                    content = storeInfo.getStoreMenuShowPic(requireContext())
                )
            )

            rvDataList.adapter = StoreManageDataAdapter(list)

//            tvStoreStatus.setContent(storeInfo.getStoreStatus(requireContext()))
//            tvStoreStatus.setContentStartDrawable(storeInfo.getStoreStatusRes())
//            tvStorePayment.setContent(storeInfo.getStorePaymentMethod(requireContext()))
//
//            tvStoreTableService.setContent(storeInfo.getStoreTableService(requireContext()))
//
//            tvStoreShowTable.setContent(storeInfo.getStoreShowTable(requireContext()))
//
//            tvStoreAutoAccept.setContent(storeInfo.getStoreAutoAccept(requireContext()))
//
//            tvStoreMenuShowPrice.setContent(storeInfo.getStoreMenuShowPic(requireContext()))


            tvStoreMealCode.setContent(storeInfo.getStorePickUpNoDesc(requireContext()))

            tvStoreInvoiceNumber.setContent(storeInfo.getStoreInvoiceNumberDesc(requireContext()))


            tvTaxTitle.setContent(requireContext().getString(R.string.no_desc))
            if (storeInfo.ticketShowTaxInfo == true) {
                tvTaxTitle.setContentVisible(false)
                tvCompanyName.isVisible = true
                tvCompanyName.text =
                    "${getString(R.string.company_name)}: ${storeInfo?.companyName ?: ""}"

                tvCompanyTax.isVisible = true
                tvCompanyTax.text =
                    "${getString(R.string.company_vat_tin)}: ${storeInfo?.companyTaxNumber ?: ""}"

                tvCompanyAddress.isVisible = true
                tvCompanyAddress.text =
                    "${getString(R.string.company_address)}: ${storeInfo?.companyAddress ?: ""}"

                tvCompanyPhone.isVisible = true
                tvCompanyPhone.text =
                    "${getString(R.string.company_phone)}: ${storeInfo?.companyContactNumber ?: ""}"

                tvCompanyEmail.isVisible = true
                tvCompanyEmail.text =
                    "${getString(R.string.company_email)}: ${storeInfo?.companyContactEmail ?: ""}"

            } else {
                tvTaxTitle.setContentVisible(true)
                tvCompanyName.isVisible = false
                tvCompanyTax.isVisible = false
                tvCompanyAddress.isVisible = false
                tvCompanyPhone.isVisible = false
                tvCompanyEmail.isVisible = false
            }

        }
    }

    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
//        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {
            SimpleEventType.UPDATE_STORE -> {
                storeManagerViewModel.getStoreInfo()
            }

            else -> {

            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * 0.45).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

}

