package com.metathought.food_order.casheir.ui.member.dialog

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.text.TextWatcher
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogCreateMemberInfoBinding
import com.metathought.food_order.casheir.extension.formatPhoneNumber
import com.metathought.food_order.casheir.extension.getRedStar
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class CreateMemberInfoDialog : BaseDialogFragment(), CountryCodePicker.DialogEventsListener {
    private var binding: DialogCreateMemberInfoBinding? = null
    private var createSuccess: (() -> Unit)? = null

    private val createMemberInfoViewModel: CreateMemberInfoViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCreateMemberInfoBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)

        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {

        createMemberInfoViewModel.uiState.observe(viewLifecycleOwner) {
            binding?.apply {
                if (it.success?.isSuccess() == true) {
                    createSuccess?.invoke()
                    dismissCurrentDialog()
                }
                if (!it.error.isNullOrEmpty()) {
                    Toast.makeText(context, "${it.error}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }


    private fun initData() {
        binding?.apply {
            textInputLayoutCustomerName.hint =
                getString(R.string.customer_nickname_required).getRedStar(resources)
            textInputLayoutPhoneNumber.hint =
                getString(R.string.mobile_phone_number).getRedStar(resources)

            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager
            checkEnable()
        }


    }

    private val ontextChange = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            checkEnable()
        }

    }
    private val onTextChangePhoneNumber = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            s?.let { editText(it) }
        }

    }

    private fun initListener() {
        binding?.apply {
            countryCodeHolder.setDialogEventsListener(this@CreateMemberInfoDialog)
//            countryCodeHolder.setTextSize(DisplayUtils.dp2px(requireContext(),10f))

            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
            edtCustomerName.addTextChangedListener(ontextChange)

            edtPhoneNumber.addTextChangedListener(ontextChange)
            edtPhoneNumber.setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && edtPhoneNumber.text.toString().replace(" ", "").length < 8) {
                    textInputLayoutPhoneNumber.error =
                        getString(R.string.invalid_format_enter_8_to_12_digits)
                    textInputLayoutPhoneNumber.isErrorEnabled = true
                } else if (!hasFocus && edtPhoneNumber.text.toString()
                        .replace(" ", "").length >= 8
                ) {
                    textInputLayoutPhoneNumber.error = ""
                    textInputLayoutPhoneNumber.isErrorEnabled = false
                }
            }
            btnClose.setOnClickListener() {
                dismissCurrentDialog()
            }
            btnReserve.setOnClickListener {

                createMemberInfoViewModel.addConsumer(
                    edtCustomerName.text.toString(),
                    "${countryCodeHolder.selectedCountryCode}${
                        edtPhoneNumber.text.toString().replace(" ", "")
                    }"
                )


            }

//            dialog?.window?.decorView?.setOnTouchListener { _, ev -> ev?.hideKeyboard(dialog?.currentFocus) == true }
        }
    }

    private fun checkEnable() {
        binding?.apply {

            var isPhone = true
            if (edtPhoneNumber.text?.isNotEmpty() == true) {
                isPhone = edtPhoneNumber.text.toString().replace(" ", "").length in 8..12
            }
            btnReserve.setEnable(
                edtCustomerName.text?.trim()
                    ?.isNotEmpty() == true && edtPhoneNumber.text?.isNotEmpty() == true && isPhone
            )


        }
    }

    companion object {
        private const val TAG = "CreateMemberInfoDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            createSuccess: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(createSuccess)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? CreateMemberInfoDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            createSuccess: (() -> Unit)
        ): CreateMemberInfoDialog {
            val fragment = CreateMemberInfoDialog()
            fragment.createSuccess = createSuccess

            return fragment
        }
    }


    override fun onCcpDialogOpen(dialog: Dialog?) {

    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.let {
            it.edtPhoneNumber.isFocusable = true
        }
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {
    }


    private fun editText(editable: Editable) {
        binding?.apply {
            edtPhoneNumber.removeTextChangedListener(onTextChangePhoneNumber)
            val formattedPhoneNumber = editable.toString().formatPhoneNumber()
            edtPhoneNumber.setText(formattedPhoneNumber)

            edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
        }
    }
}
