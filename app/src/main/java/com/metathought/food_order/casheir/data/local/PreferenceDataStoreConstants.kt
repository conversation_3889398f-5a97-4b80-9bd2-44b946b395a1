package com.metathought.food_order.casheir.data.local

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey

object PreferenceDataStoreConstants {
    val DATA_STORE_KEY_USER_SESSION = stringPreferencesKey("USER_INFO")
    val DATA_STORE_KEY_CURRENT_DINING_STYLE = intPreferencesKey("CURRENT_DINING_STYLE")
    val DATA_STORE_KEY_LAST_UPDATE_DATE = stringPreferencesKey("LAST_UPDATE_DATE")
    val DATA_STORE_KEY_IGNORE_VERSION = stringPreferencesKey("IGNORE_VERSION")
    val DATA_STORE_KEY_PAYMENT_CHANNEL = stringPreferencesKey("PAYMENT_CHANNEL")
    val DATA_STORE_KEY_LOGIN_ACCOUNT = stringPreferencesKey("LOGIN_ACCOUNT")
    val DATA_STORE_KEY_LOGIN_PASSWORD = stringPreferencesKey("LOGIN_PASSWORD")
    val DATA_STORE_KEY_LOGIN_REMEMBER = booleanPreferencesKey("LOGIN_REMEMBER")


    //钱箱密码相关
    val DATA_STORE_KEY_CASH_BOX_PWD = stringPreferencesKey("CASH_BOX_PWD")

    // 关闭的公告id
    val DATA_STORE_KEY_NOTICE_ID = stringPreferencesKey("NOTICE_ID")

    // 切换语言时当前的tab id
    val DATA_STORE_KEY_CURRENT_TAB_INDEX = stringPreferencesKey("CURRENT_TAB_INDEX")

    //通用桌台信息
    val DATA_STORE_KEY_UNIVERSAL_TABLE = stringPreferencesKey("UNIVERSAL_TABLE")

    //店铺信息
    val DATA_STORE_KEY_STORE_INFO = stringPreferencesKey("STORE_INFO")

    //自定义KHR输入
    val DATA_STORE_KEY_CUSTOM_KHR_INPUT = stringPreferencesKey("DATA_STORE_KEY_CUSTOM_KHR_INPUT")

    //快捷备注列表
    val DATA_STORE_KEY_QUICK_REMARK_LIST = stringPreferencesKey("DATA_STORE_KEY_QUICK_REMARK_LIST")

    //购物车请求版本号  有下单一次就清掉
    val DATA_STORE_KEY_CART_DATA_VERSION = stringPreferencesKey("DATA_STORE_KEY_CART_DATA_VERSION")

    //开启手动输入重量
    val OPEN_MANUAL_INPUT_WEIGHT = stringPreferencesKey("OPEN_MANUAL_INPUT_WEIGHT")
}