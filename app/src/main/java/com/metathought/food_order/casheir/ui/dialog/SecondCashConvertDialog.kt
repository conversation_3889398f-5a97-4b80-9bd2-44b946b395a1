package com.metathought.food_order.casheir.ui.dialog

import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.lxj.xpopup.util.KeyboardUtils
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.databinding.DialogCashConvertBinding
import com.metathought.food_order.casheir.databinding.DialogSecondCashConvertBinding
import com.metathought.food_order.casheir.databinding.DialogSecondOrderFoodDetailBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getColor
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.roundToTwoDecimalPlaces
import com.metathought.food_order.casheir.extension.setNumberRange
import com.tencent.bugly.crashreport.CrashReport
import java.math.BigDecimal
import kotlin.math.absoluteValue
import kotlin.math.round

/**
 * 副屏找零钱弹窗
 * Change amount Dialog
 * <AUTHOR>
 * @date 2024/5/912:31
 * @description
 */
class SecondCashConvertDialog : Dialog {

    constructor(context: Context) : this(context, 0)
    constructor(context: Context, themeResId: Int) : super(context, themeResId)

    private lateinit var mContext: Context
    private lateinit var binding: DialogSecondCashConvertBinding
    override fun onCreate(savedInstanceState: Bundle?) {

        /**
         * 防止子弹窗不会显示，还不知道有啥问题
         */
        try {
            if (window != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY - 1)
                } else {
                    window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                }
            }
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }
        super.onCreate(savedInstanceState)
        binding = DialogSecondCashConvertBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    fun initResource(mContext: Context) {
        this.mContext = mContext
        binding.run {
            tvTitle.text = mContext.getString(R.string.pay_by_cash)
            orderAmount.text = mContext.getString(R.string.order_amount)
            conversionRatio.text = mContext.getString(R.string.conversion_ratio)
            amountReceivable.text = mContext.getString(R.string.amount_receivable)
            cashCollection.text = mContext.getString(R.string.cash_collection)
            change.text = mContext.getString(R.string.back_your_change_amount)
            tvChangeAmountTip.text = mContext.getString(R.string.insufficient_cash_received)
        }
    }

    fun updatePrice(
        orderPrice: String,
        conversionRatio: String,
        amountReceivable: String,
        usdAmount: String,
        khrAmount: String,
        changeAmount: String,
    ) {
        binding?.apply {
            tvOrderPrice.text = orderPrice

            tvConversionRatio.text = conversionRatio

            tvAmountReceivable.text = amountReceivable

            edtUsdAmount.text = usdAmount

            edtKhrAmount.text = khrAmount

            tvChangeAmount.text = changeAmount

        }
    }

    fun isErrorStatus(isError: Boolean) {
        binding?.apply {
            tvChangeAmountTip.isVisible = isError
            if (isError) {
                context?.let {
                    tvChangeAmount.setTextColor(R.color.main_red.getColor(it))
                }
            } else {
                context?.let {
                    tvChangeAmount.setTextColor(R.color.black.getColor(it))
                }
            }
        }
    }
}