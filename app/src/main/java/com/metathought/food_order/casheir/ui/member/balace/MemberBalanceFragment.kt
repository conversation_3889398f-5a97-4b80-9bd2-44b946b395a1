package com.metathought.food_order.casheir.ui.member.balace

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.BalanceTypeEnum
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.RecordBalance
import com.metathought.food_order.casheir.databinding.FragmentMemberBalanceBinding
import com.metathought.food_order.casheir.databinding.PopupMemberBalanceTypeBinding
import com.metathought.food_order.casheir.extension.getBalanceTypeEnum
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.BalanceNameAdapter
import com.metathought.food_order.casheir.ui.adapter.BalanceOperationAdapter
import com.metathought.food_order.casheir.ui.adapter.MemberBalanceListAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.widget.RecyclerViewSyncManager
import com.metathought.food_order.casheir.ui.widget.SyncScrollRecyclerView
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import com.metathought.food_order.casheir.ui.member.dialog.RechargeDetailDialog
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@AndroidEntryPoint
class MemberBalanceFragment : BaseFragment() {

    companion object {
        const val MEMBER_BALANCE_FRAGMENT_BUNDLE_DATA_PHONE =
            "MEMBER_BALANCE_FRAGMENT_BUNDLE_DATA_PHONE"

        fun newInstance() = MemberBalanceFragment()
    }

    private var _binding: FragmentMemberBalanceBinding? = null
    private val binding get() = _binding
    private val memberMainViewModel: MemberMainViewModel by viewModels()
    private lateinit var memberListOrderAdapter: MemberBalanceListAdapter
    private lateinit var balanceNameAdapter: BalanceNameAdapter
    private lateinit var balanceOperationAdapter: BalanceOperationAdapter
    private val syncManager = RecyclerViewSyncManager()
    var startDateString: String = ""
    var endDateString: String = ""

    private var isExactMatch: Boolean = false
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMemberBalanceBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserver()
        initListener()

    }

    private fun initObserver() {
        memberMainViewModel.cancelTopUpState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    showProgress()
                }

                is ApiResponse.Error -> {
                    dismissProgress()
                    it.message?.let { it1 -> showToast(it1) }
                }

                is ApiResponse.Success -> {
                    dismissProgress()
                    getMemberBalance()
                }

                else -> {}
            }
        }
        memberMainViewModel.uiBalanceListState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        showProgress()
                }
                if (it.showEnd) {
                    dismissProgress()
                    if (it.isRefresh != false) {
                        layoutEmpty.root.isVisible = true
                        memberListOrderAdapter.replaceData(arrayListOf())
                        balanceNameAdapter.replaceData(arrayListOf())
                        balanceOperationAdapter.replaceData(arrayListOf())
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    dismissProgress()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    dismissProgress()
                    layoutEmpty.root.isVisible = false
                    if (it.isRefresh != false) {
                        ArrayList(response.records).let { it1 ->
                            memberListOrderAdapter.replaceData(it1)
                            balanceNameAdapter.replaceData(it1)
                            balanceOperationAdapter.replaceData(it1)
                        }
                        refreshLayout.finishRefresh()

                    } else {
                        ArrayList(response.records).let { it1 ->
                            memberListOrderAdapter.addData(it1)
                            balanceNameAdapter.addData(it1)
                            balanceOperationAdapter.addData(it1)
                        }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }

    }

    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getMemberBalance(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getMemberBalance(false)
                }

            })

            tvCalendar.setOnClickListener {
                datePickerDialog()
            }

            edtSearch.setTextChangedListenerCallBack {
                isExactMatch = false
                getMemberBalance()
            }

//            edtSearch.addTextChangedListener {
//                isExactMatch = false
//                getMemberBalance()
//            }
//
//            edtSearch.setOnEditorActionListener { textView, i, keyEvent ->
//                if (i == EditorInfo.IME_ACTION_SEARCH) {
//                    context.hideKeyboard(edtSearch)
//                    true
//                }
//                false
//            }

            dropdownFilter.setOnClickListener {
                arrow.animate().rotation(180f).setDuration(200)
                dropdownFilter.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupWindowLanguage(dropdownFilter)
            }
            tvClearFilter.setOnClickListener {
                tvCalendar.text = ""
                tvCalendar.updateCalendarColor()
                tvType.text = getString(R.string.all)
                startDateString = ""
                endDateString = ""
                edtSearch.setSearchContent("")
                edtSearch.removeFocus()
//                activity?.hideKeyboard(edtSearch)
//                getMemberBalance()

            }
        }
    }


    private fun initView() {
        binding?.apply {

            arguments?.let {
                if (it.containsKey(MEMBER_BALANCE_FRAGMENT_BUNDLE_DATA_PHONE)) {
                    val phone = it.getString(MEMBER_BALANCE_FRAGMENT_BUNDLE_DATA_PHONE)
                    edtSearch.setSearchContent(phone ?: "")
                    isExactMatch = true
                }
            }

            context?.let {
                showProgress()
                getMemberBalance()
            }
            memberListOrderAdapter = MemberBalanceListAdapter(arrayListOf(), {
                //详情
                RechargeDetailDialog.showDialog(parentFragmentManager, it)
            }, {
                //取消
                ConfirmDialog.showDialog(
                    parentFragmentManager,
                    content = getString(R.string.sure_to_cancel_this_top_up),
                    positiveButtonTitle = getString(R.string.yes_cancel),
                    negativeButtonTitle = getString(R.string.go_back)
                ) {
                    it.id?.let { it1 -> memberMainViewModel.cancelTopUp(it1) }
                }
            })

            balanceNameAdapter = BalanceNameAdapter(arrayListOf())
            balanceOperationAdapter = BalanceOperationAdapter(arrayListOf())

            // 设置操作按钮适配器
            balanceOperationAdapter.setOnOperationListener(object :
                BalanceOperationAdapter.OnOperationListener {
                override fun onDetailClick(record: RecordBalance) {
                    RechargeDetailDialog.showDialog(parentFragmentManager, record)
                }

                override fun onCancelClick(record: RecordBalance) {
                    ConfirmDialog.showDialog(
                        parentFragmentManager,
                        content = getString(R.string.sure_to_cancel_this_top_up),
                        positiveButtonTitle = getString(R.string.yes_cancel),
                        negativeButtonTitle = getString(R.string.go_back)
                    ) {
                        record.id?.let { it1 -> memberMainViewModel.cancelTopUp(it1) }
                    }
                }
            })

            recyclerviewMemberBalance.adapter = memberListOrderAdapter
            recyclerviewBalanceNames.adapter = balanceNameAdapter
            recyclerviewBalanceOperations.adapter = balanceOperationAdapter

            // 设置同步滚动 - 三个RecyclerView都需要同步
            syncManager.addRecyclerView(recyclerviewMemberBalance)
            syncManager.addRecyclerView(recyclerviewBalanceNames)
            syncManager.addRecyclerView(recyclerviewBalanceOperations)

            // 设置表头与列表项的水平同步滑动
            (recyclerviewMemberBalance as? SyncScrollRecyclerView)?.setHeaderScrollView(
                balanceHeaderScrollView
            )
        }
    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate))
            endDateString = sdf.format(Date(endDate))
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"
            getMemberBalance()
            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"

            // Displaying the selected date range in the TextView
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun showPopupWindowLanguage(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupMemberBalanceTypeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }
        popupView.tvAll.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.all)) {
                binding?.tvType?.text = getString(R.string.all)
                getMemberBalance()
            }
            popupWindow.dismiss()

        }
        popupView.tvPaid.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.consumption)) {
                binding?.tvType?.text = getString(R.string.consumption)
                getMemberBalance()
            }
            popupWindow.dismiss()

        }
        popupView.tvRefund.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.refund)) {
                binding?.tvType?.text = getString(R.string.refund)
                getMemberBalance()
            }
            popupWindow.dismiss()
        }
        popupView.tvTopup.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.top_up)) {
                binding?.tvType?.text = getString(R.string.top_up)
                getMemberBalance()
            }
            popupWindow.dismiss()
        }
        context?.let {
            val type = context?.let { binding?.tvType?.text.toString().getBalanceTypeEnum(it) }
            when (type) {
                BalanceTypeEnum.PAID.id -> setSelectedLanguages(popupView.tvPaid, it)
                BalanceTypeEnum.REFUND.id -> setSelectedLanguages(popupView.tvRefund, it)
                BalanceTypeEnum.TOP_UP.id -> setSelectedLanguages(popupView.tvTopup, it)
                else -> {
                    setSelectedLanguages(popupView.tvAll, it)
                }
            }
        }
    }

    private fun setSelectedLanguages(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }

    fun getMemberBalance(isRefresh: Boolean? = null) {
        val type = context?.let { binding?.tvType?.text.toString().getBalanceTypeEnum(it) }
        memberMainViewModel.getMemberBalanceList(
            isRefresh,
            startDate = startDateString,
            endDate = endDateString,
            keyword = binding?.edtSearch?.getSearchContent(),
            type = if (type == -1) null else type.toString(),
            exactMatch = isExactMatch
        )
    }

}