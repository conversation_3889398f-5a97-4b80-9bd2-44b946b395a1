package com.metathought.food_order.casheir.ui.work_handover


import android.os.Bundle
import android.text.Editable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.databinding.DialogCloseClassesBinding
import com.metathought.food_order.casheir.extension.NumberInputFilter
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.navigateWithAnim
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.UnReadAndUnPrintHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.math.BigDecimal

/**
 * 交班并退出
 */
@AndroidEntryPoint
class CloseClassesDialog : BaseDialogFragment() {
    private var binding: DialogCloseClassesBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    private val viewModel: CloseClassesViewModel by viewModels()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCloseClassesBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        viewModel.logoutState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbProgress?.isVisible = true
                    binding?.viewKeyBoard?.setClearEnable(false)
                    binding?.viewKeyBoard?.setConfirm(false)
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbProgress.isVisible = false
                        binding?.viewKeyBoard?.setClearEnable(true)
                        binding?.viewKeyBoard?.setConfirm(true)

                    }
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    if (it.data != null) {
                        Timber.e("交班退出:${PrinterDeviceHelper.getPrinterList().size}")
                        Printer.printPrinterClosingReport(requireActivity(), it.data)
                    }
                    moveToLogin()


                }
            }
        }
        viewModel.shiftLogtState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.pbProgress?.isVisible = true
                }

                is ApiResponse.Error -> {
                    binding?.pbProgress?.isVisible = false
                    if (!it.message.isNullOrEmpty()) Toast.makeText(
                        context,
                        it.message.toString(),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                is ApiResponse.Success -> {
                    binding?.pbProgress?.isVisible = false
                    it.data.data?.let {
                        binding?.apply {
                            tvImprestUSDValue.text = it.openingCashUsd?.decimalFormatTwoDigitZero()
                            tvImprestKHRValue.text = it.openingCashKhr?.decimalFormatTwoDigit()
                        }
                    }
                }
            }
        }
    }

    private fun initData() {
//        val content = arguments?.getString(CONTENT)
        binding?.apply {
            binding?.viewKeyBoard?.setConfirm(false)
            binding?.viewKeyBoard?.setClearEnable(false)
            edtExpenseUSD.filters = arrayOf(NumberInputFilter(7, 2))
            edtTransferUSD.filters = arrayOf(NumberInputFilter(7, 2))
            edtExpenseKhmer.filters = arrayOf(NumberInputFilter(7, 0))
            edtTransferKhmer.filters = arrayOf(NumberInputFilter(7, 0))
        }
        viewModel.getShiftLog()
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            binding?.viewKeyBoard?.setClearTitle(title = getString(R.string.handover))
            binding?.viewKeyBoard?.setOnClearClick {
                //退出登录
                viewModel.logout(
                    khrAmount = edtTransferKhmer.text.toString(),
                    usdAmount = edtTransferUSD.text.toString(),
                    changeShiftRemark = edtRemark.text.toString(),
                    amountPaidUsd = edtExpenseUSD.text.toString(),
                    amountPaidKhr = edtExpenseKhmer.text.toString(),
                    exit = false
                )
            }
            binding?.viewKeyBoard?.setConfirmTitle(title = getString(R.string.classes_close))
            binding?.viewKeyBoard?.setonConfirmClick {
                //退出登录
                viewModel.logout(
                    khrAmount = edtTransferKhmer.text.toString(),
                    usdAmount = edtTransferUSD.text.toString(),
                    changeShiftRemark = edtRemark.text.toString(),
                    amountPaidUsd = edtExpenseUSD.text.toString(),
                    amountPaidKhr = edtExpenseKhmer.text.toString(),
                    exit = true
                )
            }

            edtExpenseKhmer.addTextChangedListener { s ->
                correctionKhmer(s)
            }
            edtExpenseKhmer.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtExpenseKhmer.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(9999999),
                    )
                    viewKeyBoard.setCurrentEditText(edtExpenseKhmer)
                }
            }

            edtExpenseUSD.addTextChangedListener { s ->
                correctionUSD(s)
            }
            edtExpenseUSD.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtExpenseUSD.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(9999999.99),
                    )
                    viewKeyBoard.setCurrentEditText(edtExpenseUSD)
                }
            }

            edtTransferKhmer.addTextChangedListener { s ->
                correctionKhmer(s)
                checkLogoutEnable()
            }
            edtTransferKhmer.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtTransferKhmer.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(9999999),
                    )
                    viewKeyBoard.setCurrentEditText(edtTransferKhmer)
                }
            }


            edtTransferUSD.addTextChangedListener { s ->
                correctionUSD(s)
                checkLogoutEnable()
            }
            edtTransferUSD.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtTransferUSD.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal(0.0),
                        BigDecimal(9999999.99),
                    )
                    viewKeyBoard.setCurrentEditText(edtTransferUSD)
                }
            }
        }
    }

    private fun correctionKhmer(editable: Editable?) {
        if (editable == null) {
            return
        }
        if (editable.isNotEmpty() && editable.toString().first() == '0') {
            if (editable.length > 1) {
                editable.replace(0, 2, editable.toString()[1].toString())
            }
        }
    }

    private fun correctionUSD(editable: Editable?) {
        if (editable == null) {
            return
        }
        if (editable.isNotEmpty() && editable.toString().first() == '.') {
            editable.replace(0, 1, "0.")
        }
        if (editable.isNotEmpty() && editable.toString().first() == '0') {
            if (editable.length > 1 && editable.toString()[1] != '.') {
                editable.replace(0, 2, editable.toString()[1].toString())
            }
        }
    }

    private fun checkLogoutEnable() {
        binding?.apply {
            binding?.viewKeyBoard?.setClearEnable(
                edtTransferUSD.text.toString().isNotEmpty() && edtTransferKhmer.text.toString()
                    .isNotEmpty()
            )
            binding?.viewKeyBoard?.setConfirm(
                edtTransferUSD.text.toString().isNotEmpty() && edtTransferKhmer.text.toString()
                    .isNotEmpty()
            )
        }
    }

    companion object {
        private const val CLOSE_CLASSES_DIALOG = "CLOSE_CLASSES_DIALOG"
        fun showDialog(
            fragmentManager: FragmentManager,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(CLOSE_CLASSES_DIALOG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener)
            fragment.show(fragmentManager, CLOSE_CLASSES_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(CLOSE_CLASSES_DIALOG) as? CloseClassesDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
        ): CloseClassesDialog {
            val args = Bundle()
            val fragment = CloseClassesDialog()
            fragment.positiveButtonListener = iAgreeListener
            fragment.arguments = args
            return fragment
        }
    }


    private fun moveToLogin() {
        lifecycleScope.launch {
            context?.let { it1 ->
                PreferenceDataStoreHelper.getInstance(it1).apply {
                    if (viewModel.isExit) {
                        val userLoginResponse = Gson().fromJson(
                            getFirstPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                ""
                            ), UserLoginResponse::class.java
                        )
                        userLoginResponse.token = ""
                        this.putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            userLoginResponse.toJson()
                        )
                        MainDashboardFragment.CURRENT_USER = null
                        UnReadAndUnPrintHelper.clear()
                        findNavController().navigateWithAnim(
                            R.id.action_mainDashboardFragment_to_loginFragment,
                            popupToId = R.id.mainDashboardFragment
                        )
                        dismissCurrentDialog()
                    } else {
                        val userLoginResponse = Gson().fromJson(
                            getFirstPreference(
                                PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                                ""
                            ), UserLoginResponse::class.java
                        )
                        userLoginResponse.isNeedStartShift = true
                        userLoginResponse.isShiftEmployee = false
                        userLoginResponse.shiftLog = null
                        MainDashboardFragment.CURRENT_USER = userLoginResponse
                        this.putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                            userLoginResponse.toJson()
                        )
                        dismissCurrentDialog()
                    }
                }
            }
        }
    }

//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }
}
