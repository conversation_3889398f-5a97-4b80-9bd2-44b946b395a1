package com.metathought.food_order.casheir.ui.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.util.AttributeSet;
import android.view.View;

import com.metathought.food_order.casheir.R;


public class DashedLineView extends View {
    private Paint paint;
    private Path path;
    private PathEffect effects;

    public DashedLineView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(0xFF000000); // 设置画笔颜色
        paint.setStyle(Paint.Style.STROKE); // 设置画笔模式为描边
        paint.setStrokeWidth(5); // 设置线条宽度

        path = new Path();

        // 虚线的间隔和每个点的长度，长度是间隔的两倍
        float[] dashIntervals = {10, 10};
        effects = new DashPathEffect(dashIntervals, 0);

        paint.setPathEffect(effects);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        path.reset();
        path.moveTo(10, 10);
        path.lineTo(300, 10);

        canvas.drawPath(path, paint);
    }
}
