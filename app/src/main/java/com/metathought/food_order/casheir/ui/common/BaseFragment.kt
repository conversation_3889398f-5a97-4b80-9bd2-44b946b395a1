package com.metathought.food_order.casheir.ui.common

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.icu.util.Calendar
import android.widget.DatePicker
import android.widget.EditText
import android.widget.TimePicker
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import com.metathought.food_order.casheir.MainActivity
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
open class BaseFragment : Fragment() {

    override fun onResume() {
        super.onResume()
        onLoad()
    }

    open fun onLoad() {}

    fun showToast(buttonName: String) {
        Timber.e("#${buttonName}#")
        if (buttonName.isNotEmpty())
            Toast.makeText(context, "$buttonName", Toast.LENGTH_LONG).show()
    }


    fun showProgress() {
        (activity as? MainActivity)?.showProgress()
    }

    fun dismissProgress(throwable: Throwable? = null) {
        (activity as? MainActivity)?.dismissProgress()
    }

    fun clearProgress(throwable: Throwable? = null) {
        (activity as? MainActivity)?.clearProgress()
    }

    val calendar: Calendar = Calendar.getInstance()
    fun showDateTimePicker(editText: EditText) {

        val datePickerDialog = context?.let {
            DatePickerDialog(
                it,
                DatePickerDialog.OnDateSetListener { view: DatePicker, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                    calendar.set(Calendar.YEAR, year)
                    calendar.set(Calendar.MONTH, monthOfYear)
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

                    showTimePicker(editText)
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            )
        }

        datePickerDialog?.show()
    }

    private fun showTimePicker(editText: EditText) {
        val timePickerDialog = TimePickerDialog(
            context,
            TimePickerDialog.OnTimeSetListener { view: TimePicker, hourOfDay: Int, minute: Int ->
                calendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                calendar.set(Calendar.MINUTE, minute)

                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.US)
                editText.setText(dateFormat.format(calendar.time))
            },
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE),
            false
        )

        timePickerDialog.show()
    }

    fun showErrorPopup(msg: String, onDone: (() -> Unit)? = null) {
        var mMsg = msg.trim()
        if (mMsg == "timeout") {
            mMsg = getString(R.string.network_timeout_try_again)
        }
        if (mMsg == "connection closed") {
            mMsg = getString(R.string.connection_closed)
        }
        if (mMsg.contains("hostname")) {
            mMsg = getString(R.string.network_dns_resolution_failed)
        }
        ConfirmDialog.showDialog(
            parentFragmentManager,
            content = mMsg,
            positiveButtonTitle = getString(R.string.ok),
            isShowCancel = false
        ) {
            onDone?.invoke()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissProgress()
    }

    fun dissmissAllDialog() {
        parentFragmentManager.fragments.forEach {
            if (it is DialogFragment) {
                it.dismissAllowingStateLoss()
            }
        }
    }
}