package com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist


import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.extension.getStringByLocale
import java.util.Locale

data class RecordBalance(
    @SerializedName("accountId")
    val accountId: String?,
    @SerializedName("amount")
    val amount: Long?,
    @SerializedName("consumerId")
    val consumerId: String?,
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("nickName")
    val nickName: String?,
    @SerializedName("payChannel")
    val payChannel: Int?,
    @SerializedName("photoUrl")
    val photoUrl: String?,
    @SerializedName("status")
    val status: Int?,
    @SerializedName("telephone")
    val telephone: String?,
    @SerializedName("type")
    val type: Int?,
    @SerializedName("couponId")
    val couponId: String?,
    @SerializedName("couponName")
    val couponName: String?,
    @SerializedName("couponAmount")
    val couponAmount: Long?,
) {
    fun getPaymentMethod(context: Context): String {
        return when (payChannel) {
            PayTypeEnum.CREDIT.id -> {
                context.getString(R.string.credit_payment)
            }
            PayTypeEnum.ONLINE_PAYMENT.id -> {
                context.getString(R.string.online_payment)
            }

            PayTypeEnum.CASH_PAYMENT.id -> {
//                 getOfflinePayMethod(context)
                "${context.getString(R.string.offline_payments)}"
            }

            PayTypeEnum.USER_BALANCE.id -> {
                context.getString(R.string.pay_by_balance)
            }

            else -> ""
        }
    }


}