package com.metathought.food_order.casheir.ui.store_dashbord

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.PointF
import android.icu.util.Calendar
import android.os.Bundle
import android.text.TextPaint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.ChartTimeType
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.CurStoreMoneyOrder
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreStatisticResponse
import com.metathought.food_order.casheir.databinding.FragmentDashboardBinding
import com.metathought.food_order.casheir.databinding.PopupDateTypeBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.getDateTypeEnum
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.ui.adapter.DashboardListAdapter
import com.metathought.food_order.casheir.ui.adapter.TopSalesAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.store_dashbord.ordered.StoreOrderListDialog
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import androidx.core.graphics.toColorInt
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup


@AndroidEntryPoint
class DashboardFragment : BaseFragment() {

    companion object {
        fun newInstance() = DashboardFragment()
    }

    private var _binding: FragmentDashboardBinding? = null
    private val binding get() = _binding
    private val viewModel: StoreDashboardViewModel by viewModels()
    private var startDateString: String = ""
    private var endDateString: String = ""
    private var type: String = "1"
    private var topSaleAdapter: TopSalesAdapter? = null
    private var dashboardListAdapter: DashboardListAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentDashboardBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
        initObserver()

//        initObserverSocket()
//        showProgress()
    }

    private fun initObserver() {
        viewModel.uiStoreStatisticState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        showProgress()
                }
                if (it.showEnd) {
                    dismissProgress()
                    if (it.isRefresh != false) {
                        dashboardListAdapter?.replaceData(arrayListOf())
                        it.showSuccess?.let {
                            setData(it)
                        }

                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    dismissProgress()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    dismissProgress()

                    if (it.isRefresh != false) {
                        ArrayList(
                            response.storeMoneyOrderDetailList?.storeMoneyOrderDetailVos ?: listOf()
                        )?.let { it1 ->
                            dashboardListAdapter?.replaceData(
                                it1
                            )
                        }
                        setData(response)
                        refreshLayout.finishRefresh()

                    } else {
                        ArrayList(
                            response.storeMoneyOrderDetailList?.storeMoneyOrderDetailVos ?: listOf()
                        ).let { it1 ->
                            dashboardListAdapter?.addData(
                                it1
                            )
                        }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }
    }


    private fun initView() {
        binding?.apply {

            pieChartView.setNoDataText(getString(R.string.no_chart_data_available))
            setTitle()
            topSaleAdapter =
                context?.let { TopSalesAdapter(ArrayList(viewModel.getDefaultTopSaleList()), it) }
            recyclerViewTopSale.adapter = topSaleAdapter
            dashboardListAdapter = DashboardListAdapter(arrayListOf()) {}
            recyclerviewDashboardRevenue.adapter = dashboardListAdapter
        }
    }


    override fun onLoad() {
        super.onLoad()
        context?.let {
            getStoreData()
        }

    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        val constraintsBuilder = CalendarConstraints.Builder()
        // Set the minimum year
        constraintsBuilder.setStart(Calendar.getInstance().apply {
            set(Calendar.YEAR, 2024)
            set(Calendar.MONTH, Calendar.JANUARY)
        }.timeInMillis)
        val constraints = constraintsBuilder.build()
        builder.setCalendarConstraints(constraints)
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            type = ""
            binding?.run {
                tvType.text = getString(R.string.not_selected)
//                radioGroupFilter.setOnCheckedChangeListener(null)
//                radioGroupFilter.clearCheck()
            }
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate))
            endDateString = sdf.format(Date(endDate))

            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"

            // Displaying the selected date range in the TextView
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
            getStoreData()

//            binding?.run {
//                setRadioGroupListener()
//            }
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getStoreData(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getStoreData(false)
                }

            })
            tvCalendar.setOnClickListener {
                datePickerDialog()
            }
            layoutViewOrderDetail.setOnClickListener {
                StoreOrderListDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    type = type,
                    startDate = startDateString,
                    endDate = endDateString
                ) {

                }
            }
//            setRadioGroupListener()
            tvClearFilter.setOnClickListener {
                onSelectDate(isReset = true)
//                radioToday.isChecked = true
            }

            dropdownFilter.setOnClickListener {
                arrow.animate().rotation(180f).setDuration(200)
                dropdownFilter.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupWindowLanguage(dropdownFilter)
            }

            layoutRevenueAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutRevenueAmount.tvTitleCue,
                    getString(R.string.revenue_amount_tip)
                )
            }

            layoutUnPaidAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutUnPaidAmount.tvTitleCue,
                    getString(R.string.unpaid_amount_tip)
                )
            }

            layoutActualRevenue.root.setOnClickListener {
                showSmartTipPopup(
                    layoutActualRevenue.tvTitleCue,
                    getString(R.string.actual_revenue_tip)
                )
            }

            layoutUnsettledAmount.tvTitleCue.isVisible = false
//            layoutUnsettledAmount.root.setOnClickListener {
////                showSmartTipPopup(
////                    layoutUnsettledAmount.tvTitleCue,
////                    getString(R.string.unsettled_amount_tip)
////                )
//            }
            layoutSettledAmount.tvTitleCue.isVisible = false
//            layoutSettledAmount.root.setOnClickListener {
//                showSmartTipPopup(
//                    layoutSettledAmount.tvTitleCue,
//                    getString(R.string.settled_amount_tip)
//                )
//            }

            layoutOrderNumbers.root.setOnClickListener {
                showSmartTipPopup(
                    layoutOrderNumbers.tvTitleCue,
                    getString(R.string.order_numbers_tip)
                )
            }

            layoutUnPaidOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutUnPaidOrders.tvTitleCue,
                    getString(R.string.unpaid_orders_tip)
                )
            }

            layoutPaidOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutPaidOrders.tvTitleCue,
                    getString(R.string.paid_orders_tip)
                )
            }

            layoutRefundsOrders.root.setOnClickListener {
                showSmartTipPopup(
                    layoutRefundsOrders.tvTitleCue,
                    getString(R.string.refund_orders_tip)
                )
            }

            layoutRefundAmount.root.setOnClickListener {
                showSmartTipPopup(
                    layoutRefundAmount.tvTitleCue,
                    getString(R.string.refund_amount_tip)
                )
            }

            llChart.setOnClickListener {
                showSmartTipPopup(
                    tvPaymentMethodTitleCue,
                    getString(R.string.payment_method_tip)
                )
            }

            pieChartView.setOnClickListener {
                showSmartTipPopup(
                    tvPaymentMethodTitleCue,
                    getString(R.string.payment_method_tip)
                )
            }
        }
    }

//    private fun FragmentDashboardBinding.setRadioGroupListener() {
//        radioGroupFilter.setOnCheckedChangeListener { group, checkedId ->
//            if (checkedId != -1) {
//                tvCalendar.text = ""
//                tvCalendar.updateCalendarColor()
//                startDateString = ""
//                endDateString = ""
//                type = checkedId.getType()
//                getStoreData()
//            }
//        }
//    }


    private fun getStoreData(isRefresh: Boolean? = null) {
        viewModel.getStoreStatistic(isRefresh, type, startDateString, endDateString)
    }

    private fun setData(storeData: StoreStatisticResponse) {
        binding?.apply {
            layoutRevenueAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.turnover?.decimalFormatTwoDigitZero()}"
            layoutUnPaidAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.waitPayMoney?.decimalFormatTwoDigitZero()}"
            layoutActualRevenue.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.payMoney?.decimalFormatTwoDigitZero()}"
            layoutUnsettledAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.waitSettledMoney?.decimalFormatTwoDigitZero()}"
            layoutSettledAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.settledMoney?.decimalFormatTwoDigitZero()}"
            layoutOrderNumbers.tvValue.text = "${storeData.curStoreMoneyOrder?.orderNum}"
            layoutUnPaidOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.waitPayOrderNum}"
            layoutPaidOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.payOrderNum}"
            layoutRefundsOrders.tvValue.text = "${storeData.curStoreMoneyOrder?.refundOrderNum}"
            layoutRefundAmount.tvValue.text =
                "$ ${storeData.curStoreMoneyOrder?.refundMoney?.decimalFormatTwoDigitZero()}"
            // yesterday

            layoutRevenueAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.turnover?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutUnPaidAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.waitPayMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutActualRevenue.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.payMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutUnsettledAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.waitSettledMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutSettledAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.settledMoney?.decimalFormatTwoDigitZero() ?: 0.00}"
            layoutOrderNumbers.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.orderNum ?: 0}"
            layoutUnPaidOrders.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.waitPayOrderNum ?: 0}"
            layoutPaidOrders.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.payOrderNum ?: 0}"
            layoutRefundsOrders.tvValueYesterday.text =
                "${getString(R.string.yesterday)} ${storeData.lastStoreMoneyOrder?.refundOrderNum ?: 0}"
            layoutRefundAmount.tvValueYesterday.text =
                "${getString(R.string.yesterday)} $${storeData.lastStoreMoneyOrder?.refundMoney?.decimalFormatTwoDigitZero() ?: 0.00}"

            layoutRevenueAmount.tvValueYesterday.isVisible = type == "1"
            layoutUnPaidAmount.tvValueYesterday.isVisible = type == "1"
            layoutActualRevenue.tvValueYesterday.isVisible = type == "1"
            layoutUnsettledAmount.tvValueYesterday.isVisible = type == "1"
            layoutSettledAmount.tvValueYesterday.isVisible = type == "1"
            layoutOrderNumbers.tvValueYesterday.isVisible = type == "1"
            layoutUnPaidOrders.tvValueYesterday.isVisible = type == "1"
            layoutPaidOrders.tvValueYesterday.isVisible = type == "1"
            layoutRefundsOrders.tvValueYesterday.isVisible = type == "1"
            layoutRefundAmount.tvValueYesterday.isVisible = type == "1"
            storeData.salesRankingList?.let {
                if (it.isEmpty())
                    topSaleAdapter?.updateItems(ArrayList(viewModel.getDefaultTopSaleList()))
                else
                    topSaleAdapter?.updateItems(ArrayList(it))
            }
            storeData.curStoreMoneyOrder?.let { showPieChart(it) }
        }
    }

    private fun setTitle() {
        binding?.apply {
            layoutRevenueAmount.tvTitle.text = getText(R.string.revenue_amount)
            layoutUnPaidAmount.tvTitle.text = getString(R.string.unpaid_amount)
            layoutActualRevenue.tvTitle.text = getString(R.string.actual_revenue)
            layoutUnsettledAmount.tvTitle.text = getString(R.string.unsettled_amount)
            layoutSettledAmount.tvTitle.text = getString(R.string.settled_amount)
            layoutOrderNumbers.tvTitle.text = getString(R.string.order_numbers)
            layoutUnPaidOrders.tvTitle.text = getString(R.string.unpaid_orders)
            layoutPaidOrders.tvTitle.text = getString(R.string.paid_orders)
            layoutRefundsOrders.tvTitle.text = getString(R.string.refund_orders)
            layoutRefundAmount.tvTitle.text = getString(R.string.refund_amount)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showPieChart(storeData: CurStoreMoneyOrder) {
        binding?.pieChartView?.apply {
            setTouchEnabled(false)
            holeRadius = 75f
            rotationAngle = 90f
            description?.isEnabled = false
            centerText = getString(R.string.payment_nmethod)
            setCenterTextColor(ContextCompat.getColor(context, R.color.black))
            setCenterTextSize(10f)
            legend?.isEnabled = false
            setDrawRoundedSlices(true)
        }
        val pieEntries = ArrayList<PieEntry>()
        val label = "type"

        val online = storeData.onlinePay?.toFloat() ?: 0.0f
        val balance = storeData.balance?.toFloat() ?: 0.0f
        val cash = storeData.cash?.toFloat() ?: 0.0f
        val credit = storeData.credit?.toFloat() ?: 0.0f
        //initializing colors for the entries
        val colors = ArrayList<Int>()
        if (online != 0.0f || balance != 0.0f || cash != 0.0f || credit != 0.0f) {
            //OnlinePayment
            colors.add("#0F9D58".toColorInt())
            //Balance
            colors.add("#FF7F00".toColorInt())
            //Cash
            colors.add("#2C99FF".toColorInt())
            //Credit
            colors.add("#A968FD".toColorInt())

            pieEntries.add(PieEntry(online, ""))
            pieEntries.add(PieEntry(balance, ""))
            pieEntries.add(PieEntry(cash, ""))
            pieEntries.add(PieEntry(credit, ""))

        } else {
            colors.add("#E7F5EE".toColorInt())
            pieEntries.add(PieEntry(1.0f, ""))
        }
        //border: 18px solid #E7F5EE
        //collecting the entries with label name
        val pieDataSet = PieDataSet(pieEntries, label)
        pieDataSet.valueTextSize = 12f
        pieDataSet.colors = colors
        pieDataSet.setDrawValues(false)
        val pieData = PieData(pieDataSet)

        binding?.apply {
            pieChartView.setData(pieData)
            pieChartView.animateY(1000)
            pieChartView.invalidate()
            tvCashValue.text = "$${storeData.cash?.decimalFormatTwoDigitZero()}"
            tvBalanceValue.text = "$${storeData.balance?.decimalFormatTwoDigitZero()}"
            tvOnlinePaymentValue.text = "$${storeData.onlinePay?.decimalFormatTwoDigitZero()}"
            tvCreditValue.text = "$${storeData.credit?.decimalFormatTwoDigitZero()}"
        }
    }


    private fun showPopupWindowLanguage(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupDateTypeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }
        popupView.tvToday.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.today)) {
                binding?.tvType?.text = getString(R.string.today)
                onSelectDate()
            }
            popupWindow.dismiss()

        }
        popupView.tvWeek.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_week)) {
                binding?.tvType?.text = getString(R.string.this_week)
                onSelectDate()
            }
            popupWindow.dismiss()

        }
        popupView.tvMonth.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_month)) {
                binding?.tvType?.text = getString(R.string.this_month)
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        popupView.tvQuarter.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_quarter)) {
                binding?.tvType?.text = getString(R.string.this_quarter)
                onSelectDate()
            }
            popupWindow.dismiss()
        }

        popupView.tvYear.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_year)) {
                binding?.tvType?.text = getString(R.string.this_year)
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        context?.let {
            val type = context?.let { binding?.tvType?.text.toString().getDateTypeEnum(it) }
            when (type) {
                ChartTimeType.TODAY.type -> setSelectedLanguages(popupView.tvToday, it)
                ChartTimeType.WEEK.type -> setSelectedLanguages(popupView.tvWeek, it)
                ChartTimeType.MONTH.type -> setSelectedLanguages(popupView.tvMonth, it)
                ChartTimeType.QUARTER.type -> setSelectedLanguages(popupView.tvQuarter, it)
                ChartTimeType.YEAR.type -> setSelectedLanguages(popupView.tvYear, it)
                else -> {
//                    setSelectedLanguages(popupView.tvToday, it)
                }
            }
        }
    }

    private fun setSelectedLanguages(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }


    private fun onSelectDate(isReset: Boolean? = false) {
        if (isReset == true) {
            binding?.tvType?.text = getString(R.string.today)
        }
        binding?.tvCalendar?.text = ""
        binding?.tvCalendar?.updateCalendarColor()
        type = context?.let { binding?.tvType?.text.toString().getDateTypeEnum(it) } ?: ""
        startDateString = ""
        endDateString = ""
        getStoreData()
    }


//    private fun initObserverSocket() {
//        viewModel.liveDataRespose.observe(viewLifecycleOwner) {
//            when (it) {
//                is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
//                is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
//                is WebSocket.Event.OnConnectionClosing -> Timber.e(
//                    "closing connection.."
//                )
//
//                is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
//                is WebSocket.Event.OnMessageReceived -> {
//                    if (it.message is Message.Text) {
//                        if ((it.message as Message.Text).value == "ping")
//                            viewModel.testingWebsocketSendMessage()
//                        else {
//                            try {
//                                Timber.e("ws ==> ${(it.message as Message.Text).value}")
//                                val socketModel = Gson().fromJson(
//                                    (it.message as Message.Text).value,
//                                    SocketModel::class.java
//                                )
//                                socketModel.cmd?.let { cmd ->
//
//                                    when (cmd) {
//                                        29 -> {
//                                            //获取Usb 打印配置
//                                            val currentFragment =
//                                                parentFragmentManager.fragments.firstOrNull()
//                                            if (currentFragment != null) {
//                                                (currentFragment as MainDashboardFragment).getUsbPrinterInfo()
//                                            } else {
//
//                                            }
//                                        }
//
//                                        else -> {}
//                                    }
//                                }
//                            } catch (e: Exception) {
//                                e.printStackTrace()
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }


    //Socket
    override fun onResume() {
//        viewModel.startLifeCycle()
//        viewModel.connectWebsocket()
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        super.onResume()
    }

    override fun onPause() {
//        viewModel.destroylifeCycle()
        super.onPause()
    }

    private fun wsHandel(event: WebSocket.Event) {

    }

//    // 添加这个方法到 DashboardFragment 类中
//    private fun showCustomTipPopup(anchorView: View, message: String) {
//        val location = IntArray(2)
//        anchorView.getLocationOnScreen(location)
//        val x = location[0] + anchorView.width / 2
//        val y = location[1]
//
//        XPopup.Builder(requireContext())
//            .hasShadowBg(false)
//            .isTouchThrough(true)
//            .isDestroyOnDismiss(true) // 对于只使用一次的弹窗，推荐设置这个
//            .atPoint(PointF(x.toFloat(), y.toFloat()))
//            .isCenterHorizontal(true)
//            .hasShadowBg(false) // 去掉半透明背景
//            .asCustom(CustomBubbleAttachPopup(requireContext(), message))
//            .show()
//    }

    /**
     * 显示智能提示弹窗，根据可用空间自动选择显示位置
     * - 左右空间都足够时，显示在中间
     * - 左侧空间不足时，显示在右侧
     * - 右侧空间不足时，显示在左侧
     *
     * @param anchorView 锚点视图，弹窗将显示在此视图附近
     * @param message 要显示的提示信息
     */
    private fun showSmartTipPopup(anchorView: View, message: String) {
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val anchorX = location[0]
        val anchorY = location[1]
        val anchorWidth = anchorView.width

        // 获取屏幕宽度
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenWidth = displayMetrics.widthPixels

// 估算消息宽度 (使用TextPaint计算实际文本宽度)
        val textPaint = TextPaint()
        textPaint.textSize =
            resources.getDimension(R.dimen._12ssp) // 使用与CustomBubbleAttachPopup相同的文本大小
        val estimatedMessageWidth = textPaint.measureText(message).toInt() +
                resources.getDimensionPixelSize(R.dimen.fragment_horizontal_margin) * 2 // 添加左右内边距
        val halfMessageWidth = estimatedMessageWidth / 2

        // 计算锚点中心位置
        val anchorCenterX = anchorX + (anchorWidth / 2)

        // 判断左右空间是否足够
        val hasEnoughSpaceOnLeft = anchorCenterX > halfMessageWidth
        val hasEnoughSpaceOnRight = (screenWidth - anchorCenterX) > halfMessageWidth

        // 创建XPopup构建器
        val popupBuilder = XPopup.Builder(requireContext())
            .hasShadowBg(false)
            .isTouchThrough(true)
            .isDestroyOnDismiss(true)

        // 根据空间决定显示位置
        if (hasEnoughSpaceOnLeft && hasEnoughSpaceOnRight) {
            // 左右空间都足够，显示在中间
            popupBuilder.popupPosition(PopupPosition.Top) // 使用Top位置，但实际上是居中的
            // 设置居中显示
            popupBuilder.isCenterHorizontal(true)
        } else if (hasEnoughSpaceOnRight) {
            // 右侧空间足够，显示在右侧
            popupBuilder.popupPosition(PopupPosition.Right)
        } else {
            // 默认显示在左侧
            popupBuilder.popupPosition(PopupPosition.Left)
        }

        // 显示弹窗
        popupBuilder
            .atView(anchorView)
//            .atPoint(PointF(anchorCenterX.toFloat(), anchorY.toFloat()))
            .asCustom(CustomBubbleAttachPopup(requireContext(), message, 5000))
            .show()
    }

}