package com.metathought.food_order.casheir.ui.member.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.constant.UpdateConsumerType
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.DialogMemberEditTipsBinding
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment


class EditMemberInfoTipsDialog : BaseDialogFragment() {
    private var binding: DialogMemberEditTipsBinding? = null
    private var editCallback: ((UpdateConsumerType) -> Unit)? = null
    private var record: Record? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMemberEditTipsBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        onTouchOutSide(binding?.layoutMain)
        initData()
        initListener()
    }


    private fun initData() {
        binding?.apply {
            tvName.text = record?.nickName
            tvPhone.text = record?.telephone
        }


    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            tvName.setOnClickListener {
                editCallback?.invoke(UpdateConsumerType.NAME)
                dismissAllowingStateLoss()
            }
//            tvPhone.setOnClickListener {
//                editCallback?.invoke(UpdateConsumerType.PHONE)
//                dismissAllowingStateLoss()
//            }
        }
    }


    companion object {
        private const val TAG = "EditMemberInfoTipsDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            record: Record,
            editCallback: ((UpdateConsumerType) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(record, editCallback)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? EditMemberInfoTipsDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            record: Record,
            editCallback: ((UpdateConsumerType) -> Unit)
        ): EditMemberInfoTipsDialog {
            val fragment = EditMemberInfoTipsDialog()
            fragment.editCallback = editCallback
            fragment.record = record

            return fragment
        }
    }
}
