package com.metathought.food_order.casheir.ui.ordered.anti_settlement

import android.content.Context
import android.widget.Toast
import androidx.core.view.isVisible
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.lxj.xpopup.util.KeyboardUtils
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogAntiSettlementBinding
import com.metathought.food_order.casheir.helper.XpopHelper
import com.metathought.food_order.casheir.ui.dialog.BaseCenterDialog
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class AntiSettlementDialog
    (private val act: Context?) : BaseCenterDialog(act!!) {
//    companion object {
//        private const val TAG = "AntiSettlementDialog"
//    }

    private var binding: DialogAntiSettlementBinding? = null
    private var onConfirmClickListener: ((OrderedInfoResponse?, Int, String?) -> Unit)? = null

    private var orderInfo: OrderedInfoResponse? = null


    fun showDialog(
        orderInfo: OrderedInfoResponse?,
        onConfirmClickListener: ((OrderedInfoResponse?, Int, String?) -> Unit)? = null,
    ): AntiSettlementDialog {
        this.orderInfo = orderInfo
        this.onConfirmClickListener = onConfirmClickListener

        XPopup.Builder(act)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }

                override fun onDismiss(popupView: BasePopupView?) {
                    XpopHelper.removeToMap(popupView)
                    super.onDismiss(popupView)
                }
            })

//            .autoOpenSoftInput(true)
//            .autoFocusEditText(true)
            .asCustom(this)
            .show()
        XpopHelper.addToMap(this)
        return this
    }


    // 返回自定义弹窗的布局
    override fun getImplLayoutId(): Int {
        return R.layout.dialog_anti_settlement
    }


    override fun onCreate() {
        super.onCreate()
        binding = DialogAntiSettlementBinding.bind(popupImplView)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {

    }

    private fun initData() {
        orderInfo?.let {
            binding?.apply {


            }
        }
    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                KeyboardUtils.hideSoftInput(binding?.edtReason)
                dismiss()
            }

            btnConfirm.setOnClickListener {
                var reason: String? = null
                var reasonType = 0
                if (radio0.isChecked) {
                    reason = radio0.text.toString()
                    reasonType = 0
                } else if (radio1.isChecked) {
                    reason = radio1.text.toString()
                    reasonType = 1
                } else if (radio2.isChecked) {
                    reason = radio2.text.toString()
                    reasonType = 2
                } else if (radio3.isChecked) {
                    reason = edtReason.text.toString()
                    reasonType = 3

                    if (reason.trim().isNullOrEmpty()) {
                        Toast.makeText(
                            context,
                            context.getString(R.string.please_input_anti_settlement_reason),
                            Toast.LENGTH_LONG
                        ).show()
                        return@setOnClickListener
                    }
                }
                onConfirmClickListener?.invoke(orderInfo, reasonType, reason)
                KeyboardUtils.hideSoftInput(binding?.edtReason)
                dismiss()
            }

            radio0.setOnCheckedChangeListener { _, isCheck ->
                if (isCheck) {
                    radio1.isChecked = false
                    radio2.isChecked = false
                    radio3.isChecked = false
                    edtReason.isVisible = false
                    KeyboardUtils.hideSoftInput(edtReason)
                }
            }
            radio1.setOnCheckedChangeListener { _, isCheck ->
                if (isCheck) {
                    radio0.isChecked = false
                    radio2.isChecked = false
                    radio3.isChecked = false
                    edtReason.isVisible = false
                    KeyboardUtils.hideSoftInput(edtReason)
                }
            }

            radio2.setOnCheckedChangeListener { _, isCheck ->
                if (isCheck) {
                    radio0.isChecked = false
                    radio1.isChecked = false
                    radio3.isChecked = false
                    edtReason.isVisible = false
                    KeyboardUtils.hideSoftInput(edtReason)
                }
            }

            radio3.setOnCheckedChangeListener { radio, isCheck ->
                if (isCheck) {
                    radio0.isChecked = false
                    radio1.isChecked = false
                    radio2.isChecked = false
                    edtReason.isVisible = true

                    KeyboardUtils.showSoftInput(edtReason)
                }
            }
        }
    }


    // 设置最大宽度，看需要而定，
    override fun getMaxWidth(): Int {
        if (context != null) {
            context.let {
                val displayMetrics = getDisplayMetrics(it)
                val screenWidth = (displayMetrics.widthPixels * 0.3).toInt()
                return screenWidth
            }
        }

        return super.getMaxWidth()
    }


}

