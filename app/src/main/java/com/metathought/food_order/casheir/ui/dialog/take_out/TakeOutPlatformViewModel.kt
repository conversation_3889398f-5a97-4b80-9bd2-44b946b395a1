package com.metathought.food_order.casheir.ui.dialog.take_out


import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TakeOutPlatformViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<ApiResponse<List<TakeOutPlatformModel>>>()
    val uiState get() = _uiState

    fun getDeliveryPlatformList() {
        viewModelScope.launch {
            uiState.postValue(ApiResponse.Loading)
            val response = repository.getDeliveryPlatformList()
            uiState.postValue(response)
        }
    }

//    data class UIModel(
//        val isEnableRequest: Boolean?,
//        val isRequestSuccess: Boolean?,
//    )

}