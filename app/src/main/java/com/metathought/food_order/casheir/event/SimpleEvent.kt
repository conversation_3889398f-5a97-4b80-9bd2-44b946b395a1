package com.metathought.food_order.casheir.event

enum class SimpleEventType {
    //更新店铺信息
    UPDATE_STORE,

    //获取未读事件
    GET_UNREAD_EVENT,

    //获取接单未读事件
    GET_ACCEPT_ORDER_UNREAD_EVENT,

    //获取公告未读事件
    GET_NOTICE_UNREAD_EVENT,

    //获取最后一条公告
    GET_LAST_NOTICE_EVENT,

    //更新未读事件
    UPDATE_UNREAD_EVENT,

    //把未打印 更新未已打印
    UPDATE_UNPRINT_EVENT,

    //刷新订单列表
    UPDATE_ORDER_LIST,

    //更新USB打印配置
    UPDATE_USB_PRINT_INFO,

    //更新打印机列表
    UPDATE_PRINTER_MANAGER_LIST,

    //更新临时商品信息
    UPDATE_TMP_GOOD_INFO,

//    //更新自定义KHR
//    UPDATE_CUSTOMIZE_KHR,

    //打印购物车预结
    PRINT_CART_PRE_STELLEMENT,

    //时价菜价格变动
    TIME_PRICE_GOOD_CHANGE_PRICE,
}

data class SimpleEvent<T>(val eventType: SimpleEventType, val data: T? = null)

