package com.metathought.food_order.casheir.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogSetCashBoxBinding
import com.metathought.food_order.casheir.extension.md5
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.showKeyboard
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.utils.SingleClickUtils
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 设置钱箱
 * **/

class SetCashBoxDialog : BaseDialogFragment() {

    private var binding: DialogSetCashBoxBinding? = null

    //钱箱密码开关
    private var isSwitchOpen = false

    //是否关闭密码
    private var isClosePwd = false

    //是否已经设置密码
    private var currentPwd = ""

    private var resetButtonListener: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSetCashBoxBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initData()
        initListener()
    }


    private fun initData() {

        binding?.apply {
            lifecycleScope.launch {
                currentPwd = PreferenceHelper.getCashBoxPwd(requireContext())
            }


            tvRest.isVisible = !currentPwd.isNullOrEmpty()

            isSwitchOpen = !currentPwd.isNullOrEmpty()


            updateSwitch()
        }
    }

    private fun initListener() {
        binding?.apply {

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }

            ivSwitch.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (!currentPwd.isNullOrEmpty() && isSwitchOpen) {
                        //如果设置了密码切当前 switch 是open 状态
                        tvTitleName.text = getString(R.string.close_cash_box_pwd)
                        tvCashBoxPwd.isVisible = false
                        ivSwitch.isVisible = false
                        tvRest.isVisible = false
                        isClosePwd = true
                        isSwitchOpen = !isSwitchOpen
                        showInputPwd(true)
                    } else {
                        isSwitchOpen = !isSwitchOpen
                        updateSwitch()
                        showInputPwd(isSwitchOpen)
                    }


                }
            }

            btnYes.setOnClickListener {
                lifecycleScope.launch {
                    Timber.e("edtPassword  ${edtPassword.text.toString()}")
                    val pwd = edtPassword.text.toString().md5()
                    if (isClosePwd) {
                        Timber.e("关闭钱箱密码")
                        Timber.e("pwd  ${pwd}")
                        Timber.e("currentPwd  ${currentPwd}")
                        if (pwd == currentPwd) {
                            PreferenceHelper.deleteCashBoxPwd(requireContext())
                            dismissAllowingStateLoss()
                        } else {
                            llError.isVisible = true
                        }

                    } else {
                        Timber.e("设置钱箱密码")
                        Toast.makeText(
                            requireActivity(),
                            getString(R.string.passcode_has_been_set_successfully),
                            Toast.LENGTH_SHORT
                        ).show()
                        PreferenceHelper.setCashBoxPwd(
                            requireContext(),
                            edtPassword.text.toString()
                        )
                        dismissAllowingStateLoss()
                    }
                }
            }

            tvRest.setOnClickListener {
                resetButtonListener?.invoke()
                dismissAllowingStateLoss()
            }

            edtPassword.setOnTextChangeListener { text, isComplete ->
                llError.isVisible = false
                btnYes.setEnableWithAlpha(edtPassword.text.length == 6)

            }

        }
    }

    private fun updateSwitch() {
        binding?.apply {
            ivSwitch.setImageResource(if (isSwitchOpen) R.drawable.icon_switch_open else R.drawable.icon_switch_close)

        }
    }

    private fun showInputPwd(isShow: Boolean) {
        binding?.apply {
            llPwd.isVisible = isShow
            btnYes.isVisible = isShow
            edtPassword.setText("")
            if (isShow) {
                requireActivity().showKeyboard(edtPassword)
            }
        }
    }


    companion object {
        private const val TAG = "SetCashBoxDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            resetButtonListener: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(resetButtonListener)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SetCashBoxDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            resetButtonListener: (() -> Unit)? = null
        ): SetCashBoxDialog {
            val args = Bundle()
            val fragment = SetCashBoxDialog()
            fragment.arguments = args
            fragment.resetButtonListener = resetButtonListener
            return fragment
        }
    }

}
