package com.metathought.food_order.casheir.ui.widget.printer

import android.content.Context
import com.alibaba.fastjson.JSON
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.NoPrintModel
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.extension.formatTimestamp2
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterTemplateHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.listener.SettableFuture
import com.metathought.food_order.casheir.ui.widget.printer.Printer.printByPrinter
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import net.posprinter.POSConst
import net.posprinter.POSPrinter
import timber.log.Timber
import java.util.Date
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 *<AUTHOR>
 *@time  2025/5/8
 *@desc  wifi 打印队列
 **/
// 扩展函数：将异步回调转换为挂起函数

private suspend fun POSPrinter.getPrinterStatus(): Int {
    return suspendCoroutine { continuation ->
        printerStatus { status -> continuation.resume(status) }
    }
}

private suspend fun POSPrinter.getConnect(): Boolean {
    return suspendCoroutine { continuation ->
        isConnect { status -> continuation.resume(status == 1) }
    }
}


object WifiPrintQueueManager {
    //    private val printQueue = PriorityQueue<PrintTask>()
//    private val printQueue = ConcurrentLinkedQueue<PrintTask>()

    // 按打印机IP分组的任务队列（IP -> 任务队列）
    private val printerQueues = ConcurrentHashMap<String, ConcurrentLinkedQueue<PrintTask>>()

    // 按打印机IP记录处理状态（IP -> 是否正在处理）
    private val processingStatus = ConcurrentHashMap<String, Boolean>()

    // 锁对象（按IP隔离，避免不同打印机间的锁竞争）
    private val lockMap = ConcurrentHashMap<String, Any>()

    /**
     * 获取或创建打印机对应的锁对象（避免不同打印机共享锁）
     */
    private fun getOrCreateLock(printerIp: String): Any {
        return lockMap.getOrPut(printerIp) { Any() }
    }


    /**
     * 添加打印任务到对应打印机的队列
     */
    fun addTask(task: PrintTask) {
        val printerIp = task.noPrintModel.printerConfigInfo?.ipAddress
            ?: run {
                Timber.e("打印机IP为空，任务丢弃: ${JSON.toJSONString(task)}")
                return
            }

        Timber.e("Chetwyn 添加任务到打印机[$printerIp]: ${JSON.toJSONString(task.noPrintModel.printerConfigInfo?.ipAddress)}")
        synchronized(getOrCreateLock(printerIp)) {
            // 初始化队列（如果不存在）
            val queue = printerQueues.getOrPut(printerIp) { ConcurrentLinkedQueue() }
            queue.add(task)
            processingStatus.getOrPut(printerIp) { false }
            processQueue(printerIp)
        }
    }


    /**
     * 处理指定打印机的任务队列
     */
    private fun processQueue(printerIp: String) {
        synchronized(getOrCreateLock(printerIp)) {
            val isProcessing = processingStatus[printerIp] ?: false
            val queue = printerQueues[printerIp] ?: return
            Timber.e("Chetwyn 打印机[$printerIp] isProcessing: $isProcessing 队列长度: ${queue.size}")

            if (isProcessing || queue.isEmpty()) return

            processingStatus[printerIp] = true
            val task = queue.poll()
            executePrint(printerIp, task)
        }
    }


    //    private val failedQueue = LinkedList<PrintTask>()
//    private var isProcessing = false
//    private val lock = Any()
    fun clearTask(printerIp: String? = null) {
        if (printerIp.isNullOrEmpty()) {
            printerQueues.clear()
            processingStatus.clear()
        } else {
            synchronized(getOrCreateLock(printerIp)) {
                printerQueues.remove(printerIp)
                processingStatus.remove(printerIp)
            }
        }
    }


    private fun executePrint(printerIp: String, task: PrintTask?) {
        if (task == null) {
            synchronized(getOrCreateLock(printerIp)) {
                processingStatus[printerIp] = false
            }
            return
        }
        CoroutineScope(Dispatchers.IO + CoroutineName("Printer-$printerIp")).launch {
            try {
                // 状态检查（已优化为带超时的独立协程）
                val isPrinterNormal = isWifiPrinterNormal(task).get() // 阻塞获取结果（需确保非UI线程）
                if (isPrinterNormal) {
                    if (task.noPrintModel.currentOrderedInfo != null) {
                        printWifiTicket(
                            MyApplication.myAppInstance,
                            task.noPrintModel,
                            task.timestamp
                        )
                    }
                    if (task.noPrintModel.productReport != null) {
                        printWifiProductReport(
                            MyApplication.myAppInstance,
                            task.noPrintModel,
                        )
                    }

                    if (task.noPrintModel.paymentMethodReport != null) {
                        printWifiPaymentMethodReport(
                            MyApplication.myAppInstance,
                            task.noPrintModel,
                        )
                    }

                    if (task.noPrintModel.createTempTableResponse != null) {
                        printWifiTmpCode(
                            MyApplication.myAppInstance,
                            task.noPrintModel,
                        )
                    }
                    if (task.noPrintModel.repaymentResponse != null && task.noPrintModel.printerOrderStatus != null) {
                        printWifiPrinterCreditRecordReport(
                            MyApplication.myAppInstance,
                            task.noPrintModel,
                        )
                    }

                    handlePrintSuccess(printerIp, task)
                } else {
                    handlePrintFailure(printerIp, task)
                }

            } catch (e: Exception) {
                handlePrintFailure(printerIp, task)
            }
        }
    }

    private fun handlePrintSuccess(printerIp: String, task: PrintTask) {
        GlobalScope.launch {
            delay(2500)
//            isProcessing = false
            synchronized(getOrCreateLock(printerIp)) {
                processingStatus[printerIp] = false
                processQueue(printerIp)
            }
        }
    }

    private fun handlePrintFailure(printerIp: String, task: PrintTask) {
        synchronized(getOrCreateLock(printerIp)) {
//            if (task.retryCount < PrintTask.MAX_RETRY) {
            /**
             * 10 分钟以外的舍去
             */
            if ((Date().time - task.noPrintModel.timeStamp) < 10 * 60 * 1000) {
                Timber.e("失败的 10分钟内的重新加入队列尝试 把任务加进队列  ${task.noPrintModel.timeStamp.formatTimestamp2()}")
                task.retryCount++
                task.priority -= 1 // 提高下次优先级
                printerQueues[printerIp]?.add(task)
            } else {
                Timber.e("失败 超过10分钟了就不加入  ${task.noPrintModel.timeStamp.formatTimestamp2()}")
//                failedQueue.add(task)
            }
            GlobalScope.launch {
                delay(2500)
//                isProcessing = false
                synchronized(getOrCreateLock(printerIp)) {
                    processingStatus[printerIp] = false
                    processQueue(printerIp)
                }
            }
        }
    }

    private fun isWifiPrinterNormal(task: PrintTask): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
        Timber.e("Chetwyn 开始查询 isWifiPrinterNormal1 ${task.noPrintModel.printerConfigInfo?.ipAddress}")
        val printerIp = task.noPrintModel.printerConfigInfo?.ipAddress ?: run {
            future.set(false)
            Timber.e("Chetwyn isWifiPrinterNormal false280:${task.noPrintModel.printerConfigInfo?.ipAddress}")
            return future
        }

        // 使用独立协程作用域处理单个打印机的状态检查
        CoroutineScope(Dispatchers.IO + CoroutineName("Printer-$printerIp")).launch {
            try {
                val posPrinter = PrinterDeviceHelper.getWifiPosPrinter(printerIp) ?: run {
                    future.set(false)
                    Timber.e("Chetwyn isWifiPrinterNormal false289:${task.noPrintModel.printerConfigInfo?.ipAddress}")
                    return@launch
                }
                Timber.e("Chetwyn isWifiPrinterNormal2 ${task.noPrintModel.printerConfigInfo?.ipAddress}")
                // 增加超时控制（避免单个请求长时间阻塞）
                // 调用挂起函数并设置超时
//                }
                val status = posPrinter.getPrinterStatus() // 内部已封装回调

                // 判断状态（根据芯烨SDK文档，正常状态码可能为0或其他值，需根据实际调整）
                val isNormal = status == POSConst.STS_NORMAL || status > 100000
                Timber.e("Chetwyn 打印机[$printerIp]状态码: $status → 正常? $isNormal ip:${task.noPrintModel.printerConfigInfo?.ipAddress}")
                future.set(isNormal)
            } catch (e: TimeoutCancellationException) {
                Timber.e("打印机[$printerIp]状态检查超时", e)
                future.set(false)
            } catch (e: Exception) {
                Timber.e("打印机[$printerIp]状态检查失败: ${e.message}", e)
                future.set(false)
            }
        }

        return future
    }


    fun printWifiTicket(context: Context, noPrintModel: NoPrintModel, code: Long?) {
        val posPrinter =
            PrinterDeviceHelper.getWifiPosPrinter(noPrintModel.printerConfigInfo?.ipAddress)
        posPrinter?.apply {
            initializePrinter()
//            val printTemplateResponseItem = noPrintModel.printTemplateResponseItem
//            //是否是厨打
            Printer.finalPrintHandler(
                noPrintModel.printTemplateResponseItem,
                context,
                noPrintModel.currentOrderedInfo,
                noPrintModel.isPrinterAgain,
                noPrintModel.printerConfigInfo,
                noPrintModel.isOrderMore,
                noPrintModel.paymentQrCode
            ) {
                it?.let {
                    printByPrinter(
                        posPrinter,
                        it,
                        noPrintModel.printTemplateResponseItem?.copies
                    )
                }
            }
        }
    }


    fun printWifiProductReport(context: Context, noPrintModel: NoPrintModel) {
        val posPrinter =
            PrinterDeviceHelper.getWifiPosPrinter(noPrintModel.printerConfigInfo?.ipAddress)
        posPrinter?.apply {
            initializePrinter()
            val bitmap =
                ReportPrinter.initProductReportTickerBitmap(
                    context,
                    printerConfigInfo = noPrintModel?.printerConfigInfo,
                    productReport = noPrintModel?.productReport
                )
            if (bitmap != null) {
                printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
                cutHalfAndFeed(1)
            }
        }
    }

    fun printWifiPaymentMethodReport(context: Context, noPrintModel: NoPrintModel) {
        val posPrinter =
            PrinterDeviceHelper.getWifiPosPrinter(noPrintModel.printerConfigInfo?.ipAddress)
        posPrinter?.apply {
            initializePrinter()
            val bitmap =
                ReportPrinter.initPaymentTickerBitmap(
                    context,
                    printerConfigInfo = noPrintModel?.printerConfigInfo,
                    productReport = noPrintModel?.paymentMethodReport
                )
            if (bitmap != null) {
                printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
                cutHalfAndFeed(1)
            }
        }
    }

    fun printWifiTmpCode(context: Context, noPrintModel: NoPrintModel) {
        val posPrinter =
            PrinterDeviceHelper.getWifiPosPrinter(noPrintModel.printerConfigInfo?.ipAddress)
        posPrinter?.apply {
            initializePrinter()
            val bitmap =
                Printer.initPrinterTempTableBitmap(
                    context,
                    noPrintModel.createTempTableResponse!!,
                    noPrintModel.printerConfigInfo
                )
            if (bitmap != null) {
                printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
                cutHalfAndFeed(1)
            }
        }
    }

    fun printWifiPrinterCreditRecordReport(context: Context, noPrintModel: NoPrintModel) {
        val posPrinter =
            PrinterDeviceHelper.getWifiPosPrinter(noPrintModel.printerConfigInfo?.ipAddress)
        posPrinter?.apply {
            initializePrinter()
            val bitmap =
                ReportPrinter.initCreditRecordBitmap(
                    context,
                    noPrintModel.printerConfigInfo,
                    noPrintModel.printerOrderStatus!!,
                    noPrintModel.repaymentResponse!!
                )
            if (bitmap != null) {
                printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
                cutHalfAndFeed(1)
            }
        }
    }

}

data class PrintTask(
    val noPrintModel: NoPrintModel,
    var priority: Int = DEFAULT_PRIORITY,
    var retryCount: Int = 0,
    val timestamp: Long = System.currentTimeMillis()
) //: Comparable<PrintTask>
{
    companion object {
        const val DEFAULT_PRIORITY = 5
        const val MAX_RETRY = 10
    }

//    override fun compareTo(other: PrintTask): Int {
//        return when {
//            priority != other.priority -> priority.compareTo(other.priority)
//            retryCount != other.retryCount -> other.retryCount.compareTo(retryCount)
//            else -> timestamp.compareTo(other.timestamp)
//        }
//    }
}