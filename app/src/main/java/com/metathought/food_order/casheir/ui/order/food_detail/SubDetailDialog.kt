package com.metathought.food_order.casheir.ui.order.food_detail

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.databinding.DialogSubDetailBinding
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.ui.adapter.SubFoodAdapter
import com.metathought.food_order.casheir.ui.order.MenuOrderViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/3/1920:20
 * @description
 */
@AndroidEntryPoint
class SubDetailDialog : DialogFragment() {

    private var binding: DialogSubDetailBinding? = null
    private val menuOrderViewModel: MenuOrderViewModel by viewModels()
    private var subFoodAdapter: SubFoodAdapter? = null
    private var confirmButtonListener: ((ArrayList<GoodsRequest>?) -> Unit)? = null
    private var subGoodListener: ((GoodsRequest) -> Unit)? = null
    private var addGoodListener: ((GoodsRequest) -> Unit)? = null
    private var diningStyle = 0

    companion object {
        private const val SUB_FOOD_LIST = "SUB_FOOD_LIST"
        private const val KEY_GOODS_ID = "KEY_GOODS_ID"
        fun showDialog(
            fragmentManager: FragmentManager,
            goodsId: String,
            diningStyleEnum: Int,
            confirmButtonListener: ((ArrayList<GoodsRequest>?) -> Unit),
            subListener: (GoodsRequest) -> Unit,
            addListener: (GoodsRequest) -> Unit,
        ) {
            var fragment = fragmentManager.findFragmentByTag(SUB_FOOD_LIST)
            if (fragment != null) return
            fragment = newInstance(
                goodsId,
                diningStyleEnum,
                confirmButtonListener = confirmButtonListener,
                subListener = subListener,
                addListener = addListener
            )

            fragment.show(fragmentManager, SUB_FOOD_LIST)
        }

        private fun newInstance(
            goodsId: String,
            diningStyleEnum: Int,
            confirmButtonListener: ((ArrayList<GoodsRequest>?) -> Unit),
            subListener: ((GoodsRequest) -> Unit),
            addListener: ((GoodsRequest) -> Unit)
        ): SubDetailDialog {
            val args = Bundle().apply {
                putString(KEY_GOODS_ID, goodsId)
            }
            val fragment = SubDetailDialog()
            fragment.confirmButtonListener = confirmButtonListener
            fragment.subGoodListener = subListener
            fragment.addGoodListener = addListener
            fragment.arguments = args
            fragment.diningStyle = diningStyleEnum
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSubDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        initData()
        initListener()
        initObserver()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.7).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initObserver() {
        menuOrderViewModel.goodsRequest.observe(viewLifecycleOwner) {
            it?.let { list ->
                binding?.apply {
                    if (list.isNotEmpty()) {
                        val title = "${list[0].goods?.name}"
                        tvDishedName.text = title
                    }
                }
                subFoodAdapter?.updateItems(list)
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            context?.let {
                subFoodAdapter = addGoodListener?.let { it1 ->
                    subGoodListener?.let { it2 ->
                        SubFoodAdapter(diningStyle, list = arrayListOf(), context = it,
                            addListener = it1, subListener = it2,
                            closeListener = {
                                dismissAllowingStateLoss()
                            },
                            reachLimitListener = { visible, limitNum ->
                                layoutReachLimitPurchase.tvReachLimit.text = getString(
                                    R.string.you_have_reached_the_maximum_quantity_limit_of,
                                    limitNum
                                )
                                activity?.runOnUiThread {
                                    layoutReachLimitPurchase.root.setVisibleGone(visible)
                                }

                            }
                        )
                    }
                }
                recyclerView.adapter = subFoodAdapter
            }
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            btnConfirm.setOnClickListener {
//                confirmButtonListener?.invoke(subFoodAdapter?.list)
                dismissAllowingStateLoss()
            }
        }
    }

    private fun initData() {
        val goodsId = arguments?.getString(KEY_GOODS_ID)
        menuOrderViewModel.getSubGoods(goodsId!!)
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }


}