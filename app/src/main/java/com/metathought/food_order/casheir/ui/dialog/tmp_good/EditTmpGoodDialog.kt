package com.metathought.food_order.casheir.ui.dialog.tmp_good


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.databinding.DialogEditTmpGoodBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.filter.CashierInputFilter
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TextInputUtil
import dagger.hilt.android.AndroidEntryPoint

/**
 * Edit tmp good dialog
 *
 * @constructor 添加/编辑临时菜
 */

@AndroidEntryPoint
class EditTmpGoodDialog : BaseDialogFragment() {

    private var binding: DialogEditTmpGoodBinding? = null

    private var positiveButtonListener: (() -> Unit)? = null

    private val tmpGoodViewModel: TmpGoodViewModel by viewModels()

    private var good: Goods? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditTmpGoodBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initListener()
        initObserver()
        initView()
    }

    private fun initView() {
        binding?.apply {
            if (good != null) {
                edtName.setText(good?.name ?: "")
                edtName.setSelection(edtName.length())

                edtPrice.setText((good?.sellPrice ?: 0).priceFormatTwoDigitZero3())
                edtPrice.setSelection(edtPrice.length())

                edtRemark.setText(good?.note ?: "")
                edtRemark.setSelection(edtRemark.length())
            } else {
                edtName.requestFocus()
            }


            btnYes.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (!tmpGoodViewModel.isRequest) {
                        tmpGoodViewModel.createTmpGood(
                            id = if (good != null) good?.id?.toLongOrNull() else null,
                            name = edtName.text.toString(),
                            sellPrice = edtPrice.text.toString().toBigDecimal(),
                            note = edtRemark.text.toString()
                        )
                    }
                }
            }
        }
    }

    private fun initObserver() {
        tmpGoodViewModel.uiCreateGoodModel.observe(viewLifecycleOwner) { state ->
            if (state.response is ApiResponse.Success) {
                positiveButtonListener?.invoke()
                dismissAllowingStateLoss()
            } else if (state.response is ApiResponse.Error) {
                if (!state.response.message.isNullOrEmpty())
                    Toast.makeText(requireContext(), state.response.message, Toast.LENGTH_LONG)
                        .show()
            }
        }

        tmpGoodViewModel.uiDeleteGoodModel.observe(viewLifecycleOwner) { state ->
            if (state.response is ApiResponse.Success) {
                positiveButtonListener?.invoke()
                dismissAllowingStateLoss()
            } else if (state.response is ApiResponse.Error) {
                if (!state.response.message.isNullOrEmpty())
                    Toast.makeText(requireContext(), state.response.message, Toast.LENGTH_LONG)
                        .show()
            }

        }
    }

    override fun onResume() {
        super.onResume()
    }


    private fun initListener() {
        binding?.apply {

            edtName.addTextChangedListener {
                checkBtn()
            }

            edtPrice.filters = arrayOf(CashierInputFilter(false, 10000000, false))
            edtPrice.addTextChangedListener {
                val result = TextInputUtil.amountInputUtil(edtPrice.toString())
                if (result != edtPrice.toString()) {
                    edtPrice.setText(result)
                    edtPrice.setSelection(result.length)
                }
                checkBtn()
            }

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnYes.setOnClickListener {
                dismissAllowingStateLoss()
            }

            topBar.getIconAfterTitle()?.setImageResource(R.drawable.ic_trash)

            topBar.getIconAfterTitle()?.isVisible = good != null
            topBar.setTitle(if (good != null) getString(R.string.edit_new_tmp_good) else getString(R.string.add_new_tmp_good))

            topBar.getIconAfterTitle()?.setOnClickListener {
                ConfirmDialog.showDialog(
                    parentFragmentManager,
                    content = getString(R.string.sure_to_delete_this_product),
                    positiveButtonTitle = getString(R.string.sure)
                ) {
                    tmpGoodViewModel.deleteTmpGood(good?.id?.toLongOrNull())
                }
            }
        }
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
    }

    private fun checkBtn() {
        binding?.apply {
            btnYes.setEnableWithAlpha(
                !edtPrice.text.isNullOrEmpty() && !edtName.text?.trim().isNullOrEmpty()
            )
        }
    }

    companion object {
        private const val TAG = "EditTmpGoodDialog"


        fun showDialog(
            fragmentManager: FragmentManager,
            good: Goods? = null,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(good, positiveButtonListener)
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentCancelOrderDialog(fragmentManager: FragmentManager): EditTmpGoodDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? EditTmpGoodDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? EditTmpGoodDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            good: Goods? = null,
            positiveButtonListener: (() -> Unit),
        ): EditTmpGoodDialog {
            val args = Bundle()
            val fragment = EditTmpGoodDialog()
            fragment.arguments = args
            fragment.good = good
            fragment.positiveButtonListener = positiveButtonListener
            return fragment
        }
    }
}
