package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.widget.RadioGroup
import com.metathought.food_order.casheir.R


/**
 *<AUTHOR>
 *@time  2024/10/28
 *@desc
 **/

class GridRadioGroup @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    RadioGroup(context, attrs) {
    //显示的列数
    private var columnNum = 2 //默认2列

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.GridRadioGroup)
        try {
            columnNum = typedArray.getInt(R.styleable.GridRadioGroup_columnNum, 2)
        } finally {
            typedArray.recycle()
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val itemWidth = (widthSize - (columnNum + 1)) / columnNum
        val childCount = childCount
        var itemHeight = 0
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            child.measure(
                MeasureSpec.makeMeasureSpec(itemWidth-10, MeasureSpec.EXACTLY),
                MeasureSpec.UNSPECIFIED
            )
            itemHeight = child.measuredHeight
        }
        val rows =
            if (childCount % columnNum == 0) childCount / columnNum else childCount / columnNum + 1
        val heightSize = rows * itemHeight + (rows + 1)
        setMeasuredDimension(widthMeasureSpec, heightSize)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        val childCount = childCount
        var rows = 0
        for (i in 0 until childCount) {
            val view = getChildAt(i)
            val width = view.measuredWidth
            val height = view.measuredHeight

            val yu = i % columnNum
            if (i >= columnNum - 1 && yu == 0) {
                rows++
            }

            var cl = yu + 1 + yu * width+10
            var ct = rows + 1 + rows * height+10
            var cr = yu + 1 + (yu + 1) * width+10
            var cb = rows + 1 + (rows + 1) * height+10

            view.layout(cl, ct, cr, cb)
        }
    }

    /**
     * 设置列数
     * @param columnNum
     */
    fun setColumnNum(columnNum: Int) {
        this.columnNum = columnNum
    }
}