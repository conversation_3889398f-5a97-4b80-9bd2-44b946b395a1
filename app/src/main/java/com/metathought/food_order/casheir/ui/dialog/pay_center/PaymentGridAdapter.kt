package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel

class PaymentGridAdapter(
    private var items: List<OfflineChannelModel>,
    private val onItemClick: (OfflineChannelModel, Int) -> Unit
) : RecyclerView.Adapter<PaymentGridAdapter.GridViewHolder>() {

    private var selectItems: MutableList<OfflineChannelModel> = mutableListOf()

    class GridViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val icon: ImageView = itemView.findViewById(R.id.gridIcon)
        val title: TextView = itemView.findViewById(R.id.gridTitle)
        val container: View = itemView
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_grid_payment, parent, false)
        return GridViewHolder(view)
    }

    override fun onBindViewHolder(holder: GridViewHolder, position: Int) {
        val item = items[position]

//        holder.icon.setImageResource(item.iconRes)
        holder.title.text = item.channelsName

        // 设置选中状态
        if (selectItems.firstOrNull { it.id == item.id } == null) {
            holder.container.setBackgroundResource(R.drawable.background_white_radius_4dp)
        } else {
            holder.container.setBackgroundResource(R.drawable.background_border_primary_4dp)
        }

        holder.container.setOnClickListener {
            onItemClick(item, position)
        }
    }

    override fun getItemCount(): Int = items.size

    fun updateData(newItems: List<OfflineChannelModel>) {
        items = newItems
        notifyDataSetChanged()
    }

    fun setSelectItems(selectItems: OfflineChannelModel) {
        this.selectItems.add(selectItems)
        notifyDataSetChanged()
    }
}
