package com.metathought.food_order.casheir.constant

enum class FeatureMenuEnum(val id: Int) {
    TABLE(0),
    ORDER(1),
    PENDING_ORDER(2),
    ORDER_MANAGEMENT(3),
    MEMBER_MANAGEMENT(4),
    STORE_DASHBOARD(5),
    PRINTER(6),
    RECEIVING_ORDER(7),
    OPEN_CASH_BOX(8),
    NOTICE(9),
}

enum class TableStatusEnum(val id: Int) {
    //所有
    ALL(0),

    //空闲
    AVAILABLE(1),
    RESERVED(2),
    OCCUPIED(3),

    //预结
    TO_BE_PAID(4),
}

enum class DiningStyleEnum(val id: Int) {
    DINE_IN(0),
    TAKE_AWAY(1),
    PRE_ORDER(2),
    TAKE_OUT(3),    //外卖
}


enum class PayTypeEnum(val id: Int) {
    ONLINE_PAYMENT(1),
    CASH_PAYMENT(2),
    USER_BALANCE(3),
    PAY_OTHER(4),
    MIXED_PAYMENT(5),
    CREDIT(6),
    PAY_AFTER(7),
//    PAY_CREDIT(8),  //挂账
}

enum class SourcePlatformEnum(val id: Int) {
    H5(1),
    Employee(2),
    Kiosk(3),
    Cashier(4)
}

enum class OrderedStatusEnum(val id: Int) {
    All(-1),
    UNPAID(1),
    PAID(2),
    PARTIAL_REFUND(3),
    FULL_REFUND(4),
    CANCEL_ORDER(5),
    BE_CONFIRM(6),
    PREORDER(7),
    TO_BE_CONFIRM(8),

    /**
     * 挂账-未支付
     */
    CREDIT_UNPAID(10),

    /**
     * 挂账-已支付
     */
    CREDIT_PAID(11),
}

enum class OrderedStatusMergeEnum(val id: Int) {
    All(-1),
    UNPAID_CONFIRM(1),//UNPAID+CONFIRM
    PREORDER(2),
    PAID(3),
    REFUNDS(4),//PARTIALREFUND+REFUNDS
    CANCEL_ORDER(5),
}

enum class SpecificationTypeEnum(val id: Int) {
    FEATURE(1), //SINGLE SELECT & NO PRICE
    SPECIFICATION(2), //SINGLE SELECT
    INGREDIENT(3), //MULTIPLE SELECT
}

enum class PrintTemplateTypeEnum(val id: Int) {
    KITCHEN(1),
    DINE_IN(2),
    TAKE_AWAY(3),
    PRE_ORDER(4),
    KOISK(5),
    CHECKOUT_RECEIPT(6),
    PRE_CHECKOUT_RECEIPT(7),
    PRODUCT_REPORT(8),
    PAYMENT_METHOD_REPORT(9),
    LABEL(10),
    SHIFT_HANDOVER(11),
    TABLE_REPORT(12),
    STORE_PROFIT_REPORT(13)
}

enum class BalanceTypeEnum(val id: Int) {
    All(-1),
    PAID(2),
    REFUND(3),
    TOP_UP(4),
}

enum class BalanceStatusEnum(val id: Int) {
    SUCCESS(1),
    FAIL(2),
    CANCEL(3),
}

enum class PaymentChannel(val id: Int) {
    ONLINE(-1),
    BALANCE(-2),
    CASH(1),
}

enum class OfflinePaymentChannelEnum(val id: Int) {
    CASH(1),
    U_PAY(2),
    ABA(3),
    WING_BANK(4),
    ACLEDA(5),
    HUIONE_PAY(6),
    CREDIT_CARD(7),
    AMK_PAY(8),
    PRINCE_BANK(9),
    KB_PRASAC_BANK(10),
    DGB_BANK(11),
    ACCOUNTS_RECEIVABLE(12),//挂账
}

enum class PricingMethodEnum(val id: Int, val desc: String, val unit: String) {
    WHOLE_UNIT(0, "整份计费", ""),
    PER_KILOGRAM(1, "每公斤", "KG"),
    PER_POUND(2, "每磅", "LB"),
    PER_LITER(3, "每升", "L"),
    PER_OUNCE(4, "每盎司", "OZ"),
    PER_GALLON(5, "每加仑", "GAL"),
    PER_GRAM(6, "每克", "G"),
    PER_TIME(7, "时价菜", "");
}

enum class ChartTimeType(val type: String) {
    TODAY("1"),
    WEEK("2"),
    MONTH("3"),
    QUARTER("4"),
    YEAR("5"),
}


enum class PermissionEnum(val type: String) {
    //线下支付权限
    OFFLINE_PAY("1"),

    //退款权限 反结账
    REFUND("2"),

    //查看数据权限
    VIEW_DATA("3"),

    //门店管理
    STORE_MANAGE("7"),

    //工作交接日志
    WORK_HANDOVER_LOG("10"),

    //取消订单
    CANCEL_ORDER("11"),

    //退菜权限
    CANCEL_GOOD("12"),

    //工作交接
    WORK_HANDOVER("13"),

    //选择指定折扣减免
    SELECT_DISCOUNT("14"),

    //自定义折扣减免
    CUSTOMIZE_DISCOUNT("15"),

    //会员充值权限
    RECHARGE("17"),

    //挂账权限
    CREDIT("18"),
}


enum class CouponTypeEnum(val type: String) {
    //无门槛立减券
    NOTHRESHOLD_LJ("NOTHRESHOLD_LJ"),

    //无门槛折扣券
    NOTHRESHOLD_ZK("NOTHRESHOLD_ZK"),

    //无门槛赠送券
    NOTHRESHOLD_ZS("NOTHRESHOLD_ZS"),

    //有门槛立减券
    THRESHOLD_LJ("THRESHOLD_LJ"),

    //有门槛折扣券
    THRESHOLD_ZK("THRESHOLD_ZK"),

    //有门槛赠送券
    THRESHOLD_ZS("THRESHOLD_ZS"),
}

enum class CouponUsageTypeEnum(val type: String) {
    //所有商品
    ALL_GOODS("ALL_GOODS"),

    //部分商品
    PARTIAL_GOODS("PARTIAL_GOODS"),

    //会员充值
    TOP_UP("TOP_UP")
}

enum class AcceptOrderedStatusEnum(val id: Int) {
    /**
     * 待接单
     */
    WAIT_ACCEPT(0),

    /**
     * 已接单
     */
    ACCEPTED(1),

    /**
     * 已拒绝
     */
    CANCEL_ACCEPTED(2),
}

enum class LocalPrinterEnum(val id: Int) {
    /**
     * 小票
     */
    TICKET_PRINTER(1),

    /**
     * 标签打印机
     */
    LABEL_PRINTER(2),

}


enum class PrinterFontSizeEnum(val id: Int) {
    /**
     * 小
     */
    SMALL(1),

    /**
     * 标准
     */
    STANDARD(2),

    /**
     * 大
     */
    BIG(3),

    /**
     *  更大
     */
    BIGGER(4),
}


enum class GoodTypeEnum(val id: Int) {
    NORMAL(0),
    TEMPORARY(1)
}

enum class SingleDiscountType(val id: Int) {
    PERCENTAGE(1), //折扣
    FIXED_AMOUNT(2), //减免
    MODIFY_PRICE(3)
}

enum class WholeDiscountType(val id: Int) {
    PERCENTAGE(1),  //折扣百分比
    FIXED_AMOUNT(2),  //减免
}

enum class WholeReduceType(val id: Int) {
    NONE(0),  //没有折扣
    SALE_PRICE(1),  //销售价
    VIP_PRICE(2),  //会员
    SALE_VIP_PRICE(3),  //销售价+会员价  没用了
    REDUCE_ACTIVITY(4),  //配置折扣活动  如果是这个 去对应折扣信息model 里面获取 折扣类型
    CUSTOMIZE_USD(5),  //USD自定义整单减免
    CUSTOMIZE_KHR(6),  //KHR自定义整单减免
}

enum class PrintTicketType(val id: Int) {
    NONE(0),
    NORMAL(1),
//    TAX_TICKET(2),
//    NORMAL_TAX_TICKET(3)
}

/**
 * 厨打小票类型
 *
 * @property id
 * @constructor Create empty Kitchen check ticket type
 */
enum class KitchenCheckTicketType(val id: Int) {
    NONE(0),
    ALL(1),
    PART(2),
}

enum class SceneEnum(val id: Int) {
    MENU(0),
    ORDER(1),
    TOPUP_BALANCE(2),//充值余额
    MEMBER_CREDIT(3),//成员挂账
}

/**
 * 打印报表枚举
 *
 * @property id
 * @constructor Create empty Printeport enum
 */
enum class ReportExportEnum(val id: Int) {
    SALES_REPORT(1),//销售报表
    PRODUCT_REPORT(2), //商品报表
    OFFLINE_CHANNEL_REPORT(3), //支付渠道报表
}

enum class ReportFileType(val id: Int) {
    EXCEL(2),//excel
    PDF(1), //pdf
}

/**
 * 日期类型
 *
 * @property id
 * @constructor Create empty Report date type
 */
enum class ReportDateType(val id: Int) {
    CUSTOMIZE(0),//今天
    TODAY(1),//今天
    YESTERDAY(2), //昨天
    LAST_WEEK(3), //昨天
    LAST_MONTH(4), //昨天
}

/**
 * 修改店铺会员账户类型
 */
enum class UpdateConsumerType(val id: Int) {
    NAME(1),
    PHONE(2),
}


enum class MerchantNameDisplayModeEnum(val id: String) {
    SINGLE_LANGUAGE("single_language"),
    MULTI_LANGUAGE_HORIZONTAL("multi_lang_horizontal"),
    MULTI_LANGUAGE_VERTICAL("multi_lang_vertical"),
}


enum class WholeDiscountCalculationTypeEnum(val id: Int) {
    INCLUDE_VAT(1),  //
    WITHOUT_VAT(2),  //不需要包含vat
}