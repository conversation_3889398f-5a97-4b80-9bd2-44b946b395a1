package com.metathought.food_order.casheir.ui.dialog.single_discount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogSingleDiscountDetailBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


/**
 *<AUTHOR>
 *@time  2025/02/28
 *@desc 单品折扣详情
 **/
@AndroidEntryPoint
class SingleDiscountDetailDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "SingleDiscountDetailDia"


        fun showDialog(
            fragmentManager: FragmentManager,
            cartGood: GoodsRequest? = null,
            orderGood: OrderedGoods? = null,
            orderedInfoResponse: OrderedInfoResponse? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(cartGood, orderGood, orderedInfoResponse)

            fragment.show(fragmentManager, TAG)
        }


        private fun newInstance(
            cartGood: GoodsRequest? = null,
            orderGood: OrderedGoods? = null,
            orderedInfoResponse: OrderedInfoResponse? = null,
        ): SingleDiscountDetailDialog {
            val fragment = SingleDiscountDetailDialog()
            fragment.cartGood = cartGood
            fragment.orderGood = orderGood
            fragment.orderedInfoResponse = orderedInfoResponse
            return fragment
        }
    }

    private var cartGood: GoodsRequest? = null
    private var orderGood: OrderedGoods? = null
    private var orderedInfoResponse: OrderedInfoResponse? = null

    private var binding: DialogSingleDiscountDetailBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSingleDiscountDetailBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {

    }

    private fun initView() {
        binding?.apply {
            if (cartGood != null) {
                tvReason.text = cartGood?.singleDiscountGoods?.remark
                tvReason.isVisible = !cartGood?.singleDiscountGoods?.remark.isNullOrEmpty()
                tvOldPrice.text =
                    cartGood?.totalDiscountPriceWithoutSingleDiscount()?.priceFormatTwoDigitZero2()
                tvNewPrice.text =
                    cartGood?.getPriceAfterSingleDiscount()?.priceFormatTwoDigitZero2()

                tvVipNewPrice.isVisible = cartGood?.isShowVipPrice() == true
                tvVipOldPrice.isVisible = cartGood?.isShowVipPrice() == true
                tvVipOldPrice.text =
                    cartGood?.totalVipPriceWithoutSingleDiscount()?.priceFormatTwoDigitZero2()
                tvVipNewPrice.text =
                    cartGood?.getVipPriceAfterSingleDiscount()?.priceFormatTwoDigitZero2()
            }

            if (orderGood != null) {
                tvReason.text = orderGood?.singleItemDiscount?.remark
                tvReason.isVisible = !orderGood?.singleItemDiscount?.remark.isNullOrEmpty()
                if (orderedInfoResponse?.isAfterPayStatus() == true) {
                    if (orderedInfoResponse?.isUseDiscount == true) {
                        tvOldPrice.text =
                            orderGood?.totalVipPrice()?.priceFormatTwoDigitZero2()
                        tvNewPrice.text =
                            orderGood?.totalVipPriceWithSingleDiscount()?.priceFormatTwoDigitZero2()
                    } else {
                        tvOldPrice.text = orderGood?.totalPrice()
                            ?.priceFormatTwoDigitZero2()
                        tvNewPrice.text =
                            orderGood?.totalPriceWithSingleDiscount()?.priceFormatTwoDigitZero2()
                    }

                    tvVipNewPrice.isVisible = false
                    tvVipOldPrice.isVisible = false

                } else {
                    tvOldPrice.text = orderGood?.totalPrice()?.priceFormatTwoDigitZero2()
                    tvNewPrice.text =
                        orderGood?.totalPriceWithSingleDiscount()?.priceFormatTwoDigitZero2()

                    tvVipNewPrice.isVisible = orderGood?.isShowVipPrice() == true
                    tvVipOldPrice.isVisible = orderGood?.isShowVipPrice() == true
                    tvVipOldPrice.text =
                        orderGood?.totalVipPrice()?.priceFormatTwoDigitZero2()
                    tvVipNewPrice.text =
                        orderGood?.totalVipPriceWithSingleDiscount()?.priceFormatTwoDigitZero2()
                }

            }
        }
    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener { dismissAllowingStateLoss() }
        }
    }

}