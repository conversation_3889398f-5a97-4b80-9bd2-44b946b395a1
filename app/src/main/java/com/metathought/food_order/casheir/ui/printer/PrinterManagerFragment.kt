package com.metathought.food_order.casheir.ui.printer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout.LayoutParams
import android.widget.PopupWindow
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.databinding.FragmentPrinterManagerBinding
import com.metathought.food_order.casheir.databinding.PopupPrinterMarchineTypeBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.ui.adapter.PrinterManagerListAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import dagger.hilt.android.AndroidEntryPoint
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber


@AndroidEntryPoint
class PrinterManagerFragment : BaseFragment() {

    companion object {
        fun newInstance() = PrinterManagerFragment()
    }

    private var _binding: FragmentPrinterManagerBinding? = null
    private val binding get() = _binding
    private val viewModel: PrinterManagerViewModel by viewModels()
    private var adapter: PrinterManagerListAdapter? = null
    private var isBiggerWidth = false  //用来判断屏幕宽度是否远大于高度
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPrinterManagerBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initEventBus()
        initView()
        initListener()
        initObserver()


    }


    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    private fun initObserver() {

    }


    private fun initView() {
        Timber.e("屏幕宽11  ${DisplayUtils.getScreenWidthPixels(requireActivity())}")
        Timber.e("屏幕高11  ${DisplayUtils.getScreenHeightPixels(requireActivity())}")

        Timber.e("屏幕宽22  ${DisplayUtils.getScreenWidthPixelsFromResource(requireActivity())}")
        Timber.e("屏幕高22  ${DisplayUtils.getScreenHeightPixelsFromResource(requireActivity())}")
        val width = DisplayUtils.getScreenWidthPixels(requireActivity()).toDouble()
        val height = DisplayUtils.getScreenHeightPixels(requireActivity()).toDouble()
        if (width / height > 1.6) {
            isBiggerWidth = true
        }

        binding?.apply {
            if (isBiggerWidth) {
//                Timber.e("修改 比例")
                (tvPrinterName.layoutParams as LayoutParams).weight = 2f
                (tvPrinterType.layoutParams as LayoutParams).weight = 2f
                (tvPrinterDevice.layoutParams as LayoutParams).weight = 2f
                (tvBindingTime.layoutParams as LayoutParams).weight = 1.6f
                (tvReceiptSize.layoutParams as LayoutParams).weight = 1.2f
                (tvTemplateConfigured.layoutParams as LayoutParams).weight = 2f
                (tvConnectionStatus.layoutParams as LayoutParams).weight = 1.2f
//                (tvOperate.layoutParams as LayoutParams).weight = 1f
            }

            adapter = PrinterManagerListAdapter(
                mutableListOf(),
                isBiggerWidth,
                { view, printerConfigInfo ->
//                    showPopupWindowLanguage(view, printerConfigInfo)
                },
                {
                    ConfirmDialog.showDialog(
                        parentFragmentManager,
                        title = getString(R.string.show_to_connect_wifi_printer),
                        content = getString(R.string.m_pos_wifi_printer_colon, it?.name),
                        positiveButtonTitle = getString(R.string.connect),
                        negativeButtonTitle = getString(R.string.cancel)
                    ) {
                        PrinterDeviceHelper.connectWifiPrinter(it?.ipAddress)
                    }
                })

            rvList.adapter = adapter

            adapter?.replaceData(PrinterDeviceHelper.getPrinterList())

            updateView()
        }
    }

    override fun onLoad() {
        super.onLoad()
        context?.let {
            viewModel.getPrinterInfoList()
        }

    }


    private fun initListener() {
        binding?.apply {

        }
    }

    fun updateView() {
        binding?.apply {
            layoutMain.isVisible = PrinterDeviceHelper.getPrinterList().isNotEmpty()

            layoutEmptyDetail.root.isVisible = PrinterDeviceHelper.getPrinterList().isEmpty()
            layoutEmptyDetail.tvEmptyText.text = getString(R.string.hint_printer_empty)
        }
    }


    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
//        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {
            SimpleEventType.UPDATE_PRINTER_MANAGER_LIST -> {
                requireActivity().runOnUiThread {
                    Timber.e("PrinterHelper.getPrinterList()   ${PrinterDeviceHelper.getPrinterList().size}")
                    adapter?.replaceData(PrinterDeviceHelper.getPrinterList())
                    updateView()

                }

            }

            SimpleEventType.UPDATE_USB_PRINT_INFO -> {
                requireActivity().runOnUiThread {
                    Timber.e("PrinterHelper.getPrinterList()   ${PrinterDeviceHelper.getPrinterList().size}")
                    adapter?.replaceData(PrinterDeviceHelper.getPrinterList())
                    updateView()

                }
            }

            else -> {

            }

        }
    }


    private fun showPopupWindowLanguage(anchorView: View, printerConfigInfo: PrinterConfigInfo?) {
        val popupView = PopupPrinterMarchineTypeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupView.tvTicket.setOnClickListener {

            popupWindow.dismiss()

        }
        popupView.tvLabel.setOnClickListener {
            popupWindow.dismiss()

        }

//        PrinterUsbDeviceHelper.isLabelPrinter(
//            printerConfigInfo?.usbDevice?.vendorId,
//            printerConfigInfo?.usbDevice?.productId
//        )
    }


    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }


    //Socket
    override fun onResume() {

        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        super.onResume()
    }


    private fun wsHandel(event: WebSocket.Event) {

    }


}