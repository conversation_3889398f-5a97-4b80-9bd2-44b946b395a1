package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.member.TopUpCanUseCouponRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.databinding.ItemDialogCouponListBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import timber.log.Timber
import kotlin.math.min


/**
 *<AUTHOR>
 *@time  2024/8/27
 *@desc
 **/

class MemberCouponListAdapter(
    val list: MutableList<CouponModel?>,
    val isShowVip: Boolean? = false,
    val isHasUnWeight: Boolean? = false,
    val topUpCanUseCouponRequest: TopUpCanUseCouponRequest? = null,
    val onItemClickListener: (CouponModel?) -> Unit,
) : RecyclerView.Adapter<MemberCouponListAdapter.MemberCouponListViewHolder>() {
    private var selectIndex = -1

    inner class MemberCouponListViewHolder(val binding: ItemDialogCouponListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: CouponModel?, position: Int) {
            itemView.context.let { context ->

                resource.let {
                    binding.apply {
                        val rule = resource?.templateSDK?.rule

                        tvAmount.text = resource?.getDiscountDesc(context)

                        tvLimitAmount.isVisible = resource?.isThresholdCoupon() == true
                        tvLimitAmount.text = resource?.getThresholdDesc(context)

                        tvCouponName.text = resource?.templateSDK?.name
                        tvEffectiveTime.text = resource?.getEffectiveTimeRange(context)
                        tvEffectiveTime.isVisible = tvEffectiveTime.text.isNotEmpty()


                        if (isHasUnWeight == true) {
                            tvPrice.text = binding.root.context.getString(R.string.to_be_confirmed)
                            tvVipPrice.isVisible = false
                        } else {
                            if (resource?.isTopUp() == true) {
                                //如果没填充值金额 显示默认的优惠券金额 如果有填金额 展示小的那个金额
                                val price =
                                    if (topUpCanUseCouponRequest?.addNum == null) resource?.couponPrice else min(
                                        resource?.couponPrice ?: 0L,
                                        topUpCanUseCouponRequest.addNum.toLong()
                                    )
                                tvPrice.text = "-".plus(price?.priceFormatTwoDigitZero2())

                            } else {
                                tvPrice.text =
                                    "-".plus(resource?.couponPrice?.priceFormatTwoDigitZero2())

                            }
                            tvPrice.isVisible = resource?.isValid == true

                            tvVipPrice.text =
                                " -".plus(resource?.vipCouponPrice?.priceFormatTwoDigitZero2())

                            tvVipPrice.isVisible = isShowVip == true && resource?.isVipValid == true
                        }

                        if (resource?.isVipValid == false && resource?.isValid == false) {
                            tvPrice.text =
                                context.getString(R.string.this_coupon_not_support)
                            tvPrice.isVisible = true
                        }


                        if (resource?.isGiftGoodCoupon(context) == true) {
                            llPreferential.isVisible = false
                            tvGiftGoodNum.isVisible = true
                            if (resource.templateSDK?.rule?.discount?.giveGoods.isNullOrEmpty()) {
                                //没有赠送商品的时候
                                tvGiftGoodNum.setEnable(false)
                                tvGiftGoodNum.text =
                                    context.getString(R.string.free_item_removed_by_merchant)
                                tvGiftGoodNum.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    null,
                                    null
                                )
                            } else {
                                tvGiftGoodNum.setEnable(true)
                                tvGiftGoodNum.text = context.getString(
                                    R.string.gift_products_num,
                                    "${resource.templateSDK?.rule?.discount?.giveGoods?.size ?: 0}"
                                )
                                //赠送商品
                                var endDrawable = ContextCompat.getDrawable(
                                    context,
                                    R.drawable.icon_arrow_down
                                )
                                if (resource.isGiftGoodsExpand == true) {
                                    endDrawable = ContextCompat.getDrawable(
                                        context,
                                        R.drawable.icon_arrow_up
                                    )
                                    rvGiftGoods.isVisible = true
                                    rvGiftGoods.adapter =
                                        CouponGoodListAdapter(
                                            resource.templateSDK?.rule?.discount?.giveGoods
                                                ?: listOf()
                                        )
                                } else {
                                    rvGiftGoods.isVisible = false

                                }
                                tvGiftGoodNum.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    endDrawable,
                                    null
                                )
                            }
                        } else {
                            llPreferential.isVisible = true
                            tvGiftGoodNum.isVisible = false
                            rvGiftGoods.isVisible = false
                        }


                        //适用规则
                        if (resource?.isRuleExpand == true) {
                            tvRuleContent.text = resource.getRuleDesc()
                            tvRuleContent.isVisible = true
                            tvRuleTitle.text = context.getString(R.string.use_rule)
                            ivRuleExpand.setImageDrawable(
                                ContextCompat.getDrawable(
                                    context, R.drawable.icon_arrow_up
                                )
                            )
                        } else {
//                            tvRuleTitle.text = context.getString(R.string.use_rule_colon)
//                                .plus(resource?.getRuleDesc())
                            tvRuleTitle.text = "${context.getString(R.string.use_rule_colon)} ${
                                resource?.getRuleDesc()
                            }"
                            tvRuleContent.text = ""
                            tvRuleContent.isVisible = false
                            ivRuleExpand.setImageDrawable(
                                ContextCompat.getDrawable(
                                    context, R.drawable.icon_arrow_down
                                )
                            )
                        }

                        llRule.isVisible = !resource?.getRuleDesc().isNullOrEmpty()





                        ivGoodsExpand.isVisible = resource?.isPartialGoods() == true
                        //适用商品
                        if (resource?.isGoodsExpand == true) {
                            rvGoods.isVisible = true
                            tvGoodsTitle.text = context.getString(
                                R.string.goods_num_available,
                                "(${resource.templateSDK?.usageGoods?.size ?: 0})"
                            )
                            ivGoodsExpand.setImageDrawable(
                                ContextCompat.getDrawable(
                                    context, R.drawable.icon_arrow_up
                                )
                            )

                            rvGoods.adapter =
                                CouponGoodListAdapter(resource.templateSDK?.usageGoods ?: listOf())

                        } else {
                            rvGoods.isVisible = false
                            llGoods.setEnable(true)
                            if (resource?.isPartialGoods() == true) {
                                //部分适用
                                if (resource?.templateSDK?.usageGoods.isNullOrEmpty()) {
                                    //适用商品被删完了
                                    llGoods.setEnable(false)
                                    llPreferential.isVisible = false
                                    ivGoodsExpand.isVisible = false
                                    tvGoodsTitle.text = "${
                                        context.getString(
                                            R.string.goods_num_available_colon,
                                            ""
                                        )
                                    } ${context.getString(R.string.no_available_items)}"
                                } else {
                                    ivGoodsExpand.isVisible = true
                                    tvGoodsTitle.text = "${
                                        context.getString(
                                            R.string.goods_num_available_colon,
                                            "(${resource?.templateSDK?.usageGoods?.size ?: 0})"
                                        )
                                    } ${resource?.getGoodsListDesc()}"
                                    ivGoodsExpand.setImageDrawable(
                                        ContextCompat.getDrawable(
                                            context, R.drawable.icon_arrow_down
                                        )
                                    )
                                }
                            } else if (resource?.isAllGoods() == true) {
                                //全部适用
                                tvGoodsTitle.text = "${
                                    context.getString(
                                        R.string.goods_num_available_colon,
                                        ""
                                    )
                                } ${context.getString(R.string.all_products)}"
                                llGoods.setEnable(false)
                            } else if (resource?.isTopUp() == true) {
                                //会员充值
                                tvGoodsTitle.text = "${
                                    context.getString(
                                        R.string.scope_of_application,
                                    )
                                } ${context.getString(R.string.member_top_up)}"
                                tvVipPrice.isVisible = false
                                llGoods.setEnable(false)
                            }
                        }


                        checkbox.isSelected = resource?.isSelected == true

                        llTop.setOnClickListener {
                            selectItem(position)
                        }

                        llGoods.setOnClickListener {
                            if (resource?.isAllGoods() == true) {
                                return@setOnClickListener
                            }
                            list[position]?.isGoodsExpand =
                                !(list[position]?.isGoodsExpand ?: false)
                            notifyItemChanged(position)
                        }


                        llRule.setOnClickListener {
                            list[position]?.isRuleExpand = !(list[position]?.isRuleExpand ?: false)
                            notifyItemChanged(position)
                        }



                        tvGiftGoodNum.setOnClickListener {
                            list[position]?.isGiftGoodsExpand =
                                !(list[position]?.isGiftGoodsExpand ?: false)
                            notifyItemChanged(position)
                        }
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberCouponListViewHolder {
        val itemView =
            ItemDialogCouponListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MemberCouponListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: MemberCouponListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }

    fun selectItem(position: Int) {
        Timber.e("position:${position}   selectIndex:${selectIndex}  isSelected:${list[position]?.isSelected}")
        //如果点的是当前选择的
        if (position == selectIndex) {
            list[position]?.isSelected = !(list[position]?.isSelected ?: false)
            Timber.e(" isSelected:${list[position]?.isSelected}")
            if (list[position]?.isSelected == false) {
                //取消勾选
                selectIndex = -1
            }
            notifyItemChanged(position)
        } else {
            list.forEachIndexed { index, couponModel ->
                if (couponModel?.isSelected == true) {
                    list[index]?.isSelected = false
                    notifyItemChanged(index)
                }
            }
            selectIndex = position
            list[position]?.isSelected = true
            notifyItemChanged(position)
        }


    }

    fun selectItemByCouponId(couponId: Long?): Int {
        val index = list.indexOfFirst { it?.id == couponId }
        if (index != -1 && list.size > index) {
            list[index]?.isSelected = true
            selectIndex = index
            notifyItemChanged(index)
        }
        return index
    }

    fun getSelectItm(): CouponModel? {
        if (selectIndex == -1) {
            return null
        }

        return this.list[selectIndex]
    }

    fun replaceData(list: MutableList<CouponModel>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: List<CouponModel>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }
}
