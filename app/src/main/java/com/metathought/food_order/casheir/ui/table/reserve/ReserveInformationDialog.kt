package com.metathought.food_order.casheir.ui.table.reserve


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.model.base.response_model.order.Customer
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.DialogReserveInformationBinding
import com.metathought.food_order.casheir.extension.formatDateWithoutSecond


class ReserveInformationDialog : DialogFragment() {
    private var binding: DialogReserveInformationBinding? = null
    private var cancelButtonListener: (() -> Unit)? = null
    private var orderButtonListener: (() -> Unit)? = null
    private var printerButtonListener: (() -> Unit)? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogReserveInformationBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }


    private fun initData() {
        val content = Gson().fromJson(arguments?.getString(CONTENT), TableResponseItem::class.java)
        binding?.apply {
            tvTableID.text = content.name
            val customer = Gson().fromJson<Customer>(content.customerJson, Customer::class.java)
            customer?.let {
                tvCustomerName.text = customer.name
                tvDiningTime.text = customer?.diningTime?.formatDateWithoutSecond()

                tvPeople.text = customer.diningNumber.toString().plus(" ")
                tvPhoneNumber.text = customer.getMobilePhoneDisplay()
            }
        }
    }


    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }
            btnCancel.setOnClickListener {
                cancelButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
            btnOrder.setOnClickListener {
                orderButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
            cardPrinter.setOnClickListener {
                //打印临时桌码 Print temporary table code
                printerButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        private const val RESERVE_DIALOG = "RESERVE_DIALOG"
        private const val CONTENT = "CONTENT"


        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            orderButtonListener: (() -> Unit),
            cancelButtonListener: (() -> Unit),
            printerButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(RESERVE_DIALOG)
            if (fragment != null) return
            fragment = newInstance(
                iOrderListener = orderButtonListener,
                iCancelListener = cancelButtonListener,
                iPrinterListener = printerButtonListener,
                content = content
            )
            fragment.show(fragmentManager, RESERVE_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(RESERVE_DIALOG) as? ReserveInformationDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iCancelListener: (() -> Unit),
            iOrderListener: (() -> Unit),
            iPrinterListener: (() -> Unit),
            content: String? = null
        ): ReserveInformationDialog {
            val args = Bundle()
            args.putString(CONTENT, content)
            val fragment = ReserveInformationDialog()
            fragment.cancelButtonListener = iCancelListener
            fragment.orderButtonListener = iOrderListener
            fragment.printerButtonListener = iPrinterListener
            fragment.arguments = args
            return fragment
        }
    }

}
