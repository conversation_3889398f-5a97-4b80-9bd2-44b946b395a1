package com.metathought.food_order.casheir.data.model.base.response_model.customer


import com.google.gson.annotations.SerializedName

data class CustomerMemberResponse(
    @SerializedName("accountId")
    val accountId: String?,
    @SerializedName("balance")
    val balance: Long?,
    @SerializedName("nickName")
    val nickName: String?,
    @SerializedName("paymentMethod")
    val paymentMethod: String?,
    @SerializedName("telephone")
    val telephone: String?
)