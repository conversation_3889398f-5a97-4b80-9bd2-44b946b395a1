package com.metathought.food_order.casheir.ui.dialog.print_report

import android.content.Context
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.export.SalesOrdersExportRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.GoodClassificationModel
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.SaleReportResponse
import com.metathought.food_order.casheir.extension.formatDate3
import com.metathought.food_order.casheir.extension.formatDate4
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PrintReportViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    private val _uiSaleModel = MutableLiveData<UISaleModel>()
    val uiSaleModel get() = _uiSaleModel


    private val _uiSaleItemsModel = MutableLiveData<UISaleItemsModel>()
    val uiSaleItemsModel get() = _uiSaleItemsModel

    private val _uiPayChannelState = MutableLiveData<UIPayChannelModel>()
    val uiPayChannelState get() = _uiPayChannelState

    private val _uiClassificationModel = MutableLiveData<UIClassificationModel>()
    val uiClassificationModel get() = _uiClassificationModel

    /**
     * 商品报表信息
     *
     * @param context
     * @param startTime
     * @param endTime
     */
    fun printProductReport(
        context: Context,
        startTime: String,
        endTime: String,
        keyWord: String,
        classificationList: List<GoodClassificationModel>,
        orderByColumn: String
    ) {
        viewModelScope.launch {
            uiSaleItemsModel.postValue(UISaleItemsModel(ApiResponse.Loading))
            var groupId = classificationList.joinToString(
                separator = ",",
            ) { it.groupId ?: "" }
            val response = repository.getSummarySalesReportPrint(
                startTime.formatDate4(),
                endTime.formatDate4(),
                keyWord,
                groupId, orderByColumn
            )
            uiSaleItemsModel.postValue(UISaleItemsModel(response))
//            if (response is ApiResponse.Success) {
//                if (!response.data.data?.salesItemOrdersDetailList.isNullOrEmpty()) {
//                    Printer.printPrinterProductReport(
//                        context,
//                        response.data
//                    )
//                    uiState.postValue(UIModel(true, true))
//                } else {
//                    Toast.makeText(
//                        context,
//                        context.getString(R.string.empty_data),
//                        Toast.LENGTH_SHORT
//                    )
//                        .show()
//                    uiState.postValue(UIModel(true, false))
//                }
//            } else {
//                uiState.postValue(UIModel(true, false))
//            }
        }
    }

    /**
     * 支付渠道报表信息
     *
     * @param context
     * @param startTime
     * @param endTime
     */
    fun printPaymentReport(context: Context, startTime: String, endTime: String) {
        viewModelScope.launch {
//            uiState.postValue(UIModel(false, false))
//            val response =
//                repository.getPayMethodReportPrint(startTime.formatDate3(), endTime.formatDate3())
//            if (response is ApiResponse.Success) {
//
//                if (!response.data.data?.salesPayMethodReportDetails.isNullOrEmpty()) {
//                    Printer.printPrinterPaymentMethodReport(
//                        context,
//                        response.data
//                    )
//
//                    uiState.postValue(UIModel(true, true))
//                } else {
//                    Toast.makeText(
//                        context,
//                        context.getString(R.string.empty_data),
//                        Toast.LENGTH_SHORT
//                    )
//                        .show()
//                    uiState.postValue(UIModel(true, false))
//                }
//            } else {
//                uiState.postValue(UIModel(true, false))
//            }
            uiPayChannelState.postValue(UIPayChannelModel(ApiResponse.Loading))
            val response =
                repository.getPayMethodReportPrint(startTime.formatDate4(), endTime.formatDate4())
            uiPayChannelState.postValue(UIPayChannelModel(response))
        }
    }

    fun printSaleReport(context: Context, startTime: String, endTime: String, keyWord: String,orderByColumn:String?) {
        viewModelScope.launch {
            uiSaleModel.postValue(UISaleModel(ApiResponse.Loading))
            val response =
                repository.getSalesReportPrint(
                    startTime.formatDate4(),
                    endTime.formatDate4(),
                    keyWord,
                    orderByColumn
                )
            uiSaleModel.postValue(UISaleModel(response))
        }
    }

    fun getClassificationList() {
        viewModelScope.launch {
            val response =
                repository.getClassificationList(
                )
            uiClassificationModel.postValue(UIClassificationModel(response))
        }
    }


    fun export(
        context: Context,
        startTime: String,
        endTime: String,
        reportType: Int,
        fileType: Int,
        keyWord: String? = null,
        classificationList: List<GoodClassificationModel>? = null,
        orderByColumn: String? = null,
        callBack: ((String?) -> Unit)? = null
    ) {
        viewModelScope.launch {
            val response =
                repository.getSalesOrdersExport(
                    SalesOrdersExportRequest(
                        startTime.formatDate4().replace("-", "/"),
                        endTime.formatDate4().replace("-", "/"),
                        reportType,
                        fileType,
                        keyWord,
                        (classificationList ?: listOf()).joinToString(
                            separator = ",",
                        ) { it.groupId ?: "" },
                        orderByColumn
                    )
                )
            if (response is ApiResponse.Success) {
                callBack?.invoke(response.data)
            } else if (response is ApiResponse.Error) {
                Toast.makeText(
                    context,
                    response.message,
                    Toast.LENGTH_SHORT
                )
                    .show()
                callBack?.invoke("")
//                uiState.postValue(UIModel(true, false))
            }
        }
    }


    data class UIModel(
        val isEnableRequest: Boolean?,
        val isRequestSuccess: Boolean?,
    )

    data class UISaleModel(
        val response: ApiResponse<SaleReportResponse>?,
    )

    data class UISaleItemsModel(
        val response: ApiResponse<ProductReportResponse>?,
    )

    data class UIPayChannelModel(
        val response: ApiResponse<PaymentMethodReportResponse>?,
    )

    data class UIClassificationModel(
        val response: ApiResponse<List<GoodClassificationModel>>?,
    )
}