package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import com.metathought.food_order.casheir.R

class KeypadAdapter(
    private val context: Context,
    private var items: List<KeypadItem>,
    private val onItemClick: (KeypadItem) -> Unit
) : BaseAdapter() {

    override fun getCount(): Int = items.size

    override fun getItem(position: Int): KeypadItem = items[position]

    override fun getItemId(position: Int): Long = position.toLong()

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: LayoutInflater.from(context)
            .inflate(R.layout.item_keypad, parent, false)
        
        val textView = view.findViewById<TextView>(R.id.keypadText)
        val item = items[position]
        
        textView.text = item.text

        // 计算每个item的高度（GridView高度平分4行）
        val gridView = parent as? android.widget.GridView
        if (gridView != null) {
            val totalHeight = gridView.height - gridView.paddingTop - gridView.paddingBottom
            val numRows = 4
            val verticalSpacing = gridView.verticalSpacing * (numRows - 1)
            val itemHeight = (totalHeight - verticalSpacing) / numRows

            if (itemHeight > 0) {
                val layoutParams = view.layoutParams
                layoutParams.height = itemHeight
                view.layoutParams = layoutParams
            }
        }

        // 根据类型设置不同的样式
        when (item.type) {
            KeypadType.NUMBER, KeypadType.ZERO -> {
                textView.setBackgroundResource(R.drawable.background_white_radius_4dp)
                textView.setTextColor(context.getColor(R.color.black))
            }
            KeypadType.OPERATOR -> {
                textView.setBackgroundResource(R.drawable.background_white_radius_4dp)
                textView.setTextColor(context.getColor(R.color.black))
            }
            KeypadType.CONFIRM -> {
                textView.setBackgroundResource(R.drawable.background_white_radius_4dp)
                textView.setTextColor(context.getColor(R.color.white))
            }
        }
        
        view.setOnClickListener {
            onItemClick(item)
        }
        
        return view
    }

    fun updateData(newItems: List<KeypadItem>) {
        items = newItems
        notifyDataSetChanged()
    }
}
