package com.metathought.food_order.casheir.ui.pending_order

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingRecord
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.databinding.FragmentPendingOrderBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.ui.adapter.PendingOrderAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.dialog.DiscountActivityDialog
import com.metathought.food_order.casheir.ui.dialog.PackPriceDetailDialog
import com.metathought.food_order.casheir.ui.dialog.ServiceFeeDetailDialog
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PendingOrderFragmentDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "PendingOrderFragment"

        //        fun newInstance() = PendingOrderFragment()
//        const val PENDING_FRAGMENT_BUNDLE_DATA = "PENDING_FRAGMENT_BUNDLE_DATA"

        fun showDialog(
            fragmentManager: FragmentManager,
            onRemoveToClear: ((Int) -> Unit)? = null,
            onResumeSuccess: ((Int) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(onRemoveToClear, onResumeSuccess)
            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            onRemoveToClear: ((Int) -> Unit)? = null,
            onResumeSuccess: ((Int) -> Unit)? = null
        ): PendingOrderFragmentDialog {
            val args = Bundle()
            val fragment = PendingOrderFragmentDialog()
            fragment.onRemoveToClear = onRemoveToClear
            fragment.onResumeSuccess = onResumeSuccess
            fragment.arguments = args
            return fragment
        }

    }

    private var onRemoveToClear: ((Int) -> Unit)? = null
    private var onResumeSuccess: ((Int) -> Unit)? = null
    private lateinit var orderedInfoAdapter: OrderedInfoAdapter

    private lateinit var pendingOrderAdapter: PendingOrderAdapter
    private val viewModel: PendingOrderViewModel by viewModels()
    private var _binding: FragmentPendingOrderBinding? = null
    private val binding get() = _binding
    private var currentPendingOrder: PendingRecord? = null


    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                viewModel.getPendingList(true, keyword = edtSearch.getSearchContent())
            }
        } catch (e: Exception) {

        }

    }

    private fun postSearch(duration: Int) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPendingOrderBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        initView()
        initObserver()
        initListener()
        viewModel.getPendingList()
    }

    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    viewModel.getPendingList(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    viewModel.getPendingList(false)
                }

            })
            edtSearch.setTextChangedListenerCallBack {
                postSearch(500)

            }

            btnResumeOrder.setOnClickListener {
                currentPendingOrder?.let {
                    var diningStyleEnum =
                        if (it.isPreOrder == true) DiningStyleEnum.PRE_ORDER.id else it.type ?: 0
                    val shopping = ShoppingHelper.get(diningStyleEnum)
                    if (shopping != null && shopping.getGoodsVoList().isNotEmpty()) {
                        ConfirmDialog.showDialog(
                            parentFragmentManager,
                            positiveButtonTitle = getString(R.string.go_to_menu),
                            content = getString(R.string.clear_items_in_cart_first_to_resume_this_order_confrimation_msg),
                            negativeButtonTitle = getString(R.string.cancel)
                        ) {
                            onRemoveToClear?.invoke(diningStyleEnum)
                            dismissAllowingStateLoss()
//                            val currentFragment =
//                                parentFragmentManager.fragments.firstOrNull()
//                            val bundle = Bundle()
//                            bundle.putInt(
//                                PENDING_FRAGMENT_BUNDLE_DATA,
//                                diningStyleEnum
//                            )
//                            if (currentFragment is MainDashboardFragment) {
//                                currentFragment.replaceFragmentFromOtherFragment(
//                                    FeatureMenuEnum.ORDER.id,
//                                    MenuOrderFragment(), bundle
//                                )
//                            }
                        }
                    } else {
                        it.id?.let { it1 -> viewModel.getPendingOrderByNo(it1) }
                    }
                }
            }

            btnPackPriceCue.setOnClickListener {
                val list = currentPendingOrder?.getPendingGoodJson()?.goodsList?.map {
                    it.orderedGoodsConvertToGoods()
                }

                if (list.isNullOrEmpty()) {
                    return@setOnClickListener
                }

                PackPriceDetailDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    list
                )
            }
            btnServiceFeeCue.setOnClickListener {
                val list = currentPendingOrder?.getPendingGoodJson()?.goodsList?.map {
                    it.setServiceChargePercentage(MainDashboardFragment.CURRENT_USER!!.getCurrentServiceChargePercentage())
                    it.orderedGoodsConvertToGoods()
                }

                if (list.isNullOrEmpty()) {
                    return@setOnClickListener
                }

                ServiceFeeDetailDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    list
                )
            }

            btnDiscountActivityPriceCue.setOnClickListener {
                DiscountActivityDialog.showDialog(
                    activity?.supportFragmentManager ?: parentFragmentManager,
                    currentPendingOrder?.couponActivityList ?: listOf(),
                    isShowVip = isShowVip
                )
            }

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnDelete.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    ConfirmDialog.showDialog(
                        fragmentManager = parentFragmentManager,
                        positiveButtonTitle = getString(R.string.clear),
                        negativeButtonTitle = getString(R.string.cancel),
                        content = getString(
                            R.string.clear_pending_order_confimation_msg
                        )
                    ) {
                        it.id?.let { it1 -> viewModel.deletePendingByOrderNo(orderNO = currentPendingOrder?.id.toString()) }
                    }
                }
            }
        }

    }

    private fun initView() {
        binding?.apply {
            pendingOrderAdapter = PendingOrderAdapter(
                arrayListOf(),
                onItemClickListener = { record ->
                    setDetailData(record)
                }, onDeleteClickListener = {
//                    ConfirmDialog.showDialog(
//                        fragmentManager = parentFragmentManager,
//                        positiveButtonTitle = getString(R.string.clear),
//                        negativeButtonTitle = getString(R.string.cancel),
//                        content = getString(
//                            R.string.clear_pending_order_confimation_msg
//                        )
//                    ) {
//                        it.id?.let { it1 -> viewModel.deletePendingByOrderNo(orderNO = it1) }
//                    }
                })
            recyclerPendingOrder.adapter = pendingOrderAdapter
            orderedInfoAdapter = OrderedInfoAdapter(arrayListOf())
            orderedInfoRecyclerView.adapter = orderedInfoAdapter
        }
    }

    private fun initObserver() {
        viewModel.uiListState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it) {
                        showProgress()
                    }
                    llProduct.isGone = it
                }


                if (it.showEnd) {
                    //判断一下搜索结果是不是当前 关键字的
//                    Timber.e("搜索关键字 ${it.keyword}   当前关键字 ${edtSearch.text}")
                    if (!it.keyword.isNullOrEmpty() && edtSearch.getSearchContent() != it.keyword) {
                        return@observe
                    }

                    dismissProgress()

                    if (it.isRefresh != false) {
                        layoutMenu.setVisibleGone(false)
                        layoutPendingList.setVisibleGone(false)
                        pendingOrderAdapter?.replaceData(arrayListOf())
                        layoutEmptyList.root.setVisibleGone(true)
                        layoutDetailInfo.setVisibleInvisible(false)
                        layoutEmptyDetail.root.setVisibleGone(true)
                        refreshLayout.finishRefresh()
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    refreshLayout.finishRefresh()
                    refreshLayout.finishLoadMore()
                    dismissProgress()
//                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    dismissProgress()
                    layoutMenu.setVisibleGone(true)
                    layoutPendingList.setVisibleGone(true)
                    layoutEmptyList.root.setVisibleGone(false)
                    layoutEmptyDetail.root.setVisibleGone(false)
                    layoutDetailInfo.setVisibleGone(true)
                    if (it.isRefresh != false) {
                        ArrayList(response.records).let { it1 ->
                            pendingOrderAdapter.replaceData(
                                it1
                            )
                        }
                        response.records?.firstOrNull()?.let { it1 -> setDetailData(it1) }
                        pendingOrderAdapter.setSelectFirst()
                        refreshLayout.finishRefresh()

                    } else {
                        ArrayList(response.records).let { it1 -> pendingOrderAdapter.addData(it1) }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }
        viewModel.uiSaveRecordState.observe(viewLifecycleOwner) {
            it.showLoading?.let {
                showProgress()
            }
            it.showSuccess?.let {
                dismissProgress()
//                val currentFragment =
//                    parentFragmentManager.fragments.firstOrNull()
                val diningStyleEnum =
                    if (currentPendingOrder?.isPreOrder == true) DiningStyleEnum.PRE_ORDER.id else currentPendingOrder?.type
                        ?: 0
//                val bundle = Bundle()
//                bundle.putInt(
//                    PENDING_FRAGMENT_BUNDLE_DATA,
//                    diningStyleEnum
//                )
//                if (currentFragment is MainDashboardFragment) {
//                    currentFragment.replaceFragmentFromOtherFragment(
//                        FeatureMenuEnum.ORDER.id,
//                        MenuOrderFragment(), bundle
//                    )
//                }
                onResumeSuccess?.invoke(diningStyleEnum)
                dismissAllowingStateLoss()
            }
        }

        viewModel.uiGetRecordState.observe(viewLifecycleOwner) {
            it?.let {

                when (it.pendingRecord) {
                    is ApiResponse.Loading -> {
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        dismissProgress()
                        if (it.pendingRecord.data == null) {
                            currentPendingOrder?.getPendingGoodJson()?.goodsList?.clear()
                        } else {
                            currentPendingOrder = it.pendingRecord.data
                        }

                        moveToMenu()

                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
//                        showToast(it.pendingRecord.message ?: "")

                    }

                    else -> {}
                }


            }


        }


        viewModel.uiDeleteState.observe(viewLifecycleOwner) { uiDeleteState ->
            uiDeleteState.response?.let {
                when (it) {
                    is ApiResponse.Loading -> {
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        dismissProgress()
                        binding?.apply {
                            viewModel.getPendingList(true, keyword = edtSearch.getSearchContent())
                        }

                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
//                        showToast(it.message ?: "")

                    }

                    else -> {}
                }
            }
        }
    }

//    override fun onLoad() {
//        super.onLoad()
//
//    }

    private var isShowVip = false
    private fun setDetailData(pendingRecord: PendingRecord) {

        currentPendingOrder = pendingRecord
        binding?.apply {
            llCustomerInfo.isVisible =
                !pendingRecord.getCustomerJson()?.name.isNullOrEmpty() || !pendingRecord.getCustomerJson()
                    ?.getMobilePhone().isNullOrEmpty()
                        || (pendingRecord.getCustomerJson()?.diningNumber
                    ?: 0) > 0 || !pendingRecord.getCustomerJson()?.diningTime.isNullOrEmpty()
            llCustomerName.isVisible = !pendingRecord.getCustomerJson()?.name.isNullOrEmpty()
            llPeople.isVisible = (pendingRecord.getCustomerJson()?.diningNumber ?: 0) > 0
            llDiningTime.isVisible = !pendingRecord.getCustomerJson()?.diningTime.isNullOrEmpty()
            tvCustomerName.text = pendingRecord.getCustomerJson()?.name
            llCustomerPhone.isVisible =
                pendingRecord.getCustomerJson()?.mobile?.isNotEmpty() ?: false
            tvPhoneNumber.text = pendingRecord.getCustomerJson()?.getMobilePhoneDisplay()
            tvDiningTime.text = pendingRecord.getCustomerJson()?.diningTime?.formatDate()
            tvPeople.text = pendingRecord.getCustomerJson()?.diningNumber.toString()
//            tvTableID.text = pendingRecord.serialNumber
            tvPendingOrderID.text = pendingRecord.id
            tvPendingOrderTime.text = pendingRecord.createTime?.formatDate()
            if (pendingRecord.isPreOrder == true) {
                tvOrderType.text = getString(R.string.pre_order)
            } else {
                when (pendingRecord.type) {
                    DiningStyleEnum.DINE_IN.id -> tvOrderType.text = getString(R.string.dine_in)
                    DiningStyleEnum.TAKE_AWAY.id -> tvOrderType.text = getString(R.string.take_away)
                }
            }
            tvRemark.text =
                if (pendingRecord.note.isNullOrEmpty()) getString(R.string.none) else pendingRecord.note

            if (tvRemark.lineCount <= 1) {
                tvRemark.gravity = Gravity.END
            } else {
                tvRemark.gravity = Gravity.START
            }


            val orderedGoodJson = pendingRecord.getPendingGoodJson()

            orderedGoodJson?.let {

                if (pendingRecord.type == DiningStyleEnum.TAKE_AWAY.id) {
                    //打包费
                    val totalPackPrice = orderedGoodJson.getTotalPackagingFee()
                    llPackPrice.isVisible = totalPackPrice > 0
                    tvPackingAmount.text = "${totalPackPrice.priceFormatTwoDigitZero2()}"
                } else {
                    llPackPrice.isVisible = false
                }


                orderedInfoAdapter.replaceData(
                    ArrayList(
                        pendingRecord.getPendingGoodJson()?.goodsList ?: arrayListOf()
                    )
                )

                isShowVip = it.isShowVipPrice()
                val isHasDiscountPrice = it.isHasDiscountPrice()
                var totalVipPrice = orderedGoodJson.totalVipPrice ?: 0
                var totalDiscountPrice = orderedGoodJson.totalDiscountPrice ?: 0
                //=======优惠活动金额================
                if (currentPendingOrder?.couponActivityList.isNullOrEmpty()) {
                    llDiscountActivity.isVisible = false
                } else {
                    btnDiscountActivityPriceCue.isVisible = true
                    if (currentPendingOrder?.couponActivityList?.size == 1) {
                        tvDiscountActivityTitle.text =
                            currentPendingOrder?.couponActivityList?.firstOrNull()?.activityLabelName
                    } else {
                        tvDiscountActivityTitle.text = getString(R.string.discount_activity)
                    }
                    val discountActivityUnWeightList =
                        currentPendingOrder?.couponActivityList?.filter { it.weightMark == true }
                            ?: listOf()
                    if (discountActivityUnWeightList.isNullOrEmpty()) {
                        val couponActivityAmount =
                            currentPendingOrder?.getTotalCouponActivityAmount()
                        tvDiscountActivityAmount.text =
                            "-${couponActivityAmount?.priceFormatTwoDigitZero2()}"
                        totalDiscountPrice -= couponActivityAmount ?: 0L

                        val couponActivityVipAmount =
                            currentPendingOrder?.getTotalVipCouponActivityAmount()
                        totalVipPrice -= (couponActivityVipAmount ?: 0L)
                    } else {
                        tvDiscountActivityAmount.text = getString(R.string.to_be_confirmed)
                    }
                    llDiscountActivity.isVisible = true
                }

                //==============================


                //会员价
                tvVipPrice.text = "${totalVipPrice.priceFormatTwoDigitZero2()}"
                tvVipPrice.isVisible = isShowVip


                tvVAT.text = "${orderedGoodJson.totalDiscountVatPrice?.priceFormatTwoDigitZero2()}"

                tvServiceFee.text =
                    "${orderedGoodJson.totalDiscountServiceCharge?.priceFormatTwoDigitZero2()}"


                tvSubtotal.text = orderedGoodJson.getSubTotal().priceFormatTwoDigitZero2()

                tvTotal.text = "${totalDiscountPrice?.priceFormatTwoDigitZero2()}"


                if (isShowVip) {
//                    tvOriginalPrice.isVisible = false

                    llServiceFee.isVisible = (orderedGoodJson.totalDiscountServiceCharge
                        ?: 0) > 0 || (orderedGoodJson.totalVipServiceCharge ?: 0) > 0
                    llVat.isVisible = (orderedGoodJson.totalDiscountVatPrice
                        ?: 0) > 0 || (orderedGoodJson.totalVipVatPrice ?: 0) > 0
                } else {
                    tvVipPrice.isVisible = false

                    llServiceFee.isVisible = (orderedGoodJson.totalDiscountServiceCharge ?: 0) > 0
                    llVat.isVisible = (orderedGoodJson.totalDiscountVatPrice ?: 0) > 0

                    if (isHasDiscountPrice) {
                        //如果有折扣价 则对应的显示的都是折扣价的数值
//                        tvOriginalPrice.isVisible = true
                        tvSubtotal.text =
                            "${orderedGoodJson.getDiscountSubTotal()?.priceFormatTwoDigitZero2()}"
                        tvTotal.text =
                            "${totalDiscountPrice?.priceFormatTwoDigitZero2()}"
//                        tvOriginalPrice.text =
//                            "${pendingRecord.totalPrice?.priceFormatTwoDigitZero2()}"
                    } else {
//                        tvOriginalPrice.isVisible = false
                    }
                }

                tvTotal.requestLayout()

                //未定价商品数量
                var unProcessNum = 0
                //未定价 服务费白名单商品数量
                var unProcessServiceWhiteGoodsNum = 0

                pendingRecord.getPendingGoodJson()?.goodsList?.forEach { orderGoods ->
                    if (!orderGoods.isHasProcessed()) {
                        unProcessNum += 1
                        if (orderGoods.serviceChargeWhitelisting == true) {
                            unProcessServiceWhiteGoodsNum += 1
                        }
                    }
                }

                //如果还含有称重商品
                if (pendingRecord.isHasNeedProcess()) {

                    tvSubtotal.text = getString(R.string.to_be_confirmed)
                    tvTotal.text = getString(R.string.to_be_confirmed)
                    tvVipPrice.isVisible = false

                    tvVAT.text = getString(R.string.to_be_confirmed)
                    llVat.isVisible =
                        (MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage() != 0)


                    if (pendingRecord.type != DiningStyleEnum.TAKE_AWAY.id) {
                        if (unProcessNum > unProcessServiceWhiteGoodsNum && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)) {
                            tvServiceFee.text = getString(R.string.to_be_confirmed)
                            llServiceFee.isVisible = true
                        }
                    }
                }
            }
        }
    }

    private fun moveToMenu() {
        currentPendingOrder?.let {
            val reserveTableRequest = ReserveTableRequest(
                diningTime = it.getCustomerJson()?.diningTime ?: "",
                diningNumber = it.getCustomerJson()?.diningNumber ?: 0,
                areaCode = it.getCustomerJson()?.areaCode ?: "",
                mobile = it.getCustomerJson()?.mobile ?: "",
                name = it.getCustomerJson()?.name ?: "",
                tableId = ""
            )
            val diningStyleEnum =
                if (it.isPreOrder == true) DiningStyleEnum.PRE_ORDER.id else it.type ?: 0
            it.getPendingGoodJson()?.goodsList?.let { it1 ->
                viewModel.updateShoppingRecord(
                    reserveTableRequest, diningStyleEnum,
                    it1, it.orderNote
                )
            }
        }
    }

    private fun showProgress() {
        binding?.apply {
            pbLoad.isVisible = true
        }
    }

    private fun dismissProgress() {
        binding?.apply {
            pbLoad.isVisible = false
        }
    }

    //Socket
    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.9).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}