package com.metathought.food_order.casheir.ui.dialog.notice

import android.content.Context

import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogNoticeListBinding
import com.metathought.food_order.casheir.ui.adapter.NoticeListAdapter
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NoticeListDialog : DialogFragment() {
    private var binding: DialogNoticeListBinding? = null

    private val viewModel: NoticeViewModel by viewModels()

    private var noticeListAdapter: NoticeListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogNoticeListBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initListener()
        initData()
        initObserver()
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
//                    pbOrderedList.isVisible = it

                }

                it.showError?.let { error ->
                    refreshLayout.finishRefresh()
                    refreshLayout.finishLoadMore()
                }

                it.showSuccess?.let { response ->
                    if (it.isRefresh != false) {
                        noticeListAdapter?.replaceData(ArrayList(response.records ?: listOf()))
                        refreshLayout.finishRefresh()
                    } else {
                        noticeListAdapter?.addData(ArrayList(response.records ?: listOf()))
                        refreshLayout.finishLoadMore()
                    }
                }

                if (it.showEnd) {
                    if (it.isRefresh != false) {
                        refreshLayout.finishRefresh()
                        refreshLayout.finishRefresh()
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    layoutEmpty.root.isVisible = noticeListAdapter?.list.isNullOrEmpty()
                    layoutEmpty.tvEmptyText.text = getString(R.string.no_notice)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.35).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {

        binding?.apply {
            noticeListAdapter = NoticeListAdapter(arrayListOf()) {
                NoticeDetailDialog.showDialog(fragmentManager = parentFragmentManager, it)
            }
            rvList.adapter = noticeListAdapter
            refreshLayout.autoRefresh()
        }

    }

    private fun initListener() {
        binding?.apply {

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            refreshLayout.setEnableRefresh(true)
            refreshLayout.setEnableLoadMore(true)
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    viewModel.getNoticeList(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    viewModel.getNoticeList(false)
                }

            })

        }
    }

    companion object {
        private const val TAG = "NoticeListDialog"


        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? NoticeListDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(

        ): NoticeListDialog {

            val fragment = NoticeListDialog()
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}



