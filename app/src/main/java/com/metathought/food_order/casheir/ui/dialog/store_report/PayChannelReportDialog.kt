package com.metathought.food_order.casheir.ui.dialog.store_report

import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITH_SECOND
import com.metathought.food_order.casheir.constant.ReportDateType
import com.metathought.food_order.casheir.constant.ReportExportEnum
import com.metathought.food_order.casheir.constant.ReportFileType
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.databinding.DialogPayChannelReportBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.PermissionHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.ui.adapter.PayChannelReportListAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.print_report.PrintReportViewModel
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.math.BigDecimal
import java.util.Date


/**
 *<AUTHOR>
 *@time  2025/4/14
 *@desc 支付渠道报表
 **/

@AndroidEntryPoint
class PayChannelReportDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "PayChannelReportDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? PayChannelReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): PayChannelReportDialog {
            val args = Bundle()
            val fragment = PayChannelReportDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private var binding: DialogPayChannelReportBinding? = null
    private val viewModel: PrintReportViewModel by viewModels()

    private var responseData: PaymentMethodReportResponse? = null

    //时间类型
    private var dateType = ReportDateType.YESTERDAY.id
    private var startTime = Date()
    private var endTime = Date()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPayChannelReportBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        initListener()
        initObserver()
        initView()
    }


    private fun initObserver() {
        viewModel.uiPayChannelState.observe(viewLifecycleOwner) { state ->
            when (state.response) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        pbLoading.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        responseData = state.response.data
                        pbLoading.isVisible = false

                        initData()
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLoading.isVisible = false
                    }
                }

                else -> {

                }

            }
        }
    }

    private fun initView() {
        binding?.apply {
            tvTotalOrderTitle.text = tvTotalOrderTitle.text.toString().uppercase()
            tvTotalOrderAmountTitle.text = tvTotalOrderAmountTitle.text.toString().uppercase()

            onlinePayment.text = "${onlinePayment.text}($)"
            offlinePayment.text = "${offlinePayment.text}($)"
            balancePayment.text = "${balancePayment.text}($)"
            creditPayment.text = "${creditPayment.text}($)"


            rvList.adapter = adapter

            initDate()

            requestData()
        }
    }

    private fun initDate() {
        binding?.apply {
            lifecycleScope.launch {
                val storeInfo = PreferenceHelper.getStoreInfo()
                val startCalendar = Calendar.getInstance()
                if (storeInfo?.isSetOpenStartTime() == true) {
                    //如果有设置营业时间,判断今天的营业时间时候已经到了
                    if (storeInfo.isCurrentInOpenTime()) {
                        dateType = ReportDateType.TODAY.id
                        Timber.e("今天的营业时间已经到了")
                        //营业时间-当前时间
                        val startTimeList = storeInfo.getStartTime()
                        endTime = (startCalendar.clone() as Calendar).time

                        //然后设置营业开始时间
                        startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(Calendar.SECOND, startTimeList[2])
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                    } else {
                        dateType = ReportDateType.YESTERDAY.id
                        //今天的营业时间还没到就显示昨天的数据
                        Timber.e("今天的营业时间还没到就显示昨天的数据")
                        val startTimeList = storeInfo.getStartTime()
                        startCalendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
                        //然后设置营业开始时间
                        startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(Calendar.SECOND, startTimeList[2])
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                        // 先将当前日期调整为今天  的营业开始时间为结束时间
                        startCalendar.add(Calendar.DAY_OF_MONTH, +1)
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        endTime = (startCalendar.clone() as Calendar).time
                    }
                } else {
                    //没设置营业 时间 显示昨天的数据
                    dateType = ReportDateType.YESTERDAY.id
                    startCalendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
                    // 获取昨天开始时间（零点整）
                    startCalendar.set(Calendar.HOUR_OF_DAY, 0)
                    startCalendar.set(Calendar.MINUTE, 0)
                    startCalendar.set(Calendar.SECOND, 0)
                    startCalendar.set(Calendar.MILLISECOND, 0)
                    startTime = (startCalendar.clone() as Calendar).time

                    // 获取昨天结束时间（23点59分59秒）
                    startCalendar.set(Calendar.HOUR_OF_DAY, 23)
                    startCalendar.set(Calendar.MINUTE, 59)
                    startCalendar.set(Calendar.SECOND, 59)
                    startCalendar.set(Calendar.MILLISECOND, 999)
                    endTime = startCalendar.time
                }
            }

            tvCalendar.text = "${startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)} - ${
                endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
            }"
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            llCalendar.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    ReportDateSelectDialog.showDialog(
                        parentFragmentManager,
                        startTime,
                        endTime,
                        dateType
                    ) { startTime1, endTime1, dateType1 ->
                        startTime = startTime1
                        endTime = endTime1
                        tvCalendar.text =
                            "${startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)} - ${
                                endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                            }"
                        dateType = dateType1

                        requestData()
                    }
                }
            }

            btnPrint.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (responseData == null || responseData?.data?.salesPayMethodReportDetails.isNullOrEmpty()) {
                        showToast(requireContext().getString(R.string.empty_data))
                        return@isFastDoubleClick
                    }
                    Printer.printPrinterPaymentMethodReport(
                        requireContext(),
                        responseData
                    )
                }
            }

            btnExport.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (responseData == null || responseData?.data?.salesPayMethodReportDetails.isNullOrEmpty()) {
                        showToast(requireContext().getString(R.string.empty_data))
                        return@isFastDoubleClick
                    }
                    if (PermissionHelper.checkPermissions(requireActivity())) {
                        btnExport.setEnableWithAlpha(false)
                        viewModel.export(
                            requireContext(),
                            startTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                            endTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                            ReportExportEnum.OFFLINE_CHANNEL_REPORT.id,
                            ReportFileType.PDF.id
                        ) { url ->
                            if (url != null) {
                                download(url)
                            } else {
                                btnExport.setEnableWithAlpha(true)
                            }
                        }
                    } else {
                        PermissionHelper.requestPermissions(requireActivity())
                    }
                }
            }
        }
    }

    private var adapter = PayChannelReportListAdapter(ArrayList())

    private fun initData() {
        binding?.apply {
            tvOnlineAmount.text = (responseData?.data?.totalOnlineAmount?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvOfflineAmount.text = (responseData?.data?.totalOfflineAmount?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvBalanceAmount.text = (responseData?.data?.totalBalanceAmount?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvCreditAmount.text = (responseData?.data?.totalCreditAmount?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()

            tvOrderNum.text = "${responseData?.data?.totalOrderNum ?: 0}"
            tvTotalOrderAmount.text =
                (responseData?.data?.totalOrderAmount?.toBigDecimalOrNull()
                    ?: BigDecimal.ZERO).priceFormatTwoDigitZero()
            if (responseData?.data?.salesPayMethodReportDetails.isNullOrEmpty()) {
                llBottom.isVisible = false
                layoutEmpty.root.isVisible = true
                rvList.isVisible = false
            } else {
                llBottom.isVisible = true
                layoutEmpty.root.isVisible = false
                rvList.isVisible = true
                adapter.replaceData(
                    ArrayList(
                        responseData?.data?.salesPayMethodReportDetails ?: listOf()
                    )
                )
            }
        }
    }

    private fun requestData() {
        binding?.apply {
            val list = tvCalendar.text.toString().split("-")
            if (list.size >= 2) {
                viewModel.printPaymentReport(
                    requireContext(),
                    startTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                    endTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
                )
            }

        }
    }

    private fun download(url: String) {
        val fileName = FileUtil.getFileNameInUrl(url)
        val dir = FolderHelper.getDownloadFolderPath()
        DownloadManager.getInstance()
            .download(
                TAG,
                url,
                fileName,
                dir ?: "",
                object : DownloadListener {
                    override fun onProgress(progress: Long, max: Long) {
                        requireActivity().apply {
                            Timber.e("progress $progress")
                            runOnUiThread {
                                binding?.apply {
                                    val currentProgress =
                                        ((progress * 1.0f / max).times(100)).toInt()
                                }
                            }
                        }
                    }

                    override fun onSuccess(localPath: String) {
                        val file = File(dir, fileName)
                        val renameToRes = File(localPath).renameTo(file)
                        FileUtil.scanFile(requireActivity(), file)
                        requireActivity().runOnUiThread {
                            binding?.apply {
                                btnExport.setEnableWithAlpha(true)
                            }
                            Toast.makeText(
                                requireActivity(),
                                requireActivity().getString(
                                    R.string.save_at_location,
                                    file.absolutePath
                                ),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                    }

                    override fun onFail(errorInfo: String) {
                        requireActivity().apply {
                            runOnUiThread {
                                binding?.apply {
                                    btnExport.setEnableWithAlpha(true)
                                }
                                if (errorInfo.isNotEmpty())
                                    Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                            }
                        }
                    }
                })
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.95).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}