package com.metathought.food_order.casheir

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.tencent.bugly.crashreport.CrashReport
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import org.litepal.LitePal
import org.litepal.LitePalDB
import timber.log.Timber


@AndroidEntryPoint
class MainActivity : AppCompatActivity() {
    companion object {
        var mainActivityInstance: MainActivity? = null

        var OVERLAY_PERMISSION_RESPONSE = 1000
    }

    // 创建一个广播接收器
    val usbPrinterReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                    // USB设备已插入
                    println("USB设备已插入")
//                    Timber.e("selectPrinter?.queryApi()?.status?.name11  ${selectPrinter?.queryApi()?.status?.name}")
//                    PrinterDeviceHelper.initConnectPrinter()
//                    EventBus.getDefault()
//                        .post(SimpleEvent(SimpleEventType.UPDATE_USB_PRINT_INFO, null))
                    /**
                     * 还没搞懂为啥summi 内置打印 没打一次usb 插拔会回调一次 所以这里先屏蔽了
                     */
                    if (Printer.isXPrinter()) {
                        PrinterUsbDeviceHelper.initUsbPrintAndConnectUSB()
                    }
                }

                UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                    // USB设备已移除
                    println("USB设备已移除")
//                    EventBus.getDefault()
//                        .post(SimpleEvent(SimpleEventType.UPDATE_USB_PRINT_INFO, null))
//                    Timber.e("selectPrinter?.queryApi()?.status?.name22  ${selectPrinter?.queryApi()?.status?.name}")
                    if (Printer.isXPrinter()) {
                        PrinterUsbDeviceHelper.initUsbPrintAndConnectUSB()
                    }
//                    PrinterDeviceHelper.resetPrinter()
                }
            }
        }
    }

    private val progressView: View by lazy { findViewById(R.id.layoutProgress) }
    private val mListApiShowDialog = arrayListOf<Long>()


    override fun onCreate(savedInstanceState: Bundle?) {
//        enableEdgeToEdge()

        super.onCreate(savedInstanceState)
//        hideNavigatorBar()

        setContentView(R.layout.activity_main)
        showProgress()
        try {
            MyApplication.myAppInstance.initSecondary(this)
//            if (Build.VERSION.SDK_INT >= 23) {
//                if (Settings.canDrawOverlays(this)) {
//                    MyApplication.myAppInstance.initSecondary(this)
//                } else {
//                    val uri = Uri.parse("package:" + <EMAIL>)
//                    val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, uri)
//                    startActivityForResult(intent, OVERLAY_PERMISSION_RESPONSE)
//                }
//            }
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }

        registerUsbPrinterReceiver()

//        SecondaryManager.onCreate()
//        SecondaryManagerV2.onCreate()
        Log.e("MainDashboard", "MainActivity_onCreate")
        lifecycleScope.launch {
            PreferenceDataStoreHelper.getInstance(this@MainActivity).apply {

                val userLoginResponse = Gson().fromJson(
                    getFirstPreference(
                        PreferenceDataStoreConstants.DATA_STORE_KEY_USER_SESSION,
                        ""
                    ), UserLoginResponse::class.java
                )
                MainDashboardFragment.CURRENT_USER = userLoginResponse
                Timber.e("userLoginResponse  ${userLoginResponse}")
                if (userLoginResponse != null) {
                    Timber.e("userLoginResponse.userAccount: ${userLoginResponse.userAccount}")
                    val litePalDB = LitePalDB.fromDefault(userLoginResponse.userAccount)
                    LitePal.use(litePalDB)
                }

                val navHostFragment =
                    supportFragmentManager.findFragmentById(R.id.fragment) as NavHostFragment
                val graphInflater = navHostFragment.navController.navInflater
                val navGraph = graphInflater.inflate(R.navigation.nav_main)
                val navController = navHostFragment.navController
                Timber.e("userLoginResponse?.token?.isNotEmpty(): ${userLoginResponse?.token?.isNotEmpty()}")
                val destination =
                    if (userLoginResponse?.token?.isNotEmpty() == true) R.id.mainDashboardFragment else R.id.loginFragment

                navGraph.setStartDestination(destination)
                navController.graph = navGraph


            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            OVERLAY_PERMISSION_RESPONSE -> {
                try {
                    if (Settings.canDrawOverlays(this)) {
                        MyApplication.myAppInstance.initSecondary(this)
                    }
                } catch (e: Exception) {
                    CrashReport.postCatchedException(e)
                }
            }
        }
    }


    private fun hideNavigatorBar() {
//        //隐藏虚拟按键，并且全屏
        if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) { // lower api
            val v = this.window.decorView
            v.systemUiVisibility = View.GONE
        } else if (Build.VERSION.SDK_INT >= 19) {
            //for new api versions.
            val decorView = window.decorView
            val uiOptions = (View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or View.SYSTEM_UI_FLAG_FULLSCREEN)
//            or View.SYSTEM_UI_FLAG_FULLSCREEN
            decorView.systemUiVisibility = uiOptions
        }
//        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
//            WindowManager.LayoutParams.FLAG_FULLSCREEN);


    }

    fun dissmissAllFragmentDialog() {
        supportFragmentManager.fragments.forEach { fragment ->
            if (fragment is DialogFragment) {
                fragment.dismiss()
            }
        }

    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase?.let { LocaleHelper.onAttach(it) })
    }

    fun showProgress() {
        runOnUiThread {
            Timber.e("显示几次弹窗")
            progressView.isVisible = true
            mListApiShowDialog.add(System.currentTimeMillis())
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
            )
        }
    }

    fun dismissProgress() {
        runOnUiThread {
            Timber.e("关闭几次弹窗")
            mListApiShowDialog.removeLastOrNull()
            if (mListApiShowDialog.isEmpty()) {
                window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
                progressView.isVisible = false
            }
        }
    }

    fun clearProgress() {
        Timber.e("清空弹窗")
        runOnUiThread {
            mListApiShowDialog.clear()
            if (mListApiShowDialog.isEmpty()) {
                window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
                progressView.isVisible = false

            }
        }
    }

    override fun onResume() {
        super.onResume()
        mainActivityInstance = this
    }

    override fun onPause() {
        Log.e("MainDashboard", "MainActivity_onPause")
//        SecondaryManager.dismissScreen()
        super.onPause()
        mainActivityInstance = null
    }

    override fun onStop() {
        Log.e("MainDashboard", "MainActivity_onStop")
        super.onStop()
    }

    override fun onDestroy() {
        Log.e("MainDashboard", "MainActivity_onDestroy")
//        SecondaryManagerV2.onDestroy()
//        SecondaryManager.onDestroy()
        unregisterUsbPrinterReceiver()
        if (MyApplication.myAppInstance.orderedScreen != null) {
            Timber.e("取消副屏弹窗")
            MyApplication.myAppInstance.orderedScreen?.dismiss()
            MyApplication.myAppInstance.orderedScreen = null
        }
        super.onDestroy()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.hideKeyboard(currentFocus)
        return super.dispatchTouchEvent(ev);
    }


    fun showToast(buttonName: String) {
        Timber.e("#${buttonName}#")
        if (buttonName.isNotEmpty())
            Toast.makeText(this@MainActivity, "$buttonName", Toast.LENGTH_LONG).show()
    }

    // 注册广播接收器
    private fun registerUsbPrinterReceiver() {
        val filter = IntentFilter()
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        <EMAIL>(usbPrinterReceiver, filter)
    }

    // 注销广播接收器
    private fun unregisterUsbPrinterReceiver() {
        <EMAIL>(usbPrinterReceiver)
    }


}