package com.metathought.food_order.casheir.ui.dialog.single_discount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogSingleDiscountBinding
import com.metathought.food_order.casheir.ui.adapter.SingleDiscountAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import java.io.Serializable

/**
 * Tmp good dialog
 *
 * @constructor 单品减免
 */
class SingleDiscountDialog : BaseDialogFragment() {

    private var binding: DialogSingleDiscountBinding? = null
    private var positiveButtonListener: ((SingleDiscountData?, String) -> Unit)? = null
    private var singleDiscountData: SingleDiscountData? = null
    private var adapter: SingleDiscountAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSingleDiscountBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        arguments?.getSerializable(SINGLE_DISCOUNT_GOODS)?.let {
            singleDiscountData = it as SingleDiscountData
        }
        initListener()
    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }
            adapter = SingleDiscountAdapter { singleDiscountGoods ->
//                EditSingleDiscountDialog.showDialog(
//                    activity?.supportFragmentManager ?: parentFragmentManager, false,
//                    singleDiscountGoods
//                ) { goods ->
//                    val index = singleDiscountData?.goods?.indexOf(goods) ?: -1
//                    if (index == -1) return@showDialog
//                    adapter?.notifyItemChanged(index)
//                }
            }
            adapter?.replaceData(singleDiscountData?.goods)
            rvDiscountSingleGood.adapter = adapter

            btnNo.setOnClickListener {
                dismissAllowingStateLoss()
            }

            btnYes.setOnClickListener {
                val remark = edtRemark.getText().toString().trim()
                //过滤掉没有设置类型的数据
                val gooods = singleDiscountData?.goods?.filter { it.type != null }
                singleDiscountData?.goods = gooods
                positiveButtonListener?.invoke(singleDiscountData, remark)
            }

            edtRemark.setText(singleDiscountData?.remark)

        }
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

    }


    companion object {
        private const val TAG = "SingleDiscountDialog"
        private const val SINGLE_DISCOUNT_GOODS = "single_Discount_goods"

        fun showDialog(
            fragmentManager: FragmentManager,
            singleDiscountData: SingleDiscountData,
            positiveButtonListener: ((SingleDiscountData?, String) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(singleDiscountData, positiveButtonListener)
            fragment.show(fragmentManager, TAG)
        }


        fun getCurrentCancelOrderDialog(fragmentManager: FragmentManager): SingleDiscountDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SingleDiscountDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SingleDiscountDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            singleDiscountData: SingleDiscountData,
            positiveButtonListener: ((SingleDiscountData?, String) -> Unit),
        ): SingleDiscountDialog {
            val args = Bundle()
            val fragment = SingleDiscountDialog()
            args.putSerializable(SINGLE_DISCOUNT_GOODS, singleDiscountData)
            fragment.arguments = args
            fragment.positiveButtonListener = positiveButtonListener
            return fragment
        }
    }
}


data class SingleDiscountData(
    var remark: String?,
    var goods: List<SingleDiscountGoods>?,
) : Serializable

/**
 * 单品减免 本地的model
 */
data class SingleDiscountGoods(
    @SerializedName("name")
    val name: String?,
    @SerializedName("num")
    var num: Int?,
    @SerializedName("singlePrice")
    val singlePrice: Long? = null,
    @SerializedName("singleVipPrice")
    val singleVipPrice: Long? = null,
    @SerializedName("totalPrice")
    var totalPrice: Long,
    @SerializedName("vipPrice")
    var vipPrice: Long,
    @SerializedName("isShowVipPrice")
    val isShowVipPrice: Boolean,
    @SerializedName("goodsId")
    var goodsId: String?,    //单品id
    @SerializedName("goodsHashKey")
    var goodsHashKey: String?,    //商品hashKey
    @SerializedName("type")
    var type: Int? = null,    //类型 1.百分比 2.固定金额
    @SerializedName("reduceRatio")
    var reduceRatio: Double? = null,    //减免（百分比）
    @SerializedName("saleReduce")
    var saleReduce: Double? = null,    //销售减免
    @SerializedName("vipReduce")
    var vipReduce: Double? = null,    //会员减免
    /**
     *   销售-改价
     */
    @SerializedName("adjustSalePrice")
    var adjustSalePrice: Double? = null,

    /**
     *   会员-改价
     */
    @SerializedName("adjustVipPrice")
    var adjustVipPrice: Double? = null,

    @SerializedName("remark")
    var remark: String? = null,    //备注

    /**
     * 所选的后台配置的折扣id
     */
    @SerializedName("discountReduceActivityId")
    var discountReduceActivityId: String? = null,

    /**
     * 原因类型 ：0：discount(正常折扣) 1:void(菜品有问题时原因必填)
     */
    @SerializedName("discountType")
    var discountType: Int? = null,


    @SerializedName("discountReduceInfo") //本地的后台配置折扣信息
    var discountReduceInfo: DiscountReduceInfo? = null

) : Serializable {
    fun clone(): SingleDiscountGoods {
        val stringJson = MyApplication.globalGson.toJson(this, SingleDiscountGoods::class.java)
        return MyApplication.globalGson.fromJson<SingleDiscountGoods>(
            stringJson,
            SingleDiscountGoods::class.java
        )
    }

    /**
     * 是否有设置过单品减免折扣
     *
     * @return
     */
    fun isSetSingleItemDiscount(): Boolean {
        if (type == null) {
            return false
        } else if (type == 1) {
            return reduceRatio != null || discountReduceActivityId != null
        } else if (type == 2) {
            return saleReduce != null || vipReduce != null || discountReduceActivityId != null
        } else if (type == 3) {
            return adjustVipPrice != null || adjustSalePrice != null || discountReduceActivityId != null
        }
        return false
    }

    fun coverToSingleDiscountRequest(): SingleDiscountRequest {
        return SingleDiscountRequest(
            type = type,
            goodsId = goodsId,
            goodsHashKey = null,  //挪进来后 就不用传这个了
            reduceRatio = reduceRatio,
            saleReduce = if (saleReduce != null) saleReduce?.toDouble() else null,
            vipReduce = if (vipReduce != null) vipReduce?.toDouble() else null,
            adjustSalePrice = adjustSalePrice,
            adjustVipPrice = adjustVipPrice,
            remark = remark,
            discountType = discountType,
            discountReduceActivityId = discountReduceActivityId,
            goodsNum = num
        )
    }

}
